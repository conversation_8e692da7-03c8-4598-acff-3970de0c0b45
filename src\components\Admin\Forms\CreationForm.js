import React, { useEffect, useState } from "react";
import { useForm, Controller } from "react-hook-form";
import Select from "react-select";
import { useSelector } from "react-redux";
import "./CreationForm.css";
import {
  useSaveCourseDataMutation,
  useSaveChaptersDataMutation,
  useSaveTopicsDataMutation,
  useDeleteCourseDataMutation,
  useDeleteChapterDataMutation,
  useDeleteTopicDataMutation,
} from "APIConnect";
import {
  getAllSubjects,
  getAllCourses,
  getAllChapters,
  getAllTopics,
} from "@/common/basicAPI";

const courseFormData = {
  name: "",
  description: "",
  subjectId: "",
};

const chapterFormData = {
  name: "",
  description: "",
  courseId: "",
};

const topicFormData = {
  name: "",
  description: "",
  chapterId: "",
};

const CreationForm = () => {
  const [formType, setFormType] = useState("course");
  const {
    register,
    handleSubmit,
    control,
    setValue,
    getValues,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues:
      formType === "course"
        ? courseFormData
        : formType === "chapter"
        ? chapterFormData
        : topicFormData,
  });

  const [saveCourseData] = useSaveCourseDataMutation();
  const [saveChaptersData] = useSaveChaptersDataMutation();
  const [saveTopicsData] = useSaveTopicsDataMutation();
  const [deleteCourseData] = useDeleteCourseDataMutation();
  const [deleteChapterData] = useDeleteChapterDataMutation();
  const [deleteTopicData] = useDeleteTopicDataMutation();

  const allCourses = useSelector((state) => state.appDetails.allCourses);
  const allChapters = useSelector((state) => state.appDetails.allChapters);
  const allTopics = useSelector((state) => state.appDetails.allTopics);

  const [subjectsList, setSubjectsList] = useState([]);
  const [coursesList, setCoursesList] = useState([]);
  const [chaptersList, setChaptersList] = useState([]);
  const [showForm, setShowForm] = useState(false);

  useEffect(() => {
    getSubjects();
    getCourses();
    getChapters();
    getTopics();
  }, []);

  useEffect(() => {
    handleReset();
  }, [formType]);

  // useEffect(() => {
  //   const handleResize = () => {
  //     window.location.reload();
  //   };

  //   window.addEventListener("resize", handleResize);
  //   return () => window.removeEventListener("resize", handleResize);
  // }, []);

  const getSubjects = async () => {
    try {
      const resultObject = await getAllSubjects();
      if (resultObject) {
        let data = [],
          obj = {};
        resultObject?.map((item) => {
          obj = {
            value: item?.id,
            label: item?.name,
          };
          data.push(obj);
        });
        setSubjectsList(data);
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const getCourses = async () => {
    try {
      const resultObject = await getAllCourses();
      if (resultObject) {
        let data = [],
          obj = {};
        resultObject?.map((item) => {
          obj = {
            value: item?.id,
            label: item?.name,
          };
          data.push(obj);
        });
        setCoursesList(data);
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const getChapters = async () => {
    try {
      const resultObject = await getAllChapters();
      if (resultObject) {
        let data = [],
          obj = {};
        resultObject?.map((item) => {
          obj = {
            value: item?.id,
            label: item?.name,
          };
          data.push(obj);
        });
        setChaptersList(data);
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const getTopics = async () => {
    try {
      await getAllTopics();
    } catch (err) {
      console.log("err", err);
    }
  };

  const onSubmit = (data) => {
    handleReset();
    if (formType === "course") {
      createCourseData(data);
    } else if (formType === "chapter") {
      createChaptersData(data);
    } else if (formType === "topic") {
      createTopicsData(data);
    }
  };

  const createCourseData = async (data) => {
    data.Premium = true;
    try {
      const {
        data: { resultObject },
      } = await saveCourseData(data);
      if (resultObject) {
        getCourses();
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const createChaptersData = async (data) => {
    try {
      const {
        data: { resultObject },
      } = await saveChaptersData(data);
      if (resultObject) {
        getChapters();
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const createTopicsData = async (data) => {
    try {
      const {
        data: { resultObject },
      } = await saveTopicsData(data);
      if (resultObject) {
        getTopics();
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const handleDelete = (formType, id) => {
    if (formType === "course") {
      deleteCourse(id);
    } else if (formType === "chapter") {
      deleteChapter(id);
    } else if (formType === "topic") {
      deleteTopic(id);
    }
  };

  const deleteCourse = async (id) => {
    try {
      const {
        data: { resultObject },
      } = await deleteCourseData({ id });
      if (resultObject) {
        getCourses();
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const deleteChapter = async (id) => {
    try {
      const {
        data: { resultObject },
      } = await deleteChapterData(id);
      if (resultObject) {
        getChapters();
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const deleteTopic = async (id) => {
    try {
      const {
        data: { resultObject },
      } = await deleteTopicData(id);
      if (resultObject) {
        getTopics();
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const handleFormType = (type) => {
    setFormType(type);
    setShowForm(false);
  };

  const handleReset = () => {
    reset(courseFormData);
    reset(chapterFormData);
    reset(topicFormData);
  };

  const handleCancel = () => {
    setShowForm(false);
    handleReset();
  };

  return (
    <div className="outContainer">
      <div className="sidebar">
        <button onClick={() => handleFormType("course")}>Courses</button>
        <button onClick={() => handleFormType("chapter")}>Chapters</button>
        <button onClick={() => handleFormType("topic")}>Topics</button>
      </div>
      <div className="form-area">
        {formType === "course" && (
          <>
            <button onClick={() => setShowForm(true)} className="create-button">
              Create Course
            </button>
            {showForm && (
              <form
                onSubmit={handleSubmit(onSubmit)}
                className={`creation-form ${showForm ? "show" : ""}`}
              >
                <div className="form-group">
                  <div className="form-row">
                    <label className="form-label">
                      Name:<span className="compulsoryInd">*</span>
                    </label>
                    <input
                      type="text"
                      name="name"
                      className="form-input"
                      {...register("name", { required: "Name is required." })}
                    />
                  </div>
                  {errors.name && (
                    <p className="form-error">{errors.name.message}</p>
                  )}
                </div>
                <div className="form-group">
                  <div className="form-row">
                    <label className="form-label">
                      Description:<span className="compulsoryInd">*</span>
                    </label>
                    <textarea
                      type="text"
                      name="description"
                      className="form-input"
                      {...register("description", {
                        required: "Description is required.",
                      })}
                    />
                  </div>
                  {errors.description && (
                    <p className="form-error">{errors.description.message}</p>
                  )}
                </div>
                <div className="form-group">
                  <div className="form-row">
                    <label className="form-label">
                      Select Subject:<span className="compulsoryInd">*</span>
                    </label>

                    <Controller
                      defaultValue=""
                      name="subjectId"
                      isClearable={true}
                      control={control}
                      rules={{ required: "Subject is required" }}
                      render={({ field }) => (
                        <Select
                          {...field}
                          options={subjectsList}
                          className="form-input select-input"
                          value={
                            subjectsList.find(
                              (option) =>
                                option.value === getValues("subjectId")
                            ) || ""
                          }
                          onChange={(item) =>
                            setValue("subjectId", item?.value)
                          }
                        />
                      )}
                    />
                  </div>
                  {errors.subjectId && (
                    <p className="form-error">{errors.subjectId.message}</p>
                  )}
                </div>
                <div className="form-buttons">
                  <button type="submit" className="form-submit">
                    Submit
                  </button>
                  <button
                    type="button"
                    className="form-cancel"
                    onClick={() => handleCancel()}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            )}

            <div className="table-container">
              <table className="styled-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Description</th>
                    <th>Subject</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {allCourses?.length > 0 ? (
                    allCourses.map((item, index) => (
                      <tr key={index}>
                        <td>{item.name}</td>
                        <td>{item.description}</td>
                        <td>{item.subjectId}</td>
                        <td>
                          <button
                            className="icon-button"
                            onClick={() => handleDelete("course", item.id)}
                          >
                            ❌
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="4" className="noDataFound">
                        No data found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </>
        )}
        {formType === "chapter" && (
          <>
            <button onClick={() => setShowForm(true)} className="create-button">
              Create Chapter
            </button>
            {showForm && (
              <form
                onSubmit={handleSubmit(onSubmit)}
                className={`creation-form ${showForm ? "show" : ""}`}
              >
                <div className="form-group">
                  <div className="form-row">
                    <label className="form-label">
                      Name:<span className="compulsoryInd">*</span>
                    </label>
                    <input
                      type="text"
                      name="name"
                      className="form-input"
                      {...register("name", { required: "Name is required." })}
                    />
                  </div>
                  {errors.name && (
                    <p className="form-error">{errors.name.message}</p>
                  )}
                </div>
                <div className="form-group">
                  <div className="form-row">
                    <label className="form-label">
                      Description:<span className="compulsoryInd">*</span>
                    </label>
                    <textarea
                      type="text"
                      name="description"
                      className="form-input"
                      {...register("description", {
                        required: "Description is required.",
                      })}
                    />
                  </div>
                  {errors.description && (
                    <p className="form-error">{errors.description.message}</p>
                  )}
                </div>
                <div className="form-group">
                  <div className="form-row">
                    <label className="form-label">
                      Select Course:<span className="compulsoryInd">*</span>
                    </label>
                    <Controller
                      name="courseId"
                      isClearable={true}
                      control={control}
                      value={getValues("courseId")}
                      rules={{ required: "Course is required" }}
                      render={({ field }) => (
                        <Select
                          {...field}
                          options={coursesList}
                          className="form-input select-input"
                          value={
                            coursesList.find(
                              (option) => option.value === getValues("courseId")
                            ) || ""
                          }
                          onChange={(item) => setValue("courseId", item?.value)}
                        />
                      )}
                    />
                  </div>
                  {errors.courseId && (
                    <p className="form-error">{errors.courseId.message}</p>
                  )}
                </div>
                <div className="form-buttons">
                  <button type="submit" className="form-submit">
                    Submit
                  </button>
                  <button
                    type="button"
                    className="form-cancel"
                    onClick={() => handleCancel()}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            )}

            <div className="table-container">
              <table className="styled-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Description</th>
                    <th>Course</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {allChapters?.length > 0 ? (
                    allChapters.map((item, index) => (
                      <tr key={index}>
                        <td>{item.name}</td>
                        <td>{item.description}</td>
                        <td>{item.courseId}</td>
                        <td>
                          <button
                            className="icon-button"
                            onClick={() => handleDelete("chapter", item.id)}
                          >
                            ❌
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="4" className="noDataFound">
                        No data found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </>
        )}
        {formType === "topic" && (
          <>
            <button onClick={() => setShowForm(true)} className="create-button">
              Create Topic
            </button>
            {showForm && (
              <form
                onSubmit={handleSubmit(onSubmit)}
                className={`creation-form ${showForm ? "show" : ""}`}
              >
                <div className="form-group">
                  <div className="form-row">
                    <label className="form-label">
                      Name:<span className="compulsoryInd">*</span>
                    </label>
                    <input
                      type="text"
                      name="name"
                      className="form-input"
                      {...register("name", { required: "Name is required." })}
                    />
                  </div>
                  {errors.name && (
                    <p className="form-error">{errors.name.message}</p>
                  )}
                </div>
                <div className="form-group">
                  <div className="form-row">
                    <label className="form-label">
                      Description:<span className="compulsoryInd">*</span>
                    </label>
                    <textarea
                      type="text"
                      name="description"
                      className="form-input"
                      {...register("description", {
                        required: "Description is required.",
                      })}
                    />
                  </div>
                  {errors.description && (
                    <p className="form-error">{errors.description.message}</p>
                  )}
                </div>
                <div className="form-group">
                  <div className="form-row">
                    <label className="form-label">
                      Select Chapter:<span className="compulsoryInd">*</span>
                    </label>
                    <Controller
                      name="chapterId"
                      isClearable={true}
                      control={control}
                      value={getValues("chapterId")}
                      rules={{ required: "Chapter is required" }}
                      render={({ field }) => (
                        <Select
                          {...field}
                          options={chaptersList}
                          className="form-input select-input"
                          value={
                            chaptersList.find(
                              (option) =>
                                option.value === getValues("chapterId")
                            ) || ""
                          }
                          onChange={(item) =>
                            setValue("chapterId", item?.value)
                          }
                        />
                      )}
                    />
                  </div>
                  {errors.chapterId && (
                    <p className="form-error">{errors.chapterId.message}</p>
                  )}
                </div>
                <div className="form-buttons">
                  <button type="submit" className="form-submit">
                    Submit
                  </button>
                  <button
                    type="button"
                    className="form-cancel"
                    onClick={() => handleCancel()}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            )}

            <div className="table-container">
              <table className="styled-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Description</th>
                    <th>Chapter</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {allTopics?.length > 0 ? (
                    allTopics.map((item, index) => (
                      <tr key={index}>
                        <td>{item.name}</td>
                        <td>{item.description}</td>
                        <td>{item.chapterId}</td>
                        <td>
                          <button
                            className="icon-button"
                            onClick={() => handleDelete("topic", item.id)}
                          >
                            ❌
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="4" className="noDataFound">
                        No data found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CreationForm;
