import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Trash2 } from 'lucide-react';
import ResourceMetadataForm from './ResourceMetadataForm';
import RichTextEditor from './RichTextEditor';
import { Question, QuestionType } from './types';

interface QuestionFormProps {
    question: Question | null;
    isEdit: boolean;
    selectedTopic: string;
    onSubmit: (formData: Omit<Question, 'id'>) => Promise<void>;
}

const QuestionForm: React.FC<QuestionFormProps> = ({
    question,
    isEdit,
    selectedTopic,
    onSubmit
}) => {
    const [formData, setFormData] = useState<Omit<Question, 'id'>>({
        anteriorId: question?.anteriorId || '',
        questionText: question?.questionText || '',
        mark: question?.mark || 0,
        answers: question?.answers || [{
            answerText: '',
            answerImageMetadata: null,
            answerVideoMetadata: null,
            answerAudioMetadata: null
        }],
        type: question?.type || 'SINGLECHOICE',
        correctAnswer: question?.correctAnswer || null,
        explanation: question?.explanation || '',
        level: question?.level || 1,
        active: question?.active ?? true,
        topicId: question?.topicId || selectedTopic,
        createdBy: question?.createdBy || 'Test',
        questionImageMetadata: question?.questionImageMetadata || null,
        questionVideoMetadata: question?.questionVideoMetadata || null,
        questionAudioMetadata: question?.questionAudioMetadata || null,
        explanationImageMetadata: question?.explanationImageMetadata || null,
        explanationVideoMetadata: question?.explanationVideoMetadata || null,
        explanationAudioMetadata: question?.explanationAudioMetadata || null,
    });

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        await onSubmit(formData);
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="basic">Basic Info</TabsTrigger>
                    <TabsTrigger value="media">Media</TabsTrigger>
                    <TabsTrigger value="answers">Answers</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4">
                    <div className="space-y-4">
                        <div>
                            <Label>Question Text</Label>
                            <div className="mt-2">
                                <RichTextEditor
                                    value={formData.questionText}
                                    onChange={(content) => setFormData({ ...formData, questionText: content })}
                                    placeholder="Enter question text..."
                                />
                            </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label>Type</Label>
                                <Select
                                    value={formData.type}
                                    onValueChange={(value) => setFormData({ ...formData, type: value as QuestionType })}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="SINGLECHOICE">Single Choice</SelectItem>
                                        <SelectItem value="MULTICHOICE">Multiple Choice</SelectItem>
                                        <SelectItem value="TEXTINPUT">Text Input</SelectItem>
                                        <SelectItem value="BINARY">Binary</SelectItem>
                                        <SelectItem value="MATCH">Match</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div>
                                <Label>Level</Label>
                                <Input
                                    type="number"
                                    value={formData.level}
                                    onChange={(e) => setFormData({ ...formData, level: parseInt(e.target.value) })}
                                    min={1}
                                    max={10}
                                />
                            </div>
                        </div>

                        <div>
                            <Label>Explanation</Label>
                            <div className="mt-2">
                                <RichTextEditor
                                    value={formData.explanation}
                                    onChange={(content) => setFormData({ ...formData, explanation: content })}
                                    placeholder="Enter explanation..."
                                />
                            </div>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Switch
                                id="active"
                                checked={formData.active}
                                onCheckedChange={(checked) => setFormData({ ...formData, active: checked })}
                            />
                            <Label htmlFor="active">Active</Label>
                        </div>
                    </div>
                </TabsContent>

                <TabsContent value="media" className="space-y-4">
                    <ResourceMetadataForm
                        label="Question Image"
                        metadata={formData.questionImageMetadata}
                        onChange={(metadata) => setFormData({ ...formData, questionImageMetadata: metadata })}
                    />
                    <ResourceMetadataForm
                        label="Question Video"
                        metadata={formData.questionVideoMetadata}
                        onChange={(metadata) => setFormData({ ...formData, questionVideoMetadata: metadata })}
                    />
                    <ResourceMetadataForm
                        label="Question Audio"
                        metadata={formData.questionAudioMetadata}
                        onChange={(metadata) => setFormData({ ...formData, questionAudioMetadata: metadata })}
                    />
                    <ResourceMetadataForm
                        label="Explanation Image"
                        metadata={formData.explanationImageMetadata}
                        onChange={(metadata) => setFormData({ ...formData, explanationImageMetadata: metadata })}
                    />
                    <ResourceMetadataForm
                        label="Explanation Video"
                        metadata={formData.explanationVideoMetadata}
                        onChange={(metadata) => setFormData({ ...formData, explanationVideoMetadata: metadata })}
                    />
                    <ResourceMetadataForm
                        label="Explanation Audio"
                        metadata={formData.explanationAudioMetadata}
                        onChange={(metadata) => setFormData({ ...formData, explanationAudioMetadata: metadata })}
                    />
                </TabsContent>

                <TabsContent value="answers" className="space-y-4">
                    {formData.answers.map((answer, index) => (
                        <Card key={index}>
                            <CardContent className="pt-6 space-y-4">
                                <div className="flex items-start justify-between">
                                    <div className="flex-1 mr-4">
                                        <Label>Answer Text</Label>
                                        <div className="mt-2">
                                            <RichTextEditor
                                                value={answer.answerText}
                                                onChange={(content) => {
                                                    const newAnswers = [...formData.answers];
                                                    newAnswers[index].answerText = content;
                                                    setFormData({ ...formData, answers: newAnswers });
                                                }}
                                                placeholder={`Enter answer ${index + 1}...`}
                                            />
                                        </div>
                                    </div>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        type="button"
                                        onClick={() => {
                                            const newAnswers = formData.answers.filter((_, i) => i !== index);
                                            setFormData({ ...formData, answers: newAnswers });
                                        }}
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </Button>
                                </div>

                                <ResourceMetadataForm
                                    label="Answer Image"
                                    metadata={answer.answerImageMetadata}
                                    onChange={(metadata) => {
                                        const newAnswers = [...formData.answers];
                                        newAnswers[index].answerImageMetadata = metadata;
                                        setFormData({ ...formData, answers: newAnswers });
                                    }}
                                />

                                <ResourceMetadataForm
                                    label="Answer Video"
                                    metadata={answer.answerVideoMetadata}
                                    onChange={(metadata) => {
                                        const newAnswers = [...formData.answers];
                                        newAnswers[index].answerVideoMetadata = metadata;
                                        setFormData({ ...formData, answers: newAnswers });
                                    }}
                                />

                                <ResourceMetadataForm
                                    label="Answer Audio"
                                    metadata={answer.answerAudioMetadata}
                                    onChange={(metadata) => {
                                        const newAnswers = [...formData.answers];
                                        newAnswers[index].answerAudioMetadata = metadata;
                                        setFormData({ ...formData, answers: newAnswers });
                                    }}
                                />
                            </CardContent>
                        </Card>
                    ))}

                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => setFormData({
                            ...formData,
                            answers: [...formData.answers, {
                                answerText: '',
                                answerImageMetadata: null,
                                answerVideoMetadata: null,
                                answerAudioMetadata: null
                            }]
                        })}
                    >
                        <Plus className="w-4 h-4 mr-2" />
                        Add Answer
                    </Button>
                </TabsContent>
            </Tabs>

            <div className="flex justify-end space-x-2">
                <Button type="submit" className="w-32">
                    {isEdit ? 'Update' : 'Create'}
                </Button>
            </div>
        </form>
    );
};

export default QuestionForm;
