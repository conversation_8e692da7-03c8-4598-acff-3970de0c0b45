import { useState, useEffect, useCallback } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { 
  discussionsApi, 
  commentsApi, 
  moderationApi, 
  leaderboardApi, 
  mediaApi,
  Discussion,
  Comment,
  CreateDiscussionPayload,
  CreateCommentPayload
} from '../services/discussionsApi';
import { toast } from './use-toast';

// Extended parameters for discussions
interface DiscussionParams {
  page?: number;
  limit?: number;
  courseId?: string;
  classId?: string;
  topicId?: string;
  schoolId?: string;
  requiresModeration?: boolean;
  searchQuery?: string;
  filter?: string;
  sort?: string;
}

// Hook for fetching discussions with pagination
export const useDiscussions = (initialPage = 1, pageSize = 20, params: DiscussionParams = {}) => {
  const [discussions, setDiscussions] = useState<Discussion[]>([]);
  const [pagination, setPagination] = useState({
    page: initialPage,
    limit: pageSize,
    total: 0,
    pages: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getAccessTokenSilently } = useAuth0();

  const fetchDiscussions = useCallback(async (currentPage = initialPage, currentLimit = pageSize) => {
    setLoading(true);
    setError(null);

    try {
      const token = await getAccessTokenSilently();
      const result = await discussionsApi.getDiscussions(token, currentPage, currentLimit, {
        ...params,
        page: currentPage,
        limit: currentLimit
      });
      
      setDiscussions(result.data);
      setPagination({
        page: result.page,
        limit: result.limit,
        total: result.total,
        pages: result.pages
      });
    } catch (err) {
      console.error('Error fetching discussions:', err);
      setError('Failed to load discussions');
    } finally {
      setLoading(false);
    }
  }, [getAccessTokenSilently, initialPage, pageSize, params]);

  useEffect(() => {
    fetchDiscussions();
  }, [fetchDiscussions]);

  return { discussions, pagination, loading, error, refetch: fetchDiscussions };
};

// Hook for fetching a single discussion
export const useDiscussion = (id: string) => {
  const [discussion, setDiscussion] = useState<Discussion | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getAccessTokenSilently } = useAuth0();

  useEffect(() => {
    const fetchDiscussion = async () => {
      if (!id) return;

      setLoading(true);
      setError(null);

      try {
        const token = await getAccessTokenSilently();
        const result = await discussionsApi.getDiscussion(token, id);
        setDiscussion(result);
      } catch (err) {
        console.error('Error fetching discussion:', err);
        setError('Failed to load discussion');
      } finally {
        setLoading(false);
      }
    };

    fetchDiscussion();
  }, [id, getAccessTokenSilently]);

  return { discussion, loading, error };
};

// Hook for creating a new discussion
export const useCreateDiscussion = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const { getAccessTokenSilently } = useAuth0();

  const createDiscussion = async (payload: CreateDiscussionPayload) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const token = await getAccessTokenSilently();
      const result = await discussionsApi.createDiscussion(token, payload);
      setSuccess(true);
      return result;
    } catch (err) {
      console.error('Error creating discussion:', err);
      setError('Failed to create discussion');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { createDiscussion, loading, error, success };
};

// Hook for deleting a discussion
export const useDeleteDiscussion = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const { getAccessTokenSilently } = useAuth0();

  const deleteDiscussion = async (id: string) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const token = await getAccessTokenSilently();
      await discussionsApi.deleteDiscussion(token, id);
      setSuccess(true);
      return true;
    } catch (err) {
      console.error('Error deleting discussion:', err);
      setError('Failed to delete discussion');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return { deleteDiscussion, loading, error, success };
};

// Hook for fetching comments for a discussion
export const useComments = (discussionId: string, initialPage = 1, pageSize = 20) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [pagination, setPagination] = useState({
    page: initialPage,
    limit: pageSize,
    total: 0,
    pages: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getAccessTokenSilently } = useAuth0();

  const fetchComments = useCallback(async (page = initialPage, limit = pageSize) => {
    if (!discussionId) return;
    
    setLoading(true);
    setError(null);

    try {
      const token = await getAccessTokenSilently();
      const result = await commentsApi.getComments(token, discussionId, page, limit);
      
      setComments(result.data);
      setPagination({
        page: result.page,
        limit: result.limit,
        total: result.total,
        pages: result.pages
      });
    } catch (err) {
      console.error('Error fetching comments:', err);
      setError('Failed to load comments');
    } finally {
      setLoading(false);
    }
  }, [discussionId, getAccessTokenSilently]);

  useEffect(() => {
    fetchComments(initialPage, pageSize);
  }, [discussionId, initialPage, pageSize, fetchComments]);

  return { comments, pagination, loading, error, refetch: fetchComments };
};

// Hook for creating a comment
export const useCreateComment = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getAccessTokenSilently } = useAuth0();

  const createComment = async (payload: CreateCommentPayload) => {
    setLoading(true);
    setError(null);

    try {
      const token = await getAccessTokenSilently();
      const result = await commentsApi.createComment(token, payload);
      return result;
    } catch (err) {
      console.error('Error creating comment:', err);
      setError('Failed to post comment');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { createComment, loading, error };
};

// Hook for deleting a comment
export const useDeleteComment = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getAccessTokenSilently } = useAuth0();

  const deleteComment = async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const token = await getAccessTokenSilently();
      await commentsApi.deleteComment(token, id);
      return true;
    } catch (err) {
      console.error('Error deleting comment:', err);
      setError('Failed to delete comment');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return { deleteComment, loading, error };
};

// Hook for voting on a comment
export const useVoteComment = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getAccessTokenSilently } = useAuth0();

  const voteComment = async (id: string, value: 1 | -1) => {
    setLoading(true);
    setError(null);

    try {
      const token = await getAccessTokenSilently();
      const result = await commentsApi.voteComment(token, id, value);
      return result;
    } catch (err) {
      console.error('Error voting on comment:', err);
      setError('Failed to vote on comment');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { voteComment, loading, error };
};

// Hook for fetching pending moderation items
export const usePendingModeration = (initialPage = 1, pageSize = 10) => {
  const [moderationItems, setModerationItems] = useState<Comment[]>([]);
  const [pagination, setPagination] = useState({
    page: initialPage,
    limit: pageSize,
    total: 0,
    pages: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getAccessTokenSilently } = useAuth0();

  const fetchModerationItems = useCallback(async (page = initialPage, limit = pageSize) => {
    setLoading(true);
    setError(null);

    try {
      const token = await getAccessTokenSilently();
      const result = await moderationApi.getPendingModeration(token, page, limit);
      
      setModerationItems(result.data);
      setPagination({
        page: result.page,
        limit: result.limit,
        total: result.total,
        pages: result.pages
      });
    } catch (err) {
      console.error('Error fetching moderation items:', err);
      setError('Failed to load moderation items');
    } finally {
      setLoading(false);
    }
  }, [getAccessTokenSilently, initialPage, pageSize]);

  useEffect(() => {
    fetchModerationItems();
  }, [fetchModerationItems]);

  return { moderationItems, pagination, loading, error, refetch: fetchModerationItems };
};

// Hook for fetching moderation statistics
export const useModerationStats = () => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getAccessTokenSilently } = useAuth0();

  const fetchStats = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const token = await getAccessTokenSilently();
      const result = await moderationApi.getModerationStats(token);
      setStats(result);
    } catch (err) {
      console.error('Error fetching moderation stats:', err);
      setError('Failed to load moderation statistics');
    } finally {
      setLoading(false);
    }
  }, [getAccessTokenSilently]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return { stats, loading, error, refetch: fetchStats };
};

// Hook for fetching leaderboard
export const useLeaderboard = (timeRange: 'daily' | 'weekly' | 'monthly' | 'alltime' = 'weekly') => {
  const [leaderboard, setLeaderboard] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getAccessTokenSilently } = useAuth0();

  const fetchLeaderboard = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const token = await getAccessTokenSilently();
      const result = await leaderboardApi.getLeaderboard(token, timeRange);
      setLeaderboard(result);
    } catch (err) {
      console.error('Error fetching leaderboard:', err);
      setError('Failed to load leaderboard data');
    } finally {
      setLoading(false);
    }
  }, [getAccessTokenSilently, timeRange]);

  useEffect(() => {
    fetchLeaderboard();
  }, [fetchLeaderboard]);

  return { leaderboard, loading, error, refetch: fetchLeaderboard };
};

// Hook for updating leaderboard (admin/teacher only)
export const useUpdateLeaderboard = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getAccessTokenSilently } = useAuth0();

  const updateLeaderboard = async (timeRange: string = 'weekly') => {
    setLoading(true);
    setError(null);

    try {
      const token = await getAccessTokenSilently();
      await leaderboardApi.updateLeaderboard(token, timeRange);
      return true;
    } catch (err) {
      console.error('Error updating leaderboard:', err);
      setError('Failed to update leaderboard');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return { updateLeaderboard, loading, error };
};

// Hook for uploading media
export const useMediaUpload = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getAccessTokenSilently } = useAuth0();

  const uploadMedia = async (file: File) => {
    setLoading(true);
    setError(null);

    try {
      const token = await getAccessTokenSilently();
      const fileUrl = await mediaApi.uploadMedia(token, file);
      return fileUrl;
    } catch (err) {
      console.error('Error uploading media:', err);
      setError('Failed to upload media file');
      toast({
        title: "Upload failed",
        description: "Could not upload file. Please try again.",
        variant: "destructive"
      });
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { uploadMedia, loading, error };
};