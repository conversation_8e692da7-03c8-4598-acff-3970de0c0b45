@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');

.dashboard-container {
  display: flex;
  flex-direction: column;
  /* justify-content: center; */
  /* align-items: center; */
  position: relative;
  margin-top: 8px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: hidden;
}


/* Overlay */
.dashboard-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.dashboard-modal-content {
  background-color: #FFFFFF;
  padding: 20px;
  border-radius: 12px;
  width: 400px;
  text-align: center;
  box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease-in-out;
}

.dashboard-modal-content .dashboard-tc-message {
  font-size: 18px;
  color: #333;
  margin-bottom: 20px;
  font-style: italic;
}

.dashboard-agree-btn {
  font-size: 18px;
  background-color: #703D3D;
  color: #FFFFFF;
  border-radius: 32px;
  font-weight: bold;
  border: solid;
  height: 40px;
  width: 45%;
}

.dashboard-nochapter-block {
  background-color: #ffffe7;
  width: 100vh;
  height: 100vh;
}