import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import {
  Upload,
  Image,
  Video,
  FileText,
  Calendar,
  Clock,
  CheckCircle
} from 'lucide-react';
import { motion } from 'framer-motion';
import { FileFolderSDK } from '@/lib/FileFolderSdk';
import {
  useLazyGetStudentSubmissionQuery,
  useCreateSubmissionMutation,
  useUpdateSubmissionMutation
} from '@/APIConnect';

interface CaseStudyRendererProps {
  title?: string;
  type?: string;
  description?: string;
  deadline?: string;
  completedTasks?: number;
  totalTasks?: number;
  assignmentId?: string; // subtopicId
  classroomId?: string;
  courseId?: string;
  onFileUpload?: (files: FileList) => void;
  onCompleted?: () => void;
}

const CaseStudyRenderer: React.FC<CaseStudyRendererProps> = ({
  title = "Case Study 1",
  type = "Academic",
  description = "This course provides a thorough exploration of the diverse world of pronouns, essential tools for clear and effective communication. We will move beyond the basic understanding of pronouns as replacements for nouns, delving into the nuanced",
  deadline = "26th June 2025, 12:00 AM",
  completedTasks = 5,
  totalTasks = 10,
  assignmentId,
  classroomId,
  courseId,
  onFileUpload,
  onCompleted
}) => {
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadedFiles, setUploadedFiles] = useState<FileList | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [existingSubmission, setExistingSubmission] = useState<any>(null);
  const [submissionSuccess, setSubmissionSuccess] = useState(false);
  const [isLoadingSubmission, setIsLoadingSubmission] = useState(false);

  // Initialize FileFolderSDK
  const fileFolderSDK = new FileFolderSDK();

  // API hooks
  const [getStudentSubmission] = useLazyGetStudentSubmissionQuery();
  const [createSubmission] = useCreateSubmissionMutation();
  const [updateSubmission] = useUpdateSubmissionMutation();

  // Dynamic progress based on submission status
  const isCompleted = existingSubmission || submissionSuccess;
  const dynamicCompletedTasks = isCompleted ? 1 : 0;
  const dynamicTotalTasks = 1; // Case study is a single assignment
  const progressPercentage = Math.round((dynamicCompletedTasks / dynamicTotalTasks) * 100);

  // Load existing submission on component mount
  useEffect(() => {
    if (assignmentId && classroomId) {
      loadExistingSubmission();
    }
  }, [assignmentId, classroomId]);

  const loadExistingSubmission = async () => {
    if (!assignmentId || !classroomId) return;

    setIsLoadingSubmission(true);
    try {
      console.log('Loading existing submission for case study:', { assignmentId, classroomId });
      const result = await getStudentSubmission({
        classroomId,
        assignmentId
      });

      console.log('Existing case study submission result:', result);

      if (result.data && result.data.resultObject) {
        const submission = result.data.resultObject;
        setExistingSubmission(submission);
        console.log('Existing case study submission loaded:', submission);
      }
    } catch (error) {
      console.error('Error loading existing case study submission:', error);
    } finally {
      setIsLoadingSubmission(false);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setUploadedFiles(e.dataTransfer.files);
      console.log('File dropped:', e.dataTransfer.files[0].name);
      onFileUpload?.(e.dataTransfer.files);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setUploadedFiles(e.target.files);
      console.log('File selected:', e.target.files[0].name);
      onFileUpload?.(e.target.files);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const handleSubmit = async () => {
    if (!uploadedFiles || !assignmentId || !classroomId) {
      console.error('Missing required data for submission:', {
        uploadedFiles: !!uploadedFiles,
        assignmentId: !!assignmentId,
        classroomId: !!classroomId
      });
      return;
    }

    setIsSubmitting(true);
    setIsUploading(true);

    try {
      // Step 1: Upload file to get URL
      const file = uploadedFiles[0];
      const uniquePrefix = Date.now();
      const fileName = `case-studies/${assignmentId}/${uniquePrefix}-${file.name}`;

      console.log('Uploading file:', fileName);
      const fileUrl = await fileFolderSDK.uploadFile(
        file,
        fileName,
        (progress) => setUploadProgress(progress)
      );

      console.log('File uploaded successfully:', fileUrl);

      // Step 2: Create or update submission
      const submissionData = {
        classroomId,
        assignmentId,
        fileUrl,
        additionalFiles: [],
        selfReflectiveAnswers: []
      };

      console.log('Creating submission with data:', submissionData);

      let result;
      if (existingSubmission) {
        console.log('Updating existing submission...');
        result = await updateSubmission(submissionData);
      } else {
        console.log('Creating new submission...');
        result = await createSubmission(submissionData);
      }

      if (result.error) {
        throw new Error(`Submission failed: ${JSON.stringify(result.error)}`);
      }

      console.log('Submission successful:', result);

      // Show success message
      setSubmissionSuccess(true);

      // Update existing submission with the new data
      if (result.data?.resultObject) {
        setExistingSubmission(result.data.resultObject);
      }

      onCompleted?.();

      // Reload submission data
      await loadExistingSubmission();

    } catch (error) {
      console.error('Error submitting case study:', error);
      alert('Failed to submit assignment. Please try again.');
    } finally {
      setIsSubmitting(false);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="flex items-center justify-end p-6 border-b border-gray-200">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Calendar className="w-4 h-4" />
          <span>Deadline- {deadline}</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6 max-w-6xl mx-auto">
        {/* Title and Type */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{title}</h1>
          <div className="flex items-center gap-2 mb-4">
            <span className="text-lg text-gray-600">Type-</span>
            <span className="text-lg font-medium text-[#347468]">{type}</span>
          </div>
          
          {/* Description */}
          <p className="text-gray-700 leading-relaxed mb-6">
            {description}
          </p>
        </motion.div>

        {/* Success Message */}
        {submissionSuccess && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-green-800">Assignment Submitted Successfully!</h3>
                    <p className="text-green-600">Your case study assignment has been submitted successfully.</p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setSubmissionSuccess(false)}
                  className="text-green-700 border-green-300 hover:bg-green-100"
                >
                  Continue Editing
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Loading State */}
        {isLoadingSubmission && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card>
              <CardContent className="p-8 text-center">
                <div className="h-8 w-8 rounded-full border-4 border-[#347468] border-t-transparent animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600">Loading your submission...</p>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Existing Submission Display */}
        {existingSubmission && !submissionSuccess && !isLoadingSubmission && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-blue-800 flex items-center gap-2">
                  <CheckCircle className="w-5 h-5" />
                  Previously Submitted
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-blue-600 mb-2">
                      <strong>Submitted on:</strong> {new Date(existingSubmission.createdOn).toLocaleDateString()} at {new Date(existingSubmission.createdOn).toLocaleTimeString()}
                    </p>
                    {existingSubmission.score !== undefined && (
                      <p className="text-sm text-blue-600 mb-2">
                        <strong>Score:</strong> {existingSubmission.score}
                      </p>
                    )}
                  </div>

                  {existingSubmission.fileUrl && (
                    <div>
                      <p className="text-sm font-medium text-blue-800 mb-2">Submitted File:</p>
                      <div className="bg-white p-3 rounded border flex items-center gap-3">
                        <FileText className="w-5 h-5 text-blue-600" />
                        <div className="flex-grow">
                          <a
                            href={existingSubmission.fileUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 underline text-sm font-medium"
                          >
                            View Submitted File
                          </a>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="pt-4 border-t border-blue-200">
                    <p className="text-sm text-blue-600 mb-3">
                      You can update your submission by uploading a new file below and resubmitting.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Progress Section */}
        {!submissionSuccess && !isLoadingSubmission && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="mb-8"
          >
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              {isCompleted && (
                <CheckCircle className="w-4 h-4 text-green-600" />
              )}
              <span className={`text-sm font-medium ${isCompleted ? 'text-green-700' : 'text-gray-700'}`}>
                {dynamicCompletedTasks}/{dynamicTotalTasks} Completed
              </span>
              <span className={`text-sm font-bold ${isCompleted ? 'text-green-800' : 'text-gray-900'}`}>
                {progressPercentage}%
              </span>
            </div>
            
            <Button 
              className="bg-[#347468] hover:bg-[#2a5d54] text-white px-6"
              onClick={openFileDialog}
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload
            </Button>
          </div>
          
          <Progress
            value={progressPercentage}
            className={`h-3 ${isCompleted ? 'bg-green-100' : 'bg-gray-200'}`}
            style={{
              background: isCompleted
                ? 'linear-gradient(to right, #16a34a 0%, #16a34a 100%)'
                : `linear-gradient(to right, #347468 0%, #347468 ${progressPercentage}%, #e5e7eb ${progressPercentage}%, #e5e7eb 100%)`
            }}
          />
        </motion.div>
        )}

        {/* Upload Area */}
        {!submissionSuccess && !isLoadingSubmission && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-6"
          >
            <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
              <CardContent className="p-8">
                <div
                  className={`relative transition-all duration-200 ${
                    dragActive ? 'bg-[#347468]/10 border-[#347468]' : ''
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  {/* Hidden File Input */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    onChange={handleFileSelect}
                    className="hidden"
                    accept="image/*,video/*,.pdf,.doc,.docx,.txt"
                  />

                  {!uploadedFiles ? (
                    // Upload Interface
                    <div className="text-center">
                      <Upload className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">
                        Upload Your Case Study Files
                      </h3>
                      <p className="text-gray-600 mb-6">
                        Upload your case study analysis, supporting documents, or any relevant files
                      </p>
                      <Button
                        onClick={openFileDialog}
                        className="bg-[#347468] hover:bg-[#2a5d54] text-white px-8 py-3"
                      >
                        Choose File
                      </Button>
                      <p className="text-xs text-gray-400 mt-4">
                        Drag and drop files here, or click to browse
                      </p>
                    </div>
                  ) : (
                    // File Selected - Show file info and submit button
                    <div className="text-center">
                      <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">
                        File Ready for Submission
                      </h3>
                      <div className="bg-white p-4 rounded-lg border mb-6">
                        <div className="flex items-center justify-center gap-3">
                          <FileText className="w-6 h-6 text-[#347468]" />
                          <div>
                            <p className="font-medium text-gray-800">{uploadedFiles[0].name}</p>
                            <p className="text-sm text-gray-500">
                              {(uploadedFiles[0].size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-4 justify-center">
                        <Button
                          variant="outline"
                          onClick={() => setUploadedFiles(null)}
                          className="text-gray-600 border-gray-300"
                        >
                          Choose Different File
                        </Button>
                        <Button
                          onClick={handleSubmit}
                          disabled={isSubmitting}
                          className="bg-[#347468] hover:bg-[#2a5d54] text-white px-8"
                        >
                          {isSubmitting ? 'Submitting...' : 'Submit Assignment'}
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Drag and Drop Overlay */}
                  {dragActive && (
                    <div className="absolute inset-0 bg-[#347468]/10 border-2 border-dashed border-[#347468] rounded-lg flex items-center justify-center">
                      <div className="text-center">
                        <Upload className="w-12 h-12 text-[#347468] mx-auto mb-2" />
                        <p className="text-[#347468] font-medium">Drop files here to upload</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}



        {/* Upload Progress */}
        {isUploading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4"
          >
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 rounded-full border-4 border-[#347468] border-t-transparent animate-spin"></div>
                  <div className="flex-grow">
                    <p className="text-sm font-medium text-gray-700">Uploading file...</p>
                    <Progress value={uploadProgress} className="h-2 mt-1" />
                  </div>
                  <span className="text-sm text-gray-500">{Math.round(uploadProgress)}%</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}



        {/* Existing Submission */}
        {existingSubmission && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4"
          >
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <div className="flex-grow">
                    <p className="text-sm font-medium text-green-800">Assignment Submitted</p>
                    <p className="text-xs text-green-600">
                      You have already submitted this assignment. You can upload a new file to update your submission.
                    </p>
                  </div>
                </div>
                {existingSubmission.fileUrl && (
                  <div className="mt-2 pt-2 border-t border-green-200">
                    <a
                      href={existingSubmission.fileUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-green-700 hover:text-green-800 underline"
                    >
                      View submitted file
                    </a>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default CaseStudyRenderer;
