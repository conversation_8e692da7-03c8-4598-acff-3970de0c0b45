import React, { useState, useMemo, useEffect } from 'react';
import { Plus, Trash, Search, Calendar, Pencil } from 'lucide-react';
import { <PERSON>et, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from '@/components/ui/sheet';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import SearchInput from './common/search-input';
import { cn } from '@/lib/utils';
import { useSelector } from 'react-redux';
import { useAuth0 } from '@auth0/auth0-react';
import {
    useGetStudentAllNotesQuery,
    useCreateNewStudentNoteMutation,
    useUpdateStudentNoteMutation,
    useDeleteStudentNoteMutation
} from '../../../APIConnect';
import { NoteDto } from '../types';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface LocalNote {
    id: string;
    title: string;
    description: string;
    createdAt: Date;
    color: string;
    courseId: string;
    topicId: string;
    hasReminder?: boolean;
    reminderDate?: string;
    reminderTime?: string;
    reminderType?: number;
}

interface RootState {
    courses: {
        currentCourseChoosen: {
            id: string;
            name: string;
        } | null;
        contentRelatedTopic: {
            id: string;
            name: string;
        } | null;
    };
}

const noteColors = [
    'bg-yellow-100',
    'bg-green-100',
    'bg-blue-100',
    'bg-pink-100',
    'bg-purple-100',
];

const NotesDialog = ({
    iconOnly = false,
    triggerText,
    buttonVariant = "ghost",
    className,
    externalOpen,
    onExternalOpenChange,
    hideTriggerButton = false
}: {
    iconOnly?: boolean;
    triggerText?: string;
    buttonVariant?: "ghost" | "link" | "default" | "destructive" | "outline" | "secondary" | null | undefined;
    className?: string;
    externalOpen?: boolean;
    onExternalOpenChange?: (open: boolean) => void;
    hideTriggerButton?: boolean;
}) => {
    const { user } = useAuth0();
    const [searchQuery, setSearchQuery] = useState('');
    const [newNote, setNewNote] = useState({
        title: '',
        description: '',
        hasReminder: false,
        reminderDate: '',
        reminderTime: '',
        reminderType: 1 // 1 = AllDay, 2 = Timed (updated enum values)
    });
    const [editingNote, setEditingNote] = useState<LocalNote | null>(null);
    const [noteToDelete, setNoteToDelete] = useState<string | null>(null);
    const [isEditorView, setIsEditorView] = useState(false);
    const isMobile = window.innerWidth < 768;

    // Get current course and topic from Redux store
    const currentCourse = useSelector((state: RootState) => state.courses);

    // API hooks
    const { data: notesData, refetch: refetchNotes } = useGetStudentAllNotesQuery(user?.sub?.replace("auth0|", "") || {});
    const [createNote] = useCreateNewStudentNoteMutation();
    const [updateNote] = useUpdateStudentNoteMutation();
    const [deleteNote] = useDeleteStudentNoteMutation();

    // Transform API notes to local format
    const notes = useMemo(() => {
        console.log('Notes data:', notesData); // Debug log

        // Handle different response structures
        let notesArray = [];
        if (notesData) {
            if (Array.isArray(notesData)) {
                // Direct array response
                notesArray = notesData;
            } else if (notesData.resultObject && Array.isArray(notesData.resultObject)) {
                // Wrapped in resultObject as array
                notesArray = notesData.resultObject;
            } else if (notesData.resultObject && !Array.isArray(notesData.resultObject)) {
                // Single note wrapped in resultObject
                notesArray = [notesData.resultObject];
            } else if (notesData.data && Array.isArray(notesData.data)) {
                // Wrapped in data
                notesArray = notesData.data;
            }
        }

        return notesArray.map((noteDto: any) => {
            const hasReminder = noteDto.ReminderDateTime || noteDto.reminderDateTime;
            // Handle both NoteType (Pascal case) and noteType (camel case) from API
            const noteType = noteDto.noteType !== undefined ? noteDto.noteType : (noteDto.NoteType !== undefined ? noteDto.NoteType : 1);
            let reminderDate = '';
            let reminderTime = '';

            console.log('Processing note:', {
                id: noteDto.Id || noteDto.id,
                title: noteDto.Title || noteDto.title,
                hasReminder,
                noteType,
                rawNoteType: noteDto.NoteType,
                rawNoteTypeLower: noteDto.noteType,
                rawReminderDateTime: hasReminder
            });

            if (hasReminder) {
                const reminderDateTime = new Date(hasReminder);
                reminderDate = reminderDateTime.toISOString().split('T')[0];

                // Only extract time for Timed reminders (NoteType = 2)
                // For NonReminder (0) and AllDay (1) reminders, we explicitly set reminderTime to empty
                if (noteType === 2) {
                    reminderTime = reminderDateTime.toTimeString().split(' ')[0].substring(0, 5);
                } else {
                    reminderTime = ''; // Explicitly set to empty for NonReminder and AllDay reminders
                }

                console.log('Reminder processing result:', {
                    noteType,
                    reminderDate,
                    reminderTime,
                    shouldShowTime: noteType === 1,
                    originalDateTime: hasReminder
                });
            }

            const result = {
                id: noteDto.Id || noteDto.id || '',
                title: noteDto.Title || noteDto.title || '',
                description: noteDto.NoteString || noteDto.noteString || noteDto.description || '',
                createdAt: new Date(), // API doesn't provide creation date in new structure
                color: noteColors[Math.floor(Math.random() * noteColors.length)],
                courseId: noteDto.CourseId || noteDto.courseId || '',
                topicId: noteDto.TopicId || noteDto.topicId || '',
                hasReminder: !!hasReminder,
                reminderDate: reminderDate,
                reminderTime: reminderTime,
                reminderType: noteType
            };

            console.log('Final note object:', result);
            return result;
        });
    }, [notesData]);

    // Filter notes based on search and current topic
    const filteredNotes = useMemo(() => {
        let filtered = notes;

        // Filter by current course and topic if available
        if (currentCourse.currentCourseChoosen?.id && currentCourse.contentRelatedTopic?.id) {
            filtered = filtered.filter((note: LocalNote) =>
                note.courseId === currentCourse?.currentCourseChoosen?.id &&
                note.topicId === currentCourse?.contentRelatedTopic?.id
            );
        }

        // Filter by search query
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            filtered = filtered.filter((note: LocalNote) =>
                note.title.toLowerCase().includes(query) ||
                note.description.toLowerCase().includes(query)
            );
        }

        return filtered;
    }, [notes, searchQuery, currentCourse]);

    const addOrUpdateNote = async () => {
        if (newNote.title && newNote.description && user?.sub) {
            // Validate reminder fields if reminder is enabled
            if (newNote.hasReminder) {
                if (!newNote.reminderDate) {
                    console.error('Reminder date is required when reminder is enabled');
                    return;
                }
                if (newNote.reminderType === 2 && !newNote.reminderTime) {
                    console.error('Reminder time is required for timed reminders');
                    return;
                }
            }
            try {
                // Create request body according to updated API structure
                let reminderDateTime = null;
                let noteType = 0; // Default to NonReminder (0)

                if (newNote.hasReminder && newNote.reminderDate) {
                    if (newNote.reminderType === 1) {
                        // All day reminder - set to 9:00 AM as default notification time
                        reminderDateTime = `${newNote.reminderDate}T09:00:00`;
                        noteType = 1; // AllDay = 1
                    } else if (newNote.reminderType === 2 && newNote.reminderTime) {
                        // Timed reminder - use exact time specified
                        reminderDateTime = `${newNote.reminderDate}T${newNote.reminderTime}:00`;
                        noteType = 2; // Timed = 2
                    }
                }

                // Build request object according to updated API specification
                const noteRequest = {
                    CourseId: currentCourse?.currentCourseChoosen?.id || null,
                    TopicId: currentCourse?.contentRelatedTopic?.id || null,
                    Title: newNote.title,
                    NoteString: newNote.description,
                    NoteType: noteType, // Always required: 0=NonReminder, 1=AllDay, 2=Timed
                    ReminderDateTime: reminderDateTime // null for NonReminder, datetime for AllDay/Timed
                };

                console.log('Sending note request:', noteRequest); // Debug log
                console.log('API endpoint will be: POST /studentnote');
                console.log('User authentication details:', {
                    userSub: user?.sub,
                    userEmail: user?.email,
                    userToken: user ? 'Present' : 'Missing'
                });
                console.log('Reminder details:', {
                    hasReminder: newNote.hasReminder,
                    reminderType: newNote.reminderType,
                    reminderDate: newNote.reminderDate,
                    reminderTime: newNote.reminderTime,
                    finalDateTime: reminderDateTime,
                    finalNoteType: noteType
                });

                if (editingNote) {
                    const updatePayload = {
                        id: editingNote.id,
                        ...noteRequest
                    };
                    console.log('Update payload:', updatePayload); // Debug log
                    await updateNote(updatePayload).unwrap();
                } else {
                    console.log('Create payload:', noteRequest); // Debug log
                    await createNote(noteRequest).unwrap();
                }

                setNewNote({
                    title: '',
                    description: '',
                    hasReminder: false,
                    reminderDate: '',
                    reminderTime: '',
                    reminderType: 1
                });
                setEditingNote(null);
                setIsEditorView(false);
                refetchNotes();
            } catch (error: any) {
                console.error('Failed to save note:', error);
                console.error('Error details:', error);
                console.error('Full error response:', {
                    status: error?.status,
                    data: error?.data,
                    message: error?.message,
                    originalError: error?.originalError
                });

                // Try to get more details from the error
                if (error?.data?.errors) {
                    console.error('Validation errors:', error.data.errors);
                }
            }
        }
    };

    const startEditNote = (note: LocalNote) => {
        console.log('Starting edit for note:', note);

        setEditingNote(note);
        setNewNote({
            title: note.title,
            description: note.description,
            hasReminder: note.hasReminder || false,
            reminderDate: note.reminderDate || '',
            reminderTime: note.reminderType === 1 ? '' : (note.reminderTime || ''), // Clear time for All Day reminders
            reminderType: note.reminderType !== undefined ? note.reminderType : 1
        });

        console.log('Edit form populated with:', {
            hasReminder: note.hasReminder || false,
            reminderDate: note.reminderDate || '',
            reminderTime: note.reminderType === 0 ? '' : (note.reminderTime || ''),
            reminderType: note.reminderType !== undefined ? note.reminderType : 1
        });

        setIsEditorView(true);
    };

    const cancelEdit = () => {
        setEditingNote(null);
        setNewNote({
            title: '',
            description: '',
            hasReminder: false,
            reminderDate: '',
            reminderTime: '',
            reminderType: 1
        });
        setIsEditorView(false);
    };

    const deleteNoteHandler = async (id: string) => {
        try {
            // Call the API to delete the note
            await deleteNote(id).unwrap();
            
            // Reset the noteToDelete state
            setNoteToDelete(null);

            // Immediately refetch notes to update the UI with the latest data from the server
            await refetchNotes();
            
            // For immediate UI feedback, refetch the notes
            refetchNotes();
        } catch (error) {
            console.error('Failed to delete note:', error);
            // You could add error handling/user feedback here
        }
    };

    const getCurrentContext = () => {
        if (currentCourse.currentCourseChoosen?.name && currentCourse.contentRelatedTopic?.name) {
            return `${currentCourse.currentCourseChoosen.name} / ${currentCourse.contentRelatedTopic.name}`;
        }
        return null;
    };

    // State for internal control if externalOpen is not provided
    const [internalOpen, setInternalOpen] = useState(false);

    const sheetOpen = externalOpen !== undefined ? externalOpen : internalOpen;

    const handleSheetOpenChange = (openValue: boolean) => {
        if (onExternalOpenChange) {
            onExternalOpenChange(openValue);
        } else {
            setInternalOpen(openValue);
        }
        // Reset editor view when sheet is closed
        if (!openValue) {
            setIsEditorView(false);
            setEditingNote(null);
            setNewNote({
                title: '',
                description: '',
                hasReminder: false,
                reminderDate: '',
                reminderTime: '',
                reminderType: 1
            });
        }
    };

    return (
        <>
            <Sheet open={sheetOpen} onOpenChange={handleSheetOpenChange}>
                {!hideTriggerButton && (
                    <SheetTrigger asChild onClick={() => handleSheetOpenChange(true)}>
                        {triggerText ? (
                            <Button variant={buttonVariant} className={className}>
                                {triggerText}
                            </Button>
                        ) : (
                            <Button variant={buttonVariant} className={cn("w-full justify-start", className)}>
                                📝 {iconOnly ? "" : "My Notes"}
                            </Button>
                        )}
                    </SheetTrigger>
                )}
                <SheetContent
                    side="right"
                    hideCloseButton={true}
                    className={cn(
                        "p-0",
                        isMobile ? "w-full h-[85vh]" : "w-[400px] sm:w-[540px]"
                    )}
                >
                    {/* Two UI states: Notes List View and Note Edit View */}
                    {!isEditorView ? (
                        // Notes List View (similar to first image)
                        <div className="flex flex-col h-full">
                            <div className="flex items-center justify-between p-4 border-b  border-b-2 border-[#347468] bg-[#ebf1f0]">
                                <div className="flex items-center">
                                    <span className="text-lg font-semibold text-black">📝 My Notes</span>
                                </div>
                                <div className="flex gap-2 items-center">
                                    <Button
                                        onClick={() => setIsEditorView(true)}
                                        className="rounded-full bg-[#347468] hover:bg-emerald-700"
                                    >
                                        <Plus className="w-4 h-4 mr-1" /> Add New
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="rounded-full"
                                        onClick={() => handleSheetOpenChange(false)}
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="lucide">
                                            <circle cx="12" cy="12" r="10" />
                                            <path d="m15 9-6 6" />
                                            <path d="m9 9 6 6" />
                                        </svg>
                                    </Button>
                                </div>
                            </div>

                            <div className="p-4">
                                <div className="relative mb-4">
                                    <SearchInput
                                        value={searchQuery}
                                        onChange={setSearchQuery}
                                        placeholder="Search Notes"
                                    />
                                </div>

                                <ScrollArea className={cn("pr-2", isMobile ? "h-[60vh]" : "h-[70vh]")}>
                                    {filteredNotes.length === 0 ? (
                                        <div className="flex flex-col items-center justify-center h-[200px] text-muted-foreground">
                                            <Search className="w-8 h-8 mb-2" />
                                            <p>No notes found</p>
                                        </div>
                                    ) : (
                                        <div className="space-y-4">
                                            {filteredNotes.map((note: LocalNote) => (
                                                <div
                                                    key={note.id}
                                                    className="p-4 border border-[#8cb04f] rounded-[14px] hover:shadow-md transition-all"
                                                >
                                                    <div className="flex justify-between items-start mb-1">
                                                        <div className="flex items-center gap-2">
                                                            <h3 className="font-medium text-emerald-700">{note.title || 'New Note'}</h3>
                                                            {note.hasReminder && (
                                                                <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full flex items-center gap-1">
                                                                    🔔 {(() => {
                                                                        console.log('Badge display logic:', {
                                                                            noteId: note.id,
                                                                            reminderType: note.reminderType,
                                                                            reminderTime: note.reminderTime,
                                                                            hasReminder: note.hasReminder
                                                                        });

                                                                        if (note.reminderType === 1) {
                                                                            return 'All Day';
                                                                        } else if (note.reminderType === 2 && note.reminderTime && note.reminderTime.trim() !== '') {
                                                                            return new Date(`2000-01-01T${note.reminderTime}`).toLocaleTimeString('en-US', {
                                                                                hour: '2-digit',
                                                                                minute: '2-digit',
                                                                                hour12: true
                                                                            });
                                                                        } else {
                                                                            return 'Timed';
                                                                        }
                                                                    })()}
                                                                </span>
                                                            )}
                                                        </div>
                                                        <div className="flex gap-1">
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => startEditNote(note)}
                                                                className="h-6 w-6 text-emerald-600 hover:text-emerald-700"
                                                            >
                                                                <Pencil className="h-4 w-4" />
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => setNoteToDelete(note.id)}
                                                                className="h-6 w-6 text-red-500 hover:text-red-600"
                                                            >
                                                                <Trash className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </div>
                                                    <p className="text-sm text-gray-600 line-clamp-3 mb-2">
                                                        {note.description}
                                                    </p>
                                                    <div className="flex flex-col gap-1">
                                                        <div className="flex items-center text-xs text-gray-400">
                                                            <Calendar className="w-3 h-3 mr-1" />
                                                            Last Edited on {new Date(note.createdAt).toLocaleDateString('en-US', { day: 'numeric', month: 'short' })}
                                                        </div>
                                                        {note.hasReminder && note.reminderDate && (
                                                            <div className="flex items-center text-xs text-blue-600">
                                                                🔔 Reminder: {new Date(note.reminderDate).toLocaleDateString('en-US', {
                                                                    day: 'numeric',
                                                                    month: 'short',
                                                                    year: 'numeric'
                                                                })}
                                                                {note.reminderType === 2 && note.reminderTime && (
                                                                    <span className="ml-1">
                                                                        at {new Date(`2000-01-01T${note.reminderTime}`).toLocaleTimeString('en-US', {
                                                                            hour: '2-digit',
                                                                            minute: '2-digit',
                                                                            hour12: true
                                                                        })}
                                                                    </span>
                                                                )}
                                                                {note.reminderType === 1 && (
                                                                    <span className="ml-1">(All Day)</span>
                                                                )}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </ScrollArea>
                            </div>
                        </div>
                    ) : (
                        // Note Edit View (similar to second image)
                        <div className="flex flex-col h-full">
                            <div className="flex items-center justify-between p-4 border-b  border-b-2 border-[#347468] bg-[#ebf1f0]">
                                <div className="flex items-center">
                                    <span className="text-lg font-medium text-black">📝 My Notes</span>
                                </div>
                                <div className="flex gap-2 ">
                                    <Button
                                        onClick={addOrUpdateNote}
                                        disabled={
                                            !newNote.title ||
                                            !newNote.description ||
                                            (newNote.hasReminder && !newNote.reminderDate) ||
                                            (newNote.hasReminder && newNote.reminderType === 2 && !newNote.reminderTime)
                                        }
                                        className="rounded-full bg-[#347468] hover:bg-emerald-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path><polyline points="17 21 17 13 7 13 7 21"></polyline><polyline points="7 3 7 8 15 8"></polyline></svg>
                                        Save
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="rounded-full"
                                        onClick={() => cancelEdit()} // Just cancel edit and return to notes list
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="lucide">
                                            <circle cx="12" cy="12" r="10" />
                                            <path d="m15 9-6 6" />
                                            <path d="m9 9 6 6" />
                                        </svg>
                                    </Button>
                                </div>
                            </div>

                            <div className="p-4 flex-1">
                                <div className="h-full">
                                    <div className="border border-[#8cb04f] rounded-[14px] overflow-hidden flex flex-col h-[calc(100vh-240px)]">
                                        <div className="p-4 pb-0 space-y-3">
                                            <Input
                                                placeholder="Title*"
                                                value={newNote.title}
                                                onChange={(e) => setNewNote({ ...newNote, title: e.target.value })}
                                                className="border border-[#8cb04f] rounded-[8px]  focus:outline-none text-gray-800 text-base bg-white w-[40%]"
                                            />

                                            {/* Reminder Section */}
                                            <div className="space-y-2">
                                                <label className="flex items-center gap-2 text-sm text-gray-600">
                                                    <input
                                                        type="checkbox"
                                                        checked={newNote.hasReminder}
                                                        onChange={(e) => setNewNote({ ...newNote, hasReminder: e.target.checked })}
                                                        className="rounded border-gray-300"
                                                    />
                                                    Set Reminder
                                                </label>

                                                {newNote.hasReminder && (
                                                    <div className="ml-6 space-y-2">
                                                        <div className="flex items-center gap-3 flex-wrap">
                                                            <select
                                                                value={newNote.reminderType}
                                                                onChange={(e) => setNewNote({ ...newNote, reminderType: parseInt(e.target.value) })}
                                                                className="text-sm border border-[#8cb04f] rounded px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-[#347468]"
                                                            >
                                                                <option value={1}>All Day</option>
                                                                <option value={2}>Timed</option>
                                                            </select>

                                                            <input
                                                                type="date"
                                                                value={newNote.reminderDate}
                                                                onChange={(e) => setNewNote({ ...newNote, reminderDate: e.target.value })}
                                                                className="text-sm border border-[#8cb04f] rounded px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-[#347468]"
                                                                min={new Date().toISOString().split('T')[0]} // Prevent past dates
                                                            />
                                                        </div>

                                                        {newNote.reminderType === 2 && (
                                                            <div className="flex flex-col">
                                                                <input
                                                                    type="time"
                                                                    value={newNote.reminderTime}
                                                                    onChange={(e) => setNewNote({ ...newNote, reminderTime: e.target.value })}
                                                                    className={`text-sm border rounded px-3 py-1.5 focus:outline-none focus:ring-2 w-32 ${
                                                                        newNote.reminderTime
                                                                            ? 'border-[#8cb04f] focus:ring-[#347468]'
                                                                            : 'border-red-300 focus:ring-red-500'
                                                                    }`}
                                                                    placeholder="--:--"
                                                                    required
                                                                />
                                                                {!newNote.reminderTime && (
                                                                    <span className="text-xs text-red-500 mt-1">Time required for timed reminder</span>
                                                                )}
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <div className="px-4 py-2 flex-1">
                                            <Textarea
                                                placeholder="Write Your Notes!"
                                                value={newNote.description}
                                                onChange={(e) => setNewNote({ ...newNote, description: e.target.value })}
                                                className="flex-1 border-0 rounded-none focus:ring-0 focus:outline-none text-black min-h-full w-full resize-none"
                                            />
                                        </div>
                                        <div className="flex px-4 py-2 bg-white">
                                            {/* Bold */}
                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 rounded-none hover:bg-transparent text-black">
                                                <span className="font-bold">B</span>
                                            </Button>
                                            {/* Italic */}
                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 rounded-none hover:bg-transparent text-black italic">
                                                <span>I</span>
                                            </Button>
                                            {/* Underline */}
                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 rounded-none hover:bg-transparent text-black underline">
                                                <span>U</span>
                                            </Button>
                                            {/* Center Alignment */}
                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 rounded-none hover:bg-transparent text-black">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="21" x2="3" y1="6" y2="6"/><line x1="21" x2="3" y1="12" y2="12"/><line x1="21" x2="3" y1="18" y2="18"/></svg>
                                            </Button>
                                            {/* End (Right) Alignment */}
                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 rounded-none hover:bg-transparent text-black">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="21" x2="3" y1="6" y2="6"/><line x1="21" x2="9" y1="12" y2="12"/><line x1="21" x2="7" y1="18" y2="18"/></svg>
                                            </Button>
                                            {/* Start (Left) Alignment */}
                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 rounded-none hover:bg-transparent text-black">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="21" x2="3" y1="6" y2="6"/><line x1="15" x2="3" y1="12" y2="12"/><line x1="17" x2="3" y1="18" y2="18"/></svg>
                                            </Button>
                                            {/* Bullet List */}
                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 rounded-none hover:bg-transparent text-black">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="8" y1="6" x2="21" y2="6"/><line x1="8" y1="12" x2="21" y2="12"/><line x1="8" y1="18" x2="21" y2="18"/><circle cx="3" cy="6" r="1"/><circle cx="3" cy="12" r="1"/><circle cx="3" cy="18" r="1"/></svg>
                                            </Button>
                                            {/* Numbered List */}
                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 rounded-none hover:bg-transparent text-black">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="10" y1="6" x2="21" y2="6"/><line x1="10" y1="12" x2="21" y2="12"/><line x1="10" y1="18" x2="21" y2="18"/><path d="M4 6h1v4"/><path d="M4 10h2"/><path d="M6 18H4c0-1 2-2 2-3s-1-1.5-2-1"/></svg>
                                            </Button>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    )}
                </SheetContent>

                {/* Delete Note Confirmation Dialog */}
                <AlertDialog open={!!noteToDelete} onOpenChange={() => setNoteToDelete(null)}>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>Are you sure you want to delete this note?</AlertDialogTitle>
                            <AlertDialogDescription>
                                This action cannot be undone. This note will be permanently deleted.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                                className="bg-red-500 hover:bg-red-600"
                                onClick={() => noteToDelete && deleteNoteHandler(noteToDelete)}
                            >
                                Delete
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </Sheet>
        </>
    );
};

export default NotesDialog;
