import React, { useState } from 'react';
import { useLeaderboard } from '../../hooks/useDiscussions';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Alert, AlertDescription } from '../ui/alert';
import { Skeleton } from '../ui/skeleton';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Badge } from '../ui/badge';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { Search, Calendar, ArrowUpDown, Star, MessageSquare } from 'lucide-react';

// Add this if you don't have recharts installed:
// npm install recharts

interface StudentEngagementStatsProps {
  timeRange: 'daily' | 'weekly' | 'monthly' | 'alltime';
}

export function StudentEngagementStats({ timeRange }: StudentEngagementStatsProps) {
  const { leaderboard, loading, error } = useLeaderboard(timeRange);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'rank' | 'score' | 'comments' | 'discussions'>('rank');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const toggleSort = (field: 'rank' | 'score' | 'comments' | 'discussions') => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('asc');
    }
  };

  // Filter and sort leaderboard data
  const filteredLeaderboard = leaderboard?.items?.filter((student:any) => {
    if (!searchQuery) return true;
    return student.name?.toLowerCase().includes(searchQuery.toLowerCase());
  }) || [];

  const sortedLeaderboard = [...filteredLeaderboard].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'rank':
        comparison = (a.rank || 999) - (b.rank || 999);
        break;
      case 'score':
        comparison = (a.score || 0) - (b.score || 0);
        break;
      case 'comments':
        comparison = (a.activity?.commentCount || 0) - (b.activity?.commentCount || 0);
        break;
      case 'discussions':
        comparison = (a.activity?.discussionsParticipated || 0) - (b.activity?.discussionsParticipated || 0);
        break;
    }
    
    return sortDirection === 'asc' ? comparison : -comparison;
  });

  // Prepare chart data
  const topPerformers = sortedLeaderboard.slice(0, 10).map(student => ({
    name: student.name || `Student ${student.userId}`,
    score: student.score || 0,
    comments: student.activity?.commentCount || 0,
    discussions: student.activity?.discussionsParticipated || 0
  }));

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-10 w-36" />
        </div>
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-6">
        <div className="relative w-full md:w-64">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search" 
            placeholder="Search students..." 
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex items-center">
          <Calendar className="h-4 w-4 mr-2 text-gray-500" />
          <Label className="text-sm text-gray-500">
            {timeRange === 'daily' ? 'Today' : 
             timeRange === 'weekly' ? 'This Week' : 
             timeRange === 'monthly' ? 'This Month' : 'All Time'}
          </Label>
        </div>
      </div>

      <Tabs defaultValue="table" className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-8">
          <TabsTrigger value="table">Leaderboard</TabsTrigger>
          <TabsTrigger value="chart">Engagement Chart</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="table" className="space-y-4">
          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16 text-center">Rank</TableHead>
                  <TableHead>Student</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => toggleSort('score')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Score</span>
                      {sortBy === 'score' && (
                        <ArrowUpDown className="h-3 w-3" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => toggleSort('comments')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Comments</span>
                      {sortBy === 'comments' && (
                        <ArrowUpDown className="h-3 w-3" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => toggleSort('discussions')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Discussions</span>
                      {sortBy === 'discussions' && (
                        <ArrowUpDown className="h-3 w-3" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="text-right">Details</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedLeaderboard.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      No student data available for this time period
                    </TableCell>
                  </TableRow>
                ) : (
                  sortedLeaderboard.map((student, index) => (
                    <TableRow key={student.userId}>
                      <TableCell className="text-center font-medium">
                        {student.rank ? (
                          <Badge 
                            variant={student.rank <= 3 ? "default" : "outline"}
                            className={`
                              ${student.rank === 1 ? 'bg-yellow-500 hover:bg-yellow-600' : ''}
                              ${student.rank === 2 ? 'bg-gray-400 hover:bg-gray-500' : ''}
                              ${student.rank === 3 ? 'bg-amber-600 hover:bg-amber-700' : ''}
                            `}
                          >
                            {student.rank <= 3 ? (
                              <Star className="h-3 w-3 mr-1" />
                            ) : null}
                            {student.rank}
                          </Badge>
                        ) : (
                          '-'
                        )}
                      </TableCell>
                      <TableCell className="font-medium">
                        {student.name || `Student ${student.userId}`}
                      </TableCell>
                      <TableCell>
                        {student.score?.toFixed(1) || '0'}
                      </TableCell>
                      <TableCell>
                        {student.activity?.commentCount || '0'}
                      </TableCell>
                      <TableCell>
                        {student.activity?.discussionsParticipated || '0'}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="outline" size="sm">
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        
        <TabsContent value="chart">
          <Card>
            <CardHeader>
              <CardTitle>Top 10 Student Engagement Scores</CardTitle>
            </CardHeader>
            <CardContent className="pt-2">
              {topPerformers.length === 0 ? (
                <div className="flex justify-center items-center h-80 text-gray-500">
                  No data available to display
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart
                    data={topPerformers}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 0,
                      bottom: 60
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="name" 
                      angle={-45} 
                      textAnchor="end"
                      height={70}
                      interval={0}
                    />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="score" fill="#8884d8" name="Engagement Score" />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Comments vs Discussions</CardTitle>
              </CardHeader>
              <CardContent className="pt-2">
                {topPerformers.length === 0 ? (
                  <div className="flex justify-center items-center h-80 text-gray-500">
                    No data available to display
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart
                      data={topPerformers}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 0,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="comments" stroke="#8884d8" name="Comments" />
                      <Line type="monotone" dataKey="discussions" stroke="#82ca9d" name="Discussions" />
                    </LineChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Engagement Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-500">Average Score</Label>
                    <div className="text-2xl font-bold">
                      {leaderboard?.averages?.score
                        ? leaderboard.averages.score.toFixed(1)
                        : '0'}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-500">Total Comments</Label>
                    <div className="text-2xl font-bold flex items-center">
                      <MessageSquare className="h-5 w-5 mr-2 text-blue-500" />
                      {leaderboard?.totals?.comments || '0'}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-500">Active Students</Label>
                    <div className="text-2xl font-bold">
                      {leaderboard?.totals?.activeStudents || '0'}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-500">Total Discussions</Label>
                    <div className="text-2xl font-bold">
                      {leaderboard?.totals?.discussions || '0'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}