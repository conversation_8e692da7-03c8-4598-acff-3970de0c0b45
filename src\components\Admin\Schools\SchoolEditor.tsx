import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import {
    ArrowLeft,
    Save,
    Building2,
    MapPin,
    Loader2,
    Users,
    BookOpen,
    School
} from 'lucide-react';
import {
    useGetSchoolByIdQuery,
    useCreateSchoolMutation,
    useUpdateSchoolMutation,
} from 'src/APIConnect';
import { toast } from "@/hooks/use-toast";
import { SchoolCreationRequest } from './types';
import ClassroomsTab from './ClassroomsTab';
import UsersTab from './UsersTab';
import CoursesTab from './CoursesTab';

interface FormState extends SchoolCreationRequest {
    name: string;
    description: string;
    location: string;
    createdOn: string;
}

interface FormErrors {
    name?: string;
    location?: string;
}

interface SchoolEditorProps { }

const SchoolEditor: React.FC<SchoolEditorProps> = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const isEditMode = !!id;

    const { data: schoolData, isLoading: isLoadingSchool, refetch } = useGetSchoolByIdQuery(id, { skip: !id });
    const [createSchool, { isLoading: isCreating }] = useCreateSchoolMutation();
    const [updateSchool, { isLoading: isUpdating }] = useUpdateSchoolMutation();

    const [formData, setFormData] = useState<FormState>({
        name: '',
        description: '',
        location: '',
        createdOn: new Date().toISOString()
    });

    const [errors, setErrors] = useState<FormErrors>({});

    useEffect(() => {
        if (schoolData) {
            setFormData({
                name: schoolData.name,
                description: schoolData.description,
                location: schoolData.location,
                createdOn: schoolData.createdOn
            });
        }
    }, [schoolData]);

    const validateForm = (): boolean => {
        const newErrors: FormErrors = {};
        let isValid = true;

        if (!formData.name.trim()) {
            newErrors.name = "School name is required";
            isValid = false;
        }

        if (!formData.location.trim()) {
            newErrors.location = "Location is required";
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            toast({
                title: "Please fix the errors",
                description: "Some required fields are missing or invalid",
                variant: "destructive",
            });
            return;
        }

        try {
            if (isEditMode) {
                await updateSchool({ id, ...formData }).unwrap();
                toast({
                    title: "School updated successfully",
                    variant: "default",
                });
            } else {
                await createSchool(formData).unwrap();
                toast({
                    title: "School created successfully",
                    variant: "default",
                });
                navigate('/admin/schools');
            }
        } catch (error) {
            console.error('Error saving school:', error);
            toast({
                title: "Failed to save school",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    if (isEditMode && isLoadingSchool) {
        return (
            <div className="flex justify-center items-center h-screen">
                <Loader2 className="w-8 h-8 animate-spin" />
            </div>
        );
    }

    const isSubmitting = isCreating || isUpdating;

    return (
        <div className="min-h-screen bg-background">
            <div className="sticky top-0 z-10 bg-card border-b shadow-sm">
                <div className="container mx-auto px-6 py-4">
                    <div className="flex items-center space-x-4">
                        <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => navigate('/admin/schools')}
                            disabled={isSubmitting}
                            className="hover:bg-accent"
                        >
                            <ArrowLeft className="h-5 w-5 text-muted-foreground" />
                        </Button>
                        <h1 className="text-2xl font-semibold flex items-center text-foreground">
                            <School className="h-6 w-6 mr-3 text-muted-foreground" />
                            {isEditMode ? 'Edit School' : 'Create School'}
                        </h1>
                    </div>
                </div>
            </div>

            <ScrollArea className="flex-1">
                <div className="container mx-auto px-6 py-8">
                    <Tabs defaultValue="details" className="space-y-8">
                        <TabsList className="bg-card border-b rounded-none p-0 h-12 w-full space-x-8">
                            <TabsTrigger
                                value="details"
                                className="data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-primary rounded-none h-12"
                            >
                                <Building2 className="h-4 w-4 mr-2" />
                                School Details
                            </TabsTrigger>
                            {isEditMode && (
                                <>
                                    <TabsTrigger
                                        value="users"
                                        className="data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-primary rounded-none h-12"
                                    >
                                        <Users className="h-4 w-4 mr-2" />
                                        Users
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="courses"
                                        className="data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-primary rounded-none h-12"
                                    >
                                        <BookOpen className="h-4 w-4 mr-2" />
                                        Courses
                                    </TabsTrigger>
                                    <TabsTrigger
                                        value="classrooms"
                                        className="data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-primary rounded-none h-12"
                                    >
                                        <School className="h-4 w-4 mr-2" />
                                        Classrooms
                                    </TabsTrigger>
                                </>
                            )}
                        </TabsList>

                        <TabsContent value="details">
                            <Card className="border shadow-sm">
                                <CardHeader className="border-b bg-muted/40">
                                    <CardTitle className="text-lg font-medium flex items-center text-foreground">
                                        <Building2 className="h-5 w-5 mr-2 text-muted-foreground" />
                                        School Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="p-6 space-y-8">
                                    <div className="grid gap-8">
                                        <div className="space-y-2">
                                            <Label className="text-sm font-medium">School Name</Label>
                                            <Input
                                                value={formData.name}
                                                onChange={(e) => {
                                                    setFormData((prev) => ({ ...prev, name: e.target.value }));
                                                    if (errors.name) {
                                                        setErrors((prev) => ({ ...prev, name: undefined }));
                                                    }
                                                }}
                                                placeholder="Enter school name..."
                                                className={cn(
                                                    "bg-background",
                                                    errors.name && "border-destructive focus-visible:ring-destructive"
                                                )}
                                                disabled={isSubmitting}
                                            />
                                            {errors.name && (
                                                <p className="text-sm text-destructive mt-1">{errors.name}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label className="text-sm font-medium">Description</Label>
                                            <Textarea
                                                value={formData.description}
                                                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                                                placeholder="Enter school description..."
                                                className="h-32 bg-background resize-none"
                                                disabled={isSubmitting}
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label className="text-sm font-medium">Location</Label>
                                            <div className="relative">
                                                <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                                <Input
                                                    value={formData.location}
                                                    onChange={(e) => {
                                                        setFormData((prev) => ({ ...prev, location: e.target.value }));
                                                        if (errors.location) {
                                                            setErrors((prev) => ({ ...prev, location: undefined }));
                                                        }
                                                    }}
                                                    placeholder="Enter school location..."
                                                    className={cn(
                                                        "pl-10 bg-background",
                                                        errors.location && "border-destructive focus-visible:ring-destructive"
                                                    )}
                                                    disabled={isSubmitting}
                                                />
                                            </div>
                                            {errors.location && (
                                                <p className="text-sm text-destructive mt-1">{errors.location}</p>
                                            )}
                                        </div>
                                    </div>
                                    <div className="flex justify-end pt-6 border-t">
                                        <Button
                                            onClick={handleSubmit}
                                            className={cn(
                                                "flex items-center transition-all",
                                                isSubmitting && "opacity-80"
                                            )}
                                            disabled={isSubmitting}
                                        >
                                            {isSubmitting ? (
                                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                            ) : (
                                                <Save className="h-4 w-4 mr-2" />
                                            )}
                                            {isEditMode ? 'Update School Details' : 'Create School'}
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        {isEditMode && (
                            <>
                                <TabsContent value="users">
                                    <Card className="border shadow-sm">
                                        <CardHeader className="border-b bg-muted/40">
                                            <CardTitle className="text-lg font-medium flex items-center text-foreground">
                                                <Users className="h-5 w-5 mr-2 text-muted-foreground" />
                                                School Users
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="p-6">
                                            <UsersTab
                                                schoolId={id!}
                                                users={schoolData?.users || []}
                                                onRefetch={refetch}
                                            />
                                        </CardContent>
                                    </Card>
                                </TabsContent>

                                <TabsContent value="courses">
                                    <Card className="border shadow-sm">
                                        <CardHeader className="border-b bg-muted/40">
                                            <CardTitle className="text-lg font-medium flex items-center text-foreground">
                                                <BookOpen className="h-5 w-5 mr-2 text-muted-foreground" />
                                                School Courses
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="p-6">
                                            <CoursesTab schoolId={id!} />
                                        </CardContent>
                                    </Card>
                                </TabsContent>

                                <TabsContent value="classrooms">
                                    <Card className="border shadow-sm">
                                        <CardHeader className="border-b bg-muted/40">
                                            <CardTitle className="text-lg font-medium flex items-center text-foreground">
                                                <School className="h-5 w-5 mr-2 text-muted-foreground" />
                                                School Classrooms
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="p-6">
                                            <ClassroomsTab schoolId={id!} />
                                        </CardContent>
                                    </Card>
                                </TabsContent>
                            </>
                        )}
                    </Tabs>
                </div>
            </ScrollArea>
        </div>
    );
};

export default SchoolEditor;
