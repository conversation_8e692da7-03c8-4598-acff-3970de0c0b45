import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  MessageSquare,
  Users,
  Calendar,
  ExternalLink,
  CheckCircle,
  Clock,
  ArrowRight
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useGetDiscussionByIdQuery, useGetDiscussionsQuery, useCreateDiscussionMutation } from '@/services/discussionsAPIjs';

interface DiscussionForumRendererProps {
  title?: string;
  description?: string;
  assignmentId?: string;    // subtopicId
  classroomId?: string;
  courseId?: string;
  discussionId?: string;    // From API response
  projectId?: string;       // From API response
  onCompleted?: () => void;
}

const DiscussionForumRenderer: React.FC<DiscussionForumRendererProps> = ({
  title = "Discussion Forum",
  description = "Participate in the discussion to share your thoughts and learn from peers",
  assignmentId,
  classroomId,
  courseId,
  discussionId,
  projectId,
  onCompleted
}) => {
  const navigate = useNavigate();
  const [isParticipated, setIsParticipated] = useState(false);
  const [createDiscussion] = useCreateDiscussionMutation();

  // Query for discussions linked to this subtopic/assignment
  const {
    data: subtopicDiscussions,
    isLoading: isLoadingSubtopicDiscussions
  } = useGetDiscussionsQuery({
    subtopicId: assignmentId,
    limit: 1
  }, {
    skip: !assignmentId
  });

  // Get the discussion ID - either provided directly or from subtopic query
  const actualDiscussionId = discussionId || subtopicDiscussions?.items?.[0]?.id;

  // Fetch discussion details if we have a discussion ID
  const {
    data: discussionData,
    isLoading: discussionLoading,
    error: discussionError
  } = useGetDiscussionByIdQuery(actualDiscussionId || '', {
    skip: !actualDiscussionId
  });

  const handleCreateDiscussionForAssignment = async () => {
    if (!assignmentId || !title || !description) return;

    try {
      const newDiscussion = await createDiscussion({
        title: title,
        description: description,
        subtopicId: assignmentId,
        courseId: courseId,
        tags: []
      }).unwrap();

      // Navigate to the newly created discussion
      navigate(`/discussions/${newDiscussion.id}`);
    } catch (error) {
      console.error('Failed to create discussion:', error);
      // Fallback to general discussions page
      navigate('/discussions');
    }
  };

  const handleJoinDiscussion = () => {
    if (actualDiscussionId) {
      // Navigate to the specific discussion (either provided or found via subtopic)
      navigate(`/discussions/${actualDiscussionId}`);
    } else if (assignmentId && title && description) {
      // Create a new discussion for this assignment
      handleCreateDiscussionForAssignment();
    } else {
      // Navigate to general discussions page
      navigate('/discussions');
    }
  };

  const handleViewProject = () => {
    if (projectId) {
      // Navigate to the specific project
      navigate(`/project-center/${projectId}`);
    } else {
      // Navigate to general project center
      navigate('/project-center');
    }
  };

  // Check if user has participated in the discussion
  useEffect(() => {
    if (actualDiscussionId && discussionData) {
      // Check if user has participated by looking at comment count and user activity
      // For now, we'll use a simple heuristic - if discussion has comments, assume some participation
      // In a real implementation, this would check if the current user has posted comments
      const hasActivity = discussionData.commentCount > 0;
      setIsParticipated(hasActivity);
    } else {
      setIsParticipated(false);
    }
  }, [actualDiscussionId, discussionData]);

  // Show loading state while querying for discussions
  if (isLoadingSubtopicDiscussions) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading discussion...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <MessageSquare className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
            <Badge variant="outline" className="mt-1">
              Discussion Forum Assignment
            </Badge>
          </div>
        </div>
        
        <p className="text-gray-700 leading-relaxed mb-6">
          {description}
        </p>
      </motion.div>

      {/* Discussion Details Card */}
      {discussionData && actualDiscussionId && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-6"
        >
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-blue-800 flex items-center gap-2">
                <MessageSquare className="w-5 h-5" />
                Discussion Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h3 className="font-semibold text-gray-900">{discussionData.title}</h3>
                  <p className="text-sm text-gray-600 mt-1">{discussionData.description}</p>
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    {discussionData.commentCount} responses
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    Created {new Date(discussionData.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* No Discussion Found Card */}
      {!actualDiscussionId && !isLoadingSubtopicDiscussions && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-6"
        >
          <Card className="border-yellow-200 bg-yellow-50">
            <CardHeader>
              <CardTitle className="text-yellow-800 flex items-center gap-2">
                <MessageSquare className="w-5 h-5" />
                No Specific Discussion Found
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-yellow-700">
                No discussion has been created for this assignment yet. You can browse general discussions or create a new one related to this topic.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Action Cards */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Discussion Forum Card */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="h-full hover:shadow-lg transition-all cursor-pointer group" onClick={handleJoinDiscussion}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <MessageSquare className="w-5 h-5 text-blue-600" />
                Join Discussion
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-gray-600 text-sm">
                  Connect with peers, explore new ideas, and deepen your understanding through collaborative learning.
                </p>
                
                {discussionData && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Participation</span>
                      <span className="font-medium">
                        {discussionData.commentCount} responses
                      </span>
                    </div>
                    <Progress value={Math.min((discussionData.commentCount / 10) * 100, 100)} className="h-2" />
                  </div>
                )}

                <Button
                  className="w-full group-hover:bg-blue-700 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleJoinDiscussion();
                  }}
                >
                  <MessageSquare className="w-4 h-4 mr-2" />
                  {actualDiscussionId
                    ? 'Join This Discussion'
                    : (assignmentId && title && description)
                      ? 'Create & Join Discussion'
                      : 'Browse Discussions'
                  }
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Project Center Card */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="h-full hover:shadow-lg transition-all cursor-pointer group" onClick={handleViewProject}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Users className="w-5 h-5 text-green-600" />
                Project Collaboration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-gray-600 text-sm">
                  Work on collaborative projects and apply your learning in practical scenarios.
                </p>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Project Status</span>
                    <span className="font-medium text-green-600">
                      {projectId ? 'Available' : 'Browse Projects'}
                    </span>
                  </div>
                </div>

                <Button
                  variant="outline"
                  className="w-full group-hover:bg-green-50 group-hover:border-green-300 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleViewProject();
                  }}
                >
                  <Users className="w-4 h-4 mr-2" />
                  {projectId ? 'View Related Project' : 'Browse Projects'}
                  <ExternalLink className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Participation Status */}
      {isParticipated && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-6"
        >
          <Card className="border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <CheckCircle className="w-6 h-6 text-green-600" />
                <div>
                  <h3 className="font-semibold text-green-800">Participation Recorded</h3>
                  <p className="text-sm text-green-600">
                    You have successfully participated in this discussion forum.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Instructions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="mt-8"
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Assignment Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-gray-600">
              <div className="flex items-start gap-2">
                <span className="font-semibold text-blue-600 min-w-[20px]">1.</span>
                <span>
                  {actualDiscussionId
                    ? 'Click "Join This Discussion" to participate in the specific forum for this assignment'
                    : (assignmentId && title && description)
                      ? 'Click "Create & Join Discussion" to start a new discussion for this assignment'
                      : 'Click "Browse Discussions" to find or create discussions related to this topic'
                  }
                </span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-semibold text-blue-600 min-w-[20px]">2.</span>
                <span>Share your thoughts and engage with peer responses</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-semibold text-blue-600 min-w-[20px]">3.</span>
                <span>
                  {projectId
                    ? 'Collaborate on the related project if available'
                    : 'Explore collaborative projects in the Project Center'
                  }
                </span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-semibold text-blue-600 min-w-[20px]">4.</span>
                <span>Your participation will be tracked for assessment</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default DiscussionForumRenderer;
