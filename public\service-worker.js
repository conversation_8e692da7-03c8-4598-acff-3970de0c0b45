const CACHE_NAME = "learnido-cache-v1";
const DYNAMIC_CACHE = "learnido-dynamic-v1";
const API_CACHE = "learnido-api-v1";
const SCORM_CACHE = "learnido-scorm-v1";

const STATIC_ASSETS = [
  "/",
  "/index.html",
  "/manifest.json",
  "/favicon.ico",
  "/logo192.png",
  "/logo512.png",
  "/course1.svg",
  "/icons/eco-light.png",
];

// Install service worker and cache static resources
self.addEventListener("install", (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      console.log("Caching static assets");
      return cache.addAll(STATIC_ASSETS);
    })
  );
  // Don't wait for other service workers to finish
  self.skipWaiting();
});

// Helper function to determine if a request is an API call
const isApiRequest = (url) => {
  return url.includes("azurewebsites.net");
};

// Helper function to determine if a request is a SCORM content request
const isScormRequest = (url) => {
  return (
    url.includes("s3.lernido.stmin.dev") || url.includes("Course%20examples")
  );
};

// Helper function to determine if a request is an auth request
const isAuthRequest = (url) => {
  return url.includes("auth0.com") || url.includes("token");
};

// Network first, falling back to cache strategy for API requests
const networkFirstStrategy = async (request) => {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(API_CACHE);
      await cache.put(request, networkResponse.clone());
      return networkResponse;
    }
    throw new Error("Network response was not ok");
  } catch (error) {
    // If it's a 4xx status code, return the error directly
    if (error.status >= 400 && error.status < 500) {
      console.error("API request failed with status", error.status);
      throw error;
    }
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    // If we're offline and don't have a cached response, return a custom offline response
    return new Response(
      JSON.stringify({
        error: "You are offline",
        offline: true,
        timestamp: new Date().toISOString(),
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  }
};

// Cache first, falling back to network strategy for static assets and SCORM content
const cacheFirstStrategy = async (request) => {
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cacheName = isScormRequest(request.url)
        ? SCORM_CACHE
        : DYNAMIC_CACHE;
      const cache = await caches.open(cacheName);
      await cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    // Return default offline page/image if available
    if (request.destination === "image") {
      return caches.match("/icons/eco-light.png");
    }
    throw error;
  }
};

// Main fetch event handler
self.addEventListener("fetch", (event) => {
  // Skip cross-origin requests except for API and SCORM content
  if (
    !event.request.url.startsWith(self.location.origin) &&
    !isApiRequest(event.request.url) &&
    !isScormRequest(event.request.url)
  ) {
    return;
  }

  // Don't cache auth requests
  if (
    (isAuthRequest(event.request.url) && event.request.method === "POST") ||
    event.request.method === "DELETE" ||
    event.request.method === "PUT"
  ) {
    return;
  }

  event.respondWith(
    isApiRequest(event.request.url)
      ? networkFirstStrategy(event.request)
      : cacheFirstStrategy(event.request)
  );
});

// Clean up old caches
self.addEventListener("activate", (event) => {
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames
            .filter((cacheName) => {
              return (
                cacheName !== CACHE_NAME &&
                cacheName !== DYNAMIC_CACHE &&
                cacheName !== API_CACHE &&
                cacheName !== SCORM_CACHE
              );
            })
            .map((cacheName) => caches.delete(cacheName))
        );
      }),
      // Claim all clients immediately
      self.clients.claim(),
    ])
  );
});

// Handle messages from the client
self.addEventListener("message", async (event) => {
  if (event.data && event.data.type === "CACHE_SUBTOPIC_CONTENT") {
    try {
      const { subTopicId, contentUrl } = event.data;
      if (contentUrl) {
        const cache = await caches.open(SCORM_CACHE);
        const response = await fetch(contentUrl);
        if (response.ok) {
          await cache.put(contentUrl, response);
          console.log(`Cached content for subtopic ${subTopicId}`);
        }
      }
    } catch (error) {
      console.error("Failed to cache subtopic content:", error);
    }
  } else if (event.data && event.data.type === "CACHE_TOPIC_CONTENT") {
    try {
      const { topicId, contentUrl } = event.data;
      if (contentUrl) {
        const cache = await caches.open(SCORM_CACHE);
        const response = await fetch(contentUrl);
        if (response.ok) {
          await cache.put(contentUrl, response);
          console.log(`Cached content for topic ${topicId}`);
        }
      }
    } catch (error) {
      console.error("Failed to cache topic content:", error);
    }
  } else if (event.data && event.data.type === "ONLINE_STATUS_CHANGE") {
    // Broadcast the status to all clients
    self.clients.matchAll().then((clients) => {
      clients.forEach((client) => {
        client.postMessage({
          type: "ONLINE_STATUS_UPDATE",
          online: event.data.online,
        });
      });
    });
  }
});
