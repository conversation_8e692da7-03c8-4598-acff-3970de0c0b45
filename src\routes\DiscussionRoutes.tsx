import React from 'react';
import { Route, Routes } from 'react-router-dom';
import { useAuth0 } from '@auth0/auth0-react';
import { DiscussionList } from '../components/Discussions/DiscussionList';
import { DiscussionDetail } from '../components/Discussions/DiscussionDetail';
import { DiscussionManagement } from '../components/Discussions/DiscussionManagement';
import { Alert, AlertDescription } from '../components/ui/alert';
import { AlertCircle } from 'lucide-react';
import DiscussionsPage from '@/components/Student/Discussions/DiscussionsPage';

export function DiscussionRoutes() {
  const { user, isAuthenticated, isLoading } = useAuth0();
  
  // Get user role
  const userRole = user?.['https://learnido-app/roleId'] || '4';
  
  // Check if user can access management dashboard
  const canAccessManagement = ['1', '2', '3'].includes(userRole);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          You must be logged in to access discussions.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Routes>
      <Route path="/" element={<DiscussionsPage />} />
      <Route path="/:id" element={<DiscussionDetail />} />
      {/* Course-specific discussions */}
      <Route path="/course/:courseId" element={<DiscussionList />} />
      <Route path="/classroom/:classId" element={<DiscussionList />} />
      <Route path="/topic/:topicId" element={<DiscussionList />} />
      
      {/* Management routes (protected by role) */}
        <Route path="/management" element={<DiscussionManagement />} />
      
      {/* Fallback route */}
      <Route path="*" element={<DiscussionsPage />} />
    </Routes>
  );
}

export default DiscussionRoutes;