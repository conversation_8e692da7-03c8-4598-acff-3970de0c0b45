import React, { useEffect, useState } from "react";
import Select from "react-select";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import "suneditor/src/assets/css/suneditor.css";
import { getAllTopics, getAllCourses } from "@/common/basicAPI";
import { useDispatch, useSelector } from "react-redux";
import {
  setQuestionsValue,
  setQuestionLevel,
  setTempQuestionLevel,
  setQuestionTypeData,
  setQuestionsValueMany,
} from "components/Admin/QuestionCreation/questionSlice";
import useDebounce from "@/helpers/useDebounce";
import {
  useLazyGetCourseDetailsDataQuery,
  useLazyGetQuestionTypeDataQuery,
} from "APIConnect";

const Question = ({ handleQuestionSubmit, handleCancel }) => {
  const dispatch = useDispatch();
  const [getCourseDetailsData] = useLazyGetCourseDetailsDataQuery();
  const [getQuestionTypeData] = useLazyGetQuestionTypeDataQuery();

  const courseId = useSelector((state) => state.questions.questions.courseId);
  const chapterId = useSelector((state) => state.questions.questions.chapterId);
  const topicId = useSelector((state) => state.questions.questions.topicId);
  const questionTypes = useSelector((state) => state.questions.questionTypes);
  const questionLevel = useSelector((state) => state.questions.questionLevel);
  const tempQuestionLevel = useSelector(
    (state) => state.questions.tempQuestionLevel
  );
  const questionType = useSelector(
    (state) => state.questions.questions.questionType
  );
  const questionText = useSelector(
    (state) => state.questions.questions.questionText
  );
  const questionText2 = useSelector(
    (state) => state.questions.questions.questionText2
  );

  const [question, setQuestion] = useState(questionText);
  const [question2, setQuestion2] = useState(questionText2);
  const [coursesList, setCoursesList] = useState([]);
  const [topicsList, setTopicsList] = useState([]);

  const [courseAllDetail, setCourseAllDetail] = useState({});
  const [chaptersList, setChaptersList] = useState([]);

  const questionTextDebounceVal = useDebounce(question);
  const questionTextDebounceVal2 = useDebounce(question2);

  useEffect(() => {
    getCourses();
    // getQuestionType() Once get from API will call that
  }, []);

  useEffect(() => {
    const obj = {
      questionText: "",
    };
    if (
      questionTextDebounceVal?.replace(/<[^>]+>/g, "")?.length > 0 &&
      question?.length > 0
    ) {
      obj["questionText"] = questionTextDebounceVal;
    }

    if (
      questionTextDebounceVal2?.replace(/<[^>]+>/g, "")?.length > 0 &&
      question2?.length > 0
    ) {
      obj["questionText2"] = questionTextDebounceVal2;
    }
    dispatch(setQuestionsValueMany(obj));
  }, [
    dispatch,
    questionText,
    questionTextDebounceVal,
    questionTextDebounceVal2,
  ]);

  useEffect(() => {
    if (
      questionText?.replace(/<[^>]+>/g, "").length === 0 &&
      questionLevel > 0
    ) {
      dispatch(setTempQuestionLevel(questionLevel));
      dispatch(setQuestionLevel(0));
    } else if (
      questionText?.replace(/<[^>]+>/g, "").length > 0 &&
      questionLevel !== tempQuestionLevel
    ) {
      dispatch(setQuestionLevel(tempQuestionLevel));
      dispatch(setTempQuestionLevel(tempQuestionLevel));
    }
  }, [dispatch, questionLevel, questionText, tempQuestionLevel]);

  const getCourses = async () => {
    try {
      const resultObject = await getAllCourses();
      if (resultObject) {
        let data = [],
          obj = {};
        resultObject?.map((item) => {
          obj = {
            value: item?.id,
            label: item?.name,
          };
          data.push(obj);
        });
        setCoursesList(data);
        handleCourseChoose(courseId);
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const getQuestionType = async () => {
    try {
      const { data } = await getQuestionTypeData();
      if (data) {
        let arr, obj;
        data?.map((item) => {
          obj = {
            label: "Multi-Choice",
            value: "Multi-Choice",
          };
          arr.push(obj);
        });

        dispatch(setQuestionTypeData(data));
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const getTopics = async () => {
    try {
      const resultObject = await getAllTopics();
      if (resultObject) {
        let data = [],
          obj = {};
        resultObject?.map((item) => {
          obj = {
            value: item?.id,
            label: item?.name,
          };
          data.push(obj);
        });
        setTopicsList(data);
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const handleSubmit = () => {
    handleQuestionSubmit(1);
  };

  const getDisabled = () => {
    if (
      questionText !== "" &&
      questionType !== "" &&
      questionText?.replace(/<[^>]+>/g, "") !== "" &&
      topicId !== ""
    ) {
      return false;
    }

    return true;
  };

  const handleSelectChange = (key, val) => {
    let obj = {
      [key]: val,
    };
    if (key === "questionType" && questionType !== "" && questionType !== val) {
      obj["answers"] = [];
      obj["correctAnswer"] = null;
    }
    dispatch(setQuestionsValueMany(obj));
    if (key == "courseId") {
      handleSelectChange("chapterId", "");
      handleSelectChange("topicId", "");
    } else if (key == "chapterId") {
      handleSelectChange("topicId", "");
    }
  };

  const handleQuestionCancel = () => {
    setQuestion("");
    handleCancel();
  };

  const handleCourseChoose = async (id) => {
    try {
      const obj = {
        courseId: id,
      };
      const {
        data: { resultObject },
      } = await getCourseDetailsData(obj);
      if (resultObject && resultObject.chapters?.length) {
        setCourseAllDetail(resultObject);
        let data = [],
          obj = {};
        resultObject.chapters?.map((item) => {
          obj = {
            value: item?.id,
            label: item?.name,
          };
          data.push(obj);
        });
        setChaptersList(data);
        // handleSelectChange("topicId", "");
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  useEffect(() => {
    if (Object.keys(courseAllDetail)?.length > 0) {
      if (courseId) {
        handleChapterChoose(chapterId);
      }
      if (topicId) {
        setTimeout(() => {
          handleSelectChange("topicId", topicId);
        });
      }
    }
  }, [courseAllDetail]);

  const handleChapterChoose = async (id) => {
    const chapter = courseAllDetail?.chapters?.find((e) => e.id === id);
    const topics = chapter?.topics;
    if (topics?.length) {
      let data = [],
        obj = {};
      topics?.map((item) => {
        obj = {
          value: item?.id,
          label: item?.name,
        };
        data.push(obj);
      });
      setTopicsList(data);
    }
  };
  return (
    <>
      <label className="form-label2">
        Course<span className="compulsoryInd2">*</span>
      </label>

      <Select
        options={coursesList}
        className="form-input2 select-input2"
        value={coursesList.find((option) => option.value === courseId) || ""}
        onChange={(item) => {
          handleCourseChoose(item?.value);
          handleSelectChange("courseId", item?.value);
        }}
      />

      <label className="form-label2">
        Chapter<span className="compulsoryInd2">*</span>
      </label>

      <Select
        options={chaptersList}
        className="form-input2 select-input2"
        value={chaptersList.find((option) => option.value === chapterId) || ""}
        onChange={(item) => {
          handleChapterChoose(item?.value);
          handleSelectChange("chapterId", item?.value);
        }}
      />

      <label className="form-label2">
        Topic<span className="compulsoryInd2">*</span>
      </label>
      <Select
        options={topicsList}
        className="form-input2 select-input2"
        value={topicsList.find((option) => option.value === topicId) || ""}
        onChange={(item) => {
          handleSelectChange("topicId", item?.value);
        }}
      />

      <span className="question-type-label">
        Question Type<span className="compulsoryInd2">*</span>
      </span>

      <Select
        options={questionTypes}
        className="form-input2 select-input2"
        value={
          questionTypes.find((option) => option.value === questionType) || ""
        }
        onChange={(item) => {
          handleSelectChange("questionType", item?.value);
        }}
      />
      <label className="form-label2">
        Question<span className="compulsoryInd2">*</span>
      </label>
      {/* <div className='html-content question-text' style={{marginTop:10,height:30, 
      }}>
      {questionText}
      </div> */}
      <div
        className="html-content question-text"
        dangerouslySetInnerHTML={{
          __html: questionText + " " + questionText2,
        }}
      />
      <div style={{ marginTop: "20px" }}>
        <textarea
          className="question-input "
          value={question}
          onChange={(e) => setQuestion(e.target.value)}
        />
      </div>
      {questionType === "TEXTINPUT" && (
        <div style={{ marginTop: "20px" }}>
          <textarea
            className="question-input "
            value={question2}
            onChange={(e) => setQuestion2(e.target.value)}
          />
        </div>
      )}
      {/* <div
        className="html-content question-text"
        dangerouslySetInnerHTML={{
          __html: questionText,
        }}
      /> */}
      {/* <SunEditor
        setOptions={{
          buttonList: [
            ["undo", "redo"],
            ["font", "fontSize", "formatBlock"],
            ["bold", "underline", "italic", "strike"],
            ["fontColor", "hiliteColor"],
            ["align", "list", "table"],
            ["link", "image", "video"],
            ["fullScreen", "showBlocks", "codeView"],
            ["preview", "print"],
          ],
          height: "70%",
          minHeight: "100px",
        }}
        setContents={questionText}
        onChange={(textContent) => setQuestion(textContent)}
      /> */}
      <div className="button-container">
        <div className="align-right">
          <button
            id="cancelbutton"
            className="cancel-button"
            onClick={() => handleQuestionCancel()}
          >
            Cancel
          </button>
        </div>
        <div id="nextbutton" className="align-right">
          <button onClick={handleSubmit} disabled={getDisabled()}>
            Next
          </button>
        </div>
      </div>
    </>
  );
};

export default Question;
