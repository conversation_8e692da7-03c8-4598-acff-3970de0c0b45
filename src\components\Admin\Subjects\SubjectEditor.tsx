import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, Save, BookOpen, Loader2 } from 'lucide-react';
import {
    useGetSubjectByIdQuery,
    useCreateSubjectMutation,
    useUpdateSubjectMutation,
} from '@/APIConnect';
import { toast } from "@/hooks/use-toast";
import { SubjectCreationRequest } from './types';

interface FormErrors {
    name?: string;
    description?: string;
}

const SubjectEditor = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const isEditMode = !!id;

    const { data: subjectData, isLoading: isLoadingSubject } = useGetSubjectByIdQuery(id, { skip: !id });
    const [createSubject, { isLoading: isCreating }] = useCreateSubjectMutation();
    const [updateSubject, { isLoading: isUpdating }] = useUpdateSubjectMutation();

    const [formData, setFormData] = useState<SubjectCreationRequest>({
        name: '',
        description: '',
        createdOn: new Date().toISOString(),
    });

    const [errors, setErrors] = useState<FormErrors>({});

    useEffect(() => {
        if (subjectData) {
            setFormData({
                name: subjectData.name,
                description: subjectData.description,
                createdOn: subjectData.createdOn,
            });
        }
    }, [subjectData]);

    const validateForm = (): boolean => {
        const newErrors: FormErrors = {};
        let isValid = true;

        if (!formData.name.trim()) {
            newErrors.name = "Name is required";
            isValid = false;
        }

        if (!formData.description.trim()) {
            newErrors.description = "Description is required";
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            toast({
                title: "Please fix the errors",
                description: "Some fields are missing or invalid",
                variant: "destructive",
            });
            return;
        }

        try {
            if (isEditMode) {
                await updateSubject({
                    id,
                    ...formData,
                }).unwrap();
                toast({
                    title: "Subject updated successfully",
                    variant: "default",
                });
            } else {
                await createSubject(formData).unwrap();
                toast({
                    title: "Subject created successfully",
                    variant: "default",
                });
            }
            navigate('/admin/subjects');
        } catch (error) {
            console.error('Error saving subject:', error);
            toast({
                title: "Failed to save subject",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    if (isEditMode && isLoadingSubject) {
        return (
            <div className="flex justify-center items-center h-screen">
                <Loader2 className="w-8 h-8 animate-spin" />
            </div>
        );
    }

    const isSubmitting = isCreating || isUpdating;

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="sticky top-0 z-10 bg-white border-b">
                <div className="container mx-auto px-4 py-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => navigate('/admin/subjects')}
                                disabled={isSubmitting}
                            >
                                <ArrowLeft className="h-5 w-5" />
                            </Button>
                            <h1 className="text-2xl font-bold flex items-center">
                                <BookOpen className="h-6 w-6 mr-2" />
                                {isEditMode ? 'Edit Subject' : 'Create Subject'}
                            </h1>
                        </div>
                        <Button
                            onClick={handleSubmit}
                            className="flex items-center"
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? (
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            ) : (
                                <Save className="h-4 w-4 mr-2" />
                            )}
                            {isEditMode ? 'Update' : 'Create'}
                        </Button>
                    </div>
                </div>
            </div>

            <div className="container mx-auto px-4 py-6">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center mb-6">
                            <BookOpen className="h-5 w-5 mr-2" />
                            <h2 className="text-xl font-semibold">Subject Details</h2>
                        </div>
                        <div className="space-y-6">
                            <div>
                                <Label>Name</Label>
                                <Input
                                    value={formData.name}
                                    onChange={(e) => {
                                        setFormData((prev) => ({ ...prev, name: e.target.value }));
                                        if (errors.name) {
                                            setErrors((prev) => ({ ...prev, name: undefined }));
                                        }
                                    }}
                                    placeholder="Enter subject name..."
                                    className={errors.name ? "border-red-500" : ""}
                                    disabled={isSubmitting}
                                />
                                {errors.name && (
                                    <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                                )}
                            </div>

                            <div>
                                <Label>Description</Label>
                                <Textarea
                                    value={formData.description}
                                    onChange={(e) => {
                                        setFormData((prev) => ({ ...prev, description: e.target.value }));
                                        if (errors.description) {
                                            setErrors((prev) => ({ ...prev, description: undefined }));
                                        }
                                    }}
                                    placeholder="Enter subject description..."
                                    className={errors.description ? "border-red-500" : ""}
                                    disabled={isSubmitting}
                                    rows={4}
                                />
                                {errors.description && (
                                    <p className="text-sm text-red-500 mt-1">{errors.description}</p>
                                )}
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default SubjectEditor;
