import { ClassroomAnalyticsResponseDto } from "@/types/analytics";
import { format, subDays, parseISO } from "date-fns";

export interface TeacherAnalyticsData {
  noData: boolean;
  participation: Array<{ date: string; value: number }>;
  classPerformance: Array<{ label: string; value: number }>;
  chapterScores: Array<{ label: string; value: number }>;
  questionTypeAttempts: Array<{
    date: string;
    "Multiple Choice": number;
    "Single Choice": number;
    "Text Input": number;
    Match: number;
    Binary: number;
    Soundbased: number;
  }>;
  difficultyAttempts: Array<{
    date: string;
    "1": number;
    "2": number;
    "3": number;
    "4": number;
    "5": number;
  }>;
}

export interface Classroom {
  id: string;
  name: string;
}

export const mockClassrooms: Classroom[] = [
  { id: "1", name: "Class 10A" },
  { id: "2", name: "Class 10B" },
  { id: "3", name: "Class 9A" },
  { id: "4", name: "Class 9B" },
];

export const generateTeacherAnalyticsDataFromApi = (
  classroomAnalyticsApiResponse: ClassroomAnalyticsResponseDto
): TeacherAnalyticsData => {
  const result = classroomAnalyticsApiResponse.resultObject;

  // Student Participation Trend
  const participationMap: { [date: string]: number } = {};
  result.forEach((item) => {
    const date = format(parseISO(item.updatedon), "yyyy-MM-dd");
    participationMap[date] =
      (participationMap[date] || 0) + item.uniqueStudentCount;
  });
  const participation = Object.keys(participationMap).map((date) => ({
    date,
    value: participationMap[date],
  }));

  // Topic Performance
  const topicMap: { [topic: string]: { totalScore: number; count: number } } =
    {};
  result.forEach((item) => {
    const topic = item.topicName || item.topicId;
    if (topic) {
      if (!topicMap[topic]) {
        topicMap[topic] = { totalScore: 0, count: 0 };
      }
      topicMap[topic].totalScore += item.averageScore;
      topicMap[topic].count += 1;
    }
  });
  const classPerformance = Object.keys(topicMap).map((topic) => ({
    label: topic,
    value: topicMap[topic].totalScore / topicMap[topic].count,
  }));

  // Chapter Performance
  const chapterMap: {
    [chapter: string]: { totalScore: number; count: number };
  } = {};
  result.forEach((item) => {
    const chapter = item.chapterName || item.chapterId;
    if (chapter) {
      if (!chapterMap[chapter]) {
        chapterMap[chapter] = { totalScore: 0, count: 0 };
      }
      chapterMap[chapter].totalScore += item.averageScore;
      chapterMap[chapter].count += 1;
    }
  });
  const chapterScores = Object.keys(chapterMap).map((chapter) => ({
    label: chapter,
    value: chapterMap[chapter].totalScore / chapterMap[chapter].count,
  }));

  // Question Type Distribution
  const questionTypes = [
    "Multiple Choice",
    "Single Choice",
    "Text Input",
    "Match",
    "Binary",
    "Soundbased",
    "Picture Match",
  ];
  const questionTypeKeys = [
    "averageMultipleChoiceCount",
    "averageSingleChoiceCount",
    "averageTextInputCount",
    "averageMatchCount",
    "averageBinaryCount",
    "averageSoundbasedCount",
    "averagePictureMatchCount",
  ];
  const questionTypeMap: {
    [date: string]: { [type: string]: number };
  } = {};
  result.forEach((item) => {
    const date = format(parseISO(item.updatedon), "yyyy-MM-dd");
    if (!questionTypeMap[date]) {
      questionTypeMap[date] = {};
    }
    questionTypeKeys.forEach((key, index) => {
      const count = (item as any)[key]?.averageTotalCount || 0;
      const type = questionTypes[index];
      questionTypeMap[date][type] = (questionTypeMap[date][type] || 0) + count;
    });
  });
  const questionTypeAttempts = Object.keys(questionTypeMap).map((date) => ({
    date,
    "Multiple Choice": questionTypeMap[date]["Multiple Choice"] || 0,
    "Single Choice": questionTypeMap[date]["Single Choice"] || 0,
    "Text Input": questionTypeMap[date]["Text Input"] || 0,
    Match: questionTypeMap[date]["Match"] || 0,
    Binary: questionTypeMap[date]["Binary"] || 0,
    Soundbased: questionTypeMap[date]["Soundbased"] || 0,
  }));

  // Difficulty Level Distribution
  const levels = ["1", "2", "3", "4", "5"];
  const levelKeys = [
    "averageLvl1Count",
    "averageLvl2Count",
    "averageLvl3Count",
    "averageLvl4Count",
    "averageLvl5Count",
  ];
  const difficultyMap: {
    [date: string]: { [level: string]: number };
  } = {};
  result.forEach((item) => {
    const date = format(parseISO(item.updatedon), "yyyy-MM-dd");
    if (!difficultyMap[date]) {
      difficultyMap[date] = {};
    }
    levelKeys.forEach((key, index) => {
      // Remove '.averageTotalCount' if not applicable
      const count = (item as any)[key] || 0;
      const level = levels[index];
      difficultyMap[date][level] = (difficultyMap[date][level] || 0) + count;
    });
  });
  const difficultyAttempts = Object.keys(difficultyMap).map((date) => ({
    date,
    "1": difficultyMap[date]["1"] || 0,
    "2": difficultyMap[date]["2"] || 0,
    "3": difficultyMap[date]["3"] || 0,
    "4": difficultyMap[date]["4"] || 0,
    "5": difficultyMap[date]["5"] || 0,
  }));

  return {
    noData: result.length === 0,
    participation,
    classPerformance,
    chapterScores,
    questionTypeAttempts,
    difficultyAttempts,
  };
};
export const generateTeacherAnalyticsData = (
  classroomAnalyticsApiResponse?: ClassroomAnalyticsResponseDto
): TeacherAnalyticsData => {
  // Generate dates for the past 7 days
  const dates = Array.from({ length: 7 }, (_, i) =>
    format(subDays(new Date(), 6 - i), "yyyy-MM-dd")
  );

  // Topics and Chapters
  const topics = [
    "Algebra",
    "Geometry",
    "Calculus",
    "Statistics",
    "Trigonometry",
  ];
  const chapters = [
    "Introduction to Functions",
    "Linear Equations",
    "Quadratic Equations",
    "Polynomials",
    "Coordinate Geometry",
    "Trigonometric Ratios",
  ];

  // Generate participation data (student count per day)
  const participation = dates.map((date) => ({
    date,
    value: Math.floor(Math.random() * 15) + 25, // Random between 25-40 students
  }));

  // Generate class performance by topic
  const classPerformance = topics.map((topic) => ({
    label: topic,
    value: Math.floor(Math.random() * 300) + 200, // Random score between 200-500
  }));

  // Generate chapter-wise average scores
  const chapterScores = chapters.map((chapter) => ({
    label: chapter,
    value: Math.floor(Math.random() * 30) + 70, // Random score between 70-100
  }));

  // Generate question type attempts over time
  const questionTypeAttempts = dates.map((date) => ({
    date,
    "Multiple Choice": Math.floor(Math.random() * 20) + 10,
    "Single Choice": Math.floor(Math.random() * 15) + 8,
    "Text Input": Math.floor(Math.random() * 12) + 6,
    Match: Math.floor(Math.random() * 10) + 5,
    Binary: Math.floor(Math.random() * 8) + 4,
    Soundbased: Math.floor(Math.random() * 5) + 2,
  }));

  // Generate difficulty level attempts over time
  const difficultyAttempts = dates.map((date) => ({
    date,
    "1": Math.floor(Math.random() * 15) + 10,
    "2": Math.floor(Math.random() * 12) + 8,
    "3": Math.floor(Math.random() * 10) + 6,
    "4": Math.floor(Math.random() * 8) + 4,
    "5": Math.floor(Math.random() * 5) + 2,
  }));

  return {
    noData: false,
    participation,
    classPerformance,
    chapterScores,
    questionTypeAttempts,
    difficultyAttempts,
  };
};
