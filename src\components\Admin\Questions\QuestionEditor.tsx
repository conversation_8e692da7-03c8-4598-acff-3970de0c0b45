import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
    BookOpen,
    BrainCircuit,
    FileQuestion,
    MessageSquareText,
    Plus,
    Trash2,
    ArrowLeft,
    Save,
    ListChecks,
    Activity,
    ToggleLeft,
    Loader2,
    Eye
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import ResourceMetadataForm from './ResourceMetadataForm';
import RichTextEditor from './RichTextEditor';
import CourseSelector from './CourseSelector';
import QuestionPreview from './QuestionPreview';
import { Question, Course, QuestionType, CorrectAnswerType } from './types';
import {
    useGetQuestionByIdQuery,
    useCreateQuestionMutation,
    useUpdateQuestionMutation,
    useLazyGetAdminCoursesQuery,
    useLazyGetCourseDetailsDataQuery,
    useListCourseInitailOrFinalQuizQuestionsQuery,
    useAddQuestionToFinalOrInitialCourseQuizMutation,
    useRemoveQuestionFromFinalOrInitialCourseQuizMutation,
} from '@/APIConnect';
import { toast } from '@/hooks/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useFileUpload } from '@/lib/utils';

const QuestionEditor = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const isEditMode = !!id;
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const initialCourseId = queryParams.get('courseId') || '';
    const initialChapterId = queryParams.get('chapterId') || '';
    const initialTopicId = queryParams.get('topicId') || '';

    const { data: questionData, isLoading: isLoadingQuestion } = useGetQuestionByIdQuery({ id }, { skip: !id });
    const [createQuestion] = useCreateQuestionMutation();
    const [updateQuestion] = useUpdateQuestionMutation();
    const [getAllCourseData] = useLazyGetAdminCoursesQuery();
    const [getCourseDetails] = useLazyGetCourseDetailsDataQuery();

    const [courses, setCourses] = useState<Course[]>([]);
    const [selectedCourse, setSelectedCourse] = useState<string>(initialCourseId);
    const [selectedChapter, setSelectedChapter] = useState<string>(initialChapterId);
    const [selectedTopic, setSelectedTopic] = useState<string>(initialTopicId);
    const [isCoursesLoaded, setIsCoursesLoaded] = useState(false);

    const { uploadFile } = useFileUpload();

    // Initialize form data
    const [formData, setFormData] = useState<Omit<Question, 'id'>>({
        anteriorId: '',
        questionText: '',
        mark: 0,
        answers: [{
            answerText: '',
            matchText: '',
            answerImageMetadata: null, // used for both left and right images in match type
            answerVideoMetadata: null,
            answerAudioMetadata: null
        }],
        type: 'SINGLECHOICE',
        correctAnswer: '',
        explanation: '',
        level: 1,
        active: true,
        topicId: initialTopicId,
        createdBy: 'Test',
        questionImageMetadata: null,
        questionVideoMetadata: null,
        questionAudioMetadata: null,
        explanationImageMetadata: null,
        explanationVideoMetadata: null,
        explanationAudioMetadata: null,
    });

    // Fetch courses data
    useEffect(() => {
        const fetchAllCourseData = async () => {
            try {
                const { data: coursesData } = await getAllCourseData('');
                if (coursesData?.resultObject) {
                    const coursesWithDetails = await Promise.all(
                        coursesData.resultObject.map(async (course: Course) => {
                            const { data: detailsData } = await getCourseDetails({ courseId: course.id });
                            if (detailsData?.resultObject) {
                                return {
                                    ...course,
                                    chapters: detailsData.resultObject.chapters
                                };
                            }
                            return course;
                        })
                    );
                    setCourses(coursesWithDetails);
                    setIsCoursesLoaded(true);
                }
            } catch (error) {
                console.error('Error fetching course data:', error);
            }
        };

        fetchAllCourseData();
    }, [getAllCourseData, getCourseDetails]);

    // Handle question data for edit mode
    useEffect(() => {
        if (isEditMode && questionData?.resultObject && isCoursesLoaded) {
            let question = { ...questionData.resultObject };
            if (question.type === 'MATCH') {
                let matchText: string[] = question.correctAnswer ? Object.values(question.correctAnswer) : [];
                const answers = question.answers.filter((a: any, i: number) => !matchText.includes(a['answerText'])).map((a: any, i: number) => {
                    return {
                        answerText: a.answerText,
                        matchText: question.correctAnswer[a.answerText],
                        answerImageMetadata: a.answerImageMetadata,
                        answerVideoMetadata: a.answerVideoMetadata,
                        answerAudioMetadata: a.answerAudioMetadata
                    };
                });
                question.answers = answers
            }


            setFormData(question);

            // Find and set course/chapter/topic based on the question's topicId
            for (const course of courses) {
                if (course.chapters) {
                    for (const chapter of course.chapters) {
                        const topic = chapter.topics.find(t => t.id === question.topicId);
                        if (topic) {
                            setSelectedCourse(course.id);
                            setTimeout(() => {
                                setSelectedChapter(chapter.id);
                                setTimeout(() => {
                                    setSelectedTopic(topic.id);
                                }, 100);
                            }, 100);
                            break;
                        }
                    }
                }
            }
        }
    }, [questionData, courses, isEditMode, isCoursesLoaded]);

    // Handle initial selection for new question
    useEffect(() => {
        if (!isEditMode && isCoursesLoaded && initialCourseId) {
            const course = courses.find(c => c.id === initialCourseId);
            if (course) {
                setSelectedCourse(initialCourseId);
                if (initialChapterId) {
                    const chapter = course.chapters?.find(ch => ch.id === initialChapterId);
                    if (chapter) {
                        setTimeout(() => {
                            setSelectedChapter(initialChapterId);
                            if (initialTopicId) {
                                const topic = chapter.topics.find(t => t.id === initialTopicId);
                                if (topic) {
                                    setTimeout(() => {
                                        setSelectedTopic(initialTopicId);
                                        setFormData(prev => ({ ...prev, topicId: initialTopicId }));
                                    }, 100);
                                }
                            }
                        }, 100);
                    }
                }
            }
        }
    }, [isCoursesLoaded, courses, initialCourseId, initialChapterId, initialTopicId, isEditMode]);

    const handleCourseSelect = useCallback((courseId: string) => {
        setSelectedCourse(courseId);
        setSelectedChapter('');
        setSelectedTopic('');
        setFormData(prev => ({ ...prev, topicId: '' }));
    }, []);

    const handleChapterSelect = useCallback((chapterId: string) => {
        setSelectedChapter(chapterId);
        setSelectedTopic('');
        setFormData(prev => ({ ...prev, topicId: '' }));
    }, []);

    const handleTopicSelect = useCallback((topicId: string) => {
        setSelectedTopic(topicId);
        setFormData(prev => ({ ...prev, topicId }));
    }, []);


    const handleMatchPairChange = useCallback((index: number, matchIndex: number) => {
        const newCorrectAnswer = formData.correctAnswer ?
            [...(formData.correctAnswer as [number, number][])] :
            [];

        // Find if this index already has a pair
        const existingPairIndex = newCorrectAnswer.findIndex(pair => pair[0] === index);

        if (existingPairIndex !== -1) {
            // Update existing pair
            newCorrectAnswer[existingPairIndex] = [index, matchIndex] as [number, number];
        } else {
            // Add new pair
            newCorrectAnswer.push([index, matchIndex] as [number, number]);
        }

        setFormData(prev => ({
            ...prev,
            correctAnswer: newCorrectAnswer as CorrectAnswerType[QuestionType]
        }));
    }, [formData.correctAnswer]);

    // New helper function for match side image upload
    const handleMatchImageChange = async (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
        const files = e.target.files;
        if (!files || !files[0]) return;
        const file = files[0];
        try {
            const fileName = `${Date.now()}_${file.name}`;
            const response = await uploadFile(file, fileName);
            if (typeof response === 'string' && response.startsWith("http")) {
                setFormData(prev => {
                    const newAnswers = [...prev.answers];
                    newAnswers[index] = { ...newAnswers[index], answerImageMetadata: { resourceSource: response, resourceName: file.name, resourcePath: "", resourceTitle: "", resourceDescription: "" } };
                    return { ...prev, answers: newAnswers };
                });
                toast({
                    title: "Match image uploaded successfully",
                    variant: "default"
                });
            } else {
                throw new Error("Upload failed");
            }
        } catch (error) {
            console.error("Error uploading match image:", error);
            toast({
                title: "Failed to upload match image",
                variant: "destructive"
            });
        }
    };

    // Update the matching pairs section in the render
    const renderMatchingPairs = () => {
        if (formData.type !== 'MATCH') return null;

        return (
            <Card className="mt-6">
                <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                        <ListChecks className="h-5 w-5 mr-2" />
                        <h2 className="text-xl font-semibold">Matching Pairs</h2>
                    </div>
                    <div className="space-y-4">
                        {formData.answers.map((answer, index) => (
                            <div key={index} className="flex items-center space-x-2">
                                <div className="flex-1">
                                    <Input
                                        value={answer.answerText}
                                        onChange={(e) => updateAnswer(index, 'answerText', e.target.value)}
                                        placeholder={`Enter prompt ${index + 1}`}
                                    />
                                </div>
                                <div className="flex-1">
                                    <Input
                                        value={answer.matchText as string || ''}
                                        onChange={(e) => updateAnswer(index, 'matchText', e.target.value)}
                                        placeholder={`Enter match for prompt ${index + 1}`}
                                    />
                                </div>
                                {/* New: File input for uploading match (right-hand side) image */}
                                <div className="flex-1">
                                    <input
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => handleMatchImageChange(e, index)}
                                    />
                                    {answer.answerImageMetadata && (
                                        <img
                                            src={answer.answerImageMetadata.resourceSource}
                                            alt="Match preview"
                                            className="h-10 w-10 object-cover mt-1"
                                        />
                                    )}
                                </div>
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    type="button"
                                    onClick={() => {
                                        setFormData(prevData => ({
                                            ...prevData,
                                            answers: prevData.answers.filter((_, i) => i !== index),
                                            // Also remove any correct answer pairs involving this index
                                        }));
                                    }}
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            </div>
                        ))}

                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => setFormData(prevData => ({
                                ...prevData,
                                answers: [...prevData.answers, {
                                    answerText: '',
                                    matchText: '',
                                    answerImageMetadata: null,
                                    answerVideoMetadata: null,
                                    answerAudioMetadata: null
                                }]
                            }))}
                            className="w-full"
                        >
                            <Plus className="w-4 h-4 mr-2" />
                            Add Pair
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    };

    // Update the handleSubmit function to properly format match type answers
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        try {
            // Validate required fields
            if (!formData.questionText || !formData.topicId) {
                toast({
                    title: 'Please fill in all required fields',
                    variant: 'destructive'
                });
                return;
            }

            // if (formData.type === 'SOUNDBASED' && !formData.questionAudioMetadata) {
            //     toast({
            //         title: 'Please upload an audio file for sound based question',
            //         variant: 'destructive'
            //     });
            //     return;
            // }

            // Additional validation for match type
            if (formData.type === 'MATCH') {
                console.log(formData);
                const pairIsntSelected = formData.answers.find((a, i) => !a.matchText || a.matchText === '');

                if (pairIsntSelected) {
                    toast({
                        title: 'Please select correct matches for all pairs',
                        variant: 'destructive'
                    });
                    return;
                }
            }


            // Updated validation for SINGLECHOICE and MULTICHOICE
            if (
                formData.type === 'SINGLECHOICE' &&
                (formData.correctAnswer === null ||
                    formData.correctAnswer === undefined ||
                    formData.correctAnswer === '')
            ) {
                toast({
                    title: 'Please select correct answer',
                    variant: 'destructive'
                });
                return;
            } else if (
                formData.type === 'MULTICHOICE' &&
                (
                    !formData.correctAnswer ||
                    (Array.isArray(formData.correctAnswer) && formData.correctAnswer.length === 0)
                )
            ) {
                toast({
                    title: 'Please select correct answer(s)',
                    variant: 'destructive'
                });
                return;
            };


            // Format the request body according to the reference doc
            const questionPayload = {
                questionText: formData.questionText,
                mark: formData.mark || 1,
                // For binary type, we don't need answers
                answers: formData.type === 'BINARY' ? [] : formData.answers.map(answer => ({
                    answerText: answer.answerText || '',
                    // matchText: formData.type === 'MATCH' ? answer.matchText || '' : undefined,
                    answerImageMetadata: answer.answerImageMetadata,
                    answerVideoMetadata: answer.answerVideoMetadata,
                    answerAudioMetadata: answer.answerAudioMetadata
                })),
                type: formData.type,
                correctAnswer: formatCorrectAnswer(formData.type, formData.correctAnswer, formData.answers),
                explanation: formData.explanation || '',
                level: formData.level || 1,
                active: formData.active,
                topicId: formData.topicId,
                questionImageMetadata: formData.questionImageMetadata,
                questionVideoMetadata: formData.questionVideoMetadata,
                questionAudioMetadata: formData.questionAudioMetadata,
                explanationImageMetadata: formData.explanationImageMetadata,
                explanationVideoMetadata: formData.explanationVideoMetadata,
                explanationAudioMetadata: formData.explanationAudioMetadata,
            };

            if (formData.type === 'MATCH') {
                formData.answers.forEach((answer, index) => {
                    questionPayload.answers.push({
                        answerText: answer['matchText'] ?? "",
                        answerImageMetadata: answer.answerImageMetadata,
                        answerVideoMetadata: answer.answerVideoMetadata,
                        answerAudioMetadata: answer.answerAudioMetadata
                    });
                });
            }

            if (isEditMode && id) {
                console.log('Updating question:', questionPayload);
                await updateQuestion({
                    id,
                    ...questionPayload
                }).unwrap();
                toast({
                    title: 'Question updated successfully!',
                    variant: 'default'
                });
                navigate(-1);
            } else {
                await createQuestion(questionPayload).unwrap();
                toast({
                    title: 'Question created successfully!',
                    variant: 'default'
                });
                // Clear the form after successful creation, preserving topic,
                // and set a valid default for SINGLECHOICE (correctAnswer=0).
                setFormData(prev => ({
                    anteriorId: '',
                    questionText: '',
                    mark: 0,
                    answers: [{
                        answerText: '',
                        matchText: '',
                        answerImageMetadata: null,
                        answerVideoMetadata: null,
                        answerAudioMetadata: null
                    }],
                    type: 'SINGLECHOICE',
                    correctAnswer: 0, // default valid value for SINGLECHOICE
                    explanation: '',
                    level: 1,
                    active: true,
                    topicId: prev.topicId, // preserve the current topic
                    createdBy: 'Test',
                    questionImageMetadata: null,
                    questionVideoMetadata: null,
                    questionAudioMetadata: null,
                    explanationImageMetadata: null,
                    explanationVideoMetadata: null,
                    explanationAudioMetadata: null,
                }));
            }

        } catch (error) {
            console.error('Error saving question:', error);
            toast({
                title: 'Question could not be saved. Please try again.',
                variant: 'destructive'
            });
        }
    };

    // Update formatCorrectAnswer to handle binary type
    const formatCorrectAnswer = (type: QuestionType, answer: any, answers: any[]) => {

        if ((answer === null || answer === undefined) && type !== 'MATCH' && type !== 'BINARY') return null;
        console.log(type, answer, answers);
        switch (type) {
            case 'SINGLECHOICE':
                return Number(answer);
            case 'MULTICHOICE':
                return Array.isArray(answer) ? answer.map(a => String(a)) : [];
            case 'TEXTINPUT':
                return String(answer);
            case 'BINARY':
                // Ensure we return a boolean value
                return Boolean(answer);
            case 'MATCH':
                let correctAnswer: any = {};
                answers.forEach((a, i) => {
                    correctAnswer[a['answerText']] = a['matchText'];
                })
                return correctAnswer;
            case 'SOUNDBASED':
                return String(answer);
            default:
                return null;
        }
    };

    const handleCorrectAnswerChange = useCallback((value: any) => {
        setFormData(prev => ({ ...prev, correctAnswer: value }));
    }, []);

    const updateAnswer = useCallback((index: number, field: string, value: any) => {
        setFormData(prevData => ({
            ...prevData,
            answers: prevData.answers.map((answer, i) =>
                i === index
                    ? { ...answer, [field]: value }
                    : answer
            )
        }));
    }, []);

    const handleAnswerChange = useCallback((index: number, content: string) => {
        updateAnswer(index, 'answerText', content);
    }, [updateAnswer]);


    // const handleAnswerChange = useCallback((index: number, content: string) => {
    //     updateAnswer(index, 'answerText', content);
    // }, [updateAnswer]);

    // const handleCorrectAnswerChange = useCallback((value: CorrectAnswerType[QuestionType]) => {
    //     setFormData(prev => ({ ...prev, correctAnswer: value }));
    // }, []);

    const renderCorrectAnswerInput = () => {
        if (formData.type === 'TEXTINPUT') {
            return (
                <Input
                    value={formData.correctAnswer as string || ''}
                    onChange={(e) => handleCorrectAnswerChange(e.target.value)}
                    placeholder="Enter correct answer text"
                />
            );
        } else if (formData.type === 'BINARY') {
            return (
                <div className="flex items-center space-x-2">
                    <Switch
                        checked={formData.correctAnswer as boolean ?? false}
                        onCheckedChange={(checked) => handleCorrectAnswerChange(checked)}
                    />
                    <Label>True/False</Label>
                </div>
            );
        } else if (formData.type === 'SOUNDBASED') {
            return (
                <Input
                    value={formData.correctAnswer as string || ''}
                    onChange={(e) => handleCorrectAnswerChange(e.target.value)}
                    placeholder="Enter correct answer text"
                />
            );
        }
        // For other types, correct answers are selected within the answers
        return null;
    };

    const [isInInitialQuiz, setIsInInitialQuiz] = useState(false);
    const [isInFinalQuiz, setIsInFinalQuiz] = useState(false);

    // Add mutations for special quiz questions
    const [addToSpecialQuiz] = useAddQuestionToFinalOrInitialCourseQuizMutation();
    const [removeFromSpecialQuiz] = useRemoveQuestionFromFinalOrInitialCourseQuizMutation();

    // Query to check if question is in special quizzes
    const { data: initialQuizData } = useListCourseInitailOrFinalQuizQuestionsQuery(
        { courseId: selectedCourse, isFinal: false },
        { skip: !selectedCourse }
    );
    const { data: finalQuizData } = useListCourseInitailOrFinalQuizQuestionsQuery(
        { courseId: selectedCourse, isFinal: true },
        { skip: !selectedCourse }
    );

    // Effect to check if question is in special quizzes when editing
    useEffect(() => {
        if (isEditMode && id && initialQuizData?.resultObject) {
            setIsInInitialQuiz(initialQuizData.resultObject?.questionSet?.some((q: any) => q.id === id) ?? []);
        }
    }, [id, initialQuizData, isEditMode]);

    useEffect(() => {
        if (isEditMode && id && finalQuizData?.resultObject) {
            setIsInFinalQuiz(finalQuizData.resultObject?.questionSet?.some((q: any) => q.id === id) ?? []);
        }
    }, [id, finalQuizData, isEditMode]);

    // Handler for special quiz checkboxes
    const handleSpecialQuizChange = async (type: 'initial' | 'final', checked: boolean) => {
        if (!id || !selectedCourse) return;

        try {
            if (checked) {
                await addToSpecialQuiz({
                    courseId: selectedCourse,
                    type: type === 'initial' ? 'INITIAL' : 'FINAL',
                    questionId: id
                }).unwrap();
                toast({
                    title: `Question added to ${type} quiz successfully!`,
                    variant: 'default'
                });
            } else {
                await removeFromSpecialQuiz({
                    courseId: selectedCourse,
                    type: type === 'initial' ? 'INITIAL' : 'FINAL',
                    questionId: id
                }).unwrap();
                toast({
                    title: `Question removed from ${type} quiz successfully!`,
                    variant: 'default'
                });
            }

            if (type === 'initial') {
                setIsInInitialQuiz(checked);
            } else {
                setIsInFinalQuiz(checked);
            }
        } catch (error) {
            console.error(`Error updating ${type} quiz status:`, error);
            toast({
                title: `Failed to update ${type} quiz status. Please try again.`,
                variant: 'destructive'
            });
        }
    };

    if (isEditMode && isLoadingQuestion) {
        return (
            <div className="flex justify-center items-center h-screen">
                <Loader2 className="w-8 h-8 animate-spin" />
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="sticky top-0 z-10 bg-white border-b">
                <div className="container mx-auto px-4 py-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => navigate(-1)}
                            >
                                <ArrowLeft className="h-5 w-5" />
                            </Button>
                            <h1 className="text-2xl font-bold flex items-center">
                                <FileQuestion className="h-6 w-6 mr-2" />
                                {isEditMode ? 'Edit Question' : 'Create Question'}
                            </h1>
                        </div>
                        <Button onClick={handleSubmit} className="flex items-center">
                            <Save className="h-4 w-4 mr-2" />
                            {isEditMode ? 'Update' : 'Create'}
                        </Button>
                    </div>
                </div>
            </div>

            <form onSubmit={handleSubmit} className="container mx-auto px-4 py-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        <CourseSelector
                            courses={courses}
                            selectedCourse={selectedCourse}
                            selectedChapter={selectedChapter}
                            selectedTopic={selectedTopic}
                            onCourseSelect={handleCourseSelect}
                            onChapterSelect={handleChapterSelect}
                            onTopicSelect={handleTopicSelect}
                        />

                        <Tabs defaultValue="edit" className="w-full">
                            <TabsList className="grid w-full grid-cols-2">
                                <TabsTrigger value="edit" className="flex items-center">
                                    <FileQuestion className="h-4 w-4 mr-2" />
                                    Edit
                                </TabsTrigger>
                                <TabsTrigger value="preview" className="flex items-center">
                                    <Eye className="h-4 w-4 mr-2" />
                                    Preview
                                </TabsTrigger>
                            </TabsList>

                            <TabsContent value="edit">
                                <Card>
                                    <CardContent className="p-6">
                                        <div className="flex items-center mb-4">
                                            <BookOpen className="h-5 w-5 mr-2" />
                                            <h2 className="text-xl font-semibold">Question Content</h2>
                                        </div>
                                        <div className="space-y-4">
                                            <div>
                                                <Label>Question Text</Label>
                                                <div className="mt-2">
                                                    <RichTextEditor
                                                        value={formData.questionText}
                                                        onChange={(content) =>
                                                            setFormData((prev) => ({ ...prev, questionText: content }))
                                                        }
                                                        placeholder="Enter question text..."
                                                    />
                                                </div>
                                            </div>

                                            <div>
                                                <Label>Explanation</Label>
                                                <div className="mt-2">
                                                    <RichTextEditor
                                                        value={formData.explanation}
                                                        onChange={(content) => setFormData(prev => ({ ...prev, explanation: content }))}
                                                        placeholder="Enter explanation..."
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Only show answers section for non-binary types */}
                                {formData.type !== 'BINARY' && (
                                    <Card className="mt-6">
                                        <CardContent className="p-6">
                                            <div className="flex items-center mb-4">
                                                <ListChecks className="h-5 w-5 mr-2" />
                                                <h2 className="text-xl font-semibold">Answers</h2>
                                            </div>
                                            <div className="space-y-4">

                                                {formData.type !== 'TEXTINPUT' && formData.type !== 'SOUNDBASED' && formData.answers.map((answer, index) => (
                                                    <Card key={index} className="border-dashed">
                                                        <CardContent className="p-4">
                                                            <div className="flex items-start justify-between">
                                                                <div className="flex-1 mr-4">
                                                                    <Label className="mb-2">Answer {index + 1}</Label>
                                                                    {formData.type === 'MATCH' ? (
                                                                        <Input
                                                                            value={answer.answerText}
                                                                            onChange={(e) => updateAnswer(index, 'answerText', e.target.value)}
                                                                            placeholder={`Enter answer ${index + 1}...`}
                                                                        />
                                                                    ) : (
                                                                        <RichTextEditor
                                                                            value={answer.answerText}
                                                                            onChange={(content) => handleAnswerChange(index, content)}
                                                                            placeholder={`Enter answer ${index + 1}...`}
                                                                        />
                                                                    )}
                                                                </div>
                                                                <div className="flex items-center space-x-2">
                                                                    {formData.type === 'SINGLECHOICE' && (
                                                                        <RadioGroup
                                                                            value={String(formData.correctAnswer ?? '')}
                                                                            onValueChange={(value) => handleCorrectAnswerChange(Number(value))}
                                                                            className="flex items-center space-x-2"
                                                                        >
                                                                            <RadioGroupItem value={String(index)} id={`answer-${index}`} />
                                                                            <Label htmlFor={`answer-${index}`}>Correct</Label>
                                                                        </RadioGroup>
                                                                    )}
                                                                    {formData.type === 'MULTICHOICE' && (
                                                                        <Checkbox
                                                                            checked={
                                                                                (formData.correctAnswer as string[] || []).includes(answer.answerText)
                                                                            }
                                                                            onCheckedChange={(checked) => {
                                                                                let currentAnswers = formData.correctAnswer ? formData.correctAnswer : [];
                                                                                if (checked) {
                                                                                    (currentAnswers as string[]).push(
                                                                                        answer.answerText ?? ""
                                                                                    );
                                                                                } else {
                                                                                    currentAnswers = (currentAnswers as string[]).filter(i => i !== answer.answerText);
                                                                                }
                                                                                handleCorrectAnswerChange(currentAnswers);
                                                                            }}
                                                                        />
                                                                    )}
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="icon"
                                                                        type="button"
                                                                        onClick={() => {
                                                                            setFormData(prevData => ({
                                                                                ...prevData,
                                                                                answers: prevData.answers.filter((_, i) => i !== index)
                                                                            }));
                                                                        }}
                                                                    >
                                                                        <Trash2 className="h-4 w-4" />
                                                                    </Button>
                                                                </div>
                                                            </div>

                                                            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                                                                <ResourceMetadataForm
                                                                    label="Image"
                                                                    metadata={answer.answerImageMetadata}
                                                                    onChange={(metadata) => updateAnswer(index, 'answerImageMetadata', metadata)}
                                                                />
                                                                <ResourceMetadataForm
                                                                    label="Video"
                                                                    metadata={answer.answerVideoMetadata}
                                                                    onChange={(metadata) => updateAnswer(index, 'answerVideoMetadata', metadata)}
                                                                />
                                                                <ResourceMetadataForm
                                                                    label="Audio"
                                                                    metadata={answer.answerAudioMetadata}
                                                                    onChange={(metadata) => updateAnswer(index, 'answerAudioMetadata', metadata)}
                                                                />
                                                            </div>
                                                        </CardContent>
                                                    </Card>
                                                ))}
                                                {formData.type !== 'TEXTINPUT' && formData.type !== 'SOUNDBASED' && (<Button
                                                    type="button"
                                                    variant="outline"
                                                    onClick={() => setFormData(prevData => ({
                                                        ...prevData,
                                                        answers: [...prevData.answers, {
                                                            answerText: '',
                                                            answerImageMetadata: null,
                                                            answerVideoMetadata: null,
                                                            answerAudioMetadata: null
                                                        }]
                                                    }))}
                                                    className="w-full"
                                                >
                                                    <Plus className="w-4 h-4 mr-2" />
                                                    Add Answer
                                                </Button>)}

                                                {
                                                    formData.type === 'TEXTINPUT' || formData.type === 'SOUNDBASED' ? (
                                                        <div>
                                                            <Label>Correct Answer</Label>
                                                            <div className="mt-2">
                                                                {renderCorrectAnswerInput()}
                                                            </div>
                                                        </div>
                                                    ) : null
                                                }
                                            </div>
                                        </CardContent>
                                    </Card>)}

                                {formData.type === 'MATCH' && renderMatchingPairs()}


                                {/* Show true/false switch for binary type */}
                                {formData.type === 'BINARY' && (
                                    <Card className="mt-6">
                                        <CardContent className="p-6">
                                            <div className="flex items-center mb-4">
                                                <ListChecks className="h-5 w-5 mr-2" />
                                                <h2 className="text-xl font-semibold">Correct Answer</h2>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    checked={formData.correctAnswer as boolean ?? false}
                                                    onCheckedChange={(checked) => handleCorrectAnswerChange(checked)}
                                                />
                                                <Label>{formData.correctAnswer ? 'True' : 'False'}</Label>
                                            </div>
                                        </CardContent>
                                    </Card>
                                )}

                            </TabsContent>

                            <TabsContent value="preview" className="mt-0">
                                <QuestionPreview question={formData} />
                            </TabsContent>
                        </Tabs>
                    </div>
                    {/* Sidebar */}
                    <div className="space-y-6">
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center mb-4">
                                    <BrainCircuit className="h-5 w-5 mr-2" />
                                    <h2 className="text-xl font-semibold">Settings</h2>
                                </div>
                                <div className="space-y-4">
                                    {/* Existing settings remain the same */}
                                    <div>
                                        <Label>Question Type</Label>
                                        <Select
                                            value={formData.type}
                                            onValueChange={(value: QuestionType) => {
                                                setFormData(prev => ({
                                                    ...prev,
                                                    type: value,
                                                    correctAnswer: value === 'MULTICHOICE' ? [] : '' // set default for MULTICHOICE as an empty array
                                                }));
                                            }}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="SINGLECHOICE">Single Choice</SelectItem>
                                                <SelectItem value="MULTICHOICE">Multiple Choice</SelectItem>
                                                <SelectItem value="TEXTINPUT">Text Input</SelectItem>
                                                <SelectItem value="SOUNDBASED">Sound Based</SelectItem>
                                                <SelectItem value="BINARY">Binary</SelectItem>
                                                <SelectItem value="MATCH">Match</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Label>Difficulty Level</Label>
                                        <div className="flex items-center space-x-2">
                                            <Activity className="h-4 w-4" />
                                            <Input
                                                type="number"
                                                value={formData.level}
                                                onChange={(e) => setFormData(prev => ({ ...prev, level: parseInt(e.target.value) }))}
                                                min={1}
                                                max={5}
                                            />
                                        </div>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                            <ToggleLeft className="h-4 w-4" />
                                            <Label>Active Status</Label>
                                        </div>
                                        <Switch
                                            checked={formData.active}
                                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, active: checked }))}
                                        />
                                    </div>

                                    {/* Add special quiz checkboxes */}
                                    {isEditMode && selectedCourse && (
                                        <div className="space-y-2">
                                            <div className="flex items-center space-x-2">
                                                <Checkbox
                                                    id="initialQuiz"
                                                    checked={isInInitialQuiz}
                                                    onCheckedChange={(checked) => handleSpecialQuizChange('initial', checked as boolean)}
                                                />
                                                <Label htmlFor="initialQuiz">Include in Initial Course Quiz</Label>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <Checkbox
                                                    id="finalQuiz"
                                                    checked={isInFinalQuiz}
                                                    onCheckedChange={(checked) => handleSpecialQuizChange('final', checked as boolean)}
                                                />
                                                <Label htmlFor="finalQuiz">Include in Final Course Quiz</Label>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center mb-4">
                                    <MessageSquareText className="h-5 w-5 mr-2" />
                                    <h2 className="text-xl font-semibold">Media</h2>
                                </div>
                                <div className="space-y-4">
                                    <ResourceMetadataForm
                                        label="Question Image"
                                        metadata={formData.questionImageMetadata}
                                        onChange={(metadata) => setFormData(prev => ({ ...prev, questionImageMetadata: metadata }))}
                                    />
                                    <ResourceMetadataForm
                                        label="Question Video"
                                        metadata={formData.questionVideoMetadata}
                                        onChange={(metadata) => setFormData(prev => ({ ...prev, questionVideoMetadata: metadata }))}
                                    />
                                    <ResourceMetadataForm
                                        label="Question Audio"
                                        metadata={formData.questionAudioMetadata}
                                        onChange={(metadata) => setFormData(prev => ({ ...prev, questionAudioMetadata: metadata }))}
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </form>
        </div>
    );

};

export default QuestionEditor;
