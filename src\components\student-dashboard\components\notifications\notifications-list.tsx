import React, { useEffect, useState } from 'react';
import { Bell, School, Clock, Bot, User } from 'lucide-react';
import { ScrollArea } from "@/components/ui/scroll-area";
import { useGetClassroomNotificationsQuery, useGetSchoolClassroomsQuery, useGetStudentNotificationsQuery, useLazyGetClassroomNotificationsQuery, useLazyGetSchoolClassroomsQuery } from '@/APIConnect';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAuth0 } from '@auth0/auth0-react';
import { Classroom } from '@/components/Admin/Schools/types';

interface Notification {
    id: string;
    senderId: string;
    notifyString: string;
    autoGenerated: boolean;
    classroomId?: string;
}

export function NotificationsList() {
    const { user } = useAuth0();

    const [getClassroomNotifications] = useLazyGetClassroomNotificationsQuery();
    const [getSchoolClassrooms] = useLazyGetSchoolClassroomsQuery();

    // const { data: classroomData, refetch: refetchClassroom } = useGetClassroomNotificationsQuery({

    // });
    const { data: studentData, refetch: refetchStudent } = useGetStudentNotificationsQuery({});
    const [classroomNotifications, setClassroomNotifications] = useState<Notification[]>([]);
    const [studentNotifications, setStudentNotifications] = useState<Notification[]>([]);

    useEffect(() => {

        const populateClassroomNotifications = async (schoolId: string) => {
            let classroomNotifications: Notification[] = [];
            if (user) {
                let { data: classroomsData } = await getSchoolClassrooms(schoolId);
                if (classroomsData && classroomsData['resultObject']) {
                    let classrooms = classroomsData['resultObject'] as Classroom[];
                    for (let classroom of classrooms) {
                        let { data: notificationsData } = await getClassroomNotifications({ classroomId: classroom.id });
                        if (notificationsData && notificationsData['resultObject']) {
                            let notifications = notificationsData['resultObject'] as Notification[];
                            classroomNotifications = classroomNotifications.concat(notifications);
                        }
                    }
                }
            }
        }

        if (user) {
            populateClassroomNotifications(user["http://learnido-app/schoolId"]);
        }

    }, [user]);

    useEffect(() => {
        const interval = setInterval(() => {
            // refetchClassroom();
            refetchStudent();
        }, 60000);

        return () => clearInterval(interval);
    }, [refetchStudent]);

    const NotificationItem = ({ notification, index }: { notification: Notification, index: number }) => (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="group flex items-start space-x-3 rounded-xl border bg-card p-4 shadow-sm transition-all duration-200 hover:shadow-md hover:scale-[1.02]"
        >
            <div className="mt-1">
                {notification.autoGenerated ? (
                    <Bot className="h-5 w-5 text-muted-foreground" />
                ) : (
                    <User className="h-5 w-5 text-primary" />
                )}
            </div>
            <div className="flex-1 space-y-1">
                <p className="text-sm text-card-foreground leading-snug">
                    {notification.notifyString}
                </p>
                <div className="flex items-center text-xs text-muted-foreground">
                    <Clock className="mr-1 h-3 w-3" />
                    <span>{notification.autoGenerated ? 'System Generated' : 'Manual Notification'}</span>
                </div>
            </div>
        </motion.div>
    );

    const EmptyState = ({ type }: { type: string }) => (
        <div

            className="flex flex-col items-center justify-center h-[200px] text-muted-foreground"
        >
            {type === 'classroom' ? (
                <School className="h-8 w-8 mb-2" />
            ) : (
                <Bell className="h-8 w-8 mb-2" />
            )}
            <p>No new {type} notifications</p>
        </div>
    );

    return (
        <Card className="rounded-xl shadow-lg overflow-hidden border-t-4 border-t-primary">
            <CardHeader className="bg-card pb-2">
                <div className="flex items-center space-x-2">
                    <Bell className="h-5 w-5 text-primary" />
                    <CardTitle>Announcements</CardTitle>
                </div>
            </CardHeader>
            <CardContent className="p-4">
                <Tabs defaultValue="classroom">
                    <TabsList className="grid w-full grid-cols-2 mb-4">
                        <TabsTrigger
                            value="classroom"
                            className={cn(
                                "data-[state=active]:bg-primary/10 data-[state=active]:text-primary",
                                "transition-all duration-200"
                            )}
                        >
                            <School className="h-4 w-4 mr-2" />
                            Classroom
                        </TabsTrigger>
                        <TabsTrigger
                            value="student"
                            className={cn(
                                "data-[state=active]:bg-primary/10 data-[state=active]:text-primary",
                                "transition-all duration-200"
                            )}
                        >
                            <User className="h-4 w-4 mr-2" />
                            Student
                        </TabsTrigger>
                    </TabsList>
                    <TabsContent value="classroom">
                        <ScrollArea className="h-[400px] w-full pr-4">
                            {classroomNotifications.length > 0 ? (
                                <div className="space-y-3">
                                    {classroomNotifications.map((notification, index) => (
                                        <NotificationItem
                                            key={notification.id}
                                            notification={notification}
                                            index={index}
                                        />
                                    ))}
                                </div>
                            ) : (
                                <EmptyState type="classroom" />
                            )}
                        </ScrollArea>
                    </TabsContent>
                    <TabsContent value="student">
                        <ScrollArea className="h-[400px] w-full pr-4">
                            {studentNotifications.length > 0 ? (
                                <div className="space-y-3">
                                    {studentNotifications.map((notification, index) => (
                                        <NotificationItem
                                            key={notification.id}
                                            notification={notification}
                                            index={index}
                                        />
                                    ))}
                                </div>
                            ) : (
                                <EmptyState type="student" />
                            )}
                        </ScrollArea>
                    </TabsContent>
                </Tabs>
            </CardContent>
        </Card>
    );
}
