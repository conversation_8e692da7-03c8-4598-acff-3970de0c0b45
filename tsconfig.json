{
    "compilerOptions": {
        "target": "es5",
        "lib": [
            "dom",
            "dom.iterable",
            "esnext"
        ],
        "allowJs": true,
        "skipLibCheck": true,
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        "strict": true,
        "forceConsistentCasingInFileNames": true,
        "noFallthroughCasesInSwitch": true,
        "module": "esnext",
        "moduleResolution": "node",
        "baseUrl": ".",
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "react-jsx",
        "paths": {
            "@/*": [
                "./src/*"
            ],
            "components/*": [
                "./src/components/*"
            ],
            "constant/*": [
                "./src/constant/*"
            ],
        }
    },
    "include": [
        "src",
        "**/*.ts",
        "**/*.tsx",
        "components/ui",
        "**/*.svg"
    ],
    "exclude": [
        "node_modules",
        "build"
    ]
}