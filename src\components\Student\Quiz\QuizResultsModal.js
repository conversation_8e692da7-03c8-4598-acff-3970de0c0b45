import React from "react";
import "./QuizView.css";
import wrongIcon from "./assets/wrong.svg";
import { quizSucMsg1, quizSucMsg2, quizComMsg } from "constant/AppConstant";

const QuizResultsModal = ({
  score,
  points,
  passingScore,
  onClose,
  onReviewQuiz,
  onRetryQuiz,
  msg,
}) => {
  return (
    <div className="modal-overlay">
      <div className="modal-content-quiz">
        <h2 className="modal-quiz-result">Quiz Results</h2>
        <div>
          <img src={wrongIcon} alt="" />
        </div>

        <p className="fail-message">Sorry, you didn't pass.</p>
        <div className="score-container">
          <div className="score-box">
            <p>Your Score</p>
            <h3>{points}%</h3>

            <hr className="custom-hr" />
            <p>Passing score: {passingScore}%</p>
          </div>
          <div className="score-box">
            <p>Your Points</p>
            <h3>{score}</h3>
            <hr className="custom-hr" />
            <p>Passing score: {passingScore}%</p>
          </div>
        </div>

        <div className="button-container">
          <button className="review-btn" onClick={onReviewQuiz}>
            Review Quiz
          </button>
          <button className="retry-btn" onClick={onRetryQuiz}>
            Retry Quiz
          </button>
        </div>
        {msg ? (
          <div className="next-topic-cont">
            <p className="next-topic-message">{quizSucMsg1} &nbsp;</p>
            <p
              onClick={onReviewQuiz}
              className="next-topic-message next-topic-name-message"
            >
              {msg || ""}
            </p>
            <p className="next-topic-message">&nbsp;{quizSucMsg2} </p>
          </div>
        ) : (
          <p className="fail-message">{quizComMsg} &nbsp;</p>
        )}
      </div>
    </div>
  );
};

export default QuizResultsModal;
