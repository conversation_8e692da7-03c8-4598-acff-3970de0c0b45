export interface Discussion {
  id: string;
  title: string;
  description: string;
  courseId: string;
  classId: string;
  topicId: string;
  createdBy: string;
  createdAt: string;
  commentCount: number;
  voteCount: number;
  courseName?: string;
  topicName?: string;
  tags?: string[];
}

export interface Comment {
  id: string;
  content: string;
  discussionId: string;
  authorId: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  aiScore: number;
  sentimentScore: number;
  accuracyScore: number;
  toneAnalysis: {
    curious: number;
    critical: number;
    analytical: number;
  };
  language: string;
  flaggedTerms: string[];
  createdAt: string;
  voteCount: number;
  authorName?: string;
  authorPictureUrl?: string;
  evaluation?: {
    engagement?: number;
    relevance?: number;
    depthOfThought?: number;
    evidence?: number;
  }
}

export interface LeaderboardEntry {
  userId: string;
  timeRange: string;
  score: number;
  rank: number | null;
  updatedAt: string;
}

export interface PaginationResponse<T> {
  items: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface ModerationStats {
  counts: {
    approved: number;
    pending: number;
    rejected: number;
    total: number;
  };
  averages: {
    aiScore: number;
    sentimentScore: number;
    accuracyScore: number;
  };
  dateRange: {
    start: string | null;
    end: string | null;
  };
}

export type TimeRange = 'daily' | 'weekly' | 'monthly' | 'alltime';

export interface Subtopic {
  id: string;
  topicId: string;
  courseId: string;
  topicName: string;
  subTopicName: string;
  topicWeight: number;
  type: string;
  content: string;
  contentUrl: string;
  assignment?: {
    type: 1 | 2; // 1 = Submission type, 2 = quiz type
    title: string;
    assignmentText: string;
    fileUrl: string;
    quizId?: string;
  };
  createdOn: string;
  updateOn: string;
}

export interface Topic {
  id: string;
  name: string;
  description: string;
  subtopics: Subtopic[];
  // ...existing topic fields...
}

export interface Chapter {
  id: string;
  name: string;
  description: string;
  topics: Topic[];
  // ...existing chapter fields...
}