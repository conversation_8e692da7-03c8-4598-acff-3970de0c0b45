import {
    CourseDetails,
    Module,
    Lesson,
    SubItem,
    PerformanceData,
    InstructorDetails,
    ApiResultObject, // Renamed import for clarity
    ApiChapter,      // Renamed import for clarity
    ApiTopic,        // Renamed import for clarity
    ApiSubTopic      // Renamed import for clarity
} from 'src/types/courseTypes'; // Assuming the types above are in this file

// Import the raw API response structure if needed for typing fetchData
// import { ApiResponse } from '../types'; // From student-dashboard/types - Uncomment if needed

// Base URL (ensure this is correct)
const BASE_URL = "https://nexo-dev-fbf0cvc7bhehbuft.australiacentral-01.azurewebsites.net/";

// --- Helper Functions ---
// This function should be called with a token from the component that uses this service
let _cachedToken: string | null = null;

export const setAuthToken = (token: string | null): void => {
  _cachedToken = token;
};

export const getAuthToken = (): string | null => {
  return _cachedToken;
};

// Use `extends unknown` to clarify generic syntax in TSX
const fetchData = async <T extends unknown>(endpoint: string, options: RequestInit = {}): Promise<T> => {
  const token = getAuthToken();
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
    ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
  };

  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, { ...options, headers });

    if (!response.ok) {
      let errorData: any;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { message: response.statusText };
      }
      console.error(`API Error (${response.status}) fetching ${endpoint}:`, errorData);
      throw new Error(`API Error (${response.status}): ${errorData?.message || 'Failed to fetch data'}`);
    }
    if (response.status === 204) return null as T; // Handle No Content
    // Expecting the structure { resultObject: ..., isError: ..., errors: ... }
    const apiResponse: { resultObject: T } = await response.json(); // Assume resultObject holds the data
    return apiResponse.resultObject;

  } catch (error) {
    console.error(`Network or Fetch Error for ${endpoint}:`, error);
    throw error;
  }
};


/**
 * CourseDetailService provides data and API interactions for the course detail page using fetch.
 */
class CourseDetailService {
  /**
   * Fetch course details by ID, including its structure.
   * Uses the GET /Courses/{courseId} endpoint.
   * @param courseId - The ID of the course to fetch
   * @param fetchSubtopicsEagerly - Whether to fetch subtopics for all topics immediately
   * @returns Promise with structured CourseDetails for the UI.
   */
  async getCourseDetails(courseId: string, fetchSubtopicsEagerly: boolean = true): Promise<CourseDetails> {
    if (!courseId) throw new Error("Course ID is required.");
    try {
      // API Call: GET /Courses/{courseId} -> Expects ApiResultObject within the response
      const apiCourseData = await fetchData<ApiResultObject>(`Courses/${courseId}`);

      if (!apiCourseData) {
        throw new Error(`Course data not found for ID: ${courseId}`);
      }

      // First transform data with initial subtopics
      const modules = this.transformApiChaptersToModules(apiCourseData.chapters || []);
      
      // If eager loading is enabled, fetch all subtopics for all topics
      if (fetchSubtopicsEagerly && apiCourseData.chapters) {
        await this.fetchAllSubtopicsEagerly(modules);
      }
      
      // Calculate stats after potentially fetching all subtopics
      const { completedLessons, totalLessons, assessmentsCount, totalDurationMinutes } = this.calculateCourseStats(modules);
      const completionPercentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;

      // TODO: Calculate timeRemaining based on totalDurationMinutes and progress
      const timeRemaining = this.formatDuration(totalDurationMinutes * (1 - completionPercentage / 100));
      const totalDuration = this.formatDuration(totalDurationMinutes);

      const transformedDetails: CourseDetails = {
        id: apiCourseData.id,
        title: apiCourseData.name || 'Untitled Course',
        instructor: apiCourseData.instructorName || 'N/A', // Use instructorName directly
        modules: modules,
        completedLessons: completedLessons,
        totalLessons: totalLessons,
        completionPercentage: completionPercentage,
        timeRemaining: timeRemaining, // Needs calculation
        duration: totalDuration, // Needs calculation
        topicsCount: totalLessons, // Assuming topicsCount is the same as totalLessons
        assessmentsCount: assessmentsCount,
      };
      return transformedDetails;

    } catch (error) {
      console.error(`Error in getCourseDetails for courseId ${courseId}:`, error);
      throw error;
    }
  }
  
  /**
   * Fetches subtopics for all topics in the modules array
   * @param modules - The course modules with lessons
   */
  private async fetchAllSubtopicsEagerly(modules: Module[]): Promise<void> {
    console.log('Eagerly fetching subtopics for all topics...');
    const fetchPromises: Promise<void>[] = [];
    
    // Create a promise for each topic to fetch its subtopics
    for (const module of modules) {
      for (const lesson of module.lessons) {
        const promise = (async () => {
          try {
            const subtopics = await this.getSubtopics(lesson.id, lesson.recommendedTime);
            // Update the lesson's subtopics with the fetched data
            if (subtopics && subtopics.length > 0) {
              lesson.subItems = subtopics;
            }
          } catch (error) {
            console.error(`Error fetching subtopics for topic ${lesson.id}:`, error);
            // Don't throw here - we want to continue with other topics if one fails
          }
        })();
        fetchPromises.push(promise);
      }
    }
    
    // Wait for all fetches to complete (concurrently)
    await Promise.all(fetchPromises);
    console.log('Finished eagerly fetching all subtopics');
  }

  // --- Transformation Helpers ---

  private transformApiChaptersToModules(apiChapters: ApiChapter[]): Module[] {
    const sortedChapters = this.sortItemsByAnteriorId(apiChapters);

    return sortedChapters.map((chapter): Module => {
        const lessons = this.transformApiTopicsToLessons(chapter.topics || []);
        // Add explicit types for reduce parameters
        const moduleDurationMinutes = lessons.reduce((sum: number, lesson: Lesson) => sum + this.calculateLessonDurationMinutes(lesson), 0);
        return {
            id: chapter.id,
            title: chapter.name || 'Untitled Chapter',
            duration: this.formatDuration(moduleDurationMinutes), // Calculate from lessons
            lessons: lessons,
            isCompleted: chapter.completed || false,
        };
    });
  }

  private transformApiTopicsToLessons(apiTopics: ApiTopic[]): Lesson[] {
    const sortedTopics = this.sortItemsByAnteriorId(apiTopics);

    return sortedTopics.map((topic, index): Lesson => {
        // Instead of using pre-loaded subTopics, we will fetch them via getSubtopics
        // We still transform what's available initially to avoid a blank screen
        const initialSubItems = this.transformApiSubTopicsToSubItems(topic.subTopics || []);
        return {
            id: topic.id,
            title: `${String(index + 1).padStart(2, '0')}: ${topic.name || 'Untitled Topic'}`,
            // TODO: Implement logic to determine isActive based on current navigation/progress state
            isActive: false, // Placeholder: This needs external state or logic
            isCompleted: topic.completed || false,
            subItems: initialSubItems,
            recommendedTime: topic.recommendedTime, // Assuming this is in minutes
        };
    });
  }

  /**
   * Fetch subtopics for a specific topic using the CoursesContent/topic/{topicId} endpoint
   * @param topicId - The ID of the topic to fetch subtopics for
   * @returns Promise with transformed SubItem[] array
   */
  async getSubtopics(topicId: string,recommendedTime?:number): Promise<SubItem[]> {
    const expectedCompletionTime = recommendedTime || 0;
    if (!topicId) {
      console.warn("Topic ID is required to fetch subtopics");
      return [];
    }

    console.log(`🔍 getSubtopics called for topic: ${topicId}`);

    try {
      // Based on the API example: https://nexo-dev-fbf0cvc7bhehbuft.australiacentral-01.azurewebsites.net/CoursesContent/topic/{topicId}
      const apiSubtopicsData = await fetchData<any>(`CoursesContent/topic/${topicId}`);

      if (!apiSubtopicsData || !apiSubtopicsData|| !Array.isArray(apiSubtopicsData)) {
        console.warn(`No subtopics found or invalid format for topic ID: ${topicId}`, apiSubtopicsData);
        return [];
      }

      console.log(`📊 API returned ${apiSubtopicsData.length} subtopics for topic ${topicId}:`, apiSubtopicsData.map(s => ({ id: s.id, name: s.subTopicName })));

      // Transform the API subtopics to our SubItem interface
      // Example response structure from API:
      // "resultObject": [
      //   {
      //     "subTopicName": "Understanding OOPs",
      //     "type": 1,
      //     "id": "680f4571235357b6c076dd33",
      //     "createdOn": "2025-04-28T09:08:01.9455952+00:00",
      //     "updatedon": "2025-04-28T09:08:01.9456374+00:00"
      //   }
      // ]
      const subtopics: SubItem[] = [];

      await Promise.all(
        apiSubtopicsData.map(async (subtopic: any) => {
          try {
            // Fetch detailed content for each subtopic
            const detail = await fetchData<any>(`CoursesContent/${subtopic.id}`);
            const detailData = detail?.resultObject || detail;

            if (!detailData) {
              console.warn(`No detail data found for subtopic ${subtopic.id}`);
              return;
            }

            const hasContent = detailData.content && detailData.content.trim() !== '';
            const hasContentUrl = detailData.contentUrl && detailData.contentUrl.trim() !== '';
            const assignment = detailData.assignment;
            const quizId = detailData.quizId || (assignment?.quizId);
            const hasQuiz = quizId && quizId.trim() !== '';

            // Check for document content (PDF, DOC, etc.) - URL comes from Content field
            const documentUrl = detailData.Content || detailData.content;

            // More strict document detection - must have a valid document URL with proper extension
            // or be type 3 WITH a valid URL (not just type 3 alone)
            const hasValidDocumentUrl = documentUrl && documentUrl.trim() !== '' &&
              (documentUrl.toLowerCase().endsWith('.pdf') ||
               documentUrl.toLowerCase().endsWith('.doc') ||
               documentUrl.toLowerCase().endsWith('.docx') ||
               documentUrl.toLowerCase().includes('.pdf?') ||
               documentUrl.toLowerCase().includes('.doc?') ||
               documentUrl.toLowerCase().includes('.docx?'));

            // Only consider it a document if it has a valid document URL
            // or if it's type 5 (document type) AND has some kind of URL (not just empty content)
            const hasDocument = hasValidDocumentUrl ||
              (subtopic.type === 5 && documentUrl && documentUrl.trim() !== '');

            console.log(`Processing subtopic ${subtopic.id}:`, {
              hasContent,
              hasContentUrl,
              hasDocument,
              hasValidDocumentUrl,
              documentUrl,
              documentUrlLength: documentUrl ? documentUrl.length : 0,
              hasAssignment: !!assignment,
              hasQuiz,
              title: subtopic.subTopicName,
              type: subtopic.type,
              detailDataContent: detailData.Content,
              detailDataContentLower: detailData.content
            });

            // Handle multiple content types - create separate entries for each type present
            const contentTypes = [];

            // Check what content types are present
            if (hasContent) contentTypes.push('content');
            if (hasDocument) contentTypes.push('document');
            if (hasQuiz) contentTypes.push('quiz');

            if (contentTypes.length > 1) {
              console.log(`Creating separate entries for ${subtopic.id} - types: ${contentTypes.join(', ')}`);

              // Add HTML content entry if present
              if (hasContent) {
                subtopics.push({
                  id: subtopic.id,
                  title: subtopic.subTopicName || 'Untitled Sub-Topic',
                  type: 'text', // HTML content is text type
                  duration: `${expectedCompletionTime / apiSubtopicsData.length || 'N/A'}`,
                  isCompleted: false,
                  contentUrl: hasContentUrl ? detailData.contentUrl : null,
                  content: detailData.content,
                  assignment: null,
                  quizId: null
                });
              }

              // Add document entry if present
              if (hasDocument) {
                console.log(`Creating document entry for ${subtopic.id} with URL: ${documentUrl}`);
                subtopics.push({
                  id: `doc_${subtopic.id}`,
                  title: subtopic.subTopicName || 'Untitled Sub-Topic',
                  type: 'document',
                  duration: `${expectedCompletionTime / apiSubtopicsData.length || 'N/A'}`,
                  isCompleted: false,
                  contentUrl: documentUrl,
                  content: null,
                  assignment: null,
                  quizId: null
                });
              }

              // Add quiz entry if present
              if (hasQuiz) {
                subtopics.push({
                  id: `quiz_${subtopic.id}`,
                  title: subtopic.subTopicName || 'Untitled',
                  type: 'quiz',
                  duration: `${expectedCompletionTime / apiSubtopicsData.length || 'N/A'}`,
                  isCompleted: false,
                  contentUrl: null,
                  content: null,
                  assignment: assignment ? {
                    type: assignment.type,
                    tags: assignment.tags || [],
                    title: assignment.title || '',
                    assignmentText: assignment.assignmentText || '',
                    fileUrl: assignment.fileUrl || '',
                    quizId: assignment.quizId || null
                  } : null,
                  quizId: quizId
                });
              }
            } else {
              // Single content type - create one entry
              let type = this.mapContentTypeFromNumber(subtopic.type);
              let contentUrl = null;
              let content = null;

              if (hasQuiz) {
                type = 'quiz';
              } else if (hasDocument) {
                type = 'document';
                contentUrl = documentUrl;
                console.log(`Single document entry for ${subtopic.id} with URL: ${documentUrl}`);
              } else if (hasContentUrl) {
                const url = detailData.contentUrl.toLowerCase();
                if (url.includes('.mp4') || url.includes('.avi') || url.includes('.mov') || url.includes('video')) {
                  type = 'video';
                } else if (url.includes('.mp3') || url.includes('.wav') || url.includes('audio')) {
                  type = 'audio';
                } else if (url.includes('scorm') || url.includes('.zip')) {
                  type = 'scorm';
                }
                contentUrl = detailData.contentUrl;
              } else if (hasContent) {
                type = 'text';
                content = detailData.content;
              }

              subtopics.push({
                id: subtopic.id,
                title: subtopic.subTopicName || 'Untitled Sub-Topic',
                type,
                duration: `${expectedCompletionTime / apiSubtopicsData.length || 'N/A'}`,
                isCompleted: false,
                contentUrl: contentUrl,
                content: content,
                assignment: assignment ? {
                  type: assignment.type,
                  tags: assignment.tags || [],
                  title: assignment.title || '',
                  assignmentText: assignment.assignmentText || '',
                  fileUrl: assignment.fileUrl || '',
                  quizId: assignment.quizId || null
                } : null,
                quizId: quizId || null
              });
            }
          } catch (error) {
            console.error(`Error processing subtopic ${subtopic.id}:`, error);
            // Add a basic entry if there's an error
            subtopics.push({
              id: subtopic.id,
              title: subtopic.subTopicName || 'Untitled Sub-Topic',
              type: this.mapContentTypeFromNumber(subtopic.type),
              duration: `${expectedCompletionTime / apiSubtopicsData.length || 'N/A'}`,
              isCompleted: false,
              contentUrl: null,
              content: null,
              assignment: null,
              quizId: null
            });
          }
        })
      );
      
      // Remove duplicates based on ID (in case there are any)
      const uniqueSubtopics = subtopics.filter((item, index, self) =>
        index === self.findIndex(t => t.id === item.id)
      );

      console.log(`✅ Final result for topic ${topicId}:`);
      console.log(`   - API subtopics: ${apiSubtopicsData.length}`);
      console.log(`   - Generated entries: ${subtopics.length}`);
      console.log(`   - After deduplication: ${uniqueSubtopics.length}`);
      console.log(`   - Final entries:`, uniqueSubtopics.map(item => ({
        id: item.id,
        title: item.title,
        type: item.type,
        hasAssignment: !!item.assignment,
        hasQuiz: !!item.quizId
      })));

      return uniqueSubtopics;
    } catch (error) {
      console.error(`Error fetching subtopics for topic ${topicId}:`, error);
      return []; // Return empty array on error
    }
  }

  /**
   * Fetch content for a specific subtopic
   * @param subTopicId - The ID of the subtopic to fetch content for
   * @returns Promise with the content data or null
   */
  async getSubtopicContent(subTopicId: string): Promise<any> {
    if (!subTopicId) {
      console.warn("SubTopic ID is required to fetch content");
      return null;
    }

    try {
      // Based on the example: https://nexo-dev-fbf0cvc7bhehbuft.australiacentral-01.azurewebsites.net/CoursesContent/{subTopicId}
      const result = await fetchData<any>(`CoursesContent/${subTopicId}`);
      
      if (result?.resultObject) {
        // Transform the API response to a more frontend-friendly format if needed
        const content = result.resultObject;
        
        // Enhance the SubItem with complete content details
        const enhancedSubItem: SubItem = {
          id: content.id,
          title: content.subTopicName || 'Untitled',
          type: this.mapContentTypeFromNumber(content.type),
          duration: this.estimateDuration(this.mapContentTypeFromNumber(content.type)),
          isCompleted: false, // This might come from elsewhere in your app
          content: content.content,
          contentUrl: content.contentUrl
        };
        
        return enhancedSubItem;
      }
      
      console.warn(`No content found for subTopic ID: ${subTopicId}`);
      return null;
    } catch (error) {
      console.error(`Failed to fetch subtopic content for ${subTopicId}:`, error);
      return null;
    }
  }

  private transformApiSubTopicsToSubItems(apiSubTopics: ApiSubTopic[]): SubItem[] {
     // SubTopics might not have anteriorId, sort by order/weight if available, or keep API order
     const sortedSubTopics = apiSubTopics.sort((a, b) => (a.weight || 0) - (b.weight || 0));

     return sortedSubTopics.map((subTopic): SubItem => ({
       id: subTopic.id,
       title: subTopic.subTopicName || 'Untitled Sub-Topic',
       type: this.mapApiContentType(subTopic.contentType),
       // TODO: Duration needs to be derived/estimated if not directly provided
       duration: this.estimateDuration(this.mapApiContentType(subTopic.contentType)), // Use mapped type for estimation
       isCompleted: subTopic.completed || false,
       contentUrl: subTopic.contentUrl || null,
       content: subTopic.content || null,
     }));
  }

  private mapApiContentType(apiType: string | undefined | null): SubItem['type'] {
      const type = (apiType || '').toLowerCase().trim();
      // Match based on the defined types in ApiSubTopic['contentType']
      if (type === 'text') return 'text';
      if (type === 'video') return 'video';
      if (type === 'audio') return 'audio'; // Assuming 'audio' is a possible type
      if (type === 'scorm') return 'scorm';
      if (type === 'html') return 'html'; // Assuming 'html' is a possible type
      if (type === 'quiz') return 'quiz'; // Map API 'quiz' to frontend 'quiz' or 'assessment'
      if (type === 'assessment') return 'assessment'; // Explicitly map assessment
      if (type.includes('doc') || type.includes('pdf')) return 'document'; // Map document types

      console.warn(`Unknown API content type encountered: ${apiType}`);
      return 'unknown';
  }

  /**
   * Maps numeric content type (from API) to string content type (for frontend)
   * Based on the enum: html=1, audio=2, video=3, scorm=4, document=5, livelecture=6
   * @param typeNum - The numeric content type from API
   * @returns The string content type for frontend
   */
  private mapContentTypeFromNumber(typeNum: number): SubItem['type'] {
    // Implement mapping based on API documentation enum
    switch(typeNum) {
      case 1: return 'html'; // html = 1
      case 2: return 'audio'; // audio = 2
      case 3: return 'video'; // video = 3
      case 4: return 'scorm'; // scorm = 4
      case 5: return 'document'; // document = 5
      case 6: return 'text'; // livelecture = 6 (mapping to text for now)
      case 7: return 'assessment';
      case 8: return 'quiz';
      default:
        console.warn(`Unknown content type number: ${typeNum}`);
        return 'unknown';
    }
  }

  // Placeholder for estimating duration based on type
  private estimateDuration(type: SubItem['type']): string {
      switch(type) {
          case 'video': return '5 min';
          case 'audio': return '3 min';
          case 'scorm': return '15 min';
          case 'quiz': return '2 min';
          case 'assessment': return '10 min';
          case 'document': return '8 min';
          case 'text': return '4 min';
          case 'html': return '6 min';
          default: return 'N/A';
      }
  }

  // Placeholder for parsing duration string "X min" to minutes number
  private parseDurationMinutes(durationStr: string): number {
      if (!durationStr || typeof durationStr !== 'string') return 0;
      const match = durationStr.match(/(\d+)\s*min/);
      return match ? parseInt(match[1], 10) : 0;
  }

  // Calculate total duration for a lesson based on its subitems
  private calculateLessonDurationMinutes(lesson: Lesson): number {
      // Add explicit types for reduce parameters
      return lesson.subItems.reduce((sum: number, item: SubItem) => sum + this.parseDurationMinutes(item.duration), 0);
  }

  // Format total minutes into a display string (e.g., "1h 30m", "45 min")
   private formatDuration(totalMinutes: number): string {
        if (totalMinutes <= 0) return '';
        const hours = Math.floor(totalMinutes / 60);
        const minutes = Math.round(totalMinutes % 60); // Round minutes for display
        let durationString = '';
        if (hours > 0) {
            durationString += `${hours} hr${hours > 1 ? 's' : ''}`;
        }
        if (minutes > 0) {
            if (durationString) durationString += ' ';
            durationString += `${minutes} min`;
        }
        return durationString || 'N/A'; // Fallback if somehow both are 0
    }


  // Calculate overall course stats from transformed modules
  private calculateCourseStats(modules: Module[]): { completedLessons: number; totalLessons: number; assessmentsCount: number; totalDurationMinutes: number } {
      let completedLessons = 0;
      let totalLessons = 0;
      let assessmentsCount = 0;
      let totalDurationMinutes = 0;

      modules.forEach((module: Module) => { // Add explicit type
          module.lessons.forEach((lesson: Lesson) => { // Add explicit type
              totalLessons++;
              if (lesson.isCompleted) {
                  completedLessons++;
              }
              totalDurationMinutes += this.calculateLessonDurationMinutes(lesson);
              lesson.subItems.forEach((item: SubItem) => { // Add explicit type
                  // Count both quiz and assessment types towards assessmentsCount
                  // Also count items that have assignments (regardless of content type)
                  if (item.type === 'quiz' || item.type === 'assessment' || item.assignment || item.quizId) {
                      assessmentsCount++;
                  }
              });
          });
      });

      return { completedLessons, totalLessons, assessmentsCount, totalDurationMinutes };
  }


  /**
   * Sorts items (chapters or topics) based on the anteriorId linking logic.
   * @param items - Array of items (ApiChapter or ApiTopic).
   * @returns Sorted array of items.
   */
  // Use `extends { id: string; anteriorId: string }` constraint
  private sortItemsByAnteriorId<T extends { id: string; anteriorId: string }>(items: T[]): T[] {
      if (!items || items.length <= 1) return items;

      const itemMap = new Map(items.map(item => [item.id, item]));
      const sortedSequence: T[] = [];
      const itemIds = new Set(items.map(item => item.id));

      // Find potential root items (anteriorId is not in the set of item IDs, or points to itself)
      let currentItem: T | undefined = items.find(item => !itemIds.has(item.anteriorId) || item.anteriorId === item.id);

      if (!currentItem && items.length > 0) {
          console.warn("Root item not clearly identified. Starting sort from the first item in the list.");
          currentItem = items[0];
      }

      const visited = new Set<string>();
      while (currentItem && sortedSequence.length < items.length) {
          const currentItemId = currentItem.id; // Store ID before potentially losing currentItem
          if (visited.has(currentItemId)) {
              console.error("Circular dependency detected in sorting by anteriorId. Aborting sort.", currentItem);
              return items; // Return original order
          }
          visited.add(currentItemId);
          sortedSequence.push(currentItem);

          // Find the next item whose anteriorId matches the current item's id
          // Add check for currentItem being defined before accessing its id
          const nextItem = items.find(item => item.anteriorId === currentItemId && item.id !== currentItemId);
          // Assign T | undefined to T | undefined (no error)
          currentItem = nextItem ? itemMap.get(nextItem.id) : undefined;
      }

      // Add any remaining items not captured (e.g., if the chain breaks)
      if (sortedSequence.length < items.length) {
          console.warn(`Sorting by anteriorId might be incomplete (${sortedSequence.length}/${items.length}). Adding remaining items.`);
          const remainingItems = items.filter(item => !visited.has(item.id));
          sortedSequence.push(...remainingItems);
      }

      return sortedSequence;
  }

  /**
   * Fetch course modules and lessons - This is now handled within getCourseDetails
   * Kept for potential future separation or compatibility, but returns data from getCourseDetails.
   * @param courseId - The ID of the course
   * @returns Promise with course modules
   * @deprecated Use getCourseDetails().modules instead.
   */
  async getCourseModules(courseId: string): Promise<Module[]> {
     console.warn("getCourseModules is deprecated. Use getCourseDetails().modules instead.");
     try {
        const details = await this.getCourseDetails(courseId);
        return details.modules;
     } catch (error) {
        console.error('Error fetching course modules via getCourseDetails:', error);
        throw error;
     }
  }

  /**
   * Fetch student performance data.
   * Uses GET /Analytic/student/{studentId}/classroom/{classroomId}.
   * @param studentId - The ID of the student.
   * @param classroomId - The ID of the classroom associated with the course enrollment.
   * @returns Promise with PerformanceData array.
   */
  async getPerformanceData(studentId: string, classroomId: string): Promise<PerformanceData[]> {
    if (!studentId || !classroomId) {
        console.warn("Student ID and Classroom ID are required for performance data.");
        return this.getMockPerformanceData(); // Fallback
    }
    try {
      // API Call: GET /Analytic/student/{studentId}/classroom/{classroomId}
      // Assuming the response structure needs mapping
      // Use `extends unknown`
      const analyticsData = await fetchData<any>(`Analytic/student/${studentId}/classroom/${classroomId}`); // Use 'any' or define a specific API type

      // --- Data Transformation ---
      // TODO: Adapt this mapping based on the actual structure of analyticsData
      if (analyticsData && Array.isArray(analyticsData.skills)) { // Example structure
          return analyticsData.skills.map((skill: any): PerformanceData => ({
              label: skill.name || 'Unknown Skill',
              value: skill.score || 0,
              color: this.mapPerformanceColor(skill.category || skill.name || ''), // Use category or name for color mapping
          }));
      } else {
          console.warn("Performance data structure not as expected. Returning mock data.", analyticsData);
          return this.getMockPerformanceData();
      }

    } catch (error) {
      console.error(`Error fetching performance data for student ${studentId} in classroom ${classroomId}:`, error);
      return this.getMockPerformanceData(); // Fallback on error
    }
  }

  private mapPerformanceColor(categoryOrLabel: string): string {
      // Keep this mapping consistent with UI needs
      const lowerCaseKey = categoryOrLabel.toLowerCase();
      const colorMap: { [key: string]: string } = {
          'critical thinking & problem solving': 'bg-[#9F9FF8]',
          'presentation and communication': 'bg-[#96E2D6]',
          'collaboration and team work': 'bg-black',
          'subject knowledge': 'bg-[#92BFFF]',
          'strategic thinking': 'bg-[#AEC7ED]',
          'innovation': 'bg-[#94E9B8]',
          'ethical decision making': 'bg-[#9F9FF8]', // Reusing color
          'sustainability quotient': 'bg-[#96E2D6]', // Reusing color
          'experiential, social, cultural, awareness': 'bg-black', // Reusing color
          'emotional intel. & adaptability': 'bg-[#92BFFF]', // Reusing color
      };
      // Attempt direct match first, then check if the key includes parts of the label
      if (colorMap[lowerCaseKey]) {
          return colorMap[lowerCaseKey];
      }
      // Fallback checks (optional, adjust as needed)
      if (lowerCaseKey.includes('critical thinking')) return 'bg-[#9F9FF8]';
      if (lowerCaseKey.includes('communication')) return 'bg-[#96E2D6]';
      if (lowerCaseKey.includes('collaboration')) return 'bg-black';
      // ... add more fallback checks if needed

      return 'bg-gray-500'; // Default color
  }

  private getMockPerformanceData(): PerformanceData[] {
      // Keep mock data consistent with the UI example
      return [
        { label: "Critical Thinking & Problem Solving", value: 50, color: "bg-[#9F9FF8]" },
        { label: "Presentation and Communication", value: 88, color: "bg-[#96E2D6]" },
        { label: "Collaboration and Team work", value: 62, color: "bg-black" },
        { label: "Subject Knowledge", value: 100, color: "bg-[#92BFFF]" },
        { label: "Strategic Thinking", value: 38, color: "bg-[#AEC7ED]" },
        { label: "Innovation", value: 75, color: "bg-[#94E9B8]" },
        { label: "Ethical Decision making", value: 75, color: "bg-[#9F9FF8]" },
        { label: "Sustainability Quotient", value: 75, color: "bg-[#96E2D6]" },
        { label: "Experiential, Social, Cultural, Awareness", value: 75, color: "bg-black" },
        { label: "Emotional Intel. & Adaptability", value: 75, color: "bg-[#92BFFF]" },
      ];
  }

  /**
   * Update completion progress for a sub-topic.
   * Uses PUT /Students/{studentId}/progress (Hypothetical - Verify Endpoint).
   * @param studentId - The ID of the student.
   * @param courseId - The ID of the course.
   * @param subTopicId - The ID of the sub-topic being completed.
   * @param isCompleted - Whether the item is completed.
   * @returns Promise indicating success.
   */
  async updateSubTopicProgress(studentId: string, courseId: string, subTopicId: string, isCompleted: boolean): Promise<{ success: boolean }> {
     if (!studentId || !courseId || !subTopicId) {
         throw new Error("Missing required parameters for updating progress.");
     }
     try {
       // Hypothetical Endpoint & Payload - VERIFY THIS
       const payload = {
         courseId,
         itemId: subTopicId,
         itemType: 'SubTopic', // API might expect 'SubTopic', 'Topic', 'Chapter' etc.
         isCompleted,
         completionDate: new Date().toISOString(),
       };

       // Assuming PUT updates the status
       // The response might not have a body or just confirmation, adjust <any> if needed
       // Use `extends unknown`
       await fetchData<any>(`Students/${studentId}/progress`, { // Adjust endpoint
         method: 'PUT', // Or POST
         body: JSON.stringify(payload),
       });

       console.log(`API: Progress updated for SubTopic ${subTopicId} in course ${courseId}: ${isCompleted}`);
       return { success: true };

     } catch (error) {
       console.error(`Error updating progress via API for SubTopic ${subTopicId}:`, error);
       throw error;
     }
  }

  /**
   * Fetch instructor details.
   * Attempts to use instructorId from course data to fetch user details.
   * Uses GET /Courses/{courseId} and potentially GET /Users/<USER>
   * @param courseId - The ID of the course.
   * @returns Promise with InstructorDetails or null.
   */
  async getInstructorDetails(classroomId: string): Promise<InstructorDetails | null> {
    try {
      // Fetch course data again or reuse if already available
      // Use `extends unknown`
      const dashboardapiresponse = await fetch(`${BASE_URL}Students/courseDashboard/${classroomId}`,{
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`,
        }
      });
      const apiCourseData = await dashboardapiresponse.json();
      const instructorName = apiCourseData?.instructorProfiles?.[0]?.name || apiCourseData?.instructorName || 'N/A'; // Fallback to instructorName if available
      // Assuming the API might provide an instructorId within the course object
      const instructorId = apiCourseData?.instructorProfiles?.[0]?.instructorId; // Use 'any' or add instructorId to ApiResultObject type if it exists

      // if (instructorId) {
      //     // API Call: GET /Users/<USER>
      //     // Define an ApiUser type if possible
      //     // Use `extends unknown`
      //     const apiUserData = await fetchData<any>(`Users/${instructorId}`); // Use 'any' or define ApiUser type
      //     if (apiUserData) {
      //         return {
      //             id: apiUserData.id,
      //             name: `${apiUserData.firstName || ''} ${apiUserData.lastName || ''}`.trim() || instructorName || 'N/A',
      //             avatar: apiUserData.profilePictureUrl || undefined, // Use undefined if not present
      //             role: apiUserData.role || 'Instructor', // Assuming role info is here
      //             bio: apiUserData.bio || undefined,
      //             email: apiUserData.email || undefined,
      //             phoneNumber: apiUserData.phoneNumber || undefined,
      //         };
      //     }
      // }

      // Fallback if only name is available or instructorId fetch fails
      if (instructorName) {
           return {
               id: `${instructorId}`, // Generate a placeholder ID
               name: instructorName,
               role: 'Instructor',
               // Provide empty strings or undefined for other fields if only name is known
               avatar: apiCourseData?.instructorProfiles?.[0]?.coverImage || undefined,
               bio: apiCourseData?.instructorProfiles?.[0]?.bio || undefined,
               email: undefined,
               phoneNumber: undefined,
           };
      }

      console.warn(`Instructor details not found for course ${classroomId}.`);
      return null;

    } catch (error) {
      console.error(`Error fetching instructor details for course ${classroomId}:`, error);
       return null; // Return null on error
    }
  }

  // --- Client-Side Helpers (No API Calls) ---

  /**
   * Finds the first incomplete item (sub-topic) in the course structure for resuming.
   * @param modules - The course modules structure (fetched previously).
   * @returns The next item details { moduleId, lessonId, itemId, title, type } or null if complete.
   */
  findNextItemToResume(modules: Module[]): { moduleId: string; lessonId: string; itemId: string; title: string; type: SubItem['type'] } | null {
      if (!modules) return null;
      for (const module of modules) {
          for (const lesson of module.lessons) {
              if (lesson.subItems && lesson.subItems.length > 0) {
                  for (const item of lesson.subItems) {
                      if (!item.isCompleted) {
                          return {
                              moduleId: module.id,
                              lessonId: lesson.id,
                              itemId: item.id, // SubItem ID
                              title: item.title,
                              type: item.type,
                          };
                      }
                  }
              }
              // If you need to check lesson completion itself (if applicable)
              // Note: Current logic assumes completion is tracked at the SubItem level primarily.
              // if (!lesson.isCompleted) { ... return lesson level info ... }
          }
      }
      return null; // All items completed
  }

   /**
   * Determines the list of tabs to display based on available data.
   * This is a client-side helper.
   * @param courseDetails - Fetched course details.
   * @param instructorDetails - Fetched instructor details.
   * @param performanceData - Fetched performance data.
   * @returns Array of tab names (e.g., ['Overview', 'Instructor', 'Performance']).
   */
  getAvailableTabs(
      courseDetails: CourseDetails | null,
      instructorDetails: InstructorDetails | null,
      performanceData: PerformanceData[] | null
  ): string[] {
      const tabs: string[] = [];

      if (instructorDetails) tabs.push('Instructor');
      if (courseDetails) tabs.push('Overview'); // Assumes Overview is always present if details load
      if (performanceData && performanceData.length > 0) tabs.push('Performance');
      // Add other potential tabs based on features/data (e.g., 'Q&A', 'Notes', 'Resources')
      // if (courseDetails?.hasQA) tabs.push('Q&A');

      // Maintain desired order
      const orderedTabs = ['Instructor', 'Overview', 'Performance']; // Add others here in desired order
      return orderedTabs.filter(tab => tabs.includes(tab));
  }

  /**
   * Resume the course - This is now handled by findNextItemToResume.
   * Kept for potential compatibility, but logic relies on findNextItemToResume.
   * @param courseId - The ID of the course (used to fetch details if needed).
   * @returns Promise with the next content item to view or null/error.
   * @deprecated Use findNextItemToResume(modules) after fetching details.
   */
  async resumeCourse(courseId: string): Promise<any> {
    console.warn("resumeCourse is deprecated. Use findNextItemToResume(modules) after fetching details.");
    try {
        const details = await this.getCourseDetails(courseId);
        const nextItem = this.findNextItemToResume(details.modules);
        if (nextItem) {
            return {
                success: true,
                nextItem: nextItem,
                message: 'Resuming from last position'
            };
        } else {
             return {
                success: false,
                nextItem: null,
                message: 'Course already completed or no items found.'
            };
        }
    } catch (error) {
        console.error('Error resuming course:', error);
        throw error; // Re-throw error
    }
  }

  // Quiz-related methods
  async fetchQuizDataFromSubtopic(subtopicId: string): Promise<{quizId: string | null, assignment: any}> {
    try {
      const result = await fetchData<any>(`CoursesContent/${subtopicId}`);
      console.log(`Fetching quiz data for subtopic ${subtopicId}:`, result);

      // Based on your API response, check both resultObject.assignment and direct assignment
      const assignment = result?.resultObject?.assignment || result?.assignment;
      console.log(`Assignment found:`, assignment);

      if (assignment && assignment.quizId) {
        console.log(`Quiz ID found in assignment: ${assignment.quizId}`);
        return {
          quizId: assignment.quizId,
          assignment: assignment
        };
      }

      console.log(`No quiz ID found for subtopic ${subtopicId}`);
      return { quizId: null, assignment: null };
    } catch (error) {
      console.error(`Failed to fetch quiz data for subtopic ${subtopicId}:`, error);
      return { quizId: null, assignment: null };
    }
  }

  async fetchQuizDetails(quizId: string): Promise<any> {
    try {
      console.log(`Fetching quiz details for ID: ${quizId}`);
      const result = await fetchData<any>(`Quizzes/${quizId}`);
      console.log(`Quiz details response:`, result);

      // Check if data is in resultObject or directly in response
      const quizData = result?.resultObject || result;
      console.log(`Processed quiz data:`, quizData);
      return quizData;
    } catch (error) {
      console.error(`Failed to fetch quiz details for ${quizId}:`, error);
      return null;
    }
  }

  async fetchQuestionDetails(questionId: string): Promise<any> {
    try {
      console.log(`Fetching question details for ID: ${questionId}`);
      const result = await fetchData<any>(`Questions/${questionId}`);
      console.log(`Question details response:`, result);

      // Check if data is in resultObject or directly in response
      const questionData = result?.resultObject || result;
      console.log(`Processed question data:`, questionData);
      return questionData;
    } catch (error) {
      console.error(`Failed to fetch question details for ${questionId}:`, error);
      return null;
    }
  }

  async fetchQuizWithQuestions(quizId: string): Promise<any> {
    try {
      console.log(`fetchQuizWithQuestions: Starting for quiz ID ${quizId}`);

      // First fetch the quiz details
      const quiz = await this.fetchQuizDetails(quizId);
      console.log(`fetchQuizWithQuestions: Quiz details:`, quiz);

      if (!quiz) {
        console.log(`fetchQuizWithQuestions: No quiz data returned`);
        return null;
      }

      if (!quiz.questionSet) {
        console.log(`fetchQuizWithQuestions: No questionSet found in quiz data`);
        console.log(`fetchQuizWithQuestions: Quiz keys:`, Object.keys(quiz));
        return null;
      }

      console.log(`fetchQuizWithQuestions: Found ${quiz.questionSet.length} questions`);
      console.log(`fetchQuizWithQuestions: First question sample:`, quiz.questionSet[0]);

      // Check if questionSet contains objects or IDs
      const firstQuestion = quiz.questionSet[0];
      if (typeof firstQuestion === 'object' && firstQuestion.id) {
        // Questions are already full objects, no need to fetch individually
        console.log(`fetchQuizWithQuestions: Questions are already complete objects`);
        return quiz;
      } else {
        // Questions are IDs, need to fetch them
        console.log(`fetchQuizWithQuestions: Questions are IDs, fetching individually`);
        const questions = await Promise.all(
          quiz.questionSet.map((questionId: string) => this.fetchQuestionDetails(questionId))
        );

        console.log(`fetchQuizWithQuestions: Fetched questions:`, questions);

        const result = {
          ...quiz,
          questionSet: questions.filter(q => q !== null) // Filter out failed requests
        };

        console.log(`fetchQuizWithQuestions: Final result:`, result);
        return result;
      }
    } catch (error) {
      console.error(`Failed to fetch quiz with questions for ${quizId}:`, error);
      return null;
    }
  }



}

// Export as a singleton
export const courseDetailService = new CourseDetailService();
export default courseDetailService;
