import React from 'react';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface SearchInputProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
}

const SearchInput = ({ value, onChange, placeholder = 'Search...' }: SearchInputProps) => {
    return (
        <div className="flex flex-col">
          
            <div className="relative">
                <Input
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    placeholder={placeholder}
                    className="pr-9 rounded-[8px] bg-muted/50 border-2 border-[#8cb04f] "
                />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground text-[#8cb04f]" />
            </div>
        </div>
    );
};

export default SearchInput;
