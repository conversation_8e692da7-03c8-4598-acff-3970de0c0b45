export interface School {
  id: string;
  name: string;
  description: string;
  location: string;
  createdOn: string;
  users: SchoolUser[];
}

export interface SchoolCreationRequest {
  name: string;
  description: string;
  location: string;
  createdOn: string;
}

export interface UserRole {
  id: number;
  role: string;
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  userRoleId: number;
  userRole: UserRole;
  deleted: boolean;
  createdOn: string;
  updatedon: string;
}

export interface SchoolUser {
  user: User;
  schoolId: string;
  announcment: boolean;
}

export interface Student {
  user: User;
  announcment: boolean;
  currentChapter: string;
  currentTopic: string;
  createdOn: string;
}

export interface Teacher {
  user: User;
  classroomId: string;
  makeAnnouncment: boolean;
  createdOn: string;
}

export interface Curriculum {
  classroomId: string;
  topicId: string;
  topicEndDate: string | null;
}

export interface Classroom {
  id: string;
  name: string;
  description: string;
  courseId: string;
  schoolId: string;
  students: Student[];
  teachers: Teacher[];
  curricula: Curriculum[];
  school: School | null;
  course: any | null;
  deleted: boolean;
  createdOn: string;
  updatedon: string;
}

export interface ClassCreationRequest {
  id?: string;
  name: string;
  description: string;
  courseId: string;
  schoolId: string;
  teacherIds: string[];
  studentIds: string[];
  createdOn: string;
}

export interface ApiResponse<T> {
  resultObject: T;
  isSuccess: boolean;
  message?: string;
  isError: boolean;
  errors: string[];
  recoverableErrors: string[];
}
