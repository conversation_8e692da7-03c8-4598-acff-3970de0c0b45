import React from 'react';

interface StatCardProps {
    icon: React.ReactNode;
    title: string;
    value: number;
}

const StatCard = ({ icon, title, value }: StatCardProps) => (
    <div className="bg-white rounded-xl shadow-md p-4 flex items-center">
        {icon}
        <div className="mx-2 w-px h-6 bg-gray-200" />
        <div>
            <h3 className="font-semibold text-lg">{value}</h3>
            <p className="text-sm text-muted-foreground">{title}</p>
        </div>
    </div>
);

export default StatCard;
