import React, { useEffect, useState } from 'react';
import { Label } from "@/components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import {
    GraduationCap,
    BookOpen,
    BookCopy,
    ChevronRight
} from 'lucide-react';
import { Course } from './types';

interface CourseSelectorProps {
    courses: Course[];
    selectedCourse: string;
    selectedChapter: string;
    selectedTopic: string;
    onCourseSelect: (courseId: string) => void;
    onChapterSelect: (chapterId: string) => void;
    onTopicSelect: (topicId: string) => void;
}

const CourseSelector: React.FC<CourseSelectorProps> = ({
    courses,
    selectedCourse,
    selectedChapter,
    selectedTopic,
    onCourseSelect,
    onChapterSelect,
    onTopicSelect,
}) => {
    const [initialized, setInitialized] = useState(false);

    // Wait for courses to load and then initialize selections
    useEffect(() => {
        if (courses.length > 0 && !initialized) {
            setTimeout(() => {
                const course = courses.find(c => c.id === selectedCourse);
                if (course) {
                    const chapter = course.chapters?.find(ch => ch.id === selectedChapter);
                    if (chapter) {
                        const topic = chapter.topics.find(t => t.id === selectedTopic);
                        if (topic) {
                            onCourseSelect(selectedCourse);
                            setTimeout(() => {
                                onChapterSelect(selectedChapter);
                                setTimeout(() => {
                                    onTopicSelect(selectedTopic);
                                }, 100);
                            }, 100);
                        }
                    }
                }
                setInitialized(true);
            }, 100);
        }
    }, [courses, selectedCourse, selectedChapter, selectedTopic, onCourseSelect, onChapterSelect, onTopicSelect, initialized]);

    const selectedCourseData = courses.find(c => c.id === selectedCourse);
    const selectedChapterData = selectedCourseData?.chapters.find(ch => ch.id === selectedChapter);

    return (
        <Card className="p-4">
            <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:space-x-4">
                <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                        <GraduationCap className="w-4 h-4 text-primary" />
                        <Label>Course</Label>
                    </div>
                    <Select
                        value={selectedCourse}
                        onValueChange={(value) => {
                            onCourseSelect(value);
                            onChapterSelect('');
                            onTopicSelect('');
                        }}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="Select course" />
                        </SelectTrigger>
                        <SelectContent>
                            {courses.map((course) => (
                                <SelectItem key={course.id} value={course.id}>
                                    <div className="flex items-center">
                                        <BookOpen className="w-4 h-4 mr-2 text-muted-foreground" />
                                        {course.name}
                                    </div>
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                <div className="hidden md:block text-muted-foreground self-center pt-6">
                    <ChevronRight className="w-5 h-5" />
                </div>

                <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                        <BookOpen className="w-4 h-4 text-primary" />
                        <Label>Chapter</Label>
                    </div>
                    <Select
                        value={selectedChapter}
                        onValueChange={(value) => {
                            onChapterSelect(value);
                            onTopicSelect('');
                        }}
                        disabled={!selectedCourse}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="Select chapter" />
                        </SelectTrigger>
                        <SelectContent>
                            {selectedCourseData?.chapters.map((chapter) => (
                                <SelectItem key={chapter.id} value={chapter.id}>
                                    <div className="flex items-center">
                                        <BookCopy className="w-4 h-4 mr-2 text-muted-foreground" />
                                        {chapter.name}
                                    </div>
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                <div className="hidden md:block text-muted-foreground self-center pt-6">
                    <ChevronRight className="w-5 h-5" />
                </div>

                <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                        <BookCopy className="w-4 h-4 text-primary" />
                        <Label>Topic</Label>
                    </div>
                    <Select
                        value={selectedTopic}
                        onValueChange={onTopicSelect}
                        disabled={!selectedChapter}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="Select topic" />
                        </SelectTrigger>
                        <SelectContent>
                            {selectedChapterData?.topics.map((topic) => (
                                <SelectItem key={topic.id} value={topic.id}>
                                    {topic.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            </div>

            {/* Mobile breadcrumb */}
            <div className="md:hidden mt-4 flex items-center text-sm text-muted-foreground">
                <span>{selectedCourseData?.name || 'No course selected'}</span>
                {selectedChapter && (
                    <>
                        <ChevronRight className="w-4 h-4 mx-1" />
                        <span>{selectedChapterData?.name}</span>
                    </>
                )}
                {selectedTopic && (
                    <>
                        <ChevronRight className="w-4 h-4 mx-1" />
                        <span>
                            {selectedChapterData?.topics.find(t => t.id === selectedTopic)?.name}
                        </span>
                    </>
                )}
            </div>
        </Card>
    );
};

export default CourseSelector;
