import React from 'react';
import { motion } from 'framer-motion';
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
    FileQuestion,
    Image,
    Video,
    AudioLines,
    Pencil,
    Trash2,
    Activity,
    CheckCircle2,
    XCircle,
    MessageSquare,
    ListChecks
} from 'lucide-react';
import { Question } from './types';

interface QuestionCardProps {
    question: Question;
    onEdit: (question: Question) => void;
    onDelete: (id: string) => void;
}

const QuestionCard: React.FC<QuestionCardProps> = ({
    question,
    onEdit,
    onDelete
}) => {
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
        >
            <Card className="p-4 hover:shadow-md transition-shadow">
                <div className="flex flex-col space-y-4">
                    {/* Header */}
                    <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3">
                            <div className="bg-primary/10 p-2 rounded-lg">
                                <FileQuestion className="w-5 h-5 text-primary" />
                            </div>
                            <div>
                                <div
                                    className="font-medium text-lg"
                                    dangerouslySetInnerHTML={{ __html: question.questionText }}
                                />
                                <div className="flex flex-wrap gap-2 mt-2">
                                    <Badge variant="outline" className="flex items-center">
                                        <Activity className="w-3 h-3 mr-1" />
                                        Level {question.level}
                                    </Badge>
                                    <Badge variant="outline" className="flex items-center">
                                        <ListChecks className="w-3 h-3 mr-1" />
                                        {question.type}
                                    </Badge>
                                    <Badge variant="outline" className="flex items-center">
                                        <MessageSquare className="w-3 h-3 mr-1" />
                                        {question.answers.length} Answers
                                    </Badge>
                                    {question.active ? (
                                        <Badge variant="default" className="bg-green-500 flex items-center">
                                            <CheckCircle2 className="w-3 h-3 mr-1" />
                                            Active
                                        </Badge>
                                    ) : (
                                        <Badge variant="destructive" className="flex items-center">
                                            <XCircle className="w-3 h-3 mr-1" />
                                            Inactive
                                        </Badge>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="flex space-x-2">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => onEdit(question)}
                                className="hover:bg-primary/10 hover:text-primary"
                            >
                                <Pencil className="w-4 h-4" />
                            </Button>
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => onDelete(question.id)}
                                className="hover:bg-destructive/10 hover:text-destructive"
                            >
                                <Trash2 className="w-4 h-4" />
                            </Button>
                        </div>
                    </div>

                    {/* Media Indicators */}
                    <div className="flex flex-wrap gap-3">
                        {question.questionImageMetadata && (
                            <div className="flex items-center text-muted-foreground">
                                <Image className="w-4 h-4 mr-1" />
                                <span className="text-sm">Question Image</span>
                            </div>
                        )}
                        {question.questionVideoMetadata && (
                            <div className="flex items-center text-muted-foreground">
                                <Video className="w-4 h-4 mr-1" />
                                <span className="text-sm">Question Video</span>
                            </div>
                        )}
                        {question.questionAudioMetadata && (
                            <div className="flex items-center text-muted-foreground">
                                <AudioLines className="w-4 h-4 mr-1" />
                                <span className="text-sm">Question Audio</span>
                            </div>
                        )}
                    </div>

                    {/* Preview of first answer */}
                    {question.answers[0] && (
                        <div className="mt-2 text-sm text-muted-foreground">
                            <div className="font-medium mb-1">First Answer:</div>
                            <div
                                dangerouslySetInnerHTML={{
                                    __html: question.answers[0].answerText.length > 100
                                        ? question.answers[0].answerText.substring(0, 100) + '...'
                                        : question.answers[0].answerText
                                }}
                            />
                        </div>
                    )}
                </div>
            </Card>
        </motion.div>
    );
};

export default QuestionCard;
