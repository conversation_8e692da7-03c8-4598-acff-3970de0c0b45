import { LucideIcon } from "lucide-react";
import { cn } from "../../../lib/utils";
import { motion } from "framer-motion";

export function StatCard({
    title,
    value,
    icon: Icon,
    className,
}: {
    title: string;
    value: number;
    icon: LucideIcon;
    className?: string;
}) {
    return (
        <motion.div
            whileHover={{ scale: 1.02 }}
            className={cn(
                "group relative p-6 transition-all duration-300",
                className
            )}
        >
            <div className="absolute inset-0 bg-gradient-to-br from-background/50 to-background/30 backdrop-blur-sm rounded-3xl" />
            <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/0 rounded-3xl" />
            </div>
            <div className="relative flex items-start gap-4">
                <div className="p-3 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5">
                    <Icon className="w-6 h-6 text-primary" />
                </div>
                <div className="flex-1">
                    <h3 className="text-base font-medium text-muted-foreground mb-1">
                        {title}
                    </h3>
                    <div className="text-4xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80">
                        {value}
                    </div>
                </div>
            </div>
        </motion.div>
    );
}
