import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

interface StreamChartProps {
    data: { [key: string]: number }[];
    keys: string[];
    colors: string[];
    width?: number;
    height?: number;
    title: string;
}

export const StreamChart: React.FC<StreamChartProps> = ({
    data,
    keys,
    colors,
    width = 600,
    height = 400,
    title
}) => {
    const svgRef = useRef<SVGSVGElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!data || !svgRef.current || !containerRef.current) return;

        // Clear previous chart
        d3.select(svgRef.current).selectAll('*').remove();

        // Get container width
        const containerWidth = containerRef.current.clientWidth;
        const actualWidth = width || containerWidth;

        const margin = { top: 20, right: 20, bottom: 30, left: 50 };
        const innerWidth = actualWidth - margin.left - margin.right;
        const innerHeight = height - margin.top - margin.bottom;

        const svg = d3.select(svgRef.current)
            .attr('width', actualWidth)
            .attr('height', height)
            .attr('viewBox', `0 0 ${actualWidth} ${height}`)
            .attr('preserveAspectRatio', 'xMidYMid meet');

        const g = svg.append('g')
            .attr('transform', `translate(${margin.left},${margin.top})`);

        // Stack the data
        const stack = d3.stack<any>()
            .keys(keys)
            .order(d3.stackOrderNone)
            .offset(d3.stackOffsetSilhouette);

        const series = stack(data);

        // Create scales
        const xScale = d3.scaleLinear()
            .domain([0, data.length - 1])
            .range([0, innerWidth]);

        const yScale = d3.scaleLinear()
            .domain([
                d3.min(series, layer => d3.min(layer, d => d[0])) || 0,
                d3.max(series, layer => d3.max(layer, d => d[1])) || 0
            ])
            .range([innerHeight, 0]);

        // Create a color scale
        const colorScale = d3.scaleOrdinal<string>()
            .domain(keys)
            .range(colors);

        // Create the area generator
        const area = d3.area<any>()
            .x((_, i) => xScale(i))
            .y0(d => yScale(d[0]))
            .y1(d => yScale(d[1]))
            .curve(d3.curveBasis);

        // Add gradient definitions
        const gradients = g.append('defs')
            .selectAll('linearGradient')
            .data(series)
            .enter()
            .append('linearGradient')
            .attr('id', (d, i) => `gradient-${i}`)
            .attr('gradientUnits', 'userSpaceOnUse')
            .attr('x1', '0%')
            .attr('y1', '0%')
            .attr('x2', '0%')
            .attr('y2', '100%');

        gradients.append('stop')
            .attr('offset', '0%')
            .attr('stop-color', d => colorScale(d.key))
            .attr('stop-opacity', 0.8);

        gradients.append('stop')
            .attr('offset', '100%')
            .attr('stop-color', d => colorScale(d.key))
            .attr('stop-opacity', 0.3);

        // Add the areas with animation
        const paths = g.selectAll('path')
            .data(series)
            .enter()
            .append('path')
            .attr('fill', (_, i) => `url(#gradient-${i})`)
            .attr('opacity', 0)
            .attr('d', d => {
                const flatData = d.map(point => {
                    return [point[0], point[0]]; // Start with height 0
                });
                return area(flatData);
            });

        // Animate the areas
        paths.transition()
            .duration(1500)
            .attr('opacity', 1)
            .attr('d', area);

        // Add hover effects
        const tooltip = d3.select('body')
            .append('div')
            .attr('class', 'tooltip')
            .style('position', 'absolute')
            .style('background-color', 'hsl(var(--background))')
            .style('padding', '8px')
            .style('border', '1px solid hsl(var(--border))')
            .style('border-radius', '4px')
            .style('pointer-events', 'none')
            .style('opacity', 0)
            .style('font-size', '12px')
            .style('color', 'hsl(var(--foreground))');

        paths.on('mouseover', function (event: MouseEvent, d) {
            d3.select(this)
                .transition()
                .duration(200)
                .attr('opacity', 1);

            tooltip.transition()
                .duration(200)
                .style('opacity', .9);
            tooltip.html(`${d.key}`)
                .style('left', (event.pageX + 10) + 'px')
                .style('top', (event.pageY - 28) + 'px');
        })
            .on('mouseout', function () {
                d3.select(this)
                    .transition()
                    .duration(200)
                    .attr('opacity', 0.8);

                tooltip.transition()
                    .duration(500)
                    .style('opacity', 0);
            });

        // Add title with fade-in animation
        svg.append('text')
            .attr('class', 'chart-title')
            .attr('x', actualWidth / 2)
            .attr('y', 20)
            .attr('text-anchor', 'middle')
            .style('font-size', '14px')
            .style('font-weight', '500')
            .style('fill', 'hsl(var(--foreground))')
            .style('opacity', 0)
            .text(title)
            .transition()
            .duration(500)
            .style('opacity', 1);

        // Handle resize
        const handleResize = () => {
            if (!containerRef.current) return;
            const newWidth = containerRef.current.clientWidth;
            svg.attr('width', newWidth)
                .attr('viewBox', `0 0 ${newWidth} ${height}`);

            const newInnerWidth = newWidth - margin.left - margin.right;
            xScale.range([0, newInnerWidth]);

            paths.attr('d', area);
        };

        window.addEventListener('resize', handleResize);

        // Cleanup
        return () => {
            d3.select('body').selectAll('.tooltip').remove();
            window.removeEventListener('resize', handleResize);
        };
    }, [data, width, height, title, keys, colors]);

    return (
        <div ref={containerRef} className="w-full h-full">
            <svg ref={svgRef} className="w-full h-full" />
        </div>
    );
};
