@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');

/* General Container Styles */
.parentContainer {
  max-width: 900px;
  margin: 0 auto; 
  padding: 20px;
  overflow: visible;
  box-shadow: 0 0 10px rgba(0,0,0,0.1); 
}

/* General <PERSON><PERSON> */
.parentContainer button {
  background-color: #2847B2;
  border: 1px solid #2847B2;
  border-radius: 5px;
  color: #ffffff;
  padding: 10px 20px;
  cursor: pointer;
  margin: 15px; 
  margin-left: 0px;
  font-weight: bold;
  font-family: 'DM Sans', sans-serif;
  transition: background-color 0.3s, color 0.3s;
}

.parentContainer button:hover {
  color: #ffffff;
  background-color: #1d2b64; 
}

.parentContainer button:disabled {
  background-color: #cccccc;
  border: 1px solid #555;
  color: #666666; 
  cursor: not-allowed;
}

.parentContainer button:disabled:hover {
  background-color: #cccccc;
  color: #666666; 
}

/* Form Label Styles */
.form-label2 {
font-size: 14px;
font-weight: bold;
margin-bottom: 5px;
display: block;
color: #333;
overflow: visible;
font-family: 'DM Sans', sans-serif;
}

.compulsoryInd2 {
color: red;
margin-left: 5px;
}


.form-input2,
.select-input2,
.text-area2 {
width: 100%;
padding: 10px;
border: 1px solid #ccc;
border-radius: 4px;
box-sizing: border-box;
font-size: 14px;
overflow: visible;
font-family: 'DM Sans', sans-serif;
margin: 20px 0 !important;
}
.select-input2{
  border:none;
  padding:0;
}
#selectTopic{
  z-index: 8;
}
.form-input2:focus,
.select-input2:focus,
.text-area2:focus {
border-color: #2847b2;
outline: none;
}

.form-error {
color: red;
font-size: 12px;
text-decoration: underline;
}

/* Custom Styles for Spans */
.question-label,
.correct-answer-label,
.question-type-label,
.exp,
.rev,
.answer-label {
  font-size: 14px;
  font-weight: bold;
  font-family: 'DM Sans', sans-serif;
  color: #333;
}
/* General styles for divs with dangerouslySetInnerHTML */
.html-content {
  padding-left: 15px; /* Adjusted padding for better spacing */
  margin: 10px 0;
  font-weight: 500;
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  font-family: 'DM Sans', sans-serif;
  line-height: 3; 
  box-shadow: 0 4px 10px rgba(3, 3, 3, 0.1); 
  overflow: hidden;
}

/* Additional styling for Question Text */
.question-text {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

/* Additional styling for Answer Text */
.answer-text {
  font-size: 14px;
  font-weight: 700px;
  margin-top:30;
  font-family: 'DM Sans', sans-serif;
  margin-bottom:0;
}

.explanation-text {
  font-size: 14px;
  color: #666;
  border-left: 4px solid #350303;
}


.parentContainer .button-container {
  margin-top: 20px;
}

.parentContainer .button-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* Answer Option Styles */
.answer-option {
display: flex;
align-items: center;
margin: 20px 0;
border-radius: 4px;
}

.answer-option div {
flex-grow: 1;
padding: 10px;
border-radius: 4px;
margin-right: 10px;
font-family: 'DM Sans', sans-serif;
}

.answer-option input[type="checkbox"] {
display: none;
}

.checkbox-toggle {
display: flex;
align-items: center;
max-width:35px;
margin-right: 10px;
}

.toggle-label {
width: 34px;
height: 20px;
background-color: #ccc;
border-radius: 10px;
position: relative;
cursor: pointer;
display: inline-block;
}

.toggle-label:before {
content: "";
position: absolute;
top: 2px;
left: 2px;
width: 16px;
height: 16px;
background-color: #fff;
border-radius: 50%;
transition: transform 0.3s;
}

input[type="checkbox"]:checked + .toggle-label {
background-color: #4CAF50;
}

input[type="checkbox"]:checked + .toggle-label:before {
transform: translateX(14px);
}

.answer-option .edit-button {
background-color: #2847B2;
border: none;
color: white;
padding: 5px 15px;
cursor: pointer;
font-size: 14px;
}

.answer-option .edit-button:hover {
background-color: #1f3c7b;
}

.answer-option .delete-button {
background-color: red; 
border: none;
color: white;
padding: 5px 10px;
cursor: pointer;
font-size: 14px;
}

.answer-option .delete-button:hover {
background-color: #c9302c;
}

/* Container and Alignment Styles */
.button-container {
text-align: right;
}

.parentContainer .sun-editor {
border: 1px solid #ccc;
border-radius: 4px;
margin-bottom: 10px;
}

.parentContainer .sun-editor .se-container {
height: auto !important;
min-height: 150px;
}

.parentContainer .sun-editor button {
  background: initial; 
  color: initial; 
  border: initial; 
  box-shadow: initial;
}
/* Alignment Utilities */
.parentContainer .align-right {
text-align: right;
}

.parentContainer .align-left {
text-align: left;
}

.parentContainer .submit-button,
.parentContainer .add-answer-button,
.parentContainer .save-button,
.parentContainer .cancel-button {
  padding: 10px 20px;
  border: 1px solid;
  border-radius: 5px;
  background-color: white;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s, color 0.3s;
  font-family: 'DM Sans', sans-serif;
}

.parentContainer .submit-button,
.parentContainer .add-answer-button,
.parentContainer .save-button {
  color: white;
  background-color: green;
}

.parentContainer .submit-button:hover,
.parentContainer .add-answer-button:hover,
.parentContainer .save-button:hover {
  background-color: white;
  color: green;
}

.parentContainer .cancel-button {
  background-color: red;
  border-color: red;
}

.parentContainer .cancel-button:hover {
  background-color: rgb(225, 17, 17);
  color: white;
}

.answer-label{
  margin-bottom: 40px;
}

/* Responsive Design */

/* Mobile devices (width < 768px) */
@media (max-width: 767px) {
  .parentContainer button {
  font-size: 14px;
  padding: 8px 12px;
}

.form-input2,
.select-input2,
.text-area2 {
  padding: 8px;
}


.answer-option .toggle-label {
  width: 30px;
  height: 18px;
}

.answer-option .toggle-label:before {
  width: 14px;
  height: 14px;
}

.parentContainer .sun-editor {
  margin-bottom: 5px;
}
}

/* Tablet devices (width < 1024px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .form-input2,
  .select-input2,
  .text-area2 {
    padding: 9px;
  }
  
  .answer-option {
    margin-right: 8px;
  }
  
  .answer-option .toggle-label {
    width: 32px;
    height: 18px;
  }

  .answer-option .toggle-label:before {
    width: 16px;
    height: 16px;
  }
  
  .parentContainer .sun-editor {
    margin-bottom: 8px;
  }
}

.question-input-cont {
  margin-top: 20px;
}

.question-input {
   width: 100%;
   height: 100px;
   /* padding:20px */
}
.match-input-cont {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;

}

.question-input-match {
  width: 45%;
  height: 100px
}

