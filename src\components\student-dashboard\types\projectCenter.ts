export enum GroupType {
  CLASS_SCOPE = "CLASS_SCOPE",
  SCHOOL_SCOPE = "SCHOOL_SCOPE", // For academic projects
  GLOBAL_SCOPE = "GLOBAL_SCOPE", // For industry projects
}

export enum ProjectStatus {
  PENDING = "PENDING",
  IN_PROGRESS = "IN_PROGRESS",
  FINISHED = "FINISHED",
}

export enum ProjectAPIType {
  ACADEMIC = "ACADEMIC",
  INDUSTRY = "INDUSTRY",
}

export interface Project {
  Id: string;
  ProjectName: string;
  ProjectType: ProjectAPIType;
  ProjectText: string | null;
  ExpressOfInterestDeadline: string | null;
  AvailableForCourses: string[] | null;
}

export interface GroupMember {
  id: string;
  name: string;
  avatarUrl?: string;
}

export interface Group {
  Id: string;
  ProjectId: string;
  InstructorId: string | null;
  LeaderId: string | null;
  GroupName: string;
  GroupType: GroupType;
  Status: ProjectStatus;
  StudentIds: string[];
  RemovedMembers: string[] | null;
  ClassroomId: string | null;
  SchoolId: string | null;
  Project: Project;
  TotalAssignments: number;
  TotalSubmissions: number;
  TotalPeerReviews: number;
  TotalInstructorReviews: number;
  members?: GroupMember[];
}

export interface LifecycleStage {
  id: string;
  title: string;
  projects: ProjectCard[];
}

export interface ProjectCard {
  id: string;
  name: string;
  members: number;
}

export interface AcademicGroupLifeCycle {
  Created: string[];
  GroupsFormed?: string[];
  PendingSubmissions?: string[];
  PeerReviewOngoing: string[];
  FinalSubmission?: string[];
  InstructorReviewOngoing: string[];
  Graded: string[];
}

export interface IndustryGroupLifeCycle {
  Created: string[];
  GroupsFormed?: string[];
  PendingSubmissions?: string[];
  PeerReviewOngoing: string[];
  FinalSubmission?: string[];
  InstructorReviewOngoing: string[];
  Graded: string[];
}

export interface StudentProjectDashboardResponse {
  Groups: Group[];
  AcademicGroupLifeCycle: AcademicGroupLifeCycle;
  IndustryGroupLifeCycle: IndustryGroupLifeCycle;
}

// For Project Activity Tracker Table
export interface ProjectActivity {
  id: string;
  projectName: string;
  type: "Academic" | "Industry";
  classAccess: string;
  members: number | GroupMember[];
  submissionStatus: { count: number; total: number; statusColor: string };
  peerReviewStatus: { count: number; total: number; statusColor: string };
  instructorReviewStatus: { count: number; total: number; statusColor: string };
}

// For Summary Cards
export interface SummaryInfo {
  id: string;
  title: string;
  value: string | number;
  iconSrc: string;
  iconBgColor: string;
  filterOptions?: {
    academicLabel: string;
    industryLabel: string;
  };
}

// For Skill Index
export interface SkillImpact {
  title: string;
  value: string;
  iconSrc: string;
  iconBgColor: string;
  skills: {
    name: string;
    progress: number;
  }[];
}

// For Notifications
export interface NotificationAlert {
  id: string;
  type: string;
  date: string;
  title: string;
  isNew?: boolean;
}

// API Response interfaces for project details
export interface ProjectAPIResponse {
  resultObject: {
    id: {
      timestamp: number;
      machine: number;
      pid: number;
      increment: number;
      creationTime: string;
    };
    projectName: string;
    projectType: ProjectAPIType;
    projectText: string;
    expressOfInterestDeadline: string | null;
    availableForCourses: string[];
    createdOn: string;
    updatedon: string;
  };
  isError: boolean;
  errors: string[];
  recoverableErrors: string[];
}

export interface AssignmentAPIResponse {
  resultObject: Array<{
    id: {
      timestamp: number;
      machine: number;
      pid: number;
      increment: number;
      creationTime: string;
    };
    projectId: {
      timestamp: number;
      machine: number;
      pid: number;
      increment: number;
      creationTime: string;
    };
    dueDate: string;
    assignment: {
      type: number;
      tags: Array<{
        tagId: number;
      }>;
      title: string;
      assignmentText: string;
      fileUrl: string;
    };
  }>;
  isError: boolean;
  errors: string[];
  recoverableErrors: string[];
}

// Submission API Response Type
export interface SubmissionAPIResponse {
  resultObject: {
    groupId: {
      timestamp: number;
      machine: number;
      pid: number;
      increment: number;
      creationTime: string;
    };
    assignmentId: {
      timestamp: number;
      machine: number;
      pid: number;
      increment: number;
      creationTime: string;
    };
    fileUrl: string;
    peerReviewed: boolean;
    instructorReviewed: boolean;
    score: number;
    id: {
      timestamp: number;
      machine: number;
      pid: number;
      increment: number;
      creationTime: string;
    };
    createdOn: string;
    updatedon: string;
  };
  isError: boolean;
  errors: string[];
  recoverableErrors: string[];
}

export interface GroupAPIResponse {
  resultObject: {
    id: {
      timestamp: number;
      machine: number;
      pid: number;
      increment: number;
      creationTime: string;
    };
    projectId: {
      timestamp: number;
      machine: number;
      pid: number;
      increment: number;
      creationTime: string;
    };
    instructorId: string;
    leaderId: string;
    groupName: string;
    groupType: GroupType;
    status: ProjectStatus;
    studentIds: string[];
    removedMembers: string[];
    classroomId: string;
    schoolId: string;
  };
  isError: boolean;
  errors: string[];
  recoverableErrors: string[];
}

// Interface for project details page
export interface ProjectDetailData {
  id: string;
  name: string;
  type: 'Academic' | 'Industry';
  description: string;
  startDate: string;
  totalTasks: number;
  completedTasks: number;
  progress: number;
  reviews: number;
  tasks: TaskItem[];
  objectives: string[];
  detailedDescription: string;
  groupId?: string;
  groupName?: string;
  members?: number;
  instructorId?: string;
  leaderId?: string;
}

export interface TaskItem {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  dueDate?: string;
  type?: number;
}

// User API interfaces
export interface UserRole {
  id: number;
  role: string;
}

export interface User {
  firstName: string;
  lastName: string;
  email: string;
  userRoleId: number;
  userRole: UserRole;
  id: string;
  deleted: boolean;
  createdOn: string;
  updatedon: string;
}
