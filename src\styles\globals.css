@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 168 38% 33%;
    --card: 0 0% 100%;
    --card-foreground: 168 38% 33%;
    --popover: 0 0% 100%;
    --popover-foreground: 168 38% 33%;
    --primary: 179 94% 21%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 168 38% 33%;
    --radius: 1.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 0 0% 95%;
    --card: 24 9.8% 10%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 95%;
    --primary: 179 94% 21%;
    --primary-foreground: 144.9 80.4% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 142.4 71.8% 29.2%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    font-size: 18px;
  }

  body {
    @apply bg-background text-foreground;
  }

  .prose {
    max-width: none;
  }

  .prose pre {
    background-color: rgb(var(--secondary));
    color: rgb(var(--foreground));
  }

  .prose code {
    background-color: rgb(var(--secondary));
    padding: 0.25rem;
    border-radius: 0.25rem;
  }

  .prose :where(p):not(:where([class~="not-prose"] *)) {
    margin-block: 0.5em;
  }
}

.mask-linear-gradient {
  mask-image: linear-gradient(to bottom, black 60%, transparent 100%);
  -webkit-mask-image: linear-gradient(to bottom, black 60%, transparent 100%);
}

.streaming-content {
  transition: all 0.2s ease-out;
}

.diff-highlight-add {
  @apply bg-green-100 dark:bg-green-900/30 px-1 rounded;
}

.diff-highlight-remove {
  @apply bg-red-100 dark:bg-red-900/30 line-through px-1 rounded;
}

.text-primary-color {
  color:#0E3D35;
}

.bg-primary-color {
  background-color: #ffffff;
}

.text-secondary-color {
  color: #fafb69;
}

.bg-secondary-color {
  background-color: #fafb69;
}

.text-accent-color {
  color: #5bcfdd;
}

.bg-accent-color {
  background-color: #5bcfdd;
}

.text-neutral-color {
  color: #644b49;
}

.bg-neutral-color {
  background-color: #644b49;
}