import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft, Save, BookOpen, Settings, Loader2, AlertCircle, Wand2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Skeleton } from "@/components/ui/skeleton";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
    useGetCourseByIdQuery,
    useCreateCourseMutation,
    useUpdateCourseMutation,
    useGetSubjectsQuery,
    useCreateContentMutation,
    useSaveChaptersDataMutation,
    useSaveTopicsDataMutation,
} from '@/APIConnect';
import { toast } from '@/hooks/use-toast';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { GenerateContentModal } from './GenerateContentModal';
import ModulesAndContent from './ModulesAndContent';
import { Subject } from '../Subjects/types';
import { CourseCreationRequest } from './types';

interface SubtopicContent {
    name: string;
    weight: number;
    type: string;
    content: string;
    contentUrl?: string;
    assignment?: {
        type: 1 | 2; // 1 = Submission type, 2 = quiz type
        title: string;
        assignmentText: string;
        fileUrl?: string;
    };
}

interface TopicContent {
    name: string;
    description: string;
    subtopics: SubtopicContent[];
}

interface ChapterContent {
    name: string;
    description: string;
    topics: TopicContent[];
}

interface CourseContentPreview {
    chapters: ChapterContent[];
}

// Utility function to strip HTML tags
const stripHtmlTags = (html: string) => {
    return html.replace(/<[^>]+>/g, '');
};

interface FormState extends CourseCreationRequest {
    name: string;
    description: string;
    premium: boolean;
    subjectId: string;
    createdOn: string;
}

interface FormErrors {
    name?: string;
    subjectId?: string;
}

const LoadingSkeleton = () => (
    <div className="space-y-6">
        <Skeleton className="h-12 w-full" />
        <div className="space-y-4">
            <Skeleton className="h-10 w-3/4" />
            <Skeleton className="h-32 w-full" />
        </div>
    </div>
);

const CourseDetailsForm = ({ formData, setFormData, errors, isSubmitting }: any) => (
    <Card className="transition-all duration-200 hover:shadow-md">
        <CardContent className="p-6">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center mb-4"
            >
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <BookOpen className="h-5 w-5 mr-2" />
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>Course Details Section</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
                <h2 className="text-xl font-semibold text-gray-800">Course Details</h2>
            </motion.div>
            <div className="space-y-4">
                <div>
                    <Label className="text-gray-700">Course Name</Label>
                    <Input
                        value={formData.name}
                        onChange={(e) => {
                            setFormData((prev: FormState) => ({ ...prev, name: e.target.value }));
                            if (errors.name) {
                                setFormData((prev: any) => ({ ...prev, errors: { ...prev.errors, name: undefined } }));
                            }
                        }}
                        placeholder="Enter course name..."
                        className={`transition-all duration-200 ${errors.name ? "border-red-500 animate-shake" : ""}`}
                        disabled={isSubmitting}
                    />
                    <AnimatePresence>
                        {errors.name && (
                            <motion.p
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                className="text-sm text-red-500 mt-1 flex items-center"
                            >
                                <AlertCircle className="h-4 w-4 mr-1" />
                                {errors.name}
                            </motion.p>
                        )}
                    </AnimatePresence>
                </div>

                <div>
                    <Label className="text-gray-700">Description</Label>
                    <Textarea
                        value={formData.description}
                        onChange={(e) => setFormData((prev: FormState) => ({ ...prev, description: e.target.value }))}
                        placeholder="Enter course description..."
                        className="h-32 transition-all duration-200"
                        disabled={isSubmitting}
                    />
                </div>
            </div>
        </CardContent>
    </Card>
);

const CourseSettings = ({ formData, setFormData, errors, isSubmitting, subjects }: any) => (
    <Card className="transition-all duration-200 hover:shadow-md">
        <CardContent className="p-6">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center mb-4"
            >
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Settings className="h-5 w-5 mr-2" />
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>Course Settings Section</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
                <h2 className="text-xl font-semibold text-gray-800">Settings</h2>
            </motion.div>
            <div className="space-y-4">
                <div>
                    <Label className="text-gray-700">Subject</Label>
                    <Select
                        value={formData.subjectId}
                        onValueChange={(value) => {
                            setFormData((prev: FormState) => ({ ...prev, subjectId: value }));
                            if (errors.subjectId) {
                                setFormData((prev: any) => ({ ...prev, errors: { ...prev.errors, subjectId: undefined } }));
                            }
                        }}
                        disabled={isSubmitting}
                    >
                        <SelectTrigger className={`transition-all duration-200 ${errors.subjectId ? "border-red-500 animate-shake" : ""}`}>
                            <SelectValue placeholder="Select subject" />
                        </SelectTrigger>
                        <SelectContent>
                            {subjects?.map((subject: Subject) => (
                                <SelectItem key={subject.id} value={subject.id}>
                                    {subject.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                    <AnimatePresence>
                        {errors.subjectId && (
                            <motion.p
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                className="text-sm text-red-500 mt-1 flex items-center"
                            >
                                <AlertCircle className="h-4 w-4 mr-1" />
                                {errors.subjectId}
                            </motion.p>
                        )}
                    </AnimatePresence>
                </div>

                {/* <div className="flex items-center justify-between">
                    <Label className="text-gray-700">Premium Course</Label>
                    <Switch
                        checked={formData.premium}
                        onCheckedChange={(checked) => setFormData((prev: FormState) => ({ ...prev, premium: checked }))}
                        disabled={isSubmitting}
                        className="transition-all duration-200"
                    />
                </div> */}
            </div>
        </CardContent>
    </Card>
);

const CourseEditor = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const [searchParams] = useSearchParams();
    const isEditMode = !!id;

    const { data: subjectsData, isLoading: isLoadingSubjects, error: subjectsError } = useGetSubjectsQuery('');
    const { data: courseData, isLoading: isLoadingCourse, error: courseError, refetch: refetchCourse } = useGetCourseByIdQuery(id, { skip: !id });
    const [createCourse, { isLoading: isCreating }] = useCreateCourseMutation();
    const [updateCourse, { isLoading: isUpdating }] = useUpdateCourseMutation();
    const [createCourseContent] = useCreateContentMutation();
    const [saveChapter] = useSaveChaptersDataMutation();
    const [saveTopic] = useSaveTopicsDataMutation();

    const [formData, setFormData] = useState<FormState>({
        name: '',
        description: '',
        premium: false,
        subjectId: '',
        createdOn: new Date().toISOString()
    });

    const [errors, setErrors] = useState<FormErrors>({});

    const [showGenerateModal, setShowGenerateModal] = useState(false);

    useEffect(() => {
        if (courseData?.resultObject) {
            setFormData({
                name: courseData.resultObject.name,
                description: courseData.resultObject.description,
                premium: courseData.resultObject.premium,
                subjectId: courseData.resultObject.subjectId,
                createdOn: courseData.resultObject.createdOn
            });
        }
    }, [courseData]);

    const validateForm = (): boolean => {
        const newErrors: FormErrors = {};
        let isValid = true;

        if (!formData.name.trim()) {
            newErrors.name = "Course name is required";
            isValid = false;
        }

        if (!formData.subjectId) {
            newErrors.subjectId = "Subject is required";
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            toast({
                title: "Please fix the errors",
                description: "Some required fields are missing or invalid",
                variant: "destructive",
            });
            return;
        }

        try {
            if (isEditMode) {
                await updateCourse({ id, ...formData }).unwrap();
                toast({
                    title: "Course updated successfully",
                    variant: "default",
                });
                refetchCourse();
            } else {
                const result = await createCourse(formData).unwrap();
                toast({
                    title: "Course created successfully",
                    variant: "default",
                });
                const chapterId = searchParams.get('chapterId');
                const topicId = searchParams.get('topicId');
                const params = new URLSearchParams();
                if (chapterId) params.set('chapterId', chapterId);
                if (topicId) params.set('topicId', topicId);
                navigate(`/admin/courses/${result.resultObject.id}${params.toString() ? `?${params.toString()}` : ''}`);
            }
        } catch (error) {
            console.error('Error saving course:', error);
            toast({
                title: "Failed to save course",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const handleGeneratedContent = async (generatedContent: any) => {
        if (!id) return;

        try {
            // Transform the generated content to match our expected format
            const content: CourseContentPreview = {
                chapters: generatedContent.chapters.map((chapter: any) => ({
                    name: chapter.name,
                    description: chapter.description,
                    topics: chapter.topics.map((topic: any) => ({
                        name: topic.name,
                        description: topic.description,
                        subtopics: [{
                            name: topic.name,
                            weight: 100, // Since we're converting single content to subtopic
                            type: topic.type.toString(),
                            content: topic.content,
                        }]
                    }))
                }))
            };

            // Create chapters sequentially
            const chaptersWithIds: any[] = [];
            for (const chapter of content.chapters) {
                const chapterResult = await saveChapter({
                    name: chapter.name,
                    description: chapter.description,
                    courseId: id,
                    createdOn: new Date().toISOString()
                }).unwrap();
                chaptersWithIds.push({ ...chapter, id: chapterResult.resultObject.id });
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            // Process each chapter's topics and subtopics
            for (const chapter of chaptersWithIds) {
                for (const topic of chapter.topics) {
                    const topicResult = await saveTopic({
                        name: topic.name,
                        description: topic.description,
                        chapterId: chapter.id,
                        createdOn: new Date().toISOString()
                    }).unwrap();

                    await new Promise(resolve => setTimeout(resolve, 200));

                    // Create subtopics for the topic
                    let totalWeight = 0;
                    for (const subtopic of topic.subtopics) {
                        totalWeight += subtopic.weight;

                        // Create the subtopic using createContent mutation
                        await createCourseContent({
                            topicId: topicResult.resultObject.id,
                            courseId: id,
                            subTopicName: subtopic.name,
                            topicWeight: subtopic.weight,
                            type: parseInt(subtopic.type),
                            content: subtopic.content,
                            contentUrl: subtopic.contentUrl,
                            assignment: subtopic.assignment ? {
                                ...subtopic.assignment,
                                quizId: undefined
                            } : undefined
                        }).unwrap();

                        await new Promise(resolve => setTimeout(resolve, 200));
                    }

                    // Validate total weight equals 100
                    if (totalWeight !== 100) {
                        throw new Error(`Total weight for topic ${topic.name} must equal 100`);
                    }
                }
            }

            toast({
                title: "Content generated successfully",
                description: "All chapters, topics, subtopics, and content have been created",
                variant: "default",
            });
            setShowGenerateModal(false);
            await refetchCourse();
        } catch (err) {
            console.error('Error saving generated content:', err);
            toast({
                title: "Failed to save generated content",
                description: err instanceof Error ? err.message : "Please try again later",
                variant: "destructive",
            });
        }
    };

    const isLoading = (isEditMode && isLoadingCourse) || isLoadingSubjects;
    const isSubmitting = isCreating || isUpdating;
    const hasError = subjectsError || courseError;

    return (
        <div className="min-h-screen bg-gray-50">
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="sticky top-0 z-10 bg-white border-b shadow-sm"
            >
                <div className="container mx-auto px-4 py-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={() => navigate('/admin/courses')}
                                            disabled={isSubmitting}
                                            className="transition-all duration-200 hover:scale-105"
                                        >
                                            <ArrowLeft className="h-5 w-5" />
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>Back to Courses</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                            <h1 className="text-2xl font-bold flex items-center text-gray-800">
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <BookOpen className="h-6 w-6 mr-2" />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>Course Editor</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                                {isEditMode ? 'Edit Course' : 'Create Course'}
                            </h1>
                        </div>
                        <div className="flex items-center space-x-2">
                            {isEditMode && (
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <Button
                                                variant="outline"
                                                onClick={() => setShowGenerateModal(true)}
                                                disabled={isSubmitting}
                                                className="flex items-center transition-all duration-200 hover:scale-105"
                                            >
                                                <Wand2 className="h-4 w-4 mr-2" />
                                                Generate Content
                                            </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>Generate course content using AI</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            )}
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <Button
                                            onClick={handleSubmit}
                                            className="flex items-center transition-all duration-200 hover:scale-105"
                                            disabled={isSubmitting}
                                        >
                                            {isSubmitting ? (
                                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                            ) : (
                                                <Save className="h-4 w-4 mr-2" />
                                            )}
                                            {isEditMode ? 'Update' : 'Create'}
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>{isEditMode ? 'Save changes' : 'Create new course'}</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>
                    </div>
                </div>
            </motion.div>

            <div className="container mx-auto px-4 py-6">
                {hasError && (
                    <Alert variant="destructive" className="mb-6">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                            Failed to load data. Please try refreshing the page.
                        </AlertDescription>
                    </Alert>
                )}

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="lg:col-span-2 space-y-6">
                        {isLoading ? (
                            <LoadingSkeleton />
                        ) : (
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                            >
                                <CourseDetailsForm
                                    formData={formData}
                                    setFormData={setFormData}
                                    errors={errors}
                                    isSubmitting={isSubmitting}
                                />
                            </motion.div>
                        )}

                        {isEditMode && courseData?.resultObject && (
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                            >
                                <Card>
                                    <CardContent className="p-6">
                                        <ModulesAndContent
                                            courseId={id!}
                                            modules={courseData.resultObject.chapters || []}
                                            onUpdate={refetchCourse}
                                            disabled={isSubmitting}
                                            classroomId={courseData.resultObject.classroomId}
                                        />
                                    </CardContent>
                                </Card>
                            </motion.div>
                        )}
                    </div>

                    <div>
                        {isLoading ? (
                            <LoadingSkeleton />
                        ) : (
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                            >
                                <CourseSettings
                                    formData={formData}
                                    setFormData={setFormData}
                                    errors={errors}
                                    isSubmitting={isSubmitting}
                                    subjects={subjectsData?.resultObject}
                                />
                            </motion.div>
                        )}
                    </div>
                </div>
            </div>

            {showGenerateModal && (
                <GenerateContentModal
                    courseName={formData.name}
                    courseDescription={formData.description}
                    numChapters={5}
                    onSave={handleGeneratedContent}
                    onClose={() => setShowGenerateModal(false)}
                />
            )}
        </div>
    );
};

export default CourseEditor;
