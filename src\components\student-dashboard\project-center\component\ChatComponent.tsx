import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Send } from 'lucide-react';

interface ChatMessage {
  id: string;
  text: string;
  sender: string; // Could be 'user' or 'other'
  avatarUrl: string;
}

const initialMessages: ChatMessage[] = []; // Chat will start empty

// interface ChatComponentProps removed as onClose is no longer needed

const ChatComponent: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>(initialMessages);
  const [newMessage, setNewMessage] = useState('');
  const messagesEndRef = useRef<null | HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (newMessage.trim() === '') return;
    const message: ChatMessage = {
      id: String(messages.length + 1),
      text: newMessage,
      sender: 'user', // Assuming the current user is sending
      avatarUrl: `https://i.pravatar.cc/32?u=currentUser${Math.random()}`, // Placeholder for current user avatar
    };
    setMessages([...messages, message]);
    setNewMessage('');
  };

  return (
    <div className=" flex flex-col h-[400px]  bg-white">
      {/* Chat Header (Optional - can be added if needed) */}
      {/* <div className="p-3 border-b border-gray-200">
        <h3 className="font-semibold text-gray-700">Group Chat</h3>
      </div> */}
      
      {/* Messages Area */}
      <div className="flex-grow p-4 space-y-4 overflow-y-auto bg-white ">
        {messages.map((msg) => (
          <div
            key={msg.id}
            className={`flex items-start gap-3 ${msg.sender === 'user' ? 'justify-end' : ''}`}
          >
            {msg.sender !== 'user' && (
              <img src={msg.avatarUrl} alt="avatar" className="w-8 h-8 rounded-full border border-[#347468]" />
            )}
            <div
              className={`max-w-[70%] p-3 rounded-[8px]  ${ 
                msg.sender === 'user'
                  ? 'bg-[#F0F0F0] text-[#2d2d2d]' // Updated user message style
                  : 'bg-gray-200 text-[#2d2d2d] rounded-[8px] rounded-bl-none' // Kept speech bubble for other sender, ensured base rounded-lg
              }`}
            >
              {msg.text}
            </div>
            {msg.sender === 'user' && (
              <img src={msg.avatarUrl} alt="avatar" className="w-8 h-8 rounded-full border border-[#347468]" />
            )}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-3  bg-white">
        <div className="relative flex items-center w-full"> {/* Parent div for relative positioning */}
          <Input
            type="text"
            placeholder="Send Message"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            className="flex-grow rounded-full px-4 py-2 pr-12 border-gray-300 " /* Added pr-12 for button space */
          />
          <Button 
            onClick={handleSendMessage} 
            className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-transparent hover:bg-gray-100 p-2 rounded-full text-gray-600 hover:text-blue-500" /* Positioned button */
            variant="ghost"
            size="icon"
            disabled={!newMessage.trim()}
          >
            <Send className="w-5 h-5" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatComponent;
