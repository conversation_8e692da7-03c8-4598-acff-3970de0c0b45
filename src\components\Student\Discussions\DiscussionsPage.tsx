import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Plus, MessageSquareText, Filter, TrendingUp, Users, Settings } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { 
  useGetDiscussionsQuery, 
  useGetDiscussionByIdQuery, 
  useGetDiscussionCommentsQuery,
} from '@/services/discussionsAPIjs'
import { Discussion } from './types'
import { DiscussionSearch } from './discussion-search'
import { DiscussionTabs } from './discussion-tabs'
import { DiscussionDetail } from './discussion-detail'
import { DiscussionLeaderboard } from './leaderboard'
import { NewDiscussionDialog } from './new-discussion-dialog'
import { useSelector } from 'react-redux'
import { 
  useLazyGetTopicsQuery,
  useLazyGetCourseDetailsDataQuery,
  useLazyGetContentByTopicIdQuery,
  useGetAdminCoursesQuery
} from '@/APIConnect'
import { useAuth0 } from '@auth0/auth0-react'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

const DiscussionsPage = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [filter, setFilter] = useState('all')
  const [showNewDiscussionDialog, setShowNewDiscussionDialog] = useState(false)
  const [selectedDiscussion, setSelectedDiscussion] = useState<Discussion | null>(null)
  const [page, setPage] = useState(1)
  const [limit] = useState(20)
  const [isTeacher, setIsTeacher] = useState(false)

  const navigate = useNavigate()

  // Get user data from Redux store
  const user = useSelector((state: any) => state.user)
  const schoolId = user?.schoolId

  const { user: auth0User } = useAuth0();
  const roleId = auth0User?.['http://learnido-app/roleId'] ?? '4'
  
  // Determine if user is teacher or admin
  useEffect(() => {
    if(!auth0User) return;
    setIsTeacher(roleId === '1' || roleId === '2' || roleId === '3');
  }, [auth0User, roleId]);

  // Fetch discussions with filters
  const { data: discussionsData, isLoading: isLoadingDiscussions } = useGetDiscussionsQuery({
    page,
    limit,
    searchQuery,
    filter,
  })

  // Fetch selected discussion and its comments
  const { data: selectedDiscussionData } = useGetDiscussionByIdQuery(
    selectedDiscussion?.id || '',
    { skip: !selectedDiscussion }
  )

  const { data: commentsData, refetch: refetchComments } = useGetDiscussionCommentsQuery(
    { discussionId: selectedDiscussion?.id || '', page: 1, limit: 20 },
    { skip: !selectedDiscussion }
  )

  // State for dropdown selections
  const [selectedCourseId, setSelectedCourseId] = useState<string>('')
  const [selectedChapterId, setSelectedChapterId] = useState<string>('')
  const [selectedTopicId, setSelectedTopicId] = useState<string>('')

  // Fetch data for NewDiscussionDialog
  const { data: courses, isLoading: isLoadingCourses, refetch: refetchCourses } = useGetAdminCoursesQuery ('');
  
  const [getChapters, { data: chapters, isLoading: isLoadingChapters }] = useLazyGetCourseDetailsDataQuery()
  const [getTopics, { data: topics, isLoading: isLoadingTopics }] = useLazyGetTopicsQuery()
  const [getSubtopics, { data: subtopics, isLoading: isLoadingSubtopics }] = useLazyGetContentByTopicIdQuery()

  // Ensure courses are loaded when the component mounts
  useEffect(() => {
    if (schoolId) {
      refetchCourses();
    }
  }, [schoolId, refetchCourses]);

  // Also ensure courses are loaded when the dialog opens
  const handleOpenNewDiscussionDialog = () => {
    if (schoolId) {
      refetchCourses();
    }
    setShowNewDiscussionDialog(true);
  }

  // Navigate to discussion management
  const handleNavigateToManagement = () => {
    navigate('/discussions/management');
  }

  // Handlers for fetching related data for discussions
  const handleCourseSelect = async (courseId: string) => {
    setSelectedCourseId(courseId);
    setSelectedChapterId('');
    setSelectedTopicId('');
    
    if (courseId) {
      await getChapters({ courseId });
      await getTopics({ courseId });
      await getSubtopics({ courseId });      
    }
  }

  const handleChapterSelect = async (chapterId: string) => {
    setSelectedChapterId(chapterId);
    setSelectedTopicId('');
    
    if (chapterId && selectedCourseId) {
      await getTopics({
        courseId: selectedCourseId,
        chapterId
      });
    }
  }

  const handleTopicSelect = async (topicId: string) => {
    setSelectedTopicId(topicId);
    
    if (topicId) {
      await getSubtopics(topicId);
    }
  }

  const handleSubtopicSelect = async (subtopicId: string) => {
    // Handle any specific logic needed when a subtopic is selected
  }

  const handleCommentSuccess = () => {
    refetchComments()
  }

  // Animation variants
  const pageVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        when: "beforeChildren",
        staggerChildren: 0.15,
        duration: 0.3
      } 
    },
    exit: { 
      opacity: 0,
      transition: { duration: 0.2 } 
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0, 
      transition: { 
        type: "spring",
        stiffness: 100,
        damping: 15,
        duration: 0.5 
      } 
    }
  };

  const getInitials = (name = 'User Name') => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="min-h-screen bg-background p-4 md:p-6 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute -top-24 -right-24 w-64 h-64 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute top-1/2 -left-32 w-80 h-80 bg-muted/20 rounded-full blur-3xl"></div>
      
      <AnimatePresence mode="wait">
        {selectedDiscussion && selectedDiscussionData ? (
          <motion.div
            key="detail"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="max-w-4xl mx-auto relative z-10"
          >
            <DiscussionDetail
              discussion={selectedDiscussionData}
              comments={commentsData?.items || []}
              onBack={() => setSelectedDiscussion(null)}
              onCommentSuccess={handleCommentSuccess}
            />
          </motion.div>
        ) : (
          <motion.div
            variants={pageVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="container mx-auto relative z-10"
          >
            <motion.div 
              variants={itemVariants}
              className="mb-8 text-center"
            >
              <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2">
                Student Discussions
              </h1>
              <p className="text-muted-foreground max-w-lg mx-auto">
                Connect with peers, explore new ideas, and deepen your understanding through collaborative learning
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <motion.div variants={itemVariants} className="flex flex-col gap-6">
                  <Card className="border shadow-sm">
                    <CardHeader className="pb-3 border-b">
                      <div className="flex justify-between items-center flex-wrap gap-2">
                        <CardTitle className="flex items-center text-xl font-bold">
                          <MessageSquareText className="h-5 w-5 mr-2 text-primary" />
                          Discussions
                        </CardTitle>
                        <div className="flex gap-2">
                          {/* Show the "Manage Discussions" button only for teachers and admins */}
                          {isTeacher && (
                            <Button 
                              onClick={handleNavigateToManagement} 
                              variant="outline"
                              size="sm"
                              className="hidden md:flex items-center"
                            >
                              <Settings className="h-4 w-4 mr-2" />
                              Manage Discussions
                            </Button>
                          )}
                          
                          {/* Show the "New Discussion" button for non-students or for admins */}
                          {(roleId !== '4' || roleId === '1') && (
                            <Button 
                              onClick={handleOpenNewDiscussionDialog} 
                              variant="default"
                              size="sm"
                              className="rounded-md"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              New Discussion
                            </Button>
                          )}
                        </div>
                      </div>
                      <CardDescription className="text-muted-foreground mt-1">
                        Engage in thought-provoking discussions with your peers and teachers
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pb-6 pt-4">
                      <DiscussionSearch
                        searchQuery={searchQuery}
                        onSearchChange={setSearchQuery}
                        filter={filter}
                        onFilterChange={setFilter}
                      />
                    </CardContent>
                  </Card>
                  
                  <Card className="border shadow-sm overflow-hidden">
                    <CardContent className="p-0">
                      <Tabs defaultValue="all" className="w-full">
                        <TabsList className="w-full justify-start p-2 bg-muted/30 border-b">
                          <TabsTrigger value="all">
                            All Discussions
                          </TabsTrigger>
                          <TabsTrigger value="trending">
                            <TrendingUp className="w-4 h-4 mr-1" /> 
                            Popular
                          </TabsTrigger>
                        </TabsList>
                        <TabsContent value="all" className="m-0 focus-visible:outline-none focus-visible:ring-0">
                          <DiscussionTabs
                            discussions={discussionsData?.items || []}
                            isLoading={isLoadingDiscussions}
                            onDiscussionClick={setSelectedDiscussion}
                            filter={filter}
                            searchQuery={searchQuery}
                          />
                        </TabsContent>
                        <TabsContent value="trending" className="m-0 p-4 focus-visible:outline-none focus-visible:ring-0">
                          {!isLoadingDiscussions && discussionsData?.items?.length > 0 ? (
                            <div className="space-y-4 p-2">
                              {[...discussionsData.items]
                                .slice(0, 5)
                                .map((discussion) => (
                                  <div 
                                    key={discussion.id} 
                                    className="p-4 border rounded-lg hover:bg-muted/30 transition-colors cursor-pointer"
                                    onClick={() => setSelectedDiscussion(discussion)}
                                  >
                                    <div className="flex items-center gap-2 mb-2">
                                      <Badge variant="outline" className="bg-primary/10">
                                        {discussion.category || 'General'}
                                      </Badge>
                                      <span className="text-xs text-muted-foreground">
                                        {new Date(discussion.createdAt).toLocaleDateString()}
                                      </span>
                                    </div>
                                    <h3 className="font-medium mb-1">{discussion.title}</h3>
                                    <p className="text-sm text-muted-foreground line-clamp-2">
                                      {discussion.content}
                                    </p>
                                  </div>
                                ))}
                            </div>
                          ) : (
                            <div className="flex flex-col items-center justify-center py-10 text-center">
                              <TrendingUp className="h-12 w-12 text-muted-foreground mb-3" />
                              <h3 className="text-xl font-medium mb-1">No discussions yet</h3>
                              <p className="text-muted-foreground max-w-md">
                                Be the first to start a discussion
                              </p>
                            </div>
                          )}
                        </TabsContent>
                      </Tabs>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>

              <div className="space-y-6">
                <motion.div
                  variants={itemVariants}
                  whileHover={{ scale: 1.01 }}
                  className="relative"
                >
                  <Card className="border shadow-sm">
                    <CardHeader className="border-b pb-3">
                      <CardTitle className="text-lg font-semibold flex items-center">
                        <Users className="h-5 w-5 mr-2 text-primary" />
                        Top Contributors
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                      {/* Replaced the old implementation with DiscussionLeaderboard and adjusted height */}
                      <DiscussionLeaderboard />
                      
                      {/* Only show top users if data is available */}
                      {!isLoadingDiscussions && discussionsData?.items?.length > 0 && (
                        <div className="px-4 py-3 space-y-2 max-h-[180px] overflow-auto">
                          <h4 className="text-sm font-medium mb-2 text-muted-foreground">Active This Month</h4>
                          {Array.from(new Set<any>(discussionsData.items.map((d:any) => d.userId)))
                            .slice(0, 3)
                            .map((userId, i) => {
                              const discussionCount = discussionsData.items.filter((d:any) => d.userId === userId).length;
                              const userName = discussionsData.items.find((d:any) => d.userId === userId)?.userName || 'User';
                              
                              return (
                                <div key={userId || i} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/20 transition-colors">
                                  <Avatar className="h-8 w-8 border">
                                    <AvatarFallback>{getInitials(userName)}</AvatarFallback>
                                  </Avatar>
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium truncate">
                                      {userName}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                      {discussionCount} {discussionCount === 1 ? 'discussion' : 'discussions'}
                                    </p>
                                  </div>
                                  <div className="inline-flex items-center bg-primary/10 px-2 py-0.5 rounded-full text-xs font-medium">
                                    #{i + 1}
                                  </div>
                                </div>
                              );
                            })}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Recent Activity */}
                <motion.div
                  variants={itemVariants}
                  whileHover={{ scale: 1.01 }}
                >
                  <Card className="border shadow-sm">
                    <CardHeader className="border-b pb-3">
                      <CardTitle className="text-lg font-semibold flex items-center">
                        <MessageSquareText className="h-5 w-5 mr-2 text-primary" />
                        Recent Discussions
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="relative space-y-3 mt-2">
                        {!isLoadingDiscussions && discussionsData?.items?.length > 0 ? (
                          [...discussionsData.items]
                            .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                            .slice(0, 3)
                            .map((discussion) => (
                              <div 
                                key={discussion.id} 
                                className="flex items-start gap-3 cursor-pointer hover:bg-muted/20 p-2 rounded-md transition-colors"
                                onClick={() => setSelectedDiscussion(discussion)}
                              >
                                <Avatar className="h-8 w-8">
                                  <AvatarFallback>{getInitials(discussion.userName)}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <p className="text-sm font-medium">
                                    {discussion.userName || 'User'}
                                  </p>
                                  <p className="text-xs line-clamp-1">{discussion.title}</p>
                                  <p className="text-xs text-muted-foreground mt-1">
                                    {new Date(discussion.createdAt).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>
                            ))
                        ) : isLoadingDiscussions ? (
                          Array.from({ length: 3 }).map((_, i) => (
                            <div key={i} className="flex items-start gap-3">
                              <Skeleton className="h-8 w-8 rounded-full" />
                              <div className="w-full">
                                <Skeleton className="h-4 w-3/4 mb-2" />
                                <Skeleton className="h-3 w-1/2" />
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="flex flex-col items-center justify-center py-6 text-center">
                            <MessageSquareText className="h-8 w-8 text-muted-foreground mb-2" />
                            <p className="text-muted-foreground">No discussions yet</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mobile Action Buttons */}
      {!selectedDiscussion && (
        <div className="fixed bottom-6 right-6 flex flex-col space-y-2 md:hidden z-50">
          {/* Show the "Manage Discussions" button only for teachers and admins */}
          {isTeacher && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ 
                type: "spring", 
                stiffness: 260, 
                damping: 20,
                delay: 0.6 
              }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Button
                onClick={handleNavigateToManagement}
                size="icon"
                variant="outline"
                className="h-12 w-12 rounded-full shadow-md bg-secondary"
              >
                <Settings className="h-5 w-5" />
                <span className="sr-only">Manage Discussions</span>
              </Button>
            </motion.div>
          )}
          
          {/* Show the "New Discussion" button for non-students or for admins */}
          {(roleId !== '4' || roleId === '1') && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ 
                type: "spring", 
                stiffness: 260, 
                damping: 20,
                delay: 0.5 
              }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Button
                onClick={handleOpenNewDiscussionDialog}
                size="icon"
                className="h-14 w-14 rounded-full shadow-md"
              >
                <Plus className="h-6 w-6" />
                <span className="sr-only">New Discussion</span>
              </Button>
            </motion.div>
          )}
        </div>
      )}

      <NewDiscussionDialog
        open={showNewDiscussionDialog}
        onOpenChange={setShowNewDiscussionDialog}
        courses={courses?.resultObject || []}
        classrooms={[]}
        topics={topics?.resultObject || []}
        modules={chapters?.resultObject?.chapters || []}
        subtopics={subtopics?.resultObject || []}
        onCourseSelect={handleCourseSelect}
        onClassSelect={handleChapterSelect}
        onTopicSelect={handleTopicSelect}
        onModuleSelect={handleChapterSelect}
        onSubtopicSelect={handleSubtopicSelect}
      />
    </div>
  )
}

export default DiscussionsPage