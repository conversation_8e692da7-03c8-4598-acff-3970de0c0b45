/* Base styles */
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;700&display=swap');

.outContainer {
  display: flex;
  min-height: 100vh;
  overflow: hidden;
}

.outContainer .sidebar {
  width: 200px;
  background-color: #ffffff;
  padding: 10px;
  border-right: 1px solid #ddd;
  box-shadow: 4px 0 6px rgba(0, 0, 0, 0.1); 
  font-family: 'DM Sans', sans-serif;
}

.outContainer .sidebar button {
  display: block;
  width: 100%;
  margin-bottom: 10px;
  padding: 10px;
  border: none;
  background-color: #f0f4ff; 
  color: #2847B2;
  cursor: pointer;
  text-align: left;
  font-weight: bold;
  font-family: 'DM Sans', sans-serif;
  transition: background-color 0.3s, color 0.3s; 
}

.outContainer .sidebar button:hover {
  background-color: #e0e6ff; 
  color: #1d2b64; 
}

.outContainer .sidebar button:focus,
.outContainer .sidebar button.active {
  background-color: #1d2b64; 
  color: white; 
}

.outContainer .form-area {
  flex-grow: 1;
  padding: 20px;
  overflow: visible;
}

.outContainer .create-button {
  color: #ffffff;
  border: 1px solid #2847B2;
  background-color: #2847B2;
  padding: 10px 20px;
  cursor: pointer;
  margin-bottom: 20px;
  font-weight: bold;
  font-family: 'DM Sans', sans-serif;
  transition: background-color 0.3s, color 0.3s;
}

.outContainer .create-button:hover {
  background-color: #ffffff;
  color: #2847B2;
}

.outContainer .creation-form {
  display: none;
  opacity: 0;
  max-height: 0;
  overflow: visible;
  transition: opacity 0.5s ease-in-out, max-height 0.5s ease-in-out;
}

.outContainer .creation-form.show {
  display: block;
  opacity: 1;
  overflow: visible;
  max-height: 1000px; 
  animation: slideDown 0.5s ease-in-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.outContainer .form-group {
  margin-bottom: 15px;
}

.outContainer .form-label {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
  color: #333;
  overflow: visible;
  font-family: 'DM Sans', sans-serif;
}

.outContainer .compulsoryInd {
  color: red;
  font-weight: bold;
}

.outContainer .form-input,
.outContainer .select-input,
.outContainer .text-area {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 5px;
  box-sizing: border-box;
  font-family: 'DM Sans', sans-serif;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.outContainer .form-input:focus,
.outContainer .select-input:focus,
.outContainer .text-area:focus {
  border-color: #2847b2;
  outline: none;
}

.outContainer .form-error {
  color: red;
  font-size: 12px;
  text-decoration: underline;
}

.outContainer .form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.outContainer .form-submit,
.outContainer .form-cancel {
  padding: 10px 20px;
  border: 1px solid;
  border-radius: 5px;
  background-color: white;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s, color 0.3s;
  font-family: 'DM Sans', sans-serif;
}

.outContainer .form-submit {
  background-color: green;
  color: white;
}

.outContainer .form-submit:hover {
  color: green;
  background-color: white;
}

.outContainer .form-cancel {
  color: red;
  border-color: red;
}

.outContainer .form-cancel:hover {
  background-color: red;
  color: white;
}

.outContainer .table-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.outContainer .styled-table {
  width: 100%;
  font-size: 14px;
  border-collapse: collapse;
  font-family: 'DM Sans', sans-serif;
}

.outContainer .styled-table thead tr {
  background-color: #f0f4ff;
  text-align: left;
}

.outContainer .styled-table th {
  padding: 12px 15px;
  text-align: left;
  color: #2847B2;
}

.outContainer .styled-table td {
  padding: 12px 15px;
  text-align: left;
  color: #00000099;
}

.outContainer .styled-table tbody tr {
  border-bottom: 1px solid #dddddd;
}

.outContainer .styled-table tbody tr:hover {
  background-color: #f3f3f3;
}

.outContainer .icon-button {
  padding: 8px 16px;
  font-size: 20px;
  color: red;
  cursor: pointer;
  background: none;
  border: none;
}

/* Media Queries for Responsive Design */

/* Mobile devices (width < 768px) */
@media (max-width: 767px) {
  .outContainer {
    flex-direction: column;
  }

  .outContainer .sidebar {
    width: 100%;
    border-right: none; 
    border-bottom: 1px solid #ddd;
    box-shadow: none; 
    margin-bottom: 20px;
  }

  .outContainer .sidebar button {
    font-size: 14px;
  }

  .outContainer .form-area {
    padding: 10px;
  }

  .outContainer .create-button {
    width: 100%;
  }

  .outContainer .creation-form {
    max-height: none; 
  }

  .outContainer .form-submit,
  .outContainer .form-cancel {
    width: 100%;
  }

  .outContainer .styled-table {
    font-size: 12px;
  }
}

/* Tablet devices (width < 1024px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .outContainer .sidebar {
    width: 150px;
  }

  .outContainer .form-area {
    padding: 15px;
  }

  .outContainer .create-button {
    width: auto;
  }
   
  .outContainer .styled-table {
    font-size: 13px;
}
}

.outContainer .styled-table  .noDataFound {
  text-align: center;
}