import React, { useState, useMemo } from 'react';
import { Plus, Trash, FolderPlus, Upload, Search, Download, Calendar, File } from 'lucide-react';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { useFileUpload } from '@/lib/utils';
import { Progress } from '@/components/ui/progress';

interface Document {
    id: string;
    name: string;
    folder: string;
    size: string;
    type: string;
    url?: string;
    createdAt: Date;
}

const TeacherMySpaceDialog = (
    { iconOnly = false }: { iconOnly?: boolean }
) => {
    const [documents, setDocuments] = useState<Document[]>([]);
    const [folders, setFolders] = useState<string[]>(['Assignments', 'Resources', 'Notes']);
    const [newFolder, setNewFolder] = useState('');
    const [selectedFolder, setSelectedFolder] = useState('All');
    const [searchQuery, setSearchQuery] = useState('');
    const [isUploading, setIsUploading] = useState(false);
    const fileInputRef = React.useRef<HTMLInputElement>(null);
    const { toast } = useToast();
    const { uploadFile, progress } = useFileUpload();
    const isMobile = window.innerWidth < 768;

    const addFolder = () => {
        if (newFolder && !folders.includes(newFolder)) {
            setFolders([...folders, newFolder]);
            setNewFolder('');
            toast({
                title: "Folder Created",
                description: `${newFolder} folder has been created successfully.`,
            });
        }
    };

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (files) {
            setIsUploading(true);
            try {
                for (const file of Array.from(files)) {
                    const fileName = `${selectedFolder === 'All' ? 'Resources' : selectedFolder}/${Date.now()}-${file.name}`;
                    const url = await uploadFile(file, fileName);

                    const newDoc: Document = {
                        id: Date.now().toString(),
                        name: file.name,
                        folder: selectedFolder === 'All' ? 'Resources' : selectedFolder,
                        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
                        type: file.type || 'Unknown',
                        url: url,
                        createdAt: new Date(),
                    };
                    setDocuments(prev => [newDoc, ...prev]);
                }
                toast({
                    title: "Upload Successful",
                    description: "Files have been uploaded successfully.",
                });
            } catch (error) {
                toast({
                    title: "Upload Failed",
                    description: "There was an error uploading your files.",
                    variant: "destructive",
                });
            } finally {
                setIsUploading(false);
            }
        }
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const deleteDocument = (id: string) => {
        setDocuments(documents.filter((doc) => doc.id !== id));
        toast({
            title: "File Deleted",
            description: "The file has been removed from your space.",
        });
    };

    const filteredDocuments = useMemo(() => {
        let filtered = documents;
        if (selectedFolder !== 'All') {
            filtered = filtered.filter((doc) => doc.folder === selectedFolder);
        }
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            filtered = filtered.filter(
                doc =>
                    doc.name.toLowerCase().includes(query) ||
                    doc.type.toLowerCase().includes(query)
            );
        }
        return filtered;
    }, [documents, selectedFolder, searchQuery]);

    const handleDownload = (doc: Document) => {
        if (doc.url) {
            window.open(doc.url, '_blank');
        }
    };

    return (
        <Sheet>
            <SheetTrigger asChild>
                <Button variant="ghost" className="w-full justify-start">
                    <File className="h-4 w-4 mr-2" />
                    {!iconOnly && "My Space"}
                </Button>
            </SheetTrigger>
            <SheetContent
                side="right"
                className={cn(
                    "p-0",
                    isMobile ? "w-full h-[85vh]" : "w-[400px] sm:w-[540px]"
                )}
            >
                <SheetHeader className="p-6 pb-0">
                    <SheetTitle className="flex items-center text-2xl font-bold">
                        <File className="h-6 w-6 mr-2" /> Teacher's Space
                    </SheetTitle>
                </SheetHeader>
                <div className="p-6 space-y-6">
                    <div className="space-y-4 bg-muted/30 p-4 rounded-2xl">
                        <div className="flex items-center gap-2">
                            <Input
                                placeholder="New Folder"
                                value={newFolder}
                                onChange={(e) => setNewFolder(e.target.value)}
                                className="rounded-xl border-muted"
                            />
                            <Button onClick={addFolder} size="icon" className="rounded-xl">
                                <FolderPlus className="h-4 w-4" />
                            </Button>
                        </div>
                        <div className="flex justify-center">
                            <input
                                type="file"
                                ref={fileInputRef}
                                onChange={handleFileUpload}
                                className="hidden"
                                multiple
                            />
                            <Button
                                onClick={() => fileInputRef.current?.click()}
                                className="w-full rounded-xl"
                                disabled={isUploading}
                            >
                                <Upload className="w-4 h-4 mr-2" />
                                {isUploading ? 'Uploading...' : 'Upload Files'}
                            </Button>
                        </div>
                        {isUploading && (
                            <Progress value={progress} className="w-full h-2" />
                        )}
                    </div>

                    <div className="space-y-4">
                        <div className="flex gap-4">
                            <div className="w-1/3">
                                <Select value={selectedFolder} onValueChange={setSelectedFolder}>
                                    <SelectTrigger className="rounded-xl border-muted">
                                        <SelectValue placeholder="Filter by folder" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="All">All Folders</SelectItem>
                                        {folders.map((folder) => (
                                            <SelectItem key={folder} value={folder}>
                                                {folder}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex-1">
                                <Input
                                    type="search"
                                    placeholder="Search files..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="rounded-xl border-muted"
                                />
                            </div>
                        </div>

                        <ScrollArea className={cn("pr-4", isMobile ? "h-[45vh]" : "h-[600px]")}>
                            {filteredDocuments.length === 0 ? (
                                <div className="flex flex-col items-center justify-center h-[200px] text-muted-foreground">
                                    <Search className="w-8 h-8 mb-2" />
                                    <p>No files found</p>
                                </div>
                            ) : (
                                filteredDocuments.map((doc) => (
                                    <Card key={doc.id} className="mb-4 rounded-2xl border-none bg-muted/30 hover:bg-muted/50 transition-colors">
                                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                            <div className="flex items-center gap-2 min-w-0 flex-1 pr-2">
                                                <File className="h-4 w-4" />
                                                <CardTitle className="text-lg font-semibold truncate">{doc.name}</CardTitle>
                                            </div>
                                            <div className="flex gap-2 flex-shrink-0">
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => handleDownload(doc)}
                                                    className="hover:bg-blue-100 hover:text-blue-600 rounded-xl"
                                                >
                                                    <Download className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => deleteDocument(doc.id)}
                                                    className="hover:bg-red-100 hover:text-red-600 rounded-xl"
                                                >
                                                    <Trash className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="flex justify-between items-center">
                                                <div className="flex gap-2">
                                                    <span className="text-xs px-2 py-1 bg-primary/10 text-primary rounded-full flex items-center">
                                                        <FolderPlus className="w-3 h-3 mr-1" />
                                                        {doc.folder}
                                                    </span>
                                                    <span className="text-xs px-2 py-1 bg-muted text-muted-foreground rounded-full">
                                                        {doc.size}
                                                    </span>
                                                </div>
                                                <span className="text-xs text-muted-foreground flex items-center">
                                                    <Calendar className="w-3 h-3 mr-1" />
                                                    {doc.createdAt.toLocaleDateString()}
                                                </span>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))
                            )}
                        </ScrollArea>
                    </div>
                </div>
            </SheetContent>
        </Sheet>
    );
};

export default TeacherMySpaceDialog;
