import { useState, useEffect } from 'react'
import { Check, X, Clock, AlertCircle, ChevronDown, ThumbsUp } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import {
  useGetPendingModerationQuery,
  useGetModerationStatsQuery,
  useApproveCommentMutation,
  useRejectCommentMutation,
} from '@/services/discussionsAPIjs'
import { toast } from '@/hooks/use-toast'
import { Comment } from '@/components/Student/Discussions/types'
import { Badge } from '@/components/ui/badge'
import {motion } from 'framer-motion'

export function TeacherModerationView() {
  const [page, setPage] = useState(1)
  const { data: pendingModeration, isLoading } = useGetPendingModerationQuery({
    page,
    limit: 10,
  })
  const { data: stats } = useGetModerationStatsQuery({})
  const [approveComment] = useApproveCommentMutation()
  const [rejectComment] = useRejectCommentMutation()

  const handleApprove = async (id: string) => {
    try {
      await approveComment(id).unwrap()
      toast({
        title: "Success",
        description: "Comment approved successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to approve comment",
        variant: "destructive",
      })
    }
  }

  const handleReject = async (id: string) => {
    try {
      await rejectComment(id).unwrap()
      toast({
        title: "Success",
        description: "Comment rejected successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reject comment",
        variant: "destructive",
      })
    }
  }

  const renderScoreBadge = (score: number) => {
    const color = score >= 0.7 ? 'success' : score >= 0.4 ? 'warning' : 'destructive'
    return (
      <Badge variant={color as any}>
        {Math.round(score * 100)}%
      </Badge>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Content Moderation</h1>
        <Badge variant="outline">
          {stats?.counts.pending || 0} Pending Reviews
        </Badge>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <h3 className="font-medium text-muted-foreground mb-2">Overall Stats</h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span>Approved</span>
              <Badge variant="default">{stats?.counts.approved || 0}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Pending</span>
              <Badge>{stats?.counts.pending || 0}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Rejected</span>
              <Badge variant="destructive">{stats?.counts.rejected || 0}</Badge>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <h3 className="font-medium text-muted-foreground mb-2">Average Scores</h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span>AI Score</span>
              {renderScoreBadge(stats?.averages.aiScore || 0)}
            </div>
            <div className="flex justify-between items-center">
              <span>Sentiment</span>
              {renderScoreBadge(stats?.averages.sentimentScore || 0)}
            </div>
            <div className="flex justify-between items-center">
              <span>Accuracy</span>
              {renderScoreBadge(stats?.averages.accuracyScore || 0)}
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <h3 className="font-medium text-muted-foreground mb-2">Moderation Queue</h3>
          <div className="space-y-4">
            <Progress value={(stats?.counts.pending || 0) / (stats?.counts.total || 1) * 100} />
            <p className="text-sm text-muted-foreground">
              {stats?.counts.pending || 0} items waiting for review
            </p>
          </div>
        </Card>
      </div>

      {/* Pending Comments */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Pending Reviews</h2>
        {isLoading ? (
          <div className="flex justify-center py-8">
            <span className="loading loading-spinner loading-lg"></span>
          </div>
        ) : (
          <div className="space-y-4">
            {pendingModeration?.moderationItems?.map((comment: Comment) => (
              <motion.div
                key={comment.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                layout
              >
                <Card className="p-4">
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <p className="mb-2">{comment.content}</p>
                      <div className="flex gap-2 text-sm">
                        {comment.flaggedTerms.map((term) => (
                          <Badge key={term} variant="destructive">
                            {term}
                          </Badge>
                        ))}
                      </div>
                      <div className="mt-2 text-sm text-muted-foreground">
                        Posted {new Date(comment.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="flex flex-col gap-2 items-end">
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-24"
                          onClick={() => handleApprove(comment.id)}
                        >
                          <Check className="w-4 h-4 mr-2" />
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          className="w-24"
                          onClick={() => handleReject(comment.id)}
                        >
                          <X className="w-4 h-4 mr-2" />
                          Reject
                        </Button>
                      </div>
                      <div className="flex gap-2">
                        {renderScoreBadge(comment.aiScore)}
                        <Badge variant={comment.sentimentScore >= 0.5 ? 'default' : 'destructive'}>
                          <ThumbsUp className="w-3 h-3 mr-1" />
                          {Math.round(comment.sentimentScore * 100)}%
                        </Badge>
                      </div>
                      <div className="mt-2 text-xs text-muted-foreground">
                        {comment.toneAnalysis && (
                          <div className="flex gap-2">
                            <span>Tone:</span>
                            {Object.entries(comment.toneAnalysis).map(([tone, score]) => (
                              <Badge key={tone} variant="secondary">
                                {tone}: {Math.round(score * 100)}%
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}

            {pendingModeration?.moderationItems?.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <AlertCircle className="mx-auto h-12 w-12 mb-4 opacity-20" />
                <p>No comments waiting for moderation</p>
              </div>
            )}
          </div>
        )}

        {/* Pagination */}
        {pendingModeration?.pagination.pages > 1 && (
          <div className="flex justify-center gap-2 mt-6">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.min(pendingModeration?.pagination.pages, p + 1))}
              disabled={page === pendingModeration?.pagination.pages}
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}