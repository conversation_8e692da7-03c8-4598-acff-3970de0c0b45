import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { useAuth0 } from '@auth0/auth0-react';
import { Comment } from '../../services/discussionsApi';
import { useDeleteComment } from '../../hooks/useDiscussions';
import { Card, CardContent, CardHeader, CardFooter } from '../ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Button } from '../ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '../ui/alert-dialog';
import { Badge } from '../ui/badge';
import { MoreHorizontal, Thum<PERSON>Up, Thum<PERSON>Down, Trash } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface CommentItemProps {
  comment: Comment;
  onVote: (commentId: string, value: 1 | -1) => Promise<void>;
}

export function CommentItem({ comment, onVote }: CommentItemProps) {
  const { user } = useAuth0();
  const { deleteComment, loading: deleteLoading } = useDeleteComment();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isVoting, setIsVoting] = useState(false);

  // Get user role
  const userRole = user?.['https://learnido.com/role'] || 'Student';
  
  // Check if user can delete this comment (author or admin roles)
  const canDelete = 
    comment.authorId === user?.sub || 
    ['Admin', 'SchoolAdmin', 'Teacher'].includes(userRole);

  // Extract first letter for avatar fallback
  const getInitials = (name?: string) => {
    if (!name) return '?';
    return name.charAt(0).toUpperCase();
  };

  const handleDelete = async () => {
    await deleteComment(comment.id);
    setIsDeleteDialogOpen(false);
  };

  const handleVote = async (value: 1 | -1) => {
    if (isVoting) return;
    
    setIsVoting(true);
    try {
      await onVote(comment.id, value);
    } finally {
      setIsVoting(false);
    }
  };

  // Custom renderer for markdown images and videos
  const CustomMarkdownRenderer = ({ children }: { children: React.ReactNode }) => {
    return (
      <div className="prose max-w-full">
        <ReactMarkdown
          components={{
            img: ({ node, ...props }) => (
              <img 
                {...props} 
                className="max-w-full max-h-96 object-contain rounded-md my-2"
                loading="lazy"
              />
            ),
            // Custom handling for videos
            a: ({ node, ...props }) => {
              // Convert children to string safely
              const childText = Array.isArray(props.children) 
                ? props.children.join('') 
                : String(props.children || '');
                
              if (props.href?.startsWith('[VIDEO]') && childText.includes('VIDEO')) {
                const videoUrl = props.href.replace('[VIDEO](', '').replace(')', '');
                return (
                  <video 
                    controls 
                    className="max-w-full max-h-96 rounded-md my-2"
                    src={videoUrl}
                  >
                    Your browser does not support the video tag.
                  </video>
                );
              }
              return <a {...props} />;
            }
          }}
        >
          {children as string}
        </ReactMarkdown>
      </div>
    );
  };

  // Parse out any media from the comment content
  const processContent = (content: string) => {
    // Split content by markdown image or video patterns
    const parts = content.split(/(!?\[.*?\]\(.*?\))/g);
    return parts.map((part, index) => {
      if (part.match(/!\[.*?\]\(.*?\)/)) {
        // This is an image in markdown
        return <CustomMarkdownRenderer key={index}>{part}</CustomMarkdownRenderer>;
      } else if (part.match(/\[VIDEO\]\(.*?\)/)) {
        // This is a video in our custom format
        const videoUrl = part.match(/\[VIDEO\]\((.*?)\)/)?.[1];
        return videoUrl ? (
          <video 
            key={index}
            controls 
            className="max-w-full max-h-96 rounded-md my-2"
            src={videoUrl}
          >
            Your browser does not support the video tag.
          </video>
        ) : part;
      }
      // Regular text
      return part ? <p key={index} className="whitespace-pre-wrap">{part}</p> : null;
    });
  };

  return (
    <Card className="mb-4 hover:shadow-sm transition-shadow">
      <CardHeader className="flex flex-row items-start space-x-4 pb-2 pt-4">
        <Avatar className="w-10 h-10">
          <AvatarImage src={comment.authorPictureUrl} alt={comment.authorName} />
          <AvatarFallback>{getInitials(comment.authorName)}</AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="font-semibold text-sm">{comment.authorName}</h4>
              <p className="text-xs text-gray-500">
                {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
              </p>
            </div>
            
            {comment.status !== 'ACTIVE' && (
              <Badge 
                variant={comment.status === 'PENDING' ? 'outline' : 'destructive'}
                className="ml-2"
              >
                {comment.status}
              </Badge>
            )}
          </div>
        </div>
        
        {canDelete && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem 
                onClick={() => setIsDeleteDialogOpen(true)}
                className="text-red-600"
              >
                <Trash className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </CardHeader>
      <CardContent>
        {processContent(comment.content)}
      </CardContent>
      <CardFooter className="flex justify-between pt-1">
        <div className="flex space-x-2">
          <Button 
            variant="ghost" 
            size="sm" 
            className="text-gray-500 hover:text-green-600" 
            onClick={() => handleVote(1)}
            disabled={isVoting}
          >
            <ThumbsUp className="h-4 w-4 mr-1" />
            <span>{comment.voteCount > 0 ? comment.voteCount : ''}</span>
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            className="text-gray-500 hover:text-red-600" 
            onClick={() => handleVote(-1)}
            disabled={isVoting}
          >
            <ThumbsDown className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Only show evaluation for teachers/admins */}
        {['Admin', 'SchoolAdmin', 'Teacher'].includes(userRole) && comment.evaluation && (
          <div className="text-xs text-gray-500">
            Engagement: {comment.evaluation.engagement}/10
          </div>
        )}
      </CardFooter>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this comment.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete} 
              disabled={deleteLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteLoading ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}