import React from 'react';
import { ChevronRight } from 'lucide-react';

interface TopicRibbonProps {
    chapterName: string;
}

const TopicRibbon = ({ chapterName }: TopicRibbonProps) => {
    const parts = chapterName.split('/').map(part => part.trim());

    return (
        <div className="flex items-center text-sm overflow-hidden">
            {parts.map((part, index) => (
                <React.Fragment key={index}>
                    {index > 0 && (
                        <ChevronRight className="h-3.5 w-3.5 mx-1.5 text-muted-foreground flex-shrink-0" />
                    )}
                    <span
                        className={`truncate ${index === parts.length - 1
                                ? 'font-medium text-foreground'
                                : 'text-muted-foreground'
                            } ${index === 0
                                ? 'max-w-[120px] sm:max-w-[200px] md:max-w-[300px]'
                                : 'max-w-[150px] sm:max-w-[250px] md:max-w-[400px]'
                            }`}
                    >
                        {part}
                    </span>
                </React.Fragment>
            ))}
        </div>
    );
};

export default TopicRibbon;
