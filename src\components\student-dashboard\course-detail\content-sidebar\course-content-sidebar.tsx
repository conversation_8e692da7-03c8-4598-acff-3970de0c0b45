import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  ChevronDown,
  ChevronRight,
  LucideIcon,
  BookOpen,
  FileText,
  PlayCircle,
  Video,
  LockIcon,
  Bot,
  FileAudio,
  CheckCircle,
  CircleDot,
  Lightbulb,
  Code,
  HelpCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { Topic, SubTopic, Chapter, } from '../../types';

interface CourseContentSidebarProps {
  courseStructure: Chapter[]; // Using the Chapter type from types.ts
  onTopicSelect: (topic: Topic) => void;
  onSubTopicSelect: (subTopic: SubTopic) => void;
  onAssessmentSelect: (assessment: any) => void;
  currentTopicId: string | null;
  currentSubTopicId: string | null;
  isChapterLocked: (chapterId: string) => boolean;
  isTopicLocked: (topicId: string) => boolean;
  isSubTopicLocked: (subTopicId: string) => boolean;
  onJoinLiveSession: (classroomId: string) => void;
  isLoadingLiveSession: boolean;
  onAiMentorClick: () => void;
}

const getContentTypeLabel = (type: number) => {
  switch (type) {
    case 1: return "Text";
    case 2: return "SCORM";
    case 3: return "Video";
    case 4: return "Audio";
    default: return "Other";
  }
};



// Helper to determine icon based on content type and assignment presence
const getContentIcon = (t: number, hasAssignment?: boolean, hasQuiz?: boolean): LucideIcon => {
  // If there's an assignment or quiz, always show Lightbulb icon
  if (hasAssignment || hasQuiz) {
    return Lightbulb;
  }

  const type = getContentTypeLabel(t).toLowerCase();
  switch (type) {
    case 'video':
      return Video;
    case 'scorm':
      return PlayCircle;
    case 'audio':
      return FileAudio;
    case 'assessment':
      return Lightbulb;
    case 'html':
      return Code;
    case 'text':
      return BookOpen;
    case 'other':
    default:
      return HelpCircle;
  }
};

const CourseContentSidebar: React.FC<CourseContentSidebarProps> = ({
  courseStructure = [], // Provide default empty array to prevent mapping errors
  onTopicSelect,
  onSubTopicSelect,
  onAssessmentSelect,
  currentTopicId,
  currentSubTopicId,
  isChapterLocked,
  isTopicLocked,
  isSubTopicLocked,
  onJoinLiveSession,
  isLoadingLiveSession,
  onAiMentorClick,
}) => {
  // State for expanded chapters and topics
  const [openChapters, setOpenChapters] = useState<string[]>([]);
  const [openTopics, setOpenTopics] = useState<string[]>([]);

  // Calculate course completion percentage
  const calculateProgress = () => {
    // This is a placeholder - implement actual completion tracking
    return 35;
  };

  // Initialize with open chapter/topic based on current selection
  useEffect(() => {
    if (currentTopicId) {
      // Find which chapter contains this topic
      for (const chapter of courseStructure) {
        const hasTopic = chapter.topics.some(topic => topic.id === currentTopicId);
        if (hasTopic && !openChapters.includes(chapter.id)) {
          setOpenChapters(prev => [...prev, chapter.id]);
          break;
        }
      }
      
      // Open this topic if it has subtopics and current subtopic is selected
      if (currentSubTopicId && !openTopics.includes(currentTopicId)) {
        setOpenTopics(prev => [...prev, currentTopicId]);
      }
    }
  }, [currentTopicId, currentSubTopicId, courseStructure]);

  // Handle chapter expansion
  const handleChapterToggle = (chapterId: string) => {
    setOpenChapters(prev =>
      prev.includes(chapterId)
        ? prev.filter(id => id !== chapterId)
        : [...prev, chapterId]
    );
  };

  // Handle topic expansion (for subtopics)
  const handleTopicToggle = (topicId: string) => {
    setOpenTopics(prev =>
      prev.includes(topicId)
        ? prev.filter(id => id !== topicId)
        : [...prev, topicId]
    );
  };

  return (
    <div className="flex flex-col h-full">
      <div className="mb-4 pb-3 border-b">
        <h2 className="text-lg font-semibold text-slate-800">Course Content</h2>
        <div className="mt-3 flex items-center">
          <Progress value={calculateProgress()} className="h-1.5 flex-1" />
          <span className="ml-2 text-xs font-medium text-slate-600">{calculateProgress()}%</span>
        </div>
      </div>

      {/* Course structure accordion */}
      <ScrollArea className="flex-grow pr-2">
        <div className="space-y-3">
          {courseStructure && courseStructure.length > 0 ? (
            courseStructure.map((chapter, chapterIndex) => {
              const isLocked = isChapterLocked(chapter.id);
              const isExpanded = openChapters.includes(chapter.id);
              
              return (
                <div key={chapter.id} className="rounded-md border border-slate-200 overflow-hidden">
                  {/* Chapter Header */}
                  <div 
                    className={cn(
                      "flex items-center justify-between p-3 cursor-pointer",
                      isLocked ? "opacity-70 bg-slate-50" : "hover:bg-slate-50",
                      isExpanded && "bg-slate-50 border-b border-slate-200"
                    )}
                    onClick={() => !isLocked && handleChapterToggle(chapter.id)}
                  >
                    <div className="flex items-center">
                      {isLocked && <LockIcon className="h-3.5 w-3.5 mr-1.5 text-slate-400" />}
                      <span className="font-medium text-slate-800">
                        {chapter.name || `Module ${chapterIndex + 1}`}
                      </span>
                    </div>
                    <div className="flex items-center">
                      {/* Optional: Add chapter completion indicator */}
                      <span className="text-xs text-slate-500 mr-2">0/{chapter.topics.length}</span>
                      {isExpanded ? (
                        <ChevronDown className="h-5 w-5 text-slate-500" />
                      ) : (
                        <ChevronRight className="h-5 w-5 text-slate-500" />
                      )}
                    </div>
                  </div>
                  
                  {/* Chapter Content (Topics) */}
                  {isExpanded && (
                    <div className="px-3 py-2 divide-y divide-slate-100">
                      {chapter.topics && chapter.topics.length > 0 ? (
                        chapter.topics.map((topic) => {
                          const isTopicActive = currentTopicId === topic.id;
                          const isLocked = isTopicLocked(topic.id);
                          const hasSubTopics = topic.subTopics && topic.subTopics.length > 0;
                          const isTopicExpanded = openTopics.includes(topic.id);
                          
                          return (
                            <div key={topic.id} className="py-1.5">
                              {/* Topic Header */}
                              <div 
                                className={cn(
                                  "flex items-center rounded-md py-2 px-2 text-sm",
                                  isTopicActive ? "bg-emerald-50 text-emerald-700" : "hover:bg-slate-50",
                                  isLocked ? "cursor-not-allowed opacity-70" : "cursor-pointer"
                                )}
                                onClick={() => !isLocked && onTopicSelect(topic)}
                              >
                                <CircleDot className="h-4 w-4 mr-2 flex-shrink-0" />
                                <span className="truncate flex-grow font-medium">{topic.name}</span>
                                {isLocked && <LockIcon className="h-3.5 w-3.5 ml-1 text-slate-400" />}
                                {hasSubTopics && (
                                  <div 
                                    className="ml-2" 
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (!isLocked) handleTopicToggle(topic.id);
                                    }}
                                  >
                                    {isTopicExpanded ? (
                                      <ChevronDown className="h-4 w-4 flex-shrink-0 opacity-70" />
                                    ) : (
                                      <ChevronRight className="h-4 w-4 flex-shrink-0 opacity-70" />
                                    )}
                                  </div>
                                )}
                              </div>
                              
                              {/* SubTopics */}
                              {hasSubTopics && isTopicExpanded && (
                                <div className="ml-6 mt-1 space-y-1 pl-2 border-l-2 border-slate-100">
                                  {topic.subTopics.map((subTopic) => {
                                    const isSubTopicActive = currentSubTopicId === subTopic.id;
                                    const isSubLocked = isSubTopicLocked(subTopic.id);

                                    // Check for assignment
                                    const hasAssignment = !!subTopic.assignment;
                                    // Legacy quiz check for backward compatibility
                                    const hasQuiz = !!subTopic.quizId;

                                    const SubIcon = getContentIcon(subTopic.type ?? 1, hasAssignment, hasQuiz);



                                    return (
                                      <div
                                        key={subTopic.id}
                                        className={cn(
                                          "flex items-center py-1.5 px-2 rounded-md text-sm",
                                          isSubTopicActive ? "bg-blue-50 text-blue-700 font-medium" : "hover:bg-slate-50",
                                          isSubLocked ? "cursor-not-allowed opacity-70" : "cursor-pointer"
                                        )}
                                        onClick={() => {
                                          if (isSubLocked) return;
                                          if (hasAssignment || hasQuiz) {
                                            onAssessmentSelect(subTopic); // Treat as assignment/assessment
                                          } else {
                                            onSubTopicSelect(subTopic);
                                          }
                                        }}
                                      >
                                        <SubIcon className="h-3.5 w-3.5 mr-2 flex-shrink-0" />
                                        <span className="truncate flex-grow">{subTopic.subTopicName}</span>
                                        {isSubLocked && <LockIcon className="h-3 w-3 ml-1 text-slate-400" />}
                                        {false && (
                                          <CheckCircle className="h-3.5 w-3.5 ml-auto text-green-500" />
                                        )}
                                      </div>
                                    );
                                  })}
                                </div>
                              )}
                            </div>
                          );
                        })
                      ) : (
                        <div className="py-3 px-4 text-sm text-slate-500">No topics available</div>
                      )}
                    </div>
                  )}
                </div>
              );
            })
          ) : (
            <div className="py-4 text-center text-slate-500">No course content available</div>
          )}
        </div>
      </ScrollArea>
      
      {/* AI Mentor Button at bottom */}
      <Button 
        onClick={onAiMentorClick}
        variant="outline" 
        className="mt-4 w-full flex items-center justify-center gap-2 border-teal-600 text-teal-700 hover:bg-teal-50"
      >
        <Bot className="h-4 w-4" />
        <span>Ask AI Mentor</span>
      </Button>
    </div>
  );
};

export default CourseContentSidebar;
