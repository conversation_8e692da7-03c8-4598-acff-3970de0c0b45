import React, { useEffect, useRef, useMemo, useCallback } from 'react';
import * as d3 from 'd3';
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface DataPoint {
    label: string;
    value: number;
}

interface BarChartProps {
    data?: DataPoint[];
    width?: number;
    height?: number;
    xLabel: string;
    yLabel: string;
    title: string;
    horizontal?: boolean;
    studentMode?: boolean;
    isLoading?: boolean;
    error?: string;
    className?: string;
}

// Constants for chart styling
const CHART_CONSTANTS = {
    MARGIN: { top: 40, right: 80, bottom: 80, left: 80 },
    ANIMATION_DURATION: 800,
    BAR_OPACITY: 0.85,
    BAR_RADIUS: 4,
    TOOLTIP_DELAY: 150,
    GRID_OPACITY: 0.08,
    FONT_SIZE: 14,
} as const;

// Pastel color schemes
const getColorScheme = (studentMode: boolean) => studentMode ? [
    ['hsl(217, 65%, 85%)', 'hsl(217, 65%, 75%)'], // Soft blue
    ['hsl(280, 65%, 85%)', 'hsl(280, 65%, 75%)'], // Soft purple
    ['hsl(180, 65%, 85%)', 'hsl(180, 65%, 75%)']  // Soft teal
] : [
    ['hsl(152, 65%, 85%)', 'hsl(152, 65%, 75%)'], // Soft mint
    ['hsl(340, 65%, 85%)', 'hsl(340, 65%, 75%)'], // Soft pink
    ['hsl(45, 65%, 85%)', 'hsl(45, 65%, 75%)']    // Soft yellow
];

const LoadingState = ({ width, height }: { width: number; height: number }) => (
    <div className="space-y-4 w-full">
        <Skeleton className="w-full h-8" />
        <Skeleton className="w-full" style={{ height: `${height}px` }} />
    </div>
);

const ErrorState = ({ error }: { error: string }) => (
    <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
    </Alert>
);

export const BarChart: React.FC<BarChartProps> = ({
    data = [],
    width = 800,
    height = 500,
    xLabel,
    yLabel,
    title,
    horizontal = false,
    studentMode = false,
    isLoading = false,
    error,
    className
}) => {
    const svgRef = useRef<SVGSVGElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    // Memoize margin based on orientation
    const margin = useMemo(() => ({
        ...CHART_CONSTANTS.MARGIN,
        left: horizontal ? 140 : CHART_CONSTANTS.MARGIN.left
    }), [horizontal]);

    // Memoize dimensions
    const dimensions = useMemo(() => {
        const containerWidth = containerRef.current?.clientWidth || width;
        const actualWidth = width || containerWidth;
        return {
            width: actualWidth,
            height,
            innerWidth: actualWidth - margin.left - margin.right,
            innerHeight: height - margin.top - margin.bottom
        };
    }, [width, height, margin]);

    // Memoize scales
    const scales = useMemo(() => {
        if (!data.length) return null;

        if (horizontal) {
            return {
                x: d3.scaleLinear()
                    .domain([0, d3.max(data, d => d.value) || 0])
                    .nice()
                    .range([0, dimensions.innerWidth]),
                y: d3.scaleBand()
                    .domain(data.map(d => d.label))
                    .range([0, dimensions.innerHeight])
                    .padding(0.3)
            };
        }

        return {
            x: d3.scaleBand()
                .domain(data.map(d => d.label))
                .range([0, dimensions.innerWidth])
                .padding(0.3),
            y: d3.scaleLinear()
                .domain([0, d3.max(data, d => d.value) || 0])
                .nice()
                .range([dimensions.innerHeight, 0])
        };
    }, [data, dimensions, horizontal]);

    // Memoize resize handler
    const handleResize = useCallback(() => {
        if (!containerRef.current || !svgRef.current || !scales) return;

        const newWidth = containerRef.current.clientWidth;
        const newInnerWidth = newWidth - margin.left - margin.right;

        const svg = d3.select(svgRef.current);
        svg.attr('width', newWidth)
            .attr('viewBox', `0 0 ${newWidth} ${height}`);

        // Update gradients
        data.forEach((_, i) => {
            d3.select(`#bar-gradient-${i}`)
                .attr('x2', horizontal ? newInnerWidth : 0);
        });

        if (horizontal) {
            (scales.x as d3.ScaleLinear<number, number>).range([0, newInnerWidth]);
            svg.selectAll<SVGRectElement, DataPoint>('.bar')
                .attr('width', d => (scales.x as d3.ScaleLinear<number, number>)(d.value));

            svg.select('.x-axis')
                .call(d3.axisBottom(scales.x as d3.ScaleLinear<number, number>)
                    .ticks(5)
                    .tickSize(0) as any);
        } else {
            (scales.x as d3.ScaleBand<string>).range([0, newInnerWidth]);
            const newBandWidth = (scales.x as d3.ScaleBand<string>).bandwidth();

            svg.selectAll<SVGRectElement, DataPoint>('.bar')
                .attr('x', d => (scales.x as d3.ScaleBand<string>)(d.label) || 0)
                .attr('width', newBandWidth);

            svg.select('.x-axis')
                .call(d3.axisBottom(scales.x as d3.ScaleBand<string>)
                    .tickSize(0) as any);
        }
    }, [data, scales, margin, height, horizontal]);

    useEffect(() => {
        if (!data || !svgRef.current || !containerRef.current || !scales) return;

        // Clear previous chart
        d3.select(svgRef.current).selectAll('*').remove();

        const svg = d3.select(svgRef.current)
            .attr('width', dimensions.width)
            .attr('height', dimensions.height)
            .attr('viewBox', `0 0 ${dimensions.width} ${dimensions.height}`)
            .attr('preserveAspectRatio', 'xMidYMid meet')
            .attr('role', 'img')
            .attr('aria-label', title);

        // Add gradients and filters
        const defs = svg.append('defs');
        const colors = getColorScheme(studentMode);

        // Create dot pattern for student mode
        if (studentMode) {
            const dotPattern = defs.append('pattern')
                .attr('id', 'dot-pattern')
                .attr('width', 10)
                .attr('height', 10)
                .attr('patternUnits', 'userSpaceOnUse')
                .attr('patternTransform', 'rotate(45)');

            dotPattern.append('circle')
                .attr('cx', 5)
                .attr('cy', 5)
                .attr('r', 1.5)
                .attr('fill', 'hsl(217, 65%, 75%)');
        }

        data.forEach((_, i) => {
            const gradient = defs.append('linearGradient')
                .attr('id', `bar-gradient-${i}`)
                .attr('gradientUnits', 'userSpaceOnUse')
                .attr('x1', 0)
                .attr('y1', 0)
                .attr('x2', horizontal ? dimensions.innerWidth : 0)
                .attr('y2', horizontal ? 0 : dimensions.innerHeight);

            gradient.append('stop')
                .attr('offset', '0%')
                .attr('stop-color', colors[i % colors.length][0]);

            gradient.append('stop')
                .attr('offset', '100%')
                .attr('stop-color', colors[i % colors.length][1]);
        });

        // Glow effect
        const glow = defs.append('filter')
            .attr('id', 'bar-glow')
            .attr('height', '300%')
            .attr('width', '300%')
            .attr('x', '-100%')
            .attr('y', '-100%');

        glow.append('feGaussianBlur')
            .attr('stdDeviation', '1.5')
            .attr('result', 'coloredBlur');

        const feMerge = glow.append('feMerge');
        feMerge.append('feMergeNode').attr('in', 'coloredBlur');
        feMerge.append('feMergeNode').attr('in', 'SourceGraphic');

        const g = svg.append('g')
            .attr('transform', `translate(${margin.left},${margin.top})`);

        // Add title
        svg.append('text')
            .attr('class', 'chart-title')
            .attr('x', dimensions.width / 2)
            .attr('y', margin.top / 2)
            .attr('text-anchor', 'middle')
            .style('font-size', '16px')
            .style('font-weight', '600')
            .style('fill', 'hsl(var(--foreground))')
            .text(title);

        // Add grid lines with animation
        if (!horizontal) {
            const yAxisGrid = d3.axisLeft(scales.y as d3.ScaleLinear<number, number>)
                .tickSize(-dimensions.innerWidth)
                .tickFormat(() => '');

            const grid = g.append('g')
                .attr('class', 'grid')
                .attr('opacity', 0)
                .call(yAxisGrid as any);

            grid.transition()
                .duration(CHART_CONSTANTS.ANIMATION_DURATION)
                .attr('opacity', CHART_CONSTANTS.GRID_OPACITY);
        }

        // Add axes with labels
        if (horizontal) {
            const xAxis = d3.axisBottom(scales.x as d3.ScaleLinear<number, number>)
                .ticks(5)
                .tickSize(0);

            const yAxis = d3.axisLeft(scales.y as d3.ScaleBand<string>)
                .tickSize(0);

            // X-axis
            g.append('g')
                .attr('transform', `translate(0,${dimensions.innerHeight})`)
                .attr('class', 'x-axis')
                .style('color', 'hsl(var(--muted-foreground))')
                .style('font-size', `${CHART_CONSTANTS.FONT_SIZE}px`)
                .style('font-weight', '500')
                .call(xAxis as any);

            // Y-axis
            g.append('g')
                .attr('class', 'y-axis')
                .style('color', 'hsl(var(--muted-foreground))')
                .style('font-size', `${CHART_CONSTANTS.FONT_SIZE}px`)
                .style('font-weight', '500')
                .call(yAxis as any);
        } else {
            const xAxis = d3.axisBottom(scales.x as d3.ScaleBand<string>)
                .tickSize(0);

            const yAxis = d3.axisLeft(scales.y as d3.ScaleLinear<number, number>)
                .ticks(5)
                .tickSize(0);

            // X-axis
            g.append('g')
                .attr('transform', `translate(0,${dimensions.innerHeight})`)
                .attr('class', 'x-axis')
                .style('color', 'hsl(var(--muted-foreground))')
                .style('font-size', `${CHART_CONSTANTS.FONT_SIZE}px`)
                .style('font-weight', '500')
                .call(xAxis as any)
                .selectAll('text')
                .attr('transform', 'rotate(-35)')
                .style('text-anchor', 'end');

            // Y-axis
            g.append('g')
                .attr('class', 'y-axis')
                .style('color', 'hsl(var(--muted-foreground))')
                .style('font-size', `${CHART_CONSTANTS.FONT_SIZE}px`)
                .style('font-weight', '500')
                .call(yAxis as any);
        }

        // Add axis labels
        // X-axis label
        g.append('text')
            .attr('class', 'x-axis-label')
            .attr('x', dimensions.innerWidth / 2)
            .attr('y', dimensions.innerHeight + margin.bottom - 6)
            .attr('text-anchor', 'middle')
            .style('fill', 'hsl(var(--muted-foreground))')
            .style('font-size', '14px')
            .text(xLabel);

        // Y-axis label
        g.append('text')
            .attr('class', 'y-axis-label')
            .attr('transform', 'rotate(-90)')
            .attr('x', -dimensions.innerHeight / 2)
            .attr('y', -margin.left + 16)
            .attr('text-anchor', 'middle')
            .style('fill', 'hsl(var(--muted-foreground))')
            .style('font-size', '14px')
            .text(yLabel);

        // Add bars with animation
        const bars = g.selectAll('.bar')
            .data(data)
            .enter()
            .append('rect')
            .attr('class', 'bar')
            .attr('fill', (_, i) => studentMode ? `url(#bar-gradient-${i}) url(#dot-pattern)` : `url(#bar-gradient-${i})`)
            .attr('opacity', CHART_CONSTANTS.BAR_OPACITY)
            .attr('rx', CHART_CONSTANTS.BAR_RADIUS)
            .attr('ry', CHART_CONSTANTS.BAR_RADIUS)
            .style('filter', 'url(#bar-glow)')
            .attr('role', 'graphics-symbol')
            .attr('aria-label', d => `${d.label}: ${d.value}`);

        if (horizontal) {
            const bandWidth = (scales.y as d3.ScaleBand<string>).bandwidth();
            bars.attr('y', d => (scales.y as d3.ScaleBand<string>)(d.label) || 0)
                .attr('height', bandWidth)
                .attr('x', 0)
                .attr('width', 0)
                .transition()
                .duration(CHART_CONSTANTS.ANIMATION_DURATION)
                .ease(d3.easeCubicOut)
                .attr('width', d => (scales.x as d3.ScaleLinear<number, number>)(d.value));
        } else {
            const bandWidth = (scales.x as d3.ScaleBand<string>).bandwidth();
            bars.attr('x', d => (scales.x as d3.ScaleBand<string>)(d.label) || 0)
                .attr('width', bandWidth)
                .attr('y', dimensions.innerHeight)
                .attr('height', 0)
                .transition()
                .duration(CHART_CONSTANTS.ANIMATION_DURATION)
                .ease(d3.easeCubicOut)
                .attr('y', d => (scales.y as d3.ScaleLinear<number, number>)(d.value))
                .attr('height', d => dimensions.innerHeight - (scales.y as d3.ScaleLinear<number, number>)(d.value));
        }

        // Enhanced tooltip
        const tooltip = d3.select('body')
            .append('div')
            .attr('class', 'tooltip')
            .style('position', 'absolute')
            .style('background-color', 'hsl(var(--background))')
            .style('padding', '12px 16px')
            .style('border', '2px solid hsl(var(--border))')
            .style('border-radius', '12px')
            .style('pointer-events', 'none')
            .style('opacity', 0)
            .style('font-size', '14px')
            .style('color', 'hsl(var(--foreground))')
            .style('z-index', 999)
            .style('box-shadow', '0 8px 24px rgba(0, 0, 0, 0.15)')
            .style('transform', 'translate(-50%, -100%)');

        bars.on('mouseover', function (event: MouseEvent, d: DataPoint) {
            const bar = d3.select(this);
            bar.transition()
                .duration(CHART_CONSTANTS.TOOLTIP_DELAY)
                .attr('opacity', 1)
                .attr('transform', studentMode ? 'scale(1.02)' : '')
                .attr('filter', 'url(#bar-glow) brightness(1.1)');

            tooltip.transition()
                .duration(CHART_CONSTANTS.TOOLTIP_DELAY)
                .style('opacity', 1);

            tooltip.html(`
                <div class="text-lg font-semibold">${d.label}</div>
                <div class="text-base text-muted-foreground mt-1">Value: ${d.value}</div>
            `)
                .style('left', (event.pageX) + 'px')
                .style('top', (event.pageY - 10) + 'px');
        })
            .on('mouseout', function () {
                const bar = d3.select(this);
                bar.transition()
                    .duration(CHART_CONSTANTS.TOOLTIP_DELAY)
                    .attr('opacity', CHART_CONSTANTS.BAR_OPACITY)
                    .attr('transform', '')
                    .attr('filter', 'url(#bar-glow)');

                tooltip.transition()
                    .duration(CHART_CONSTANTS.TOOLTIP_DELAY)
                    .style('opacity', 0);
            });

        window.addEventListener('resize', handleResize);

        // Cleanup
        return () => {
            d3.select('body').selectAll('.tooltip').remove();
            window.removeEventListener('resize', handleResize);
        };
    }, [data, dimensions, scales, margin, title, xLabel, yLabel, horizontal, studentMode, handleResize]);

    if (isLoading) {
        return <LoadingState width={width} height={height} />;
    }

    if (error) {
        return <ErrorState error={error} />;
    }

    return (
        <div ref={containerRef} className={cn("w-full h-full", className)}>
            <svg
                ref={svgRef}
                className="w-full h-full"
                role="img"
                aria-label={title}
            />
        </div>
    );
};
