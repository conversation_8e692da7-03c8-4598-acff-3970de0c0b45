import React, { useState, useEffect, useRef } from "react";
import "./ContentView.css";
import { useDispatch, useSelector } from "react-redux";
import {
  useLazyGetCourseDetailsDataQuery,
  useLazyGetContentRelatedTopicDataQuery,
} from "APIConnect";
import {
  setStudentCoursesDetail,
  setContentRelatedTopic,
  setBookmarksId,
} from "components/Student/Courses/courseSlice";
import { useNavigate } from "react-router-dom";
import { contentType } from "constant/AppConstant";
import chapterIcon from "./assets/chapterIcon.svg";
import topicIcon from "./assets/topicIcon.svg";
import topicBackIcon from "./assets/topicBackIcon.svg";
import quizStartIcon from "./assets/quizStartIcon.svg";
import { YtbeURL } from "constant/AppConstant";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import "suneditor/src/assets/css/suneditor.css";
import noteIcon from "./assets/Notes.svg";
import noteListIcon from "./assets/NotesList.svg";
import plusIcon from "./assets/Plus.svg";

const notes = [
  { id: 1, title: "Notes 1", details: "Detail for Note 1" },
  { id: 2, title: "Notes 2", details: "Detail for Note 2" },
  { id: 3, title: "Notes 3", details: "Detail for Note 3" },
  { id: 1, title: "Notes 1", details: "Detail for Note 1" },
  { id: 2, title: "Notes 2", details: "Detail for Note 2" },
  { id: 3, title: "Notes 3", details: "Detail for Note 3" },
  { id: 1, title: "Notes 1", details: "Detail for Note 1" },
  { id: 2, title: "Notes 2", details: "Detail for Note 2" },
  { id: 3, title: "Notes 3", details: "Detail for Note 3" },
  { id: 1, title: "Notes 1", details: "Detail for Note 1" },
  { id: 2, title: "Notes 2", details: "Detail for Note 2" },
  { id: 3, title: "Notes 3", details: "Detail for Note 3" },
];

const ContentView = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [getCourseDetailsData, getCourseDetailsDataResult] =
    useLazyGetCourseDetailsDataQuery();
  const [getContentRelatedTopicData, getContentRelatedTopicDataResult] =
    useLazyGetContentRelatedTopicDataQuery();
  const courseDetail = useSelector((state) => state.courses.courseDetail);
  const contentRelatedTopic = useSelector(
    (state) => state.courses.contentRelatedTopic
  );

  const currentCourseChoosen = useSelector(
    (state) => state.courses.currentCourseChoosen
  );
  const bookmarksId = useSelector((state) => state.courses.bookmarksId);
  const [selectedChapterIndex, setSelectedChapterIndex] = useState(0);
  const [selectedTopicIndex, setSelectedTopicIndex] = useState(null);
  const contentRef = useRef(null);
  const [atEnd, setAtEnd] = useState(false);
  const [startQuizModal, setStartQuizModal] = useState(false);
  const [noteModal, setNoteModal] = useState(false);

  const [selectedNote, setSelectedNote] = useState(null);
  const [isEditPopupOpen, setIsEditPopupOpen] = useState(false);
  const [editedText, setEditedText] = useState("");
  const [bookMarkModal, setBookMarkModal] = useState(false);
  const [choosenBookMarkId, setChoosenBookMarkId] = useState("");
  const [bookMarkTitle, setBookMarkTitle] = useState("");

  useEffect(() => {
    getCourseDetails(currentCourseChoosen);
  }, []);

  const handleSelectTopic = (topic) => {
    let chapterIndex = courseDetail?.chapters.findIndex((chapter) =>
      chapter.topics.some((item) => topic.id === item.id)
    );

    let topicIndex = courseDetail?.chapters?.[chapterIndex].topics?.findIndex(
      (e) => e?.id === topic?.id
    );

    setSelectedChapterIndex(chapterIndex);
    setSelectedTopicIndex(topicIndex);

    getContentRelatedTopic(topic?.id);
  };

  const getCourseDetails = async () => {
    try {
      const obj = {
        courseId: currentCourseChoosen?.id,
      };
      const { data } = await getCourseDetailsData(obj);
      if (data?.resultObject) {
        dispatch(setStudentCoursesDetail(data.resultObject));
        if (data.resultObject?.chapters?.[0]?.topics?.[0]?.id) {
          getContentRelatedTopic(
            data.resultObject?.chapters?.[0]?.topics?.[0]?.id
          );
          setSelectedChapterIndex(0);
          setSelectedTopicIndex(0);
        }
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  const getContentRelatedTopic = async (topicId) => {
    try {
      const { data } = await getContentRelatedTopicData(topicId);
      setLoading(false);
      if (data?.resultObject) {
        dispatch(setContentRelatedTopic(data.resultObject));
      } else {
        dispatch(setContentRelatedTopic({}));
      }
    } catch (err) {
      console.log("err err", err);
      dispatch(setContentRelatedTopic({}));
    }
  };

  const handleNext = () => {
    setNoteModal(false);
    setIsEditPopupOpen(false);
    setSelectedNote(null);

    // if (atEnd) {
    let chapter = courseDetail?.chapters[selectedChapterIndex];
    let topic = chapter?.topics;
    let chapterLength = courseDetail?.chapters?.length;

    if (
      chapterLength === selectedChapterIndex + 1 &&
      topic?.length === selectedTopicIndex + 1
    ) {
      setStartQuizModal(true);
    } else if (topic?.length < selectedTopicIndex + 2) {
      setSelectedChapterIndex(selectedChapterIndex + 1);
      setSelectedTopicIndex(0);
      getContentRelatedTopic(
        courseDetail?.chapters?.[selectedChapterIndex + 1]?.topics?.[0]?.id
      );
    } else {
      setSelectedTopicIndex(selectedTopicIndex + 1);
      getContentRelatedTopic(
        courseDetail?.chapters?.[selectedChapterIndex]?.topics?.[
          selectedTopicIndex + 1
        ]?.id
      );
    }

    // Move to the next chapter, or loop back to the first chapter
    // const nextIndex = (selectedChapterIndex + 1) % courseDetail?.chapters?.length;
    // setSelectedChapterIndex(nextIndex);
    // setSelectedTopicIndex(null);
    // Scroll to the top of the content area when changing chapters
    // if (contentRef.current) {
    //   contentRef.current.scrollTo({ top: 0, behavior: "smooth" });
    // }
    // } else {
    //   // Scroll down in the current content area
    //   if (contentRef.current) {
    //     const viewportHeight = contentRef.current.clientHeight; // Calculate viewport height
    //     contentRef.current.scrollBy({
    //       top: viewportHeight,
    //       behavior: "smooth",
    //     });
    //   }
    // }
  };

  const handleScroll = () => {
    if (contentRef.current) {
      const content = contentRef.current;
      const contentHeight = content.scrollHeight;
      const viewportHeight = content.clientHeight;
      const scrollTop = content.scrollTop;

      // Check if we are at the bottom of the scrollable area
      const isAtBottom = contentHeight - scrollTop <= viewportHeight + 10;

      // Set atEnd based on the condition
      setAtEnd(isAtBottom);
    }
  };

  const handleNote = () => {
    setNoteModal(!noteModal);
    setIsEditPopupOpen(false);
  };

  // const handleAddNote = () => {
  //   const newNote = { id: notes.length + 1, content: `New note ${notes.length + 1}` };
  //   setNotes([...notes, newNote]);
  // };

  const handleNoteClick = (note) => {
    setSelectedNote(note);
    //   setEditedText(note.details);
    // setIsEditPopupOpen(true);
    setNoteModal(false);
  };

  const handleAddNote = () => {
    setIsEditPopupOpen(true);
  };

  // useEffect(() => {
  //   const handleClickOutside = (event) => {
  //     if(noteModal) {
  //     setNoteModal(false);
  //     }
  //   };

  //   document.addEventListener("mousedown", handleClickOutside);
  //   return () => {
  //     document.removeEventListener("mousedown", handleClickOutside);
  //   };
  // }, []);

  const handleSaveChanges = () => {
    setIsEditPopupOpen(false);
    setNoteModal(false);
  };

  const closeNoteModal = () => {
    setNoteModal(false);
    setIsEditPopupOpen(false);
  };

  useEffect(() => {
    const contentEl = contentRef?.current;
    contentEl?.addEventListener("scroll", handleScroll);
    handleScroll(); // Initial check for content height
    return () => contentEl?.removeEventListener("scroll", handleScroll);
  }, [loading]);

  useEffect(() => {
    handleScroll(); // Check content height when chapter changes
  }, [selectedChapterIndex]);

  const getChapterName = () => {
    const chapter = courseDetail?.chapters?.[selectedChapterIndex];
    return chapter?.name;
  };

  const getTopicName = () => {
    const chapter = courseDetail?.chapters?.[selectedChapterIndex];
    const topic = chapter?.topics?.[selectedTopicIndex];
    return topic?.name;
  };

  const handleQuizModal = (option) => {
    setStartQuizModal(false);
    if (option === "cancel") {
      navigate("/");
    } else if (option === "quiz") {
      navigate("/course/quiz");
    }
  };

  const getVideoUrl = (url) => {
    const code = url?.split("v=")?.[1];
    return YtbeURL + code;
  };

  const handleBoorks = (id) => {
    setBookMarkModal(true);
    setChoosenBookMarkId(id);
  };

  const handleBookark = () => {
    const data = [...bookmarksId, choosenBookMarkId];
    dispatch(setBookmarksId(data));
    setBookMarkTitle("");
    setChoosenBookMarkId("");
    setBookMarkModal(false);
  };

  const handleCamcelBookMark = () => {
    setBookMarkTitle("");
    setChoosenBookMarkId("");
    setBookMarkModal(false);
  };

  const handleRemoveBoorks = (id) => {
    const data = bookmarksId?.filter((item) => item !== id);
    dispatch(setBookmarksId(data));
  };

  const handleEditNotesCancel = () => {
    setNoteModal(false);
    setIsEditPopupOpen(false);
  };

  return (
    <>
      {loading ||
      getCourseDetailsDataResult?.isLoading ||
      getContentRelatedTopicDataResult?.isLoading ? (
        <div className="loading" />
      ) : (
        <div className="maincontentview">
          <div className="maincontentview-header">
            <div className="maincontentview-header-button">
              <button className="back-button" onClick={() => navigate("/")}>
                &lt; Back
              </button>
            </div>
            <div className="maincontentview-chapter-name">
              {/* {getChapterName()} */}
            </div>
            <div />
          </div>
          <div className="contentview">
            <div className="contentContainer">
              <div className="contentSidebar">
                <div className="contentSideBar-container">
                  <div className="contentSidebar-header">
                    <img src={topicIcon} alt="" />
                    <div className="contentSidebar-header-text">Topics</div>
                  </div>
                  <div className="contentSidebar-header-icon">
                    {" "}
                    <img src={topicBackIcon} alt="" />
                  </div>
                </div>

                <ul>
                  {courseDetail?.chapters?.map((item, index) => {
                    return (
                      <li
                        key={index}
                        //onClick={() => setSelectedChapterIndex(index)}
                        //className={index === selectedChapterIndex ? "active" : ""}
                      >
                        <div
                          className="chapterName"
                          //className={`contentChapter ${index === selectedChapterIndex ? "active" : ""}`}
                        >
                          <div className="chapterIconCotainer">
                            <img
                              src={chapterIcon}
                              alt=""
                              className="chapterIcon"
                            />
                          </div>
                          <div className="chapterName-text"> {item.name}</div>
                        </div>

                        <div className="sidebarTopicName-container">
                          <ul>
                            {item.topics.map((topic, idx) => (
                              <div
                                className="sidebarTopicName"
                                key={idx}
                                onClick={() => {
                                  handleSelectTopic(topic);
                                  setSelectedTopicIndex(idx);
                                }}
                              >
                                {/* <li
                              // style={{ display:'flex'}}
                                key={idx}
                                // className={`contentTopic ${contentRelatedTopic?.topicId === topic?.id || (selectedTopicIndex === idx && index === selectedChapterIndex) ? "selected" : ""}`}
                                onClick={() => {
                                  handleSelectTopic(topic);
                                  setSelectedTopicIndex(idx);
                                }}
                              > */}
                                <div className="sidebarTopicName-text">
                                  {topic?.name}
                                </div>

                                {/* </li> */}
                                {bookmarksId?.includes(topic?.id) ? (
                                  <div className="star-container">
                                    <div
                                      className="star"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleRemoveBoorks(topic?.id);
                                      }}
                                    />
                                  </div>
                                ) : (
                                  <div className="star-container">
                                    <div
                                      className="star-dull"
                                      onClick={(e) => {
                                        e.stopPropagation();

                                        handleBoorks(topic?.id);
                                      }}
                                    />
                                  </div>
                                )}
                              </div>
                            ))}
                          </ul>
                        </div>
                      </li>
                    );
                  })}
                </ul>
              </div>
              <div className="contents" ref={contentRef}>
                <div className="contents-topicName">
                  {/* {getTopicName()} */}
                  </div>
                {/* <button
            id="backButton"
            onClick={() => console.log("Back button clicked")}
          >
            &lt; Back
          </button> */}
                {Object.keys(contentRelatedTopic)?.length > 0 ? (
                  <>
                    {contentType[contentRelatedTopic.type] === "html" && (
                      <div
                        dangerouslySetInnerHTML={{
                          __html: contentRelatedTopic?.content,
                        }}
                      />
                    )}
                    {contentType[contentRelatedTopic.type] === "video" && (
                      <div className="content-media-video">
                        <div className="content-media-video-container">
                          <iframe
                            // src='https://louuu03.github.io/Scorm_Example/story.html'
                            src={getVideoUrl(contentRelatedTopic?.contentUrl)}
                            title="YouTube video"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowFullScreen
                          ></iframe>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <p>No content available</p>
                )}
              </div>

              <button className="note-button" onClick={handleNote}>
                Notes
              </button>

              <button className="next-button" onClick={handleNext}>
                Continue
                {/* {atEnd ? "Continue" : "Scroll Down"} */}
              </button>
            </div>
          </div>

          {noteModal && (
            <div className="note-modal">
              <div className="noteModalHeader">
                <div className="headerLeft">
                  <img src={noteIcon} alt="Notes Icon" className="headerIcon" />
                  <h2>Notes</h2>
                </div>
                <button className="closeButton" onClick={closeNoteModal}>
                  <img src={topicBackIcon} alt="Close Icon" />
                </button>
              </div>
              <div className="noteList">
                {notes.map((note, index) => (
                  <li
                    key={index}
                    className="noteItem"
                    onClick={() => handleNoteClick(note)}
                  >
                    <div className="noteContent">
                      <img
                        src={noteListIcon}
                        alt="Note Icon"
                        className="noteIcon"
                      />
                      <span>{note.title}</span>
                    </div>
                  </li>
                ))}
              </div>
              <button className="addNoteButton" onClick={() => handleAddNote()}>
                <div className="addNoteButton-text">Add Notes</div>{" "}
                <img src={plusIcon} alt="Add Note Icon" />
              </button>
            </div>
          )}

          {selectedNote && (
            <div className="note-modal">
              <div className="noteModalHeader">
                <div className="headerLeft">
                  <h2>{selectedNote?.title}</h2>
                </div>
                <button
                  className="closeButton"
                  onClick={() => setSelectedNote(null)}
                >
                  <img src={topicBackIcon} alt="Close Icon" />
                </button>
              </div>
              <div className="noteList">{selectedNote?.details}</div>
            </div>
          )}
          {isEditPopupOpen && (
            <div className="notedetail-modal">
              <div className="editor-container">
                <SunEditor
                  setOptions={{
                    buttonList: [
                      ["font", "fontSize", "formatBlock"],
                      ["bold", "underline", "italic", "strike"],
                      ["fontColor", "hiliteColor"],
                      ["align", "list", "table"],
                      ["link", "image", "video"],
                      ["fullScreen", "showBlocks", "codeView"],
                    ],
                    height: "70%",
                    minHeight: "240px",
                  }}
                  onChange={(content) => setEditedText(content)}
                  setDefaultStyle="overflow-y: scroll; width:100%"
                />
              </div>
              <div className="note-button-container">
                <button
                  className="notescancelButton"
                  onClick={() => handleEditNotesCancel()}
                >
                  Cancel
                </button>
                <button className="saveButton" onClick={handleSaveChanges}>
                  Save
                </button>
              </div>
            </div>
          )}

          {startQuizModal && (
            <div className="modal-overlay">
              <div className="modal-content-quiz">
                <h4 className="modal-quiz-result">
                  You are about to start a Quiz
                </h4>
                <div>
                  <img src={quizStartIcon} alt="" />
                </div>
                <p className="fail-message">
                  You can take a break or continue with the quiz.
                </p>

                <div className="button-container">
                  <div className="align-right">
                    <button
                      id="cancelbutton"
                      className="cancel-button"
                      onClick={() => handleQuizModal("cancel")}
                    >
                      Cancel
                    </button>
                  </div>
                  <div id="nextbutton" className="align-right">
                    <button
                      className="takeQuiz-button"
                      onClick={() => handleQuizModal("quiz")}
                    >
                      Take the Quiz
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
          {bookMarkModal && (
            <div className="modal-overlay">
              <div className="modal-content-quiz">
                <p className="fail-message-bookMark">Title for bookmark</p>
                <input
                  onChange={(e) => setBookMarkTitle(e.target.value)}
                  value={bookMarkTitle}
                />
                <div id="nextbutton">
                  <button
                    className="takeQuiz-button"
                    onClick={() => handleCamcelBookMark()}
                  >
                    Cancel
                  </button>
                  <button
                    className="takeQuiz-button"
                    onClick={() => handleBookark()}
                    disabled={!bookMarkTitle?.length}
                    style={{
                      backgroundColor: !bookMarkTitle?.length
                        ? "#EBEBE4"
                        : "#703D3D",
                    }}
                  >
                    Submit
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default ContentView;
