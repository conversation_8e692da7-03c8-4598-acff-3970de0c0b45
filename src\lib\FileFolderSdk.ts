import { JWTToken } from "@/constant/AppConstant";

export class FileFolderSDK {
  private baseURL: string;

  constructor(
    private authToken?: string,
    baseURL: string = "https://nexo-file.azurewebsites.net"
  ) {
    this.baseURL = baseURL;
  }

  private async fetchWithAuth(url: string, options: RequestInit = {}) {
    const token = await this.getAzureAccessToken();
    const headers = {
      "Content-Type": "application/json",
      "X-ZUMO-AUTH": token,
      ...options.headers,
    };
    return fetch(url, { ...options, headers });
  }

  private async getAzureAccessToken(): Promise<string> {
    const accessTokenStr = localStorage.getItem(JWTToken) ?? "";
    const idToken = localStorage.getItem("IdToken") ?? "";
    const accessToken = accessTokenStr
      ? JSON.parse(accessTokenStr).accessToken
      : "";

    const response = await fetch(`${this.baseURL}/.auth/login/auth0`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        access_token: accessToken,
        id_token: idToken,
      }),
    });
    const data = await response.json();
    return data.authenticationToken;
  }

  public async uploadFile(
    file: File,
    fileName: string,
    onUploadProgress?: (progress: number) => void
  ): Promise<string> {
    const azureAccessToken = await this.getAzureAccessToken();

    const presignedUrlResponse = await fetch(`${this.baseURL}/api/UploadFile`, {
      method: "POST",
      headers: {
        "X-ZUMO-AUTH": azureAccessToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ fileName }),
    });
    const presignedUrlData = await presignedUrlResponse.json();
    const uploadUrl: string = presignedUrlData.presignedUrl;
    const finalUrl: string = presignedUrlData.getUrl;

    // Upload file
    if (onUploadProgress) {
      const xhr = new XMLHttpRequest();
      await new Promise((resolve, reject) => {
        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable && onUploadProgress) {
            const percentComplete = (event.loaded / event.total) * 100;
            onUploadProgress(percentComplete);
          }
        };
        xhr.onload = () => resolve(xhr.response);
        xhr.onerror = () => reject(xhr.statusText);
        xhr.open("PUT", uploadUrl);
        xhr.setRequestHeader("Content-Type", file.type);
        xhr.setRequestHeader("x-ms-blob-type", "BlockBlob");
        xhr.send(file);
      });
    } else {
      await fetch(uploadUrl, {
        method: "PUT",
        headers: {
          "Content-Type": file.type,
          "x-ms-blob-type": "BlockBlob",
        },
        body: file,
      });
    }
    return finalUrl;
  }

  public async createDirectory(directoryPath: string) {
    return this.fetchWithAuth(`${this.baseURL}/api/CreateDirectory`, {
      method: "POST",
      body: JSON.stringify({ directoryPath }),
    });
  }

  public async renameDirectory(
    directoryPath: string,
    newDirectoryName: string
  ) {
    return this.fetchWithAuth(`${this.baseURL}/api/RenameDirectory`, {
      method: "POST",
      body: JSON.stringify({ directoryPath, newDirectoryName }),
    });
  }

  public async listFilesAndDirectories() {
    const response = await this.fetchWithAuth(
      `${this.baseURL}/api/ListFilesAndDirectories`
    );
    const data = await response.json();
    // data sample: { "fileAndDirectoryList": [ "file.jpeg", "homework", "homework/file2.jpeg" ] }
    const list: string[] = data.fileAndDirectoryList || [];
    const directories = new Set<string>();
    list.forEach((item) => {
      if (item.includes("/")) {
        directories.add(item.split("/")[0]);
      } else if (!item.includes(".")) {
        directories.add(item);
      }
    });
    return { directories: Array.from(directories), fileAndDirectoryList: list };
  }

  public async deleteDirectory(directoryPath: string) {
    return this.fetchWithAuth(`${this.baseURL}/api/DeleteDirectory`, {
      method: "DELETE",
      body: JSON.stringify({ directoryPath }),
    });
  }



  public async deleteFile(filePath: string) {
    const response = await this.fetchWithAuth(`${this.baseURL}/api/DeleteFileFromPath`, {
      method: "DELETE",
      body: JSON.stringify({ FilePath: filePath }),
    });

    if (!response.ok) {
      throw new Error(`Delete failed: ${response.status} ${response.statusText}`);
    }

    return response;
  }
}
