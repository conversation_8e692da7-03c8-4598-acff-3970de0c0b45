import React from 'react';
import { WifiOff } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OfflineBannerProps {
    className?: string;
}

const OfflineBanner = ({ className }: OfflineBannerProps) => {
    const [isOffline, setIsOffline] = React.useState(!navigator.onLine);

    React.useEffect(() => {
        const handleOnline = () => {
            setIsOffline(false);
            // Notify service worker about online status
            navigator.serviceWorker?.controller?.postMessage({
                type: 'ONLINE_STATUS_CHANGE',
                online: true
            });
        };

        const handleOffline = () => {
            setIsOffline(true);
            // Notify service worker about offline status
            navigator.serviceWorker?.controller?.postMessage({
                type: 'ONLINE_STATUS_CHANGE',
                online: false
            });
        };

        // Listen for service worker messages
        const handleServiceWorkerMessage = (event: MessageEvent) => {
            if (event.data && event.data.type === 'ONLINE_STATUS_UPDATE') {
                setIsOffline(!event.data.online);
            }
        };

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);
        navigator.serviceWorker?.addEventListener('message', handleServiceWorkerMessage);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
            navigator.serviceWorker?.removeEventListener('message', handleServiceWorkerMessage);
        };
    }, []);

    if (!isOffline) return null;

    return (
        <div
            className={cn(
                'fixed bottom-0 left-0 right-0 bg-yellow-100 text-yellow-800 px-4 py-2 flex items-center justify-center space-x-2 z-50',
                'border-t border-yellow-200 shadow-lg',
                className
            )}
        >
            <WifiOff className="h-4 w-4" />
            <span>You are currently offline. Some features may be limited.</span>
        </div>
    );
};

export default OfflineBanner;
