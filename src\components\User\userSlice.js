import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  userToken: "",
  userIsRendered: false,
  idToken: "",
};

export const userSlice = createSlice({
  initialState,
  name: "user",
  reducers: {
    setUserToken: (state, action) => {
      state.userToken = action.payload;
    },
    setUserIsRendered: (state, action) => {
      state.userIsRendered = action.payload;
    },
    setIdToken: (state, action) => {
      state.idToken = action.payload;
    },
  },
});

export const { setUserToken, setUserIsRendered, setIdToken } =
  userSlice.actions;

export default userSlice;
