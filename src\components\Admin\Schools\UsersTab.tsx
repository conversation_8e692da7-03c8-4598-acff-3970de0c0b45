import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    <PERSON><PERSON>,
    DialogContent,
    <PERSON><PERSON>Header,
    <PERSON><PERSON>Title,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Plus, Loader2, Search, Pencil, Trash2, Users, Eye, EyeOff } from 'lucide-react';
import { useCreateUserMutation, useDeleteUserMutation } from 'src/APIConnect';
import { User } from './types';
import { toast } from "@/hooks/use-toast";
import { useNavigate } from 'react-router-dom';

interface UsersTabProps {
    schoolId: string;
    users: Array<{
        user: User;
        schoolId: string;
        announcment: boolean;
    }>;
    onRefetch: () => void;
}

interface FormErrors {
    firstName?: string;
    lastName?: string;
    nickName?: string;
    email?: string;
    password?: string;
    userRoleId?: string;
}

interface FormData {
    firstName: string;
    lastName: string;
    nickName: string;
    email: string;
    password: string;
    userRoleId: number;
    schoolId: string;
    createdOn: string;
}

const userRoles = [
    { id: 2, role: 'School Admin' },
    { id: 3, role: 'Teacher' },
    { id: 4, role: 'Student' }
];

const UsersTab = ({ schoolId, users, onRefetch }: UsersTabProps) => {
    const [searchQuery, setSearchQuery] = useState('');
    const navigate = useNavigate();
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState<User | null>(null);
    const [formData, setFormData] = useState<FormData>({
        firstName: '',
        lastName: '',
        nickName: '',
        email: '',
        password: '',
        userRoleId: 4, // Default to Student
        schoolId,
        createdOn: new Date().toISOString()
    });
    const [errors, setErrors] = useState<FormErrors>({});
    const [showPassword, setShowPassword] = useState(false);

    const [createUser, { isLoading: isCreating }] = useCreateUserMutation();
    const [deleteUser] = useDeleteUserMutation();

    const handleDelete = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this user?')) {
            try {
                await deleteUser({ Id: id }).unwrap();
                toast({
                    title: "User deleted successfully",
                    variant: "default",
                });
                onRefetch();
            } catch (error) {
                console.error('Error deleting user:', error);
                toast({
                    title: "Failed to delete user",
                    description: "Please try again later",
                    variant: "destructive",
                });
            }
        }
    };

    const validateForm = (): boolean => {
        const newErrors: FormErrors = {};
        let isValid = true;

        if (!formData.firstName.trim()) {
            newErrors.firstName = "First name is required";
            isValid = false;
        }

        if (!formData.lastName.trim()) {
            newErrors.lastName = "Last name is required";
            isValid = false;
        }

        if (!formData.nickName.trim()) {
            newErrors.nickName = "Nickname is required";
            isValid = false;
        }

        if (!formData.email.trim()) {
            newErrors.email = "Email is required";
            isValid = false;
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = "Invalid email format";
            isValid = false;
        }

        if (!selectedUser && (!formData.password || !formData.password.trim())) {
            newErrors.password = "Password is required for new users";
            isValid = false;
        } else if (!selectedUser && formData.password) {
            const passwordRegex = /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};':"\\|,.<>\/?]).{8,}$/;
            if (!passwordRegex.test(formData.password)) {
                newErrors.password = "Password must be at least 8 characters long and include an uppercase letter, a number, and a special character";
                isValid = false;
            }
        }

        if (!formData.userRoleId) {
            newErrors.userRoleId = "User role is required";
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            toast({
                title: "Please fix the errors",
                description: "Some required fields are missing or invalid",
                variant: "destructive",
            });
            return;
        }

        try {
            const submitData = {
                ...formData,
                name: `${formData.firstName} ${formData.lastName}`,
            };
            // Remove any fields not required by the API
            // delete submitData.password; // If password is handled differently
            // delete submitData.userRole; // If userRole is not needed

            await createUser(submitData).unwrap();

            toast({
                title: "User created successfully",
                variant: "default",
            });
            setIsCreateDialogOpen(false);
            setFormData({
                firstName: '',
                lastName: '',
                nickName: '',
                email: '',
                password: '',
                userRoleId: 4,
                schoolId,
                createdOn: new Date().toISOString()
            });
            onRefetch();
        } catch (error) {
            console.error('Error saving user:', error);
            toast({
                title: "Failed to create user",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const filteredUsers = users.filter(({ user }) => {
        const matchesSearch = searchQuery
            ? user.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
            user.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
            user.email.toLowerCase().includes(searchQuery.toLowerCase())
            : true;
        return matchesSearch;
    });

    const renderForm = () => (
        <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
                <div>
                    <Label>First Name</Label>
                    <Input
                        value={formData.firstName}
                        onChange={(e) => {
                            setFormData((prev) => ({ ...prev, firstName: e.target.value }));
                            if (errors.firstName) {
                                setErrors((prev) => ({ ...prev, firstName: undefined }));
                            }
                        }}
                        placeholder="Enter first name..."
                        className={errors.firstName ? "border-red-500" : ""}
                    />
                    {errors.firstName && (
                        <p className="text-sm text-red-500 mt-1">{errors.firstName}</p>
                    )}
                </div>

                <div>
                    <Label>Last Name</Label>
                    <Input
                        value={formData.lastName}
                        onChange={(e) => {
                            setFormData((prev) => ({ ...prev, lastName: e.target.value }));
                            if (errors.lastName) {
                                setErrors((prev) => ({ ...prev, lastName: undefined }));
                            }
                        }}
                        placeholder="Enter last name..."
                        className={errors.lastName ? "border-red-500" : ""}
                    />
                    {errors.lastName && (
                        <p className="text-sm text-red-500 mt-1">{errors.lastName}</p>
                    )}
                </div>
            </div>

            <div>
                <Label>Nickname</Label>
                <Input
                    value={formData.nickName}
                    onChange={(e) => {
                        setFormData((prev) => ({ ...prev, nickName: e.target.value }));
                        if (errors.nickName) {
                            setErrors((prev) => ({ ...prev, nickName: undefined }));
                        }
                    }}
                    placeholder="Enter nickname..."
                    className={errors.nickName ? "border-red-500" : ""}
                />
                {errors.nickName && (
                    <p className="text-sm text-red-500 mt-1">{errors.nickName}</p>
                )}
            </div>

            <div>
                <Label>Email</Label>
                <Input
                    value={formData.email}
                    onChange={(e) => {
                        setFormData((prev) => ({ ...prev, email: e.target.value }));
                        if (errors.email) {
                            setErrors((prev) => ({ ...prev, email: undefined }));
                        }
                    }}
                    placeholder="Enter email address..."
                    className={errors.email ? "border-red-500" : ""}
                />
                {errors.email && (
                    <p className="text-sm text-red-500 mt-1">{errors.email}</p>
                )}
            </div>

            {!selectedUser && (
                <div>
                    <Label>Password</Label>
                    <div className="relative">
                        <Input
                            type={showPassword ? "text" : "password"}
                            value={formData.password}
                            onChange={(e) => {
                                setFormData((prev) => ({ ...prev, password: e.target.value }));
                                if (errors.password) {
                                    setErrors((prev) => ({ ...prev, password: undefined }));
                                }
                            }}
                            placeholder="Enter password..."
                            className={`${errors.password ? "border-red-500" : ""} pr-10`}
                        />
                        <button
                            type="button"
                            onClick={() => setShowPassword((prev) => !prev)}
                            className="absolute right-2 top-2.5 text-muted-foreground"
                        >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                    </div>
                    {errors.password && (
                        <p className="text-sm text-red-500 mt-1">{errors.password}</p>
                    )}
                    <p className="text-sm text-muted-foreground mt-1">
                        Password must be at least 8 characters long and include an uppercase letter, a number, and a special character.
                    </p>
                </div>
            )}

            <div>
                <Label>Role</Label>
                <Select
                    value={formData.userRoleId.toString()}
                    onValueChange={(value) => {
                        setFormData((prev) => ({ ...prev, userRoleId: parseInt(value) }));
                        if (errors.userRoleId) {
                            setErrors((prev) => ({ ...prev, userRoleId: undefined }));
                        }
                    }}
                >
                    <SelectTrigger className={errors.userRoleId ? "border-red-500" : ""}>
                        <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                        {userRoles.map((role) => (
                            <SelectItem key={role.id} value={role.id.toString()}>
                                {role.role}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                {errors.userRoleId && (
                    <p className="text-sm text-red-500 mt-1">{errors.userRoleId}</p>
                )}
            </div>

            <div className="flex justify-end space-x-2">
                <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                        setIsCreateDialogOpen(false);
                        setSelectedUser(null);
                    }}
                >
                    Cancel
                </Button>
                <Button type="submit" disabled={isCreating}>
                    {isCreating ? (
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                        <Plus className="w-4 h-4 mr-2" />
                    )}
                    Create
                </Button>
            </div>
        </form>
    );

    return (
        <div className="space-y-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
                <h2 className="text-xl font-semibold flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    Users
                </h2>
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full md:w-auto">
                    <div className="relative flex-1 sm:flex-none">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                            placeholder="Search users..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-8"
                        />
                    </div>
                    <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                        <DialogTrigger asChild>
                            <Button className="w-full sm:w-auto">
                                <Plus className="w-4 h-4 mr-2" />
                                Add User
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Add New User</DialogTitle>
                            </DialogHeader>
                            {renderForm()}
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            <ScrollArea className="border rounded-md">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Role</TableHead>
                            <TableHead>Created On</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredUsers.map(({ user }) => (
                            <TableRow key={user.id}>
                                <TableCell className="font-medium">
                                    {`${user.firstName} ${user.lastName}`}
                                </TableCell>
                                <TableCell>{user.email}</TableCell>
                                <TableCell>{user.userRole.role}</TableCell>
                                <TableCell>{new Date(user.createdOn).toLocaleDateString()}</TableCell>
                                <TableCell className="text-right">
                                    <div className="flex justify-end space-x-2">
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={() => {

                                                navigate(`/admin/users/${user.id}`);
                                            }}
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={() => handleDelete(user.id)}
                                        >
                                            <Trash2 className="h-4 w-4 text-red-500" />
                                        </Button>
                                    </div>
                                </TableCell>
                            </TableRow>
                        ))}
                        {filteredUsers.length === 0 && (
                            <TableRow>
                                <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                                    No users found
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </ScrollArea>
        </div>
    );
};

export default UsersTab;
