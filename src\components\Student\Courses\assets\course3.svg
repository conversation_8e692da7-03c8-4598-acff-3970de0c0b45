<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 27.5.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 500 500" style="enable-background:new 0 0 500 500;" xml:space="preserve">
<g id="BACKGROUND">
	<g>
		<rect style="fill:#FFF7F0;" width="500" height="500"/>
		<path style="opacity:0.3;fill:#6AC8BF;" d="M238.064,377.666c0,0,25.998-66.317,58.464-32.708
			c54.975,56.911,150.707,22.893,135.274-63.903c-8.634-48.557,0.131-44.448,38.174-102.428
			c23.28-35.48-14.854-100.43-90.754-91.401c-75.9,9.029-89.369,4.301-106.556-11.586c-17.186-15.887-44.015-65.001-91.918-58.428
			s-30.779,74.835-37.873,104.338c-7.094,29.503-48.162-10.84-76.61,35.568C36.302,206,90.576,230.61,86.693,283.587
			C82.81,336.564,183.964,447.369,238.064,377.666z"/>
	</g>
</g>
<g id="OBJECTS">
	<g>
		<g>
			<path style="fill:#D31107;" d="M382.703,92c-2.632,0-4.773-2.141-4.773-4.773c0-2.632,2.141-4.773,4.773-4.773
				s4.773,2.141,4.773,4.773C387.476,89.859,385.335,92,382.703,92z M382.703,83.747c-1.919,0-3.48,1.561-3.48,3.48
				c0,1.919,1.561,3.48,3.48,3.48s3.48-1.561,3.48-3.48C386.183,85.308,384.622,83.747,382.703,83.747z"/>
		</g>
		<path style="fill:#009CA8;" d="M263.428,90.572l-0.025,2.962c-0.005,0.635,0.398,1.201,1.001,1.402l2.809,0.939
			c1.346,0.45,1.33,2.36-0.024,2.787l-2.825,0.891c-0.605,0.191-1.019,0.75-1.024,1.385l-0.025,2.962
			c-0.012,1.419-1.833,1.994-2.658,0.838l-1.72-2.411c-0.369-0.517-1.028-0.737-1.633-0.546l-2.825,0.891
			c-1.354,0.427-2.463-1.127-1.619-2.269l1.761-2.381c0.378-0.51,0.383-1.206,0.015-1.722l-1.72-2.411
			c-0.824-1.155,0.311-2.691,1.657-2.241l2.809,0.939c0.602,0.201,1.265-0.008,1.643-0.518l1.761-2.381
			C261.629,88.547,263.44,89.153,263.428,90.572z"/>
		<g>
			<path style="fill:#6AC8BF;" d="M149.264,170.291c-5.853,0-10.615-4.762-10.615-10.615c0-5.853,4.762-10.615,10.615-10.615
				c5.853,0,10.614,4.762,10.614,10.615C159.878,165.529,155.117,170.291,149.264,170.291z M149.264,151.937
				c-4.268,0-7.739,3.472-7.739,7.739c0,4.267,3.471,7.739,7.739,7.739c4.268,0,7.739-3.472,7.739-7.739
				C157.003,155.409,153.532,151.937,149.264,151.937z"/>
		</g>
		<path style="fill:#FCA13D;" d="M60.543,173.171l1.011,3.543c0.105,0.369,0.485,0.589,0.858,0.495l3.574-0.896
			c0.695-0.174,1.183,0.671,0.684,1.186l-2.563,2.647c-0.267,0.276-0.267,0.714,0,0.99l2.563,2.647
			c0.498,0.515,0.01,1.36-0.684,1.186l-3.574-0.896c-0.373-0.093-0.752,0.126-0.858,0.495l-1.011,3.543
			c-0.196,0.689-1.173,0.689-1.369,0l-1.011-3.543c-0.105-0.369-0.485-0.589-0.858-0.495l-3.574,0.896
			c-0.695,0.174-1.183-0.671-0.685-1.186l2.563-2.647c0.267-0.276,0.267-0.714,0-0.99l-2.563-2.647
			c-0.498-0.514-0.01-1.36,0.685-1.186l3.574,0.896c0.373,0.093,0.752-0.126,0.858-0.495l1.011-3.543
			C59.371,172.483,60.347,172.483,60.543,173.171z"/>
		<path style="fill:#D31107;" d="M435.821,209.609l1.104,3.867c0.115,0.403,0.529,0.642,0.936,0.54l3.901-0.978
			c0.758-0.19,1.291,0.732,0.747,1.294l-2.797,2.89c-0.292,0.301-0.292,0.78,0,1.081l2.797,2.89
			c0.544,0.562,0.011,1.484-0.747,1.294l-3.901-0.978c-0.407-0.102-0.821,0.137-0.936,0.54l-1.104,3.867
			c-0.214,0.752-1.28,0.752-1.494,0l-1.104-3.867c-0.115-0.403-0.529-0.642-0.936-0.54l-3.901,0.978
			c-0.758,0.19-1.291-0.732-0.747-1.294l2.797-2.89c0.292-0.301,0.292-0.779,0-1.081l-2.797-2.89
			c-0.544-0.561-0.011-1.484,0.747-1.294l3.901,0.978c0.407,0.102,0.821-0.137,0.936-0.54l1.104-3.867
			C434.541,208.858,435.606,208.858,435.821,209.609z"/>
		<path style="fill:#D31107;" d="M335.181,286.492l23.176,5.256c0.746,0.169,1.396-0.533,1.17-1.264l-7.036-22.699
			c-0.227-0.731-1.16-0.943-1.68-0.381l-16.14,17.443C334.151,285.409,334.434,286.323,335.181,286.492z"/>
		<path style="fill:#005B8E;" d="M162.285,36.994l12.494-5.727c0.696-0.319,0.785-1.272,0.161-1.715l-11.206-7.956
			c-0.624-0.443-1.494-0.044-1.566,0.718l-1.287,13.683C160.809,36.759,161.59,37.313,162.285,36.994z"/>
		<path style="fill:#FCA13D;" d="M298.852,155.117l8.317,3.059c0.718,0.264,1.454-0.348,1.323-1.103l-1.509-8.732
			c-0.13-0.754-1.029-1.085-1.616-0.595l-6.808,5.673C297.971,153.91,298.133,154.853,298.852,155.117z"/>
		<g>
			<path style="fill:#D31107;" d="M169.362,91.475c1.262,2.009,1.556,4.191,0.881,6.546c-0.675,2.355-2.135,4.238-4.38,5.649
				c-0.726,0.456-1.538,0.813-2.435,1.071c-0.897,0.258-1.811,0.396-2.741,0.416c-1.703-2.297-3.23-4.428-4.582-6.393
				c-1.353-1.964-2.697-4.01-4.033-6.137c-0.637-1.013-1.124-1.789-1.464-2.33c-0.339-0.54-0.654-1.06-0.945-1.56
				c0.589-0.936,1.284-1.808,2.082-2.616c0.8-0.808,1.663-1.504,2.592-2.088c1.975-1.241,3.72-1.837,5.234-1.788
				c1.514,0.05,2.691,0.741,3.528,2.074c0.531,0.844,0.805,1.731,0.824,2.661c0.019,0.93-0.22,1.881-0.716,2.852
				c1.335-0.462,2.54-0.548,3.616-0.259C167.901,89.863,168.747,90.496,169.362,91.475z M162.938,101.657
				c0.339-0.024,0.66-0.084,0.964-0.181c0.304-0.096,0.583-0.225,0.836-0.384c1.03-0.647,1.663-1.616,1.9-2.907
				c0.237-1.291,0.027-2.46-0.631-3.507c-0.541-0.861-1.199-1.354-1.972-1.48c-0.774-0.126-1.659,0.124-2.654,0.75
				c-0.355,0.223-0.717,0.509-1.086,0.859c-0.369,0.35-0.722,0.725-1.059,1.125c0.212,0.338,0.427,0.68,0.644,1.025
				c0.218,0.346,0.432,0.688,0.645,1.026c0.424,0.675,0.831,1.314,1.222,1.916C162.136,100.501,162.533,101.088,162.938,101.657z
				 M159.67,87.181c-0.488-0.776-1.106-1.218-1.856-1.324c-0.75-0.106-1.589,0.133-2.518,0.716
				c-0.135,0.085-0.283,0.196-0.444,0.332c-0.162,0.137-0.333,0.286-0.514,0.447c0.441,0.853,0.926,1.755,1.454,2.706
				c0.527,0.953,1.161,2.055,1.902,3.308c1.301-1.076,2.115-2.124,2.441-3.141C160.462,89.209,160.307,88.194,159.67,87.181z"/>
		</g>
		<g>
			<path style="fill:#005B8E;" d="M395.32,247.236c0.823,0.036,1.643,0.19,2.46,0.458c0.817,0.269,1.446,0.589,1.886,0.961
				c-0.616,2.943-1.886,5.097-3.809,6.46c-1.925,1.364-4.385,1.838-7.382,1.421c-2.998-0.417-5.188-1.75-6.57-4.001
				c-1.383-2.25-1.819-5.216-1.307-8.9c0.642-4.623,2.326-8.311,5.052-11.065c2.725-2.753,5.866-3.884,9.422-3.389
				c2.311,0.321,4.001,1.294,5.07,2.918c1.068,1.624,1.475,3.816,1.222,6.577c-1.03,0.323-1.935,0.508-2.719,0.555
				c-0.783,0.046-1.731-0.021-2.846-0.201c0.33-1.999,0.315-3.523-0.044-4.569c-0.359-1.046-1.072-1.644-2.139-1.792
				c-1.398-0.194-2.734,0.708-4.007,2.705c-1.275,1.998-2.133,4.584-2.574,7.759c-0.413,2.971-0.329,5.307,0.251,7.006
				c0.579,1.699,1.618,2.652,3.116,2.86c1.423,0.198,2.568-0.219,3.436-1.251C394.706,250.717,395.2,249.213,395.32,247.236z"/>
		</g>
		<g>
			<path style="fill:#FCA13D;" d="M262.411,226.689c-0.61,0.215-1.286,0.341-2.026,0.378c-0.74,0.037-1.62-0.002-2.641-0.118
				c0.206-1.615,0.362-3.134,0.468-4.558c0.105-1.424,0.169-2.782,0.19-4.074c-1.257-0.321-2.459-0.591-3.607-0.812
				c-1.147-0.221-2.241-0.391-3.281-0.513c-0.672,0.94-1.315,1.806-1.928,2.596c-0.614,0.79-1.221,1.52-1.824,2.19
				c-0.701-0.323-1.323-0.729-1.867-1.217c-0.545-0.489-0.951-1.015-1.219-1.577c2.348-2.509,4.44-5.059,6.274-7.647
				c1.834-2.59,3.848-5.889,6.044-9.899c0.749,0.212,1.415,0.428,1.996,0.648c0.582,0.22,1.201,0.497,1.858,0.829
				c0.965,3.478,1.578,7.149,1.838,11.014C262.946,217.794,262.855,222.046,262.411,226.689z M257.622,206.971
				c-0.702,1.303-1.41,2.564-2.123,3.781c-0.712,1.219-1.423,2.375-2.135,3.47c0.918,0.111,1.792,0.236,2.621,0.375
				c0.829,0.139,1.632,0.298,2.41,0.476c-0.042-1.451-0.127-2.852-0.255-4.203C258.011,209.521,257.838,208.221,257.622,206.971z"/>
		</g>
		<g>
			<g>
				<g>
					<path style="fill:#0086C9;" d="M284.257,175.856l-4.081-2.726c-0.29-0.194-0.685-0.03-0.752,0.312l-4.756,24.111
						L284.257,175.856z"/>
					<path style="opacity:0.5;fill:#0D71AB;" d="M278.328,178.972l-3.67,18.609l9.589-21.697l-0.245-0.164
						C282.695,176.832,280.412,178.546,278.328,178.972z"/>
				</g>
				<g>
					<path style="fill:#DBD0CA;" d="M286.998,169.34l-12.345,28.157l19.277-23.978l-5.995-4.382
						C287.624,168.91,287.186,169.005,286.998,169.34z"/>
					<g style="opacity:0.5;">
						<defs>
							<path id="SVGID_1_" style="opacity:0.5;" d="M286.972,169.287l-12.32,28.209l19.251-24.03l-5.995-4.382
								C287.599,168.858,287.16,168.953,286.972,169.287z"/>
						</defs>
						<clipPath id="SVGID_00000031926990574879098540000002453107332452509576_">
							<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
						</clipPath>
						<g style="clip-path:url(#SVGID_00000031926990574879098540000002453107332452509576_);">
							<g>
								
									<rect x="256.345" y="186.214" transform="matrix(0.5007 -0.8656 0.8656 0.5007 -22.5799 333.7014)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="258.539" y="187.482" transform="matrix(0.5003 -0.8659 0.8659 0.5003 -22.504 336.3935)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="260.732" y="188.751" transform="matrix(0.5007 -0.8656 0.8656 0.5007 -22.5853 338.7657)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="262.926" y="190.019" transform="matrix(0.5003 -0.8659 0.8659 0.5003 -22.5095 341.4572)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="265.12" y="191.287" transform="matrix(0.5007 -0.8656 0.8656 0.5007 -22.5775 343.8567)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="267.314" y="192.556" transform="matrix(0.5007 -0.8656 0.8656 0.5007 -22.5933 346.3621)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="269.507" y="193.824" transform="matrix(0.5006 -0.8657 0.8657 0.5006 -22.5643 348.9585)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="271.701" y="195.093" transform="matrix(0.5007 -0.8656 0.8656 0.5007 -22.5868 351.4505)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="273.895" y="196.361" transform="matrix(0.5006 -0.8657 0.8657 0.5006 -22.5692 354.0237)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="276.089" y="197.629" transform="matrix(0.5006 -0.8657 0.8657 0.5006 -22.57 356.5595)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="298.139" y="160.947" transform="matrix(0.5006 -0.8657 0.8657 0.5006 -2.3953 345.634)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="296.834" y="163.203" transform="matrix(0.5006 -0.8657 0.8657 0.5006 -4.9997 345.6315)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="295.53" y="165.459" transform="matrix(0.5005 -0.8657 0.8657 0.5005 -7.6042 345.6295)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="294.226" y="167.715" transform="matrix(0.5005 -0.8657 0.8657 0.5005 -10.2085 345.6271)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="292.921" y="169.971" transform="matrix(0.5005 -0.8657 0.8657 0.5005 -12.8124 345.6256)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="291.617" y="172.227" transform="matrix(0.5006 -0.8657 0.8657 0.5006 -15.4175 345.6216)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="290.313" y="174.482" transform="matrix(0.5005 -0.8657 0.8657 0.5005 -18.0214 345.6201)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="289.008" y="176.739" transform="matrix(0.5005 -0.8657 0.8657 0.5005 -20.6262 345.6176)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="287.704" y="178.994" transform="matrix(0.5005 -0.8657 0.8657 0.5005 -23.2306 345.6151)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="286.399" y="181.25" transform="matrix(0.5005 -0.8657 0.8657 0.5005 -25.8351 345.6122)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="285.095" y="183.506" transform="matrix(0.5005 -0.8657 0.8657 0.5005 -28.4393 345.6102)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="283.791" y="185.762" transform="matrix(0.5005 -0.8657 0.8657 0.5005 -31.0432 345.6086)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="282.486" y="188.018" transform="matrix(0.5006 -0.8657 0.8657 0.5006 -33.6489 345.6042)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="281.182" y="190.274" transform="matrix(0.5006 -0.8657 0.8657 0.5006 -36.2533 345.6017)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="279.878" y="192.53" transform="matrix(0.5006 -0.8657 0.8657 0.5006 -38.8577 345.5993)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="278.573" y="194.786" transform="matrix(0.5006 -0.8657 0.8657 0.5006 -41.462 345.5968)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="277.269" y="197.042" transform="matrix(0.5005 -0.8657 0.8657 0.5005 -44.0661 345.5949)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
						</g>
					</g>
				</g>
				<g>
					<path style="fill:#E8DDD6;" d="M291.539,170.417l-16.87,27.135l23.377-22.322l-5.555-4.928
						C292.203,170.048,291.758,170.102,291.539,170.417z"/>
					<g style="opacity:0.5;">
						<defs>
							<path id="SVGID_00000054250450083411496000000004295157640807527319_" style="opacity:0.5;" d="M291.518,170.363
								l-16.85,27.19l23.356-22.376l-5.555-4.928C292.182,169.994,291.737,170.047,291.518,170.363z"/>
						</defs>
						<clipPath id="SVGID_00000049196629979938112160000008583656988033874310_">
							<use xlink:href="#SVGID_00000054250450083411496000000004295157640807527319_"  style="overflow:visible;"/>
						</clipPath>
						<g style="clip-path:url(#SVGID_00000049196629979938112160000008583656988033874310_);">
							<g>
								
									<rect x="259.315" y="186.366" transform="matrix(0.5801 -0.8145 0.8145 0.5801 -34.002 307.1928)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="261.379" y="187.835" transform="matrix(0.5801 -0.8145 0.8145 0.5801 -34.3323 309.4914)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="263.443" y="189.305" transform="matrix(0.58 -0.8146 0.8146 0.58 -34.6458 311.8339)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="265.508" y="190.775" transform="matrix(0.5801 -0.8145 0.8145 0.5801 -34.9929 314.0885)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="267.572" y="192.244" transform="matrix(0.58 -0.8146 0.8146 0.58 -35.2993 316.4496)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="269.636" y="193.714" transform="matrix(0.58 -0.8146 0.8146 0.58 -35.6364 318.7305)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="271.701" y="195.184" transform="matrix(0.58 -0.8146 0.8146 0.58 -35.9666 321.0293)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="273.765" y="196.653" transform="matrix(0.58 -0.8146 0.8146 0.58 -36.2968 323.3282)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="275.829" y="198.123" transform="matrix(0.58 -0.8146 0.8146 0.58 -36.627 325.6271)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="277.894" y="199.593" transform="matrix(0.58 -0.8146 0.8146 0.58 -36.9501 327.9446)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="302.105" y="163.07" transform="matrix(0.58 -0.8146 0.8146 0.58 -17.2218 320.6314)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="300.594" y="165.193" transform="matrix(0.58 -0.8146 0.8146 0.58 -19.5855 320.2928)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="299.083" y="167.316" transform="matrix(0.58 -0.8146 0.8146 0.58 -21.9502 319.9526)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="297.571" y="169.439" transform="matrix(0.58 -0.8146 0.8146 0.58 -24.3143 319.6131)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="296.06" y="171.562" transform="matrix(0.58 -0.8146 0.8146 0.58 -26.6784 319.2736)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="294.549" y="173.684" transform="matrix(0.58 -0.8146 0.8146 0.58 -29.0422 318.9348)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="293.038" y="175.807" transform="matrix(0.58 -0.8146 0.8146 0.58 -31.4063 318.5953)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="291.526" y="177.93" transform="matrix(0.58 -0.8146 0.8146 0.58 -33.771 318.255)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="290.015" y="180.053" transform="matrix(0.58 -0.8146 0.8146 0.58 -36.1349 317.9162)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="288.503" y="182.176" transform="matrix(0.58 -0.8146 0.8146 0.58 -38.4992 317.576)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="286.992" y="184.299" transform="matrix(0.58 -0.8146 0.8146 0.58 -40.8636 317.2356)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="285.481" y="186.422" transform="matrix(0.58 -0.8146 0.8146 0.58 -43.2274 316.897)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="283.969" y="188.545" transform="matrix(0.58 -0.8146 0.8146 0.58 -45.5919 316.5574)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="282.458" y="190.667" transform="matrix(0.58 -0.8146 0.8146 0.58 -47.9557 316.2187)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="280.947" y="192.79" transform="matrix(0.58 -0.8146 0.8146 0.58 -50.3198 315.8792)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="279.436" y="194.913" transform="matrix(0.58 -0.8146 0.8146 0.58 -52.6842 315.5387)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="277.924" y="197.036" transform="matrix(0.58 -0.8146 0.8146 0.58 -55.0482 315.1995)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
						</g>
					</g>
				</g>
				<g>
					<path style="fill:#F8EEE7;" d="M295.44,172.567l-20.772,24.986l26.837-19.627l-5.108-5.39
						C296.134,172.257,295.686,172.272,295.44,172.567z"/>
					<g style="opacity:0.5;">
						<defs>
							<path id="SVGID_00000029731369265474195860000001501702321019889563_" style="opacity:0.5;" d="M295.424,172.511
								l-20.772,24.986l26.837-19.627l-5.108-5.39C296.118,172.201,295.67,172.216,295.424,172.511z"/>
						</defs>
						<clipPath id="SVGID_00000021096573160595744810000017044904460379298971_">
							<use xlink:href="#SVGID_00000029731369265474195860000001501702321019889563_"  style="overflow:visible;"/>
						</clipPath>
						<g style="clip-path:url(#SVGID_00000021096573160595744810000017044904460379298971_);">
							<g>
								
									<rect x="261.858" y="187.54" transform="matrix(0.6484 -0.7613 0.7613 0.6484 -43.251 281.8591)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="263.788" y="189.183" transform="matrix(0.6481 -0.7615 0.7615 0.6481 -43.7915 284.0155)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="265.717" y="190.826" transform="matrix(0.6484 -0.7613 0.7613 0.6484 -44.3952 285.9522)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="267.647" y="192.468" transform="matrix(0.6483 -0.7614 0.7614 0.6483 -44.9527 288.0501)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="269.576" y="194.111" transform="matrix(0.6483 -0.7614 0.7614 0.6483 -45.5247 290.0971)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="271.506" y="195.754" transform="matrix(0.6484 -0.7613 0.7613 0.6484 -46.1116 292.092)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="273.435" y="197.396" transform="matrix(0.6481 -0.7615 0.7615 0.6481 -46.6514 294.2527)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="275.365" y="199.039" transform="matrix(0.6483 -0.7614 0.7614 0.6483 -47.2409 296.238)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="277.294" y="200.682" transform="matrix(0.6481 -0.7615 0.7615 0.6481 -47.7954 298.3475)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="279.224" y="202.324" transform="matrix(0.6483 -0.7614 0.7614 0.6483 -48.385 300.332)" style="fill:#2F8ED7;" width="43.294" height="0.422"/>
							</g>
							<g>
								
									<rect x="305.396" y="166.127" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -29.6137 296.0439)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="303.707" y="168.111" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -31.7183 295.4566)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="302.017" y="170.096" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -33.8237 294.868)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="300.328" y="172.08" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -35.929 294.279)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="298.639" y="174.064" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -38.0338 293.6915)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="296.95" y="176.048" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -40.139 293.1026)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="295.261" y="178.033" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -42.244 292.5143)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="293.571" y="180.017" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -44.3493 291.9259)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="291.882" y="182.001" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -46.4543 291.3378)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="290.193" y="183.985" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -48.5596 290.7484)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="288.504" y="185.969" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -50.6643 290.1612)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="286.814" y="187.954" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -52.7689 289.5747)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="285.125" y="189.938" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -54.8744 288.9855)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="283.436" y="191.922" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -56.9797 288.3961)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="281.747" y="193.906" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -59.0845 287.809)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="280.057" y="195.891" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -61.1897 287.2199)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
							<g>
								
									<rect x="278.368" y="197.875" transform="matrix(0.6482 -0.7614 0.7614 0.6482 -63.2945 286.6324)" style="fill:#2F8ED7;" width="0.422" height="27.892"/>
							</g>
						</g>
					</g>
				</g>
				<path style="fill:#E13F4E;" d="M311.809,235.593l-37.141-38.04l22.618-22.084c1.034-1.009,2.69-0.989,3.699,0.044l33.486,34.297
					c1.009,1.034,0.99,2.69-0.044,3.699L311.809,235.593z"/>
				<path style="fill:#2F8ED7;" d="M311.846,235.753l-37.141-38.041l22.618-22.084c1.034-1.009,2.69-0.99,3.699,0.044l33.486,34.297
					c1.009,1.034,0.99,2.69-0.044,3.699L311.846,235.753z"/>
				<path style="opacity:0.5;fill:#0D71AB;" d="M318.101,229.45c-8.086-1.717-17.328-7.844-24.718-17.209
					c-5.847-7.409-9.45-15.473-10.599-22.612l-8.115,7.923l37.141,38.04L318.101,229.45z"/>
				<g>
					<path style="fill:#F8EEE7;" d="M327.489,213.281l-2.535,2.596c-1.349,1.381-3.562,1.408-4.943,0.059l0,0
						c-1.381-1.349-1.408-3.562-0.059-4.943l2.536-2.597c1.349-1.381,3.562-1.408,4.943-0.059l0,0
						C328.812,209.686,328.838,211.9,327.489,213.281z"/>
					<g>
						<g>
							<path style="fill:#2F8ED7;" d="M325.644,207.488l-6.413,6.567c-0.05,0.051-0.132,0.052-0.183,0.002
								c-0.051-0.05-0.052-0.132-0.002-0.183l6.413-6.568c0.05-0.051,0.132-0.052,0.183-0.002
								C325.693,207.355,325.694,207.437,325.644,207.488z"/>
						</g>
						<g>
							<path style="fill:#2F8ED7;" d="M327.019,208.83l-6.413,6.568c-0.05,0.051-0.132,0.052-0.183,0.002
								c-0.051-0.05-0.052-0.132-0.002-0.183l6.413-6.568c0.05-0.051,0.132-0.052,0.183-0.002
								C327.068,208.697,327.069,208.779,327.019,208.83z"/>
						</g>
						<g>
							<path style="fill:#2F8ED7;" d="M328.393,210.173l-6.413,6.568c-0.05,0.051-0.132,0.052-0.183,0.002
								c-0.051-0.05-0.052-0.132-0.002-0.183l6.413-6.568c0.05-0.051,0.132-0.052,0.183-0.002
								C328.442,210.039,328.443,210.121,328.393,210.173z"/>
						</g>
					</g>
				</g>
				<g>
					<path style="fill:#F8EEE7;" d="M327.526,213.441l-2.535,2.597c-1.349,1.381-3.562,1.408-4.943,0.059l0,0
						c-1.381-1.349-1.408-3.562-0.059-4.943l2.535-2.596c1.349-1.381,3.562-1.408,4.943-0.059l0,0
						C328.848,209.846,328.874,212.059,327.526,213.441z"/>
					<g>
						<g>
							<path style="fill:#2F8ED7;" d="M325.68,207.648l-6.413,6.568c-0.05,0.051-0.132,0.052-0.183,0.002
								c-0.051-0.05-0.052-0.132-0.002-0.183l6.413-6.568c0.05-0.051,0.132-0.052,0.183-0.002
								C325.729,207.515,325.73,207.597,325.68,207.648z"/>
						</g>
						<g>
							<path style="fill:#2F8ED7;" d="M327.055,208.99l-6.413,6.568c-0.05,0.051-0.132,0.052-0.183,0.002
								c-0.051-0.05-0.052-0.132-0.002-0.183l6.413-6.568c0.05-0.051,0.132-0.052,0.183-0.002
								C327.104,208.857,327.105,208.939,327.055,208.99z"/>
						</g>
						<g>
							<path style="fill:#2F8ED7;" d="M328.429,210.333l-6.413,6.568c-0.05,0.051-0.132,0.052-0.183,0.002
								c-0.051-0.05-0.052-0.132-0.002-0.183l6.413-6.568c0.05-0.051,0.132-0.052,0.183-0.002
								C328.478,210.199,328.479,210.281,328.429,210.333z"/>
						</g>
					</g>
				</g>
				<path style="opacity:0.1;fill:#FFFFFF;" d="M300.734,202.038c-6.322-6.84-8.824-14.809-5.587-17.801
					c3.237-2.992,10.986,0.127,17.308,6.967s8.824,14.809,5.587,17.801C314.805,211.997,307.056,208.878,300.734,202.038z"/>
			</g>
			<g>
				<g>
					<polygon style="fill:#EEEAE6;" points="443.494,158.508 447.574,168.512 463.352,157.28 478.136,131.91 					"/>
					<path style="opacity:0.15;fill:#41383E;" d="M471.641,136.898c3.122,4.746-19.678,23.162-25.609,27.832l1.543,3.782
						l15.778-11.231l14.784-25.37L471.641,136.898z"/>
					<path style="opacity:0.15;fill:#41383E;" d="M455.072,163.361l-5.165-1.45c0.178,0.427,0.315,0.88,0.391,1.357
						c0.22,1.388-0.063,2.738-0.71,3.869L455.072,163.361z"/>
					<polygon style="fill:#F2F2F2;" points="478.136,131.91 430.826,151.569 443.494,158.508 					"/>
					<polygon style="fill:#FFFFFF;" points="478.136,131.91 448.767,161.51 464.453,169.674 					"/>
					<path style="opacity:0.1;fill:#41383E;" d="M478.142,131.908l-11.892,11.986c2.582-1.651,4.928-2.787,5.843-2.211
						c0.896,0.564,1.012,2.491,0.801,4.71L478.142,131.908z"/>
					<path style="opacity:0.1;fill:#41383E;" d="M461.179,145.037l17.206-13.21l-19.764,8.212c3.374-0.784,5.973-1.201,6.53-0.796
						C465.728,139.663,463.934,142.003,461.179,145.037z"/>
				</g>
				<g>
					<path style="fill:#2F8ED7;" d="M460.095,186.005c-2.858-0.201-5.152-1.429-6.5-3.493c-0.509-0.78-0.855-1.655-1.032-2.563
						c-0.452-0.129-0.899-0.294-1.339-0.501c-2.147-1.01-3.555-2.41-4.184-4.161c-0.916-2.549,0.165-4.888,0.211-4.986
						c0.093-0.196,0.327-0.28,0.523-0.188c0.197,0.092,0.281,0.327,0.189,0.523c-0.1,0.215-2.388,5.284,3.596,8.098
						c0.292,0.137,0.587,0.254,0.887,0.352c-0.085-1.128,0.084-2.276,0.52-3.331c0.993-2.402,3.201-4.265,5.624-4.746
						c1.9-0.377,3.672,0.143,4.989,1.464c1.021,1.024,1.028,2.433,0.02,3.866c-1.675,2.379-6.011,4.566-10.187,3.805
						c0.172,0.684,0.454,1.34,0.842,1.935c1.29,1.977,3.579,3.104,6.433,3.163c0.218,0,0.39,0.184,0.385,0.402
						c-0.005,0.218-0.185,0.39-0.402,0.385C460.477,186.027,460.284,186.018,460.095,186.005z M453.258,179.307
						c3.915,0.846,8.155-1.228,9.698-3.42c0.55-0.782,1.008-1.912,0.066-2.857c-1.125-1.129-2.644-1.572-4.278-1.247
						c-2.172,0.431-4.154,2.109-5.05,4.274C453.269,177.085,453.128,178.213,453.258,179.307z"/>
				</g>
			</g>
			<g>
				<g>
					<g>
						
							<rect x="93.633" y="187.585" transform="matrix(0.7303 -0.6831 0.6831 0.7303 -111.9727 122.2358)" style="fill:#FDA740;" width="10.407" height="30.713"/>
						<g>
							<g>
								
									<rect x="95.482" y="191.507" transform="matrix(0.7304 -0.683 0.683 0.7304 -115.0062 121.3185)" style="fill:#FA9F3E;" width="1.373" height="29.655"/>
								
									<rect x="96.709" y="190.36" transform="matrix(0.7304 -0.683 0.683 0.7304 -113.8912 121.8464)" style="fill:#FA9F3E;" width="1.373" height="29.655"/>
								
									<rect x="97.936" y="189.212" transform="matrix(0.7304 -0.683 0.683 0.7304 -112.7726 122.3646)" style="fill:#FA9F3E;" width="1.373" height="29.655"/>
								
									<rect x="99.167" y="188.061" transform="matrix(0.7303 -0.6831 0.6831 0.7303 -111.6625 122.9159)" style="fill:#FA9F3E;" width="1.373" height="29.655"/>
								
									<rect x="100.394" y="186.914" transform="matrix(0.7303 -0.6831 0.6831 0.7303 -110.548 123.4453)" style="fill:#FA9F3E;" width="1.373" height="29.655"/>
								
									<rect x="101.621" y="185.766" transform="matrix(0.7303 -0.6831 0.6831 0.7303 -109.4372 123.9845)" style="fill:#FA9F3E;" width="1.373" height="29.655"/>
							</g>
						</g>
						<g>
							<g>
								<path style="fill:#EAB964;" d="M111.643,211.99c0,0-2.026-0.277-1.684,1.575c0,0-1.936-0.399-1.615,1.511l-0.038,0.035
									c-0.276-0.099-1.86-0.593-1.427,1.334l-0.023,0.021c-0.228-0.028-1.883-0.182-1.331,1.245l9.298,2.481l-1.697-9.59
									C113.126,210.603,111.615,209.844,111.643,211.99z"/>
								<path style="fill:#41383E;" d="M109.921,218.884c0,0,1.317-1.94,4.007-3.748l0.895,5.056L109.921,218.884z"/>
							</g>
						</g>
						<path style="fill:#ED6B54;" d="M93.708,189.84l-7.601,7.109l-3.649-3.902c-1.4-1.497-1.321-3.844,0.175-5.244l2.181-2.04
							c1.497-1.4,3.844-1.321,5.244,0.175L93.708,189.84z"/>
						<path style="opacity:0.1;fill:#FFFFFF;" d="M87.06,185.066c0.331,0.354-0.581,1.745-2.037,3.107
							c-1.456,1.362-2.904,2.178-3.235,1.824c-0.331-0.354,0.581-1.745,2.036-3.106C85.28,185.529,86.729,184.712,87.06,185.066z"/>
						<path style="opacity:0.3;fill:#E04C38;" d="M87.709,184.515c0,0,1.364,0.514-2.353,4.012c-3.739,3.475-4.161,2.08-4.161,2.08
							c-0.077,0.824,0.599,1.73,0.599,1.73l3.489,3.731l3.733-3.492l0.134-0.126l3.733-3.491l-3.489-3.731
							C89.395,185.229,88.536,184.494,87.709,184.515z"/>
						
							<rect x="84.271" y="191.281" transform="matrix(0.7303 -0.6831 0.6831 0.7303 -107.6594 113.1411)" style="fill:#D2CECD;" width="10.407" height="3.3"/>
					</g>
					<path style="opacity:0.15;fill:#FFFFFF;" d="M96.93,200.164c2.561,3.17,3.741,6.463,2.636,7.356
						c-1.105,0.893-4.077-0.953-6.637-4.123c-2.561-3.17-3.741-6.463-2.637-7.356C91.397,195.148,94.369,196.994,96.93,200.164z"/>
				</g>
				<g>
					<path style="fill:#41383E;" d="M116.184,233.036c-0.385-0.577-0.472-1.127-0.258-1.637c0.672-1.602,4.092-1.981,9.232-2.377
						c2.141-0.165,5.059-0.389,5.548-0.802c0.05-0.194,0.018-0.364-0.102-0.543c-0.002-0.003-0.005-0.006-0.007-0.009
						c-0.279-0.408-1.728-1.775-8.664-2.22c-6.653-0.428-8.331-2.83-8.399-2.932c-0.181-0.271-0.108-0.638,0.163-0.819
						c0.268-0.179,0.631-0.11,0.814,0.156c0.039,0.052,1.572,2.036,7.497,2.417c3.672,0.236,8.312,0.901,9.563,2.732
						c0.319,0.483,0.425,1.06,0.246,1.625c-0.333,1.048-2.367,1.25-6.569,1.574c-2.914,0.224-7.791,0.6-8.234,1.656
						c-0.09,0.213,0.156,0.554,0.312,0.737c3.314,3.939,10.733,4.629,10.807,4.635c0.324,0.029,0.565,0.313,0.537,0.639
						c-0.028,0.324-0.314,0.565-0.639,0.537c-0.324-0.028-7.978-0.735-11.609-5.051
						C116.334,233.246,116.254,233.141,116.184,233.036z M130.729,228.2L130.729,228.2L130.729,228.2z"/>
				</g>
			</g>
		</g>
	</g>
	<g>
		<g>
			<path style="fill:#D31107;" d="M165.148,193.456c16.111-0.523,29.457,7.869,29.81,18.744c0.175,5.404-2.892,10.403-7.998,14.131
				c-0.069,0.876-0.224,5.093,3.029,6.499c0.464,0.201,0.326,0.898-0.178,0.927c-2.658,0.154-6.489-0.382-8.631-4.139
				c-4.292,1.88-9.333,3.043-14.754,3.219c-16.111,0.523-29.457-7.869-29.81-18.744
				C136.263,203.219,149.038,193.979,165.148,193.456z"/>
			<path style="fill:#FCA13D;" d="M220.887,31.648c-16.782-0.544-30.685,8.197-31.053,19.525c-0.183,5.629,3.012,10.837,8.331,14.72
				c0.072,0.912,0.233,5.305-3.155,6.77c-0.483,0.209-0.34,0.935,0.186,0.966c2.769,0.16,6.76-0.398,8.991-4.311
				c4.471,1.958,9.723,3.17,15.369,3.353c16.782,0.545,30.685-8.197,31.053-19.525C250.976,41.818,237.669,32.193,220.887,31.648z"
				/>
			<path style="fill:#005B8E;" d="M430.819,69.796c-17.336-0.563-31.698,8.468-32.077,20.169
				c-0.189,5.815,3.112,11.195,8.606,15.206c0.074,0.942,0.241,5.48-3.259,6.993c-0.499,0.216-0.351,0.966,0.192,0.998
				c2.861,0.166,6.983-0.411,9.288-4.453c4.619,2.022,10.043,3.275,15.876,3.464c17.336,0.563,31.698-8.468,32.077-20.17
				C461.901,80.301,448.155,70.359,430.819,69.796z"/>
		</g>
		<g>
			<path style="fill:#005B8E;" d="M98.98,158.55c15.537-7.565,26.245-23.504,26.245-41.947c0-25.754-20.878-46.632-46.632-46.632
				c-25.754,0-46.632,20.878-46.632,46.632c0,25.754,20.878,46.632,46.632,46.632c2.755,0,5.449-0.253,8.073-0.711l35.04,38.544
				L98.98,158.55z"/>
			<path style="fill:#6AC8BF;" d="M238.975,184.132c12.601-6.136,21.286-19.062,21.286-34.02c0-20.887-16.933-37.82-37.82-37.82
				s-37.82,16.933-37.82,37.82s16.933,37.82,37.82,37.82c2.234,0,4.419-0.205,6.547-0.576l4.365,25.924L238.975,184.132z"/>
			<path style="fill:#FCA13D;" d="M358.672,207.436c-15.537-7.565-26.245-23.504-26.245-41.947
				c0-25.754,20.878-46.632,46.632-46.632s46.632,20.878,46.632,46.632s-20.878,46.632-46.632,46.632
				c-2.755,0-5.449-0.253-8.072-0.711l-35.04,38.544L358.672,207.436z"/>
			<path style="fill:#D31107;" d="M304.892,110.624c-12-5.843-20.271-18.153-20.271-32.398c0-19.891,16.125-36.017,36.017-36.017
				s36.017,16.125,36.017,36.017s-16.125,36.017-36.017,36.017c-2.128,0-4.208-0.195-6.235-0.549l-17.55,12.102L304.892,110.624z"/>
			<path style="fill:#009CA8;" d="M390.058,315.81c-7.512-11.032-8.576-25.824-1.5-38.187c9.881-17.264,31.886-23.249,49.15-13.369
				c17.264,9.881,23.249,31.886,13.369,49.15c-9.881,17.264-31.886,23.249-49.15,13.369c-1.847-1.057-3.556-2.26-5.139-3.573
				l-21.243,1.786L390.058,315.81z"/>
		</g>
	</g>
	<g>
		<path style="fill:#D8D8D8;" d="M124.401,270.991c0,0-1.107-32.782-30.567-47.179c0,0-20.821-4.652-23.036-13.29
			c0,0-2.88,4.208-2.88,9.081l47.844,66.228C115.762,285.832,129.938,293.141,124.401,270.991z"/>
		<path style="fill:#FAF0E9;" d="M107.788,240.424c-10.782-10.607-43.851-23.91-43.851-23.91l-9.197,5.598
			c5.42,2.843,13.584,10.439,13.584,10.439c-7.309-4.334-15.166-7.233-17.504-8.053l-4.562,2.777l83.236,66.974
			C129.495,294.249,123.673,256.053,107.788,240.424z"/>
		<path style="fill:#D8D8D8;" d="M120.532,285.554c0,0,8.642-37.706,50.65-40.841c41.416-3.09,95.67-6.456,103.894-10.457
			l5.112,6.001L120.532,285.554z"/>
		<path style="fill:#FAF0E9;" d="M120.857,280.516c0,0,3.362-11.182,32.116-23.224c28.754-12.042,69.551-9.093,96.83-13.517
			c27.28-4.424,31.154-5.823,31.154-5.823l10.248,10.504l-167.725,44.717L120.857,280.516z"/>
		<g>
			<path style="fill:#FF3028;" d="M298.397,246.939l92.448,190.041c2.116,4.348-0.581,9.503-5.359,10.244l-146.829,22.068
				l-99.376-191.145l151.059-35.068C293.597,242.337,296.935,243.936,298.397,246.939z"/>
			<path style="fill:#BC0702;" d="M288.748,244.45c3.256-0.742,6.595,0.857,8.057,3.861l93.062,191.186
				c0.777,1.597,0.901,3.302,0.514,4.858c1.466-2.044,1.858-4.823,0.635-7.336l-93.062-191.186c-1.462-3.003-4.8-4.603-8.056-3.86
				l-151.059,35.201l1.025,1.841L288.748,244.45z"/>
			<path style="fill:#BC0702;" d="M43.751,227.007L43.751,227.007c2.221-0.222,4.44,0.433,6.183,1.826l58.414,46.665l6.235,11.257
				L43.751,227.007z"/>
			<path style="fill:#034354;" d="M107.097,277.172l1.252-1.674c0,0,3.306,8.12,30.49,1.674l4.845,9.389l-34.853,4.402
				L107.097,277.172z"/>
			<path style="fill:#035275;" d="M139.794,278.978l98.863,190.315c0,0-23.388,10.441-31.324,0.418L107.097,277.172
				C107.097,277.172,112.646,291.09,139.794,278.978z"/>
			<path style="fill:#FF3028;" d="M107.097,277.172l-62.709-49.935c-1.13-0.9-2.687,0.385-2.017,1.665l99.275,189.744
				c0.901,1.722,2.174,3.223,3.727,4.392l61.96,46.671L107.097,277.172z"/>
			<path style="opacity:0.3;fill:#BC0702;" d="M107.097,277.172l-62.709-49.935c-1.13-0.9-2.687,0.385-2.017,1.665l2.008,3.838
				c20.441,20.399,66.05,69.454,92.456,125.199c18.636,39.342,13.556,54.827,4.826,60.731c0.9,1.712,2.167,3.204,3.712,4.368
				l61.96,46.671L107.097,277.172z"/>
			<path style="opacity:0.3;fill:#BC0702;" d="M388.42,431.998c-13.438,6.885-60.76,27.039-119.603,5.203
				c-55.984-20.776-86.787-126.732-96.802-167.942l-33.2,7.91l99.671,191.951l146.999-21.897c4.779-0.741,7.476-5.896,5.359-10.244
				L388.42,431.998z"/>
			<g style="opacity:0.3;">
				<path style="fill:#034354;" d="M107.604,277.96l-0.304-0.584C107.3,277.375,107.389,277.604,107.604,277.96z"/>
				<path style="fill:#034354;" d="M138.838,277.172c0.036,4.045,94.848,175.289,70.657,175.168
					c-14.927-0.075-80.071-153.027-101.69-174.07c-0.076-0.112-0.144-0.215-0.201-0.31l99.932,191.953
					c7.935,10.024,31.324-0.418,31.324-0.418L138.838,277.172z"/>
			</g>
			
				<ellipse transform="matrix(0.8997 -0.4365 0.4365 0.8997 -142.2035 110.5455)" style="opacity:0.05;fill:#FFFFFF;" cx="169.462" cy="364.729" rx="8.339" ry="78.051"/>
			<g>
				<path style="fill:#FAF0E9;" d="M247.491,455.562c-4.057,0-7.846-2.25-9.757-5.929l-81.008-155.915
					c-0.971-1.868-1.022-4.037-0.142-5.95c0.881-1.913,2.564-3.284,4.616-3.761l125.444-29.122c3.549-0.809,7.139,0.909,8.731,4.18
					l81.355,167.137c1.093,2.244,1.055,4.8-0.103,7.012c-1.157,2.212-3.235,3.699-5.702,4.082l-121.813,18.146
					C248.571,455.522,248.029,455.562,247.491,455.562z M288.392,257.687c-0.355,0-0.716,0.04-1.075,0.122l-125.437,29.12
					c-1.143,0.266-2.079,1.028-2.57,2.094c-0.491,1.065-0.462,2.272,0.079,3.313l81.008,155.915c1.575,3.03,4.907,4.728,8.276,4.224
					l121.805-18.145c1.507-0.233,2.783-1.147,3.494-2.507c0.712-1.359,0.734-2.93,0.064-4.309l-81.355-167.137
					C291.862,258.701,290.19,257.687,288.392,257.687z"/>
			</g>
		</g>
		<g>
			<path style="fill:#C3C6C2;" d="M124.22,278.872c9.035-10.377,21.446-17.304,34.212-22.081c12.918-4.758,26.682-6.529,40.35-7.375
				c27.276-1.662,54.784-2.449,81.407-9.158c-26.441,7.469-54.099,8.413-81.34,10.154c-20.369,0.982-41.05,5.004-58.66,15.744
				C134.357,269.673,128.764,273.761,124.22,278.872L124.22,278.872z"/>
			<path style="fill:#C3C6C2;" d="M129.61,277.075c14.635-13.727,34.518-21.151,54.427-22.338c23.184-1.869,46.532-1.973,69.5-5.884
				c9.828-1.68,19.544-3.911,29.091-6.814c-25.262,8.679-52.1,11.12-78.619,12.381c-13.206,0.816-26.64,0.923-39.496,4.288
				C151.75,262.155,139.533,268.26,129.61,277.075L129.61,277.075z"/>
			<path style="fill:#C3C6C2;" d="M134.102,276.476c12.987-10.367,29.284-16.701,45.821-18.217
				c16.279-1.04,33.473-1.878,49.703-3.343c-8.229,1.127-16.501,1.919-24.777,2.612c-8.095,0.742-16.725,1.054-24.839,1.727
				C163.557,260.677,147.485,266.815,134.102,276.476L134.102,276.476z"/>
			<path style="fill:#C3C6C2;" d="M140.989,274.38c9.757-5.862,20.579-10.161,31.873-11.918c8.5-0.961,17.056-0.452,25.557-1.393
				c2.827-0.241,5.65-0.56,8.449-1.062c-5.529,1.372-11.223,1.958-16.895,2.397c-5.643,0.501-11.37,0.227-16.971,1.048
				C161.804,265.09,151.047,269.264,140.989,274.38L140.989,274.38z"/>
			<path style="fill:#C3C6C2;" d="M148.775,273.482c5.125-3.69,11.395-5.482,17.494-6.824c4.453-0.975,9.465-1.485,14.03-1.768
				c1.572-0.03,3.14-0.093,4.71-0.092c-1.556,0.225-3.114,0.386-4.667,0.58C169.63,266.897,158.463,268.406,148.775,273.482
				L148.775,273.482z"/>
		</g>
		<g>
			<path style="fill:#C3C6C2;" d="M131.32,267.023c12.439-14.323,31.828-21.207,50.469-21.641
				c31.244-0.571,62.447-3.709,93.154-9.532c-30.557,6.726-61.857,10.051-93.133,10.44
				C163.306,246.683,144.076,253.16,131.32,267.023L131.32,267.023z"/>
			<path style="fill:#C3C6C2;" d="M138.94,261.943c13.682-10.839,31.698-14.304,48.749-15.101
				c16.67-1.087,35.051-2.082,51.577-3.764c11.431-1.144,22.841-2.627,34.06-5.149c-11.13,2.923-22.554,4.606-33.978,5.914
				c-16.504,1.877-34.931,2.949-51.604,3.906C170.722,248.482,152.924,251.621,138.94,261.943L138.94,261.943z"/>
		</g>
		<g>
			<path style="fill:#C3C6C2;" d="M122.545,277.183c-3.112-13.165-8.627-30.089-20.106-38.16
				c-12.029-8.538-25.606-14.66-39.005-20.721c10.362,3.945,20.535,8.487,30.195,13.956c4.816,2.781,9.676,5.662,13.678,9.591
				C116.172,251.467,120.242,264.572,122.545,277.183L122.545,277.183z"/>
			<path style="fill:#C3C6C2;" d="M119.774,276.721c-2.521-13.475-7.506-29.186-20.477-35.9
				c-10.734-6.412-26.598-14.809-37.712-20.903c6.506,3.07,12.93,6.306,19.311,9.628c6.379,3.326,12.687,6.789,18.91,10.416
				c3.131,1.768,6.198,3.95,8.516,6.763C115.287,255.124,118.44,266.093,119.774,276.721L119.774,276.721z"/>
			<path style="fill:#C3C6C2;" d="M116.08,276.952c-1.31-10.323-4.348-21.17-11.704-28.836c-7.77-7.162-17.536-11.841-26.77-16.874
				c-6.276-3.284-12.637-6.403-19.022-9.475c13.872,5.848,27.643,12.27,40.212,20.613c2.097,1.518,4.454,3.211,6.226,5.098
				C112.464,255.311,115.335,266.468,116.08,276.952L116.08,276.952z"/>
			<path style="fill:#C3C6C2;" d="M112.616,275.797c0.606-9.967-4.545-19.535-11.99-25.887c-8.975-7.07-18.943-12.85-28.996-18.253
				c-2.694-1.444-5.431-2.807-8.197-4.119c5.7,2.243,11.207,4.958,16.597,7.873c5.386,2.928,10.616,6.146,15.685,9.603
				c5.042,3.451,9.879,7.627,12.932,13.058C111.749,263.398,113.392,269.696,112.616,275.797L112.616,275.797z"/>
			<path style="fill:#C3C6C2;" d="M109.615,274.643c-1.309-5.749-4.59-10.867-8.258-15.395c-3.59-4.453-8.209-8.575-12.768-12.046
				c-1.125-0.929-2.918-2.027-4.127-2.877c-1.207-0.847-2.942-2.039-4.223-2.736c-10.031-6.176-21.033-10.653-32.045-14.743
				c6.462,1.977,12.816,4.256,19.039,6.906c12.99,5.935,25.797,13.627,34.67,25.063
				C105.483,263.457,108.657,268.789,109.615,274.643L109.615,274.643z"/>
			<path style="fill:#C3C6C2;" d="M110.076,269.332c-6.173-14.247-18.606-24.964-32.08-32.151c-3.034-1.671-6.535-3.188-9.671-4.63
				c3.238,1.156,6.859,2.503,9.957,4.067C92.11,243.319,104.945,254.499,110.076,269.332L110.076,269.332z"/>
			<path style="fill:#C3C6C2;" d="M105.458,270.255c-12.541-17.811-31.314-30.429-51.492-38.099c0,0,3.868,1.294,3.868,1.294
				C76.704,239.918,95.031,253.053,105.458,270.255L105.458,270.255z"/>
			<path style="fill:#C3C6C2;" d="M98.993,267.023c-9.359-10.449-20.702-18.91-32.558-26.323
				C78.994,246.92,90.658,255.663,98.993,267.023L98.993,267.023z"/>
			<path style="fill:#C3C6C2;" d="M121.16,259.634c-0.81-8.439-6.493-15.383-12.407-21.03c-0.541-0.471-3.56-2.96-4.168-3.471
				l-3.561-2.488c-9.569-6.616-20.509-10.986-31.356-15.036c2.787,0.851,5.558,1.765,8.343,2.648c2.327,0.774,5.83,2.303,8.16,3.197
				c2.332,0.925,5.598,2.712,7.911,3.797c1.28,0.75,4.455,2.612,5.686,3.335c1.254,0.759,4.15,2.931,5.4,3.792
				c0.626,0.538,3.631,3.063,4.194,3.566c1.777,1.777,3.704,3.673,5.197,5.693C118.077,248.19,121.062,253.754,121.16,259.634
				L121.16,259.634z"/>
		</g>
		<path style="opacity:0.1;fill:#191715;" d="M280.958,237.952l4.897,4.923l-50.22,11.695
			C235.635,254.57,283.972,242.832,280.958,237.952z"/>
		<path style="opacity:0.1;fill:#191715;" d="M149.641,258.526c-15.353,7.336-24.629,15.988-24.629,15.988l-2.508-7.818
			c0.376,3.328-0.492,4.77-1.863,5.18c-1.504,0.449-3.004-0.702-3.227-2.256c-1.348-9.394-10.206-32.255-62.673-47.508
			c0,0,7.483,4.361,13.584,10.439c0,0,42.521,11.795,43.823,36.122c0.86,9.115-34.397-17.887-34.397-17.887l30.598,24.712
			c2.89,5.243,14.935,4.106,14.935,4.106c0.001-0.011,0.004-0.02,0.005-0.031c10.498-0.347,48.727-10.314,48.727-10.314
			C139.697,271.566,149.641,258.526,149.641,258.526z"/>
		<g>
			<g>
				<polygon style="fill:#005B8E;" points="213.012,360.158 225.953,384.86 267.09,376.496 254.423,351.362 				"/>
				<g>
					<polygon style="fill:#FFFFFF;" points="224.266,357.767 213.012,360.158 217.112,367.984 293.197,397.549 303.843,388.69 					
						"/>
				</g>
				<g>
					<polygon style="fill:#FF3028;" points="213.355,360.813 216.174,366.195 293.329,396.176 293.717,392.041 					"/>
				</g>
				<polygon style="fill:#005B8E;" points="314.742,338.549 326.987,364.318 284.232,373.011 271.684,347.695 				"/>
				<g>
					<polygon style="fill:#FFFFFF;" points="302.807,341.084 314.742,338.549 318.622,346.715 281.979,399.734 265.409,396.26 					
						"/>
				</g>
				<g>
					<polygon style="fill:#FF3028;" points="315.067,339.233 317.735,344.848 280.63,398.655 276.571,395.41 					"/>
				</g>
				<polygon style="fill:#005B8E;" points="350.853,414.541 339.141,389.894 296.691,398.144 308.701,422.373 				"/>
				<g>
					<polygon style="fill:#FFFFFF;" points="339.173,416.711 350.853,414.541 347.197,406.848 270.597,377.085 260.196,386.02 					
						"/>
				</g>
				<g>
					<polygon style="fill:#FF3028;" points="350.549,413.901 348.037,408.616 270.484,378.48 270.145,382.659 					"/>
				</g>
				<polygon style="fill:#005B8E;" points="251.203,433.056 238.807,409.395 279.667,401.453 291.793,425.515 				"/>
				<g>
					<polygon style="fill:#FFFFFF;" points="262.238,431.006 251.203,433.056 247.333,425.669 281.885,374.795 298.74,378.286 					
						"/>
				</g>
				<g>
					<polygon style="fill:#FF3028;" points="250.881,432.441 248.223,427.367 283.254,375.895 287.351,379.199 					"/>
				</g>
				<polygon style="fill:#FFFFFF;" points="326.987,364.318 284.321,372.993 271.773,347.676 254.17,351.415 266.838,376.547 
					225.953,384.86 238.525,408.856 279.141,400.954 291.545,425.561 308.789,422.357 296.506,397.575 338.874,389.332 				"/>
				<polygon style="fill:#FF3028;" points="329.439,369.478 283.113,378.813 268.02,348.473 257.888,350.625 273.064,380.839 
					228.546,389.809 235.975,403.989 280.319,395.282 295.188,424.884 305.114,423.04 290.328,393.317 336.463,384.259 				"/>
			</g>
			<g>
				<g>
					<path style="fill:#FFFFFF;" d="M201.384,324.267c-0.433,0.221-1.95,0.672-4.598,1.167c-2.648,0.495-4.924,0.717-6.801,0.773
						c-1.606-2.339-3.359-5.065-5.256-8.169c-1.897-3.105-3.366-5.68-4.414-7.737c2.52-0.499,4.698-1.013,6.567-1.431
						c1.869-0.418,3.726-0.811,5.579-1.161c0.273,0.549,0.523,1.139,0.753,1.769c0.229,0.631,0.424,1.296,0.585,1.994
						c-0.956,0.17-1.941,0.347-2.96,0.523c-1.019,0.177-2.078,0.355-3.179,0.53c0.125,0.328,0.284,0.702,0.474,1.125
						c0.19,0.422,0.484,1.055,0.883,1.898c0.781-0.308,1.585-0.571,2.416-0.789c0.831-0.218,1.7-0.401,2.611-0.549
						c0.395,0.499,0.766,1.018,1.107,1.557c0.341,0.539,0.698,1.182,1.067,1.927c-0.981,0.159-1.945,0.327-2.89,0.501
						c-0.946,0.176-1.861,0.349-2.745,0.523c0.347,0.665,0.694,1.315,1.044,1.948c0.35,0.635,0.703,1.238,1.06,1.811
						c0.509-0.046,1.539-0.227,3.07-0.605c1.533-0.379,2.662-0.732,3.405-1.005c0.294,0.291,0.665,0.784,1.113,1.479
						C200.724,323.04,201.093,323.682,201.384,324.267z"/>
					<path style="fill:#FFFFFF;" d="M206.851,318.615c0.879,1.759,1.319,2.641,2.201,4.405c-0.382,0.232-0.967,0.494-1.757,0.777
						c-0.791,0.283-1.53,0.493-2.216,0.634c-1.963-3.221-3.688-6.148-5.18-8.787c-1.493-2.64-3.714-5.831-3.615-6.728
						c0.006-0.059,0.148-0.174,0.201-0.219c0.053-0.045,0.105-0.09,0.158-0.135c0.511-0.446,1.147-0.867,1.91-1.269
						c0.763-0.402,1.597-0.744,2.501-1.031c1.654,1.027,3.177,2.052,4.568,3.085c1.391,1.033,2.774,2.168,4.15,3.407
						c-0.913-1.82-1.766-3.423-2.558-4.81c-0.792-1.387-1.485-2.499-2.08-3.342c0.742-0.516,1.505-0.983,2.284-1.4
						c0.779-0.416,1.582-0.775,2.409-1.073c0.485,0.846,0.938,1.669,1.36,2.469s0.825,1.579,1.207,2.339
						c0.942,1.872,1.885,3.852,2.827,5.937c0.942,2.085,2.821,5.513,2.621,6.057c-0.121,0.33-1.774,0.883-2.421,1.164
						c-0.647,0.281-1.286,0.532-1.911,0.751c-2.266-2.045-4.063-3.607-5.402-4.691s-2.642-2.054-3.91-2.912
						c0.292,0.622,0.624,1.306,0.997,2.052C205.567,316.044,206.119,317.149,206.851,318.615z"/>
					<path style="fill:#FFFFFF;" d="M231.124,305.032c0.738,1.487,1.56,2.908,2.47,4.265c0.909,1.356,1.903,2.649,2.985,3.877
						c-0.45,0.332-0.968,0.663-1.552,0.991s-1.25,0.659-2,0.994c-0.403-0.31-0.799-0.636-1.188-0.978
						c-0.389-0.343-0.766-0.694-1.132-1.054c-0.28,0.973-0.734,1.797-1.365,2.47c-0.631,0.673-1.444,1.2-2.439,1.576
						c-1.737,0.657-3.412,0.568-5.019-0.237c-1.607-0.805-2.913-2.209-3.918-4.201c-1.508-2.989-1.99-5.779-1.466-8.429
						s1.963-4.463,4.34-5.453c1.645-0.685,3.194-0.806,4.644-0.341c1.449,0.466,2.662,1.465,3.63,3
						c-0.632,0.53-1.337,1.023-2.114,1.478s-1.66,0.885-2.648,1.289c-0.749-0.998-1.35-1.665-1.803-2
						c-0.452-0.335-0.862-0.427-1.228-0.277c-0.633,0.259-0.84,1.056-0.623,2.392c0.216,1.336,0.819,2.98,1.81,4.937
						c0.765,1.511,1.492,2.55,2.18,3.116c0.689,0.566,1.404,0.707,2.145,0.418c0.471-0.184,0.828-0.497,1.07-0.938
						c0.243-0.441,0.347-0.973,0.315-1.596c-0.33-0.447-0.649-0.897-0.961-1.351c-0.311-0.453-0.606-0.914-0.883-1.38
						c0.585-0.442,1.256-0.87,2.014-1.284C229.145,305.904,230.056,305.475,231.124,305.032z"/>
					<path style="fill:#FFFFFF;" d="M248.929,305.544c0.356,0.358,0.797,0.891,1.323,1.597c0.526,0.706,0.926,1.324,1.198,1.85
						c-1.784,0.672-3.441,1.381-4.982,2.118s-3.413,1.728-5.599,2.961c-1.619-2.184-3.504-5.005-5.651-8.451
						c-2.146-3.445-5.214-7.882-5.027-8.704c0.068-0.301,1.381-0.991,2.092-1.409c0.711-0.418,1.443-0.788,2.199-1.11
						c0.223-0.095,0.444-0.185,0.663-0.268c0.219-0.084,0.458-0.16,0.715-0.231c0.264,0.841,0.689,1.934,1.275,3.279
						c0.586,1.346,1.26,2.757,2.019,4.235c1.098,2.139,1.947,3.72,2.542,4.735c0.596,1.016,1.156,1.87,1.677,2.556
						c0.851-0.645,1.765-1.246,2.744-1.805C247.096,306.339,248.034,305.889,248.929,305.544z"/>
					<path style="fill:#FFFFFF;" d="M251.328,291.931c1.127,2.17,2.252,4.464,3.377,6.883c1.125,2.419,2.035,4.507,2.726,6.26
						c-0.453,0.31-1.103,0.672-1.942,1.088c-0.84,0.416-1.632,0.762-2.377,1.038c-1.73-3.344-3.378-6.343-4.946-8.999
						c-1.567-2.655-3.067-4.993-4.498-7.014c0.296-0.423,0.697-0.821,1.204-1.193s1.085-0.684,1.735-0.934
						c0.487-0.188,0.937-0.335,1.348-0.443c0.411-0.108,0.882-0.195,1.416-0.261c0.274,0.486,0.58,1.032,0.917,1.639
						C250.624,290.6,250.972,291.246,251.328,291.931z"/>
					<path style="fill:#FFFFFF;" d="M269.296,291.526c0.738,0.407,1.373,0.875,1.901,1.401c0.529,0.527,0.971,1.125,1.324,1.797
						c0.887,1.684,0.861,3.254-0.054,4.723c-0.915,1.47-2.451,2.49-4.567,3.09c-2.333,0.662-4.545,0.659-6.67-0.038
						c-2.125-0.698-4.543-2.783-4.437-3.465c0.041-0.264,1.073-0.931,1.835-1.402c0.761-0.47,1.43-0.796,2.001-0.978
						c0.64,1.226,1.428,2.129,2.368,2.712c0.94,0.583,1.885,0.733,2.84,0.454c0.681-0.199,1.128-0.507,1.336-0.929
						c0.207-0.422,0.167-0.909-0.123-1.462c-0.247-0.471-0.68-0.876-1.295-1.214c-0.616-0.337-1.799-0.733-3.521-1.161
						c-1.285-0.319-2.246-0.585-2.895-0.808c-0.65-0.222-1.227-0.457-1.733-0.704c-0.737-0.395-1.369-0.846-1.899-1.355
						c-0.531-0.508-0.97-1.099-1.32-1.77c-0.834-1.601-0.848-3.139-0.03-4.602c0.818-1.463,2.325-2.561,4.571-3.24
						c1.995-0.604,3.842-0.657,5.511-0.183c1.67,0.474,3.135,1.464,4.375,2.945c-0.429,0.449-0.972,0.851-1.627,1.209
						c-0.654,0.357-1.564,0.727-2.717,1.118c-0.705-0.955-1.379-1.592-2.02-1.912c-0.64-0.318-1.323-0.372-2.04-0.158
						c-0.447,0.134-0.737,0.358-0.876,0.672c-0.139,0.314-0.106,0.664,0.096,1.05c0.278,0.532,0.736,0.979,1.376,1.347
						c0.64,0.367,1.837,0.768,3.608,1.215c1.296,0.327,2.281,0.613,2.928,0.851C268.192,290.965,268.776,291.233,269.296,291.526z"
						/>
					<path style="fill:#FFFFFF;" d="M287.034,278.864c1.642,2.713,3.51,5.903,5.607,9.578c2.097,3.674,3.898,6.918,5.393,9.716
						c-1.307,0.05-2.667,0.081-4.076,0.094c-1.41,0.013-2.434-0.004-3.094-0.081c-0.578-1.087-1.266-2.355-2.06-3.803
						c-0.795-1.449-1.597-2.911-2.406-4.388c-1.093,0.135-2.101,0.291-3.025,0.459c-0.926,0.168-1.777,0.36-2.555,0.571
						c0.416,0.829,0.862,1.704,1.338,2.624c0.475,0.919,2.011,3.296,1.899,3.616c-0.048,0.137-1.298,0.466-2.391,0.761
						c-1.093,0.295-2.089,0.534-2.991,0.711c-2.659-4.117-4.742-7.418-6.263-9.93c-1.52-2.511-3.787-5.655-3.681-6.424
						c0.029-0.212,0.864-0.715,1.533-1.021c0.67-0.306,1.402-0.538,2.203-0.693c0.589-0.114,1.142-0.189,1.659-0.228
						c0.515-0.039,1.045-0.051,1.593-0.034c0.304,0.752,0.734,1.716,1.287,2.893s1.192,2.494,1.917,3.95
						c0.889-0.268,1.765-0.501,2.627-0.699c0.861-0.198,1.746-0.358,2.656-0.479c-0.813-1.436-1.591-2.764-2.335-3.984
						c-0.743-1.219-1.378-2.236-1.905-3.049c0.48-0.166,0.936-0.3,1.367-0.402c0.433-0.102,0.897-0.178,1.395-0.228
						c0.722-0.073,1.467-0.068,2.228,0.016C285.719,278.495,286.413,278.648,287.034,278.864z"/>
					<path style="fill:#FFFFFF;" d="M219.279,343.496c0.338,0.369,0.755,0.905,1.251,1.608c0.496,0.703,0.872,1.31,1.126,1.818
						c-1.724,0.377-3.352,0.754-4.887,1.133c-1.535,0.379-3.422,0.884-5.682,1.431c-1.551-2.222-3.348-5.048-5.386-8.466
						c-2.037-3.417-4.923-8.092-4.746-8.541c0.073-0.185,1.444-0.509,2.177-0.708c0.732-0.2,1.479-0.363,2.243-0.491
						c0.225-0.038,0.449-0.072,0.669-0.102c0.22-0.03,0.46-0.051,0.717-0.065c0.238,0.781,0.627,1.811,1.169,3.09
						c0.542,1.28,1.169,2.634,1.878,4.061c1.027,2.065,1.822,3.599,2.382,4.594c0.562,0.996,1.091,1.842,1.588,2.537
						c0.862-0.401,1.778-0.769,2.747-1.106C217.493,343.953,218.411,343.686,219.279,343.496z"/>
					<path style="fill:#FFFFFF;" d="M235.026,341.563c-0.428,0.303-1.943,0.997-4.55,1.978s-4.821,1.663-6.641,2.103
						c-1.665-2.354-3.489-5.119-5.471-8.288c-1.981-3.168-3.522-5.818-4.632-7.96c2.41-1.105,4.522-2.089,6.35-2.878
						c1.828-0.789,3.662-1.512,5.508-2.154c0.29,0.573,0.559,1.195,0.807,1.864c0.249,0.67,0.464,1.383,0.646,2.139
						c-0.952,0.323-1.929,0.665-2.934,1.023c-1.005,0.358-2.046,0.734-3.122,1.124c0.136,0.349,0.306,0.745,0.51,1.189
						c0.204,0.444,0.518,1.108,0.942,1.993c0.755-0.486,1.539-0.918,2.355-1.297c0.816-0.379,1.674-0.717,2.578-1.014
						c0.411,0.499,0.797,1.026,1.155,1.577c0.358,0.552,0.734,1.219,1.127,1.997c-0.976,0.317-1.93,0.651-2.86,0.999
						c-0.931,0.348-1.828,0.698-2.69,1.051c0.367,0.691,0.734,1.366,1.103,2.021c0.369,0.657,0.739,1.277,1.113,1.863
						c0.499-0.141,1.504-0.526,3.012-1.191c1.508-0.665,2.629-1.215,3.365-1.62c0.303,0.284,0.69,0.781,1.16,1.494
						C234.327,340.289,234.717,340.952,235.026,341.563z"/>
					<path style="fill:#FFFFFF;" d="M243.863,328.594c0.659,0.357,1.229,0.781,1.708,1.272c0.48,0.492,0.887,1.063,1.22,1.715
						c0.837,1.637,0.881,3.24,0.135,4.784c-0.747,1.546-2.065,2.656-3.941,3.319c-2.068,0.731-4.078,0.737-6.034,0.045
						c-1.956-0.693-4.232-2.734-4.127-3.415c0.039-0.251,0.978-0.872,1.668-1.323c0.69-0.449,1.292-0.768,1.807-0.95
						c0.605,1.193,1.338,2.071,2.201,2.63c0.863,0.559,1.719,0.691,2.567,0.388c0.605-0.216,0.995-0.538,1.168-0.966
						c0.172-0.427,0.122-0.91-0.152-1.448c-0.233-0.458-0.63-0.843-1.187-1.154c-0.558-0.311-1.622-0.657-3.182-1.042
						c-1.164-0.287-2.041-0.538-2.634-0.752c-0.595-0.213-1.124-0.441-1.59-0.683c-0.679-0.386-1.265-0.828-1.758-1.326
						c-0.494-0.497-0.906-1.074-1.237-1.728c-0.789-1.559-0.821-3.041-0.104-4.469c0.717-1.428,2.058-2.522,4.037-3.287
						c1.759-0.68,3.377-0.852,4.851-0.494c1.475,0.358,2.782,1.235,3.915,2.635c-0.351,0.496-0.806,0.951-1.362,1.363
						c-0.555,0.412-1.336,0.85-2.339,1.308c-0.653-0.918-1.268-1.518-1.845-1.805c-0.576-0.286-1.182-0.306-1.815-0.065
						c-0.394,0.151-0.647,0.383-0.763,0.698c-0.116,0.315-0.077,0.66,0.114,1.036c0.263,0.518,0.684,0.946,1.267,1.289
						c0.582,0.342,1.663,0.7,3.242,1.056c1.156,0.261,2.03,0.485,2.605,0.682C242.879,328.107,243.398,328.335,243.863,328.594z"/>
					<path style="fill:#FFFFFF;" d="M258.806,323.043c0.687,0.368,1.28,0.804,1.779,1.307c0.499,0.503,0.92,1.087,1.264,1.751
						c0.862,1.667,0.891,3.285,0.098,4.846c-0.794,1.562-2.177,2.688-4.125,3.383c-2.148,0.766-4.22,0.817-6.23,0.158
						c-2.011-0.66-4.338-2.687-4.239-3.4c0.037-0.268,0.999-0.937,1.708-1.421c0.708-0.482,1.328-0.825,1.86-1.023
						c0.623,1.214,1.378,2.101,2.268,2.659c0.89,0.558,1.775,0.679,2.655,0.36c0.628-0.227,1.034-0.559,1.216-0.997
						c0.181-0.437,0.132-0.93-0.15-1.477c-0.24-0.466-0.651-0.857-1.228-1.172c-0.579-0.314-1.683-0.662-3.295-1.033
						c-1.204-0.278-2.108-0.519-2.72-0.725c-0.613-0.206-1.158-0.428-1.637-0.666c-0.699-0.381-1.301-0.821-1.808-1.32
						c-0.508-0.499-0.932-1.082-1.273-1.748c-0.812-1.586-0.849-3.119-0.11-4.605c0.739-1.486,2.125-2.634,4.186-3.425
						c1.831-0.704,3.523-0.869,5.064-0.493c1.541,0.376,2.905,1.284,4.081,2.716c-0.375,0.495-0.856,0.948-1.443,1.359
						c-0.586,0.411-1.407,0.848-2.456,1.312c-0.675-0.934-1.313-1.546-1.912-1.838c-0.599-0.29-1.229-0.31-1.888-0.06
						c-0.411,0.156-0.674,0.395-0.795,0.718c-0.121,0.323-0.083,0.676,0.114,1.058c0.271,0.527,0.706,0.961,1.309,1.306
						c0.602,0.344,1.723,0.701,3.366,1.063c1.203,0.265,2.114,0.497,2.715,0.701C257.779,322.54,258.322,322.776,258.806,323.043z"
						/>
					<path style="fill:#FFFFFF;" d="M266.341,309.391c2.307-0.706,4.466-0.575,6.44,0.365c1.974,0.94,3.597,2.609,4.843,4.984
						c0.674,1.285,1.147,2.645,1.42,4.077c0.273,1.433,0.316,2.771,0.099,3.964c-0.262,1.44-0.916,2.67-1.889,3.655
						c-0.972,0.986-2.265,1.709-3.868,2.182c-2.318,0.683-4.481,0.488-6.521-0.61c-2.039-1.097-3.71-2.917-5.035-5.475
						c-1.516-2.929-1.878-5.618-1.07-8.072C261.566,312.006,263.401,310.292,266.341,309.391z M272.938,325.06
						c0.871-0.255,1.261-0.95,1.16-2.089c-0.101-1.139-0.765-2.883-1.989-5.226c-1.052-2.015-1.985-3.385-2.802-4.114
						c-0.817-0.729-1.669-0.959-2.549-0.687c-0.882,0.273-1.271,1.023-1.178,2.244c0.093,1.221,0.646,2.806,1.663,4.76
						c0.965,1.856,1.942,3.235,2.932,4.135C271.165,324.984,272.085,325.309,272.938,325.06z"/>
					<path style="fill:#FFFFFF;" d="M290.198,319.426c1.028,1.947,1.543,2.923,2.575,4.877c-0.492,0.223-1.238,0.471-2.227,0.755
						c-0.991,0.284-1.902,0.512-2.74,0.678c-2.311-3.56-4.338-6.79-6.088-9.704c-1.751-2.915-4.34-6.488-4.232-7.429
						c0.008-0.066,0.182-0.199,0.247-0.251c0.064-0.052,0.129-0.103,0.194-0.155c0.627-0.51,1.412-0.982,2.366-1.407
						c0.954-0.425,2.006-0.76,3.159-1.004c2.038,1.216,3.93,2.455,5.662,3.704c1.732,1.25,3.458,2.625,5.177,4.121
						c-1.07-2.017-2.076-3.8-3.017-5.351c-0.941-1.551-1.771-2.804-2.492-3.764c1.004-0.419,2.045-0.76,3.119-1.023
						c1.072-0.262,2.185-0.439,3.338-0.526c0.58,0.953,1.119,1.875,1.618,2.767c0.499,0.891,0.974,1.758,1.423,2.601
						c1.106,2.076,2.202,4.258,3.288,6.544c1.085,2.286,3.144,6.341,2.998,6.614c-0.143,0.268-2.474,0.469-3.359,0.626
						c-0.885,0.156-1.746,0.299-2.582,0.425c-2.828-2.464-5.053-4.318-6.71-5.604c-1.656-1.286-3.263-2.431-4.82-3.436
						c0.34,0.687,0.728,1.443,1.164,2.269C288.695,316.58,289.341,317.804,290.198,319.426z"/>
				</g>
			</g>
		</g>
		<g>
			<path style="fill:#FCCEB6;" d="M115.48,369.949c0,0,32.85-12.643,26.563-24.219c-5.921-10.901-23.438-2.955-31.444,0.204
				c-2.352,0.928-6.854,3.912-8.2,6.053C98.357,358.412,98.928,369.154,115.48,369.949z"/>
			<g>
				<path style="opacity:0.2;fill:#FAF0E9;" d="M137.146,344.709l2.416,6.509c0.193,0.519,0.024,1.095-0.416,1.431
					c-2.211,1.685-8.822,6.296-11.325,3.254c-2.948-3.584-3.84-11.161,8.039-12.018
					C136.415,343.844,136.953,344.188,137.146,344.709z"/>
				<path style="opacity:0.2;fill:#FAF0E9;" d="M126.333,348.736c0,0,4.306,2.883,2.563,7.953c0,0-1.421-0.163-2.522-3.368
					C125.272,350.115,126.333,348.736,126.333,348.736z"/>
			</g>
			<path style="fill:#FCCEB6;" d="M119.669,400.394c0,0,16.487-3.192,27.11-7.995c12.466-5.636,19.812,4.585,18.078,8.872
				c-1.012,2.504-7.2,6.253-10.12,8.332c-7.325,5.218-25.792,19.471-35.471,6.315C117.229,413.15,116.305,404.016,119.669,400.394z"
				/>
			<g>
				<path style="opacity:0.2;fill:#FAF0E9;" d="M160.016,394.977l2.996,6.454c0.239,0.514,0.115,1.117-0.308,1.495
					c-2.122,1.901-8.504,7.149-11.308,4.244c-3.303-3.422-4.83-11.093,7.24-12.932C159.198,394.153,159.776,394.461,160.016,394.977
					z"/>
				<path style="opacity:0.2;fill:#FAF0E9;" d="M149.293,399.969c0,0,4.634,2.597,3.263,7.919c0,0-1.464-0.051-2.85-3.237
					C148.321,401.465,149.293,399.969,149.293,399.969z"/>
			</g>
			<path style="fill:#FCCEB6;" d="M116.261,369.559l6.638,7.81c0,0,28.31-6.443,32.996,1.952c1.962,3.515,1.262,8.103-0.048,9.543
				c-5.764,6.336-24.226,11.214-29.367,12.475c-0.919,0.225-1.836,0.245-2.762,0.05c-4.916-1.035-20.785-5.294-18.645-16.818
				C105.768,380.83,109.627,369.782,116.261,369.559z"/>
			<g>
				<path style="opacity:0.2;fill:#FAF0E9;" d="M153.603,379.974l1.893,6.86c0.151,0.546-0.072,1.12-0.55,1.424
					c-2.407,1.526-9.565,5.652-11.853,2.326c-2.695-3.919-2.939-11.737,9.269-11.566
					C152.931,379.027,153.451,379.426,153.603,379.974z"/>
				<path style="opacity:0.2;fill:#FAF0E9;" d="M142.205,383.134c0,0,4.144,3.324,1.917,8.347c0,0-1.436-0.291-2.278-3.661
					C141,384.45,142.205,383.134,142.205,383.134z"/>
			</g>
			<path style="fill:#FCCEB6;" d="M122.658,411.551c4.05-2.321,21.56,5.006,21.56,5.006s18.422-10.401,23.425-7.368
				c10.315,6.253-7.087,17.668-14.225,21.264c-1.997,1.007-4.122,1.727-6.33,2.082C134.109,434.624,114.185,416.409,122.658,411.551
				z"/>
			<g>
				<path style="opacity:0.2;fill:#FAF0E9;" d="M165.648,411.506l2.405,3.74c0.192,0.298,0.134,0.757-0.142,1.138
					c-1.391,1.914-5.591,7.338-7.69,6.048c-2.473-1.52-3.911-6.514,4.427-11.073C165.036,411.146,165.455,411.207,165.648,411.506z"
					/>
				<path style="opacity:0.2;fill:#FAF0E9;" d="M158.403,417.923c0,0,3.361,0.578,2.663,4.7c0,0-1.025,0.359-2.147-1.512
					C157.797,419.241,158.403,417.923,158.403,417.923z"/>
			</g>
			<path style="fill:#DDA891;" d="M120.3,417.18c4.637,9.623,17.814,4.509,24.404-0.229c1.034-0.79,2.093-1.533,3.091-2.356
				c-2.359,1.093-4.591,2.34-6.933,3.348C134.52,420.747,125.22,423.978,120.3,417.18L120.3,417.18z"/>
			<path style="fill:#DDA891;" d="M121.016,400.73c3.472,1.663,7.22,1.273,10.855,0.331c7.893-2.094,15.275-5.826,22.075-10.275
				c-7.518,3.039-14.743,6.531-22.436,8.985C128.026,400.971,124.552,401.577,121.016,400.73L121.016,400.73z"/>
			<g>
				<path style="opacity:0.3;fill:#DDA891;" d="M116.261,369.559l6.638,7.81c0,0-10.794-5.438-16.091,1.768
					C106.808,379.137,110.04,369.853,116.261,369.559z"/>
				<path style="opacity:0.3;fill:#DDA891;" d="M127.085,401.802c0,0-9.223-0.569-9.223,10.703c0,0-1.673-7.927,1.807-12.111
					c0,0,1.835,0.944,5.415,0.896L127.085,401.802z"/>
				<path style="opacity:0.3;fill:#DDA891;" d="M137.671,420.787c0,0-7.114,6.81,0.638,10.641c0,0-9.121-2.979-15.262-10.732
					C123.047,420.695,125.844,423.614,137.671,420.787z"/>
			</g>
		</g>
		<g>
			<path style="fill:#FCCEB6;" d="M471.796,499.815l-43.375-65.377c-17.049-45.993-29.784-74.598-41.818-88.789
				c-5.652-6.665-13.183-11.462-21.599-13.815l-34.248-7.952l-34.658,4.979c-1.709,0.245-3.08,1.546-3.421,3.238
				c-3.594,17.8,34.385,12.926,34.385,12.926l13.87,7.037l-15.337-1.548c-1.865-0.419-3.787-0.513-5.684-0.28l-36.258,4.457
				c-2.15,0.264-3.987,1.743-4.633,3.811c-6.038,19.336,36.957,11.77,36.957,11.77l22.47,7.883l-17.338-2.751
				c0,0-29.062-1.954-36.32,6.156c-1.669,1.865-1.646,5.214-0.464,8.159c2.85,7.102,32.31,6.157,32.31,6.157l16.968,6.853
				l-21.174-3.072c-0.718-0.158-1.457-0.202-2.189-0.13l-19.619,1.92c-1.514,0.148-2.713,1.253-3.151,2.71
				c-4.153,13.814,22.143,12.505,22.143,12.505l24.221,14.369c0,0,17.786,28.379,36.169,33.93l13.017,35.016L471.796,499.815z"/>
			<g>
				<g>
					<path style="opacity:0.2;fill:#FAF0E9;" d="M289.733,408.61l0.146-5.578c0.012-0.444,0.31-0.824,0.738-0.943
						c2.154-0.595,8.466-2.056,9.423,0.962c1.127,3.555-0.476,9.475-9.594,6.559C290.021,409.474,289.722,409.056,289.733,408.61z"
						/>
					<path style="opacity:0.2;fill:#FAF0E9;" d="M299.005,408.842c0,0-2.349-3.44,0.468-6.699c0,0,1.011,0.546,0.873,3.267
						C300.209,408.131,299.005,408.842,299.005,408.842z"/>
				</g>
				<g>
					<path style="opacity:0.2;fill:#FAF0E9;" d="M281.979,363.45l0.179-6.829c0.014-0.544,0.379-1.008,0.903-1.154
						c2.637-0.729,10.363-2.517,11.534,1.177c1.38,4.352-0.582,11.599-11.744,8.03C282.331,364.508,281.965,363.996,281.979,363.45z
						"/>
					<path style="opacity:0.2;fill:#FAF0E9;" d="M293.329,363.734c0,0-2.876-4.211,0.573-8.201c0,0,1.237,0.668,1.069,3.999
						C294.803,362.863,293.329,363.734,293.329,363.734z"/>
				</g>
				<g>
					<path style="opacity:0.2;fill:#FAF0E9;" d="M286.495,389.414l0.191-7.303c0.015-0.582,0.405-1.078,0.966-1.234
						c2.82-0.779,11.083-2.692,12.335,1.259c1.476,4.655,1.147,11.141-12.56,8.587C286.854,390.616,286.48,389.997,286.495,389.414z
						"/>
					<path style="opacity:0.2;fill:#FAF0E9;" d="M298.809,389.814c0,0-3.076-4.503,0.613-8.77c0,0,1.323,0.715,1.143,4.276
						C300.385,388.882,298.809,389.814,298.809,389.814z"/>
				</g>
				<g>
					<path style="opacity:0.2;fill:#FAF0E9;" d="M296.398,336.433l-0.609-5.858c0.014-0.544,0.379-1.008,0.903-1.154
						c2.637-0.729,11.163-2.525,12.322,0.206c1.784,4.203-0.582,11.599-11.744,8.03C296.75,337.491,296.384,336.978,296.398,336.433
						z"/>
					<path style="opacity:0.2;fill:#FAF0E9;" d="M307.748,336.717c0,0-2.973-3.953,0.476-7.943c0,0,1.424,0.489,1.255,3.819
						C309.311,335.925,307.748,336.717,307.748,336.717z"/>
				</g>
			</g>
			<g>
				<path style="fill:#DDA891;" d="M334.892,351.418c7.353,0.197,14.634,1.069,21.823,2.63
					C349.361,353.857,342.081,352.974,334.892,351.418L334.892,351.418z"/>
				<path style="fill:#DDA891;" d="M330.995,376.943c5.695,0.063,11.311,0.802,16.826,2.228
					C342.125,379.113,336.51,378.364,330.995,376.943L330.995,376.943z"/>
				<path style="fill:#DDA891;" d="M326.638,401.694c6.486,0.585,12.851,1.839,19.07,3.773
					C339.221,404.887,332.859,403.623,326.638,401.694L326.638,401.694z"/>
			</g>
			<path style="fill:#DDA891;" d="M364.871,462.785c5.119,2.442,10.867,3.089,16.396,1.822
				C376.117,467.286,369.317,466.51,364.871,462.785L364.871,462.785z"/>
			<path style="opacity:0.3;fill:#DDA891;" d="M381.267,464.606l6.073,35.342l-4.32,0.029l-12.886-34.453
				C370.134,465.525,373.946,467.218,381.267,464.606z"/>
			<g>
				<polygon style="opacity:0.3;fill:#DDA891;" points="327.06,345.025 340.068,351.681 356.715,354.048 				"/>
				<polygon style="opacity:0.3;fill:#DDA891;" points="347.82,379.171 315.976,370.273 335.252,376.988 				"/>
				<polygon style="opacity:0.3;fill:#DDA891;" points="345.708,405.467 316.634,395.877 333.175,402.672 				"/>
			</g>
		</g>
		<path style="opacity:0.2;fill:#FFFFFF;" d="M207.32,283.237c8.06,8.473,59.727-17.773,75.433,6.613
			c15.707,24.387,25.833,39.68,32.653,31.827c6.82-7.853-15.094-67.143-28.727-69.646
			C276.553,250.17,190.502,265.557,207.32,283.237z"/>
	</g>
</g>
<g id="TEXTS">
	<g>
		
			<text transform="matrix(1 0 0 1 39.6656 128.68)" style="fill:#FFFFFF; font-family:'Montserrat-ExtraBold'; font-size:38.5691px;">ok!</text>
		
			<text transform="matrix(1 0 0 1 296.4782 87.2716)" style="fill:#FFFFFF; font-family:'Montserrat-ExtraBold'; font-size:25.175px;">no!</text>
		
			<text transform="matrix(1 0 0 1 189.9315 158.1398)" style="fill:#FFFFFF; font-family:'Montserrat-ExtraBold'; font-size:25.175px;">hey!</text>
		
			<text transform="matrix(1 0 0 1 339.9694 172.137)" style="fill:#FFFFFF; font-family:'Montserrat-ExtraBold'; font-size:21.974px;">great</text>
		
			<text transform="matrix(1 0 0 1 390.9687 301.4612)" style="fill:#FFFFFF; font-family:'Montserrat-ExtraBold'; font-size:19.028px;">done</text>
	</g>
	<g>
		
			<text transform="matrix(1 0 0 1 141.735 217.0804)" style="fill:#FFFFFF; font-family:'Montserrat-ExtraBoldItalic'; font-size:14.3892px;">who?</text>
		
			<text transform="matrix(1 0 0 1 195.9167 56.6915)" style="fill:#FFFFFF; font-family:'Montserrat-ExtraBoldItalic'; font-size:13.823px;">speak</text>
		
			<text transform="matrix(1 0 0 1 407.8522 97.8364)" style="fill:#FFFFFF; font-family:'Montserrat-ExtraBoldItalic'; font-size:17.807px;">yes!</text>
	</g>
</g>
</svg>
