import React, { useState } from 'react';
import { useAddSchoolCourseMutation, useGetAdminCoursesQuery, useGetSchoolCoursesQuery } from 'src/APIConnect';
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { Loader2, Plus, GraduationCap } from 'lucide-react';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

interface Course {
    id: string;
    name: string;
    description: string;
}

interface SchoolCourse {
    id: string;
    name: string;
    description: string;
}

interface CoursesTabProps {
    schoolId: string;
    isSchoolAdmin?: boolean;
}

const CoursesTab = ({ schoolId, isSchoolAdmin = false }: CoursesTabProps) => {
    const [isAddingCourse, setIsAddingCourse] = useState(false);
    const [selectedCourseId, setSelectedCourseId] = useState<string>('');

    const { data: allCourses = [], isLoading: isLoadingCourses } = useGetAdminCoursesQuery({});
    // These hooks will be available after adding to APIConnect.js
    const { data: schoolCourses = [], isLoading: isLoadingSchoolCourses, refetch: refetchSchoolCourses } = useGetSchoolCoursesQuery(schoolId);
    const [addSchoolCourse, { isLoading: isCreateCourseForSchool }] = useAddSchoolCourseMutation();

    const handleAddCourse = async () => {
        if (!selectedCourseId) {
            toast({
                title: "Please select a course",
                variant: "destructive",
            });
            return;
        }

        try {
            await addSchoolCourse({
                schoolId,
                courseId: selectedCourseId,
                createdOn: new Date().toISOString()
            });

            toast({
                title: "Course added successfully",
                variant: "default",
            });
            setIsAddingCourse(false);
            setSelectedCourseId('');
            refetchSchoolCourses();
        } catch (error) {
            toast({
                title: "Failed to add course",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    if (isLoadingSchoolCourses) {
        return (
            <div className="flex justify-center items-center p-8">
                <Loader2 className="w-8 h-8 animate-spin" />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                    <GraduationCap className="h-5 w-5" />
                    <h2 className="text-xl font-semibold">Courses</h2>
                </div>
                {!isSchoolAdmin && <Dialog open={isAddingCourse} onOpenChange={setIsAddingCourse}>
                    <DialogTrigger asChild>
                        <Button className="flex items-center space-x-2">
                            <Plus className="h-4 w-4" />
                            <span>Add Course</span>
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Add Course to School</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4 py-4">
                            <div className="space-y-2">
                                <Select
                                    value={selectedCourseId}
                                    onValueChange={setSelectedCourseId}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a course" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {allCourses.resultObject?.filter((course: Course) =>
                                            !schoolCourses.resultObject?.some((sc: Course) => sc.id === course.id)
                                        )
                                            .map((course: Course) => (
                                                <SelectItem key={course.id} value={course.id}>
                                                    {course.name}
                                                </SelectItem>
                                            ))
                                        }
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button
                                    variant="outline"
                                    onClick={() => setIsAddingCourse(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    onClick={handleAddCourse}
                                    disabled={isCreateCourseForSchool}
                                >
                                    {isCreateCourseForSchool && (
                                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                    )}
                                    Add Course
                                </Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>}
            </div>

            <div className="grid gap-4">
                {schoolCourses?.resultObject?.map((course: Course) => (
                    <Card key={course.id} className="p-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="font-semibold">{course.name}</h3>
                                <p className="text-sm text-gray-500">{course.description}</p>
                            </div>
                        </div>
                    </Card>
                ))}
                {schoolCourses?.resultObject?.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                        No courses added yet
                    </div>
                )}
            </div>
        </div>
    );
};

export default CoursesTab;
