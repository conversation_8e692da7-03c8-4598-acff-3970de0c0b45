import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Question } from './types';
import { motion } from 'framer-motion';
import InteractiveMatchTheFollowing from '@/components/student-dashboard/components/match-the-following';

// Helper component to safely render HTML content
const HTMLContent = ({ content, className }: { content: string, className?: string }) => {
    return (
        <div
            className={className}
            dangerouslySetInnerHTML={{ __html: content }}
        />
    );
};

// Media components
const MediaRenderer = ({ metadata }: { metadata: any }) => {
    if (!metadata?.url) return null;

    const { url, type } = metadata;

    switch (type) {
        case 'image':
            return (
                <div className="my-4 rounded-lg overflow-hidden">
                    <img
                        src={url}
                        alt="Question media"
                        className="w-full h-auto object-contain max-h-[200px]"
                    />
                </div>
            );
        case 'video':
            return (
                <div className="my-4 rounded-lg overflow-hidden">
                    <video
                        controls
                        className="w-full max-h-[200px]"
                    >
                        <source src={url} type="video/mp4" />
                        Your browser does not support the video tag.
                    </video>
                </div>
            );
        case 'audio':
            return (
                <div className="my-4">
                    <audio controls className="w-full">
                        <source src={url} type="audio/mpeg" />
                        Your browser does not support the audio element.
                    </audio>
                </div>
            );
        default:
            return null;
    }
};

interface QuestionPreviewProps {
    question: Omit<Question, 'id'>;
}

const QuestionPreview = ({ question }: QuestionPreviewProps) => {
    const renderAnswers = () => {
        switch (question.type) {
            case 'SINGLECHOICE':
                return (
                    <RadioGroup className="space-y-3">
                        {question.answers.map((option, index) => (
                            <motion.div
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-accent cursor-pointer"
                                key={index}
                            >
                                <RadioGroupItem value={index.toString()} id={`preview-option-${index}`} />
                                <label
                                    className="text-sm font-medium leading-none cursor-pointer flex-grow"
                                    htmlFor={`preview-option-${index}`}
                                >
                                    <HTMLContent content={option.answerText} />
                                    {option.answerImageMetadata && (
                                        <MediaRenderer metadata={{ type: 'image', url: option.answerImageMetadata.resourceSource, }} />
                                    )}
                                    {option.answerVideoMetadata && (
                                        <MediaRenderer metadata={{ type: 'video', url: option.answerVideoMetadata.resourceSource, }} />
                                    )}
                                    {option.answerAudioMetadata && (
                                        <MediaRenderer metadata={{ type: 'audio', url: option.answerAudioMetadata.resourceSource, }} />
                                    )}
                                </label>
                            </motion.div>
                        ))}
                    </RadioGroup>
                );

            case 'MULTICHOICE':
                return question.answers.map((option, index) => (
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-accent cursor-pointer"
                        key={index}
                    >
                        <Checkbox id={`preview-option-${index}`} />
                        <label
                            className="text-sm font-medium leading-none cursor-pointer flex-grow"
                            htmlFor={`preview-option-${index}`}
                        >
                            <HTMLContent content={option.answerText} />
                            {option.answerImageMetadata && (
                                <MediaRenderer metadata={option.answerImageMetadata} />
                            )}
                            {option.answerVideoMetadata && (
                                <MediaRenderer metadata={option.answerVideoMetadata} />
                            )}
                            {option.answerAudioMetadata && (
                                <MediaRenderer metadata={option.answerAudioMetadata} />
                            )}
                        </label>
                    </motion.div>
                ));

            case 'TEXTINPUT':
                return (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                    >
                        <Input
                            type="text"
                            placeholder="Type your answer here..."
                            className="mt-2"
                        />
                    </motion.div>
                );

            case 'BINARY':
                return (
                    <RadioGroup className="space-y-3">
                        {['true', 'false'].map((value, index) => (
                            <motion.div
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-accent cursor-pointer"
                                key={value}
                            >
                                <RadioGroupItem value={value} id={`preview-${value}`} />
                                <Label
                                    htmlFor={`preview-${value}`}
                                    className="text-sm font-medium leading-none cursor-pointer"
                                >
                                    {value.charAt(0).toUpperCase() + value.slice(1)}
                                </Label>
                            </motion.div>
                        ))}
                    </RadioGroup>
                );

            case 'MATCH':
                // Validate that all match questions have matchText
                const validAnswers = question.answers.every(answer => answer.matchText);
                if (!validAnswers) {
                    return (
                        <div className="text-red-500 p-4 border border-red-300 rounded-lg">
                            Error: All match questions must have both question text and match text defined.
                        </div>
                    );
                }

                // Create items for the matching component
                const leftItems = question.answers.map((answer, index) => ({
                    id: index.toString(),
                    text: answer.answerText
                }));

                const rightItems = question.answers.map((answer, index) => ({
                    id: index.toString(),
                    text: answer.matchText || ''
                }));

                return (
                    <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="mt-4"
                    >
                        <InteractiveMatchTheFollowing
                            question="Match the following"
                            leftItems={leftItems}
                            rightItems={rightItems}
                            onMatch={() => { }} // No-op function since we're in preview mode
                            preview={true} // Enable preview mode
                        />
                    </motion.div>
                );

            default:
                return null;
        }
    };

    return (
        <Card className="w-full">
            <CardHeader>
                <CardTitle className="text-xl">Question Preview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="space-y-4">
                    <HTMLContent
                        content={question.questionText}
                        className="text-lg font-medium"
                    />
                    {question.questionImageMetadata && (
                        <MediaRenderer metadata={question.questionImageMetadata} />
                    )}
                    {question.questionVideoMetadata && (
                        <MediaRenderer metadata={question.questionVideoMetadata} />
                    )}
                    {question.questionAudioMetadata && (
                        <MediaRenderer metadata={question.questionAudioMetadata} />
                    )}
                </div>

                <div className="space-y-4">
                    {renderAnswers()}
                </div>

                {question.explanation && (
                    <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-sm text-muted-foreground bg-muted p-3 rounded-lg"
                    >
                        <HTMLContent content={question.explanation} />
                    </motion.div>
                )}
            </CardContent>
        </Card>
    );
};

export default QuestionPreview;
