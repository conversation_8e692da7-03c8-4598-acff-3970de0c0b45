.topnav {
  background: linear-gradient(90.2deg, #386C8D 0%, #036664 12.17%, #0E3D35 86.96%, #060B0B 105.19%);
  padding: 0.25rem 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  position: sticky;
  top: 0;
  z-index: 50;
  backdrop-filter: blur(10px);
}

.topnav-content {
  max-width: full;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  height: 32px;
}

.desktop-menu {
  display: none;
}

.nav-item {
  display: flex;
  align-items: center;
  margin: 0 0.5rem;
  border: 2px solid transparent;
  color: #ffffff;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 25px;
  position: relative;
  opacity: 0.8;
}

.nav-item>span {
  background: transparent;
  padding: 0.25rem 0.6rem;
  border-radius: 20px;
  width: 100%;
  text-align: start;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ffffff;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s;
}

.nav-item:hover::before,
.nav-item.active::before {
  opacity: 1;
}

.nav-item:hover,
.nav-item.active {
  color: #ffffff;
  opacity: 1;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-icon {
  margin-right: 0.4rem;
  width: 28px;
  height: 28px;
  opacity: 0.8;
  transition: opacity 0.3s;
}

.nav-item:hover .nav-icon,
.nav-item.active .nav-icon {
  opacity: 1;
}

.user-actions {
  display: flex;
  align-items: center;
  position: relative;
  gap: 1rem;
}

.calendar-popover {
  position: absolute;
  top: 100%;
  right: 0;
  transform: translateY(10px);
}

.calendar-popover-content {
  margin-right: 10px;
  margin-top: 10px;
}

.icon-button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #ffffff;
  font-size: 0.9rem;
  margin-left: 0.4rem;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-button {
  display: flex;
  align-items: center;
  background: none;
  border: 2px solid transparent;
  color: #000000;
  font-size: 0.8rem;
  margin-left: 0.75rem;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 20px;
  padding: 0.3rem 0.8rem;
  position: relative;
}

.user-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #FAFB69, #2E645B);
  border-radius: 20px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s;
}

.user-button:hover::before {
  opacity: 1;
}

.user-button:hover {
  color: #000000;
}

.user-button span {
  margin-left: 0.5rem;
}

.mobile-menu-button {
  display: block;
  background: none;
  border: none;
  color: #ffffff;
  font-size: 1.25rem;
  cursor: pointer;
  padding-left: 0.5rem;
}

.mobile-menu {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: linear-gradient(to right, #2E645B, #5BCFDD);
  border-top: 1px solid rgba(255, 255, 255, 0.1);

}

@media (min-width: 768px) {
  .topnav {
    padding: 0.4rem 1.5rem;
  }

  .desktop-menu {
    display: flex;
    gap: 1rem;
  }

  .mobile-menu-button {
    display: none;
  }

  .mobile-menu {
    display: none;
  }

  .nav-item {
    font-size: 15px;
  }

  .nav-item>span {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    width: 100%;
  }

  .nav-item.active>span,
  .nav-item:hover>span {
    background: transparent;
  }
}

/* Updated styles */
.nav-item,
.icon-button,
.user-button {
  font-weight: bold;
  text-transform: capitalize;
}

.nav-item {
  transition: transform 0.3s, box-shadow 0.3s;
}

.nav-item:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.icon-button,
.user-button {
  transition: transform 0.3s, color 0.3s;
}

.icon-button:hover,
.user-button:hover {
  transform: scale(1.1);
  color: #2E645B;
}

.user-button {
  border-radius: 20px;
  width: auto;
  padding: 0.3rem 0.8rem;
}

/* Add to your existing CSS */
.desktop-menu .nav-item {
  transition: all 0.2s ease-in-out;
}

.desktop-menu .nav-item span {
  display: flex;
  align-items: center;
}

/* Make sure mobile menu still shows text normally */
.mobile-menu .nav-item .nav-text {
  width: auto !important;
  opacity: 1 !important;
  margin-left: 8px !important;
  display: inline !important;
}

@media (max-width: 767px) {
  .digital-clock {
    padding-left: 0.5rem;
  }
}