import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Plus, Loader2, Search, Pencil, Trash2, BookOpen, GraduationCap } from 'lucide-react';
import { useGetSubjectsQuery, useDeleteSubjectMutation } from '@/APIConnect';
import { Subject } from './types';
import { toast } from '@/hooks/use-toast';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const SubjectsPage = () => {
    const navigate = useNavigate();
    const [searchQuery, setSearchQuery] = useState('');
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [subjectToDelete, setSubjectToDelete] = useState<Subject | null>(null);

    const { data: subjectsData, isLoading, refetch } = useGetSubjectsQuery('');
    const [deleteSubject, { isLoading: isDeleting }] = useDeleteSubjectMutation();

    const handleEdit = (subject: Subject) => {
        navigate(`/admin/subjects/${subject.id}`);
    };

    const handleDeleteClick = (subject: Subject) => {
        setSubjectToDelete(subject);
        setDeleteDialogOpen(true);
    };

    const handleDeleteConfirm = async () => {
        if (!window.confirm('Are you sure you want to delete this user?')) return;
        if (!subjectToDelete) return;

        try {
            await deleteSubject(subjectToDelete.id).unwrap();
            toast({
                title: "Subject deleted successfully",
                variant: "default",
            });
            refetch();
        } catch (error) {
            console.error('Error deleting subject:', error);
            toast({
                title: "Failed to delete subject",
                description: "Please try again later",
                variant: "destructive",
            });
        } finally {
            setDeleteDialogOpen(false);
            setSubjectToDelete(null);
        }
    };

    const handleCreate = () => {
        navigate('/admin/subjects/create');
    };

    const filteredSubjects = (subjectsData?.resultObject || []).filter((subject: Subject) => {
        return searchQuery
            ? subject.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            subject.description.toLowerCase().includes(searchQuery.toLowerCase())
            : true;
    });

    return (
        <div className="container mx-auto p-6">
            <div className="flex flex-col space-y-8">
                <Card className="p-6">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
                        <div>
                            <h1 className="text-3xl font-semibold tracking-tight text-foreground">Subjects</h1>
                            <p className="text-sm text-muted-foreground mt-1">Manage and organize academic subjects</p>
                        </div>
                        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full md:w-auto">
                            <div className="relative flex-1 sm:flex-none">
                                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search subjects..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="pl-9 w-full sm:w-[260px] bg-background"
                                />
                            </div>
                            <Button
                                onClick={handleCreate}
                                className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-primary-foreground transition-colors"
                            >
                                <Plus className="w-4 h-4 mr-2" />
                                Create Subject
                            </Button>
                        </div>
                    </div>
                </Card>

                <Card>
                    <ScrollArea className="h-[calc(100vh-280px)] rounded-md">
                        {isLoading ? (
                            <div className="flex justify-center items-center h-32">
                                <div className="flex flex-col items-center space-y-3">
                                    <Loader2 className="w-8 h-8 animate-spin text-muted-foreground" />
                                    <p className="text-sm text-muted-foreground">Loading subjects...</p>
                                </div>
                            </div>
                        ) : (
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-muted/50">
                                        <TableHead className="w-[300px]">Subject Details</TableHead>
                                        <TableHead>Description</TableHead>
                                        <TableHead className="w-[120px]">Created On</TableHead>
                                        <TableHead className="w-[100px] text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredSubjects.map((subject: Subject) => (
                                        <TableRow key={subject.id} className="group hover:bg-muted/50 transition-colors">
                                            <TableCell>
                                                <div className="flex items-center space-x-3">
                                                    <div className="h-10 w-10 rounded-lg bg-muted flex items-center justify-center">
                                                        <GraduationCap className="h-5 w-5 text-muted-foreground" />
                                                    </div>
                                                    <div className="font-medium text-foreground">
                                                        {subject.name}
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-muted-foreground">
                                                {subject.description}
                                            </TableCell>
                                            <TableCell className="text-muted-foreground">
                                                {new Date(subject.createdOn).toLocaleDateString()}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex justify-end space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        onClick={() => handleEdit(subject)}
                                                        className="hover:bg-muted hover:text-foreground"
                                                    >
                                                        <Pencil className="h-4 w-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        onClick={() => handleDeleteClick(subject)}
                                                        disabled={isDeleting}
                                                        className="hover:bg-muted hover:text-destructive"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                    {filteredSubjects.length === 0 && (
                                        <TableRow>
                                            <TableCell colSpan={4}>
                                                <div className="flex flex-col items-center justify-center py-8 space-y-2">
                                                    <GraduationCap className="h-8 w-8 text-muted-foreground" />
                                                    <p className="text-muted-foreground text-sm">No subjects found</p>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        )}
                    </ScrollArea>
                </Card>
            </div>

            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This will permanently delete the subject "{subjectToDelete?.name}". This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDeleteConfirm} disabled={isDeleting}>
                            {isDeleting ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                                "Delete"
                            )}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
};

export default SubjectsPage;
