import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, AlertCircle, History, RotateCcw, Check } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import DOMPurify from 'dompurify';
import { CourseContentPreview } from './GenerateContentModal';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card } from '@/components/ui/card';

interface ImproveContentDialogProps {
    content: CourseContentPreview;
    target: string;
    targetType: 'chapter' | 'topic' | 'content';
    onClose: () => void;
    onImproveComplete: (improvedContent: CourseContentPreview) => void;
}

interface TargetInfo {
    content: string;
    path: string;
    parentPath: string[];
}

interface ImprovementHistory {
    prompt: string;
    content: string;
    timestamp: number;
}

export const ImproveContentDialog: React.FC<ImproveContentDialogProps> = ({
    content,
    target,
    targetType,
    onClose,
    onImproveComplete,
}) => {
    const [isImproving, setIsImproving] = useState(false);
    const [improvementPrompt, setImprovementPrompt] = useState('');
    const [streamedContent, setStreamedContent] = useState<string>('');
    const [error, setError] = useState<string | null>(null);
    const [previewMode, setPreviewMode] = useState<'original' | 'improved'>('original');
    const [streamingText, setStreamingText] = useState('');
    const [history, setHistory] = useState<ImprovementHistory[]>([]);
    const [activeTab, setActiveTab] = useState<'current' | 'history'>('current');
    const abortControllerRef = useRef<AbortController | null>(null);

    const maxPromptLength = 500;
    const [targetContent, targetPath] = getTargetContent(content, target, targetType);

    const [showDiff, setShowDiff] = useState(false);
    const [improvementSuggestions] = useState([
        "Make it more engaging and interactive",
        "Simplify the language and concepts",
        "Add more examples and explanations",
        "Improve structure and organization",
        "Add key takeaways and summary"
    ]);

    const handleSuggestionClick = (suggestion: string) => {
        setImprovementPrompt(prev =>
            prev ? `${prev}\n${suggestion}` : suggestion
        );
    };

    const handleImprove = async () => {
        setIsImproving(true);
        setError(null);

        // Cancel any ongoing streaming
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }

        abortControllerRef.current = new AbortController();

        try {
            const response = await fetch("https://lcf.stmin.dev/api/improve-content", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    content,
                    target,
                    targetType,
                    improvement: improvementPrompt
                }),
                signal: abortControllerRef.current.signal
            });

            const reader = response.body?.getReader();
            if (!reader) throw new Error("No response stream available");

            const decoder = new TextDecoder();
            let accumulatedContent = '';

            while (true) {
                const { value, done } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value, { stream: true });
                accumulatedContent += chunk;
                setStreamingText(accumulatedContent);
            }

            // Add to history
            setHistory(prev => [{
                prompt: improvementPrompt,
                content: accumulatedContent,
                timestamp: Date.now()
            }, ...prev]);

            setStreamedContent(accumulatedContent);
        } catch (err: any) {
            if (err.name !== 'AbortError') {
                setError("Failed to improve content. Please try again.");
                toast({
                    title: "Error improving content",
                    description: "Please try again later",
                    variant: "destructive",
                });
            }
        } finally {
            setIsImproving(false);
            abortControllerRef.current = null;
        }
    };

    const applyImprovement = () => {
        onImproveComplete(buildImprovedContent(content, target, targetType, streamedContent));
        toast({
            title: "Content improved successfully",
            variant: "default",
        });
        onClose();
    };

    return (
        <Dialog open onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="max-w-5xl max-h-[90vh] flex flex-col">
                <DialogHeader className="pb-4 border-b">
                    <DialogTitle>Improve {targetType.charAt(0).toUpperCase() + targetType.slice(1)}</DialogTitle>
                    <div className="flex items-center justify-between mt-2">
                        <Badge variant="outline" className="w-fit">
                            {targetPath}
                        </Badge>
                    </div>
                </DialogHeader>

                <Tabs value={activeTab} onValueChange={(value: string) => setActiveTab(value as 'history' | 'current')} className="flex-1 min-h-0">
                    <div className="flex items-center justify-between py-2">
                        <TabsList>
                            <TabsTrigger value="current">Current</TabsTrigger>
                            <TabsTrigger value="history" className="flex items-center gap-2">
                                <History className="w-4 h-4" />
                                History ({history.length})
                            </TabsTrigger>
                        </TabsList>
                        <div className="flex items-center gap-2">
                            {streamedContent && (
                                <>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setPreviewMode(prev => prev === 'original' ? 'improved' : 'original')}
                                    >
                                        {previewMode === 'original' ? 'Show Improved' : 'Show Original'}
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setShowDiff(!showDiff)}
                                    >
                                        {showDiff ? 'Hide Diff' : 'Show Diff'}
                                    </Button>
                                </>
                            )}
                        </div>
                    </div>

                    <TabsContent value="current" className="flex flex-col flex-1 min-h-0 mt-0">
                        <div className="grid grid-cols-2 gap-4 flex-1 min-h-0">
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <div className="flex justify-between items-center">
                                        <label className="text-sm font-medium">Improvement Prompt</label>
                                        <span className="text-xs text-muted-foreground">
                                            {improvementPrompt.length}/{maxPromptLength}
                                        </span>
                                    </div>
                                    <Textarea
                                        value={improvementPrompt}
                                        onChange={(e) => setImprovementPrompt(e.target.value.slice(0, maxPromptLength))}
                                        placeholder="Describe how you want to improve the content..."
                                        className="h-32"
                                        disabled={isImproving}
                                    />
                                    <div className="flex flex-wrap gap-2">
                                        {improvementSuggestions.map((suggestion, idx) => (
                                            <Badge
                                                key={idx}
                                                variant="secondary"
                                                className="cursor-pointer hover:bg-secondary/80"
                                                onClick={() => handleSuggestionClick(suggestion)}
                                            >
                                                {suggestion}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>

                                <ScrollArea className="h-[calc(100vh-26rem)] border rounded-md">
                                    <div className="p-4">
                                        <div className="flex justify-between items-center mb-2">
                                            <label className="text-sm font-medium">Original Content</label>
                                        </div>
                                        <div className="prose prose-sm max-w-none" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(targetContent) }} />
                                    </div>
                                </ScrollArea>
                            </div>

                            <div className="space-y-4">
                                <div className="flex justify-between items-center">
                                    <label className="text-sm font-medium">
                                        {isImproving ? 'Improving Content...' : 'Improved Content'}
                                    </label>
                                    {isImproving && (
                                        <motion.div
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1 }}
                                            className="flex items-center gap-2"
                                        >
                                            <Loader2 className="w-4 h-4 animate-spin" />
                                            <span className="text-xs">Streaming improvements...</span>
                                        </motion.div>
                                    )}
                                </div>
                                <ScrollArea className="h-[calc(100vh-26rem)] border rounded-md">
                                    <div className="p-4">
                                        <div
                                            className="prose prose-sm max-w-none"
                                            dangerouslySetInnerHTML={{
                                                __html: DOMPurify.sanitize(
                                                    showDiff ? generateDiff(targetContent, streamingText || streamedContent)
                                                        : streamingText || streamedContent || 'Click "Improve" to start'
                                                )
                                            }}
                                        />
                                    </div>
                                </ScrollArea>
                            </div>
                        </div>
                    </TabsContent>

                    <TabsContent value="history" className="mt-0">
                        <ScrollArea className="h-[calc(100vh-26rem)] border rounded-md">
                            <div className="p-4 space-y-4">
                                {history.map((item, idx) => (
                                    <Card key={idx} className="p-4">
                                        <div className="flex justify-between items-start mb-2">
                                            <div>
                                                <p className="text-sm font-medium">Prompt:</p>
                                                <p className="text-sm text-muted-foreground">{item.prompt}</p>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => {
                                                        setStreamedContent(item.content);
                                                        setActiveTab('current');
                                                    }}
                                                >
                                                    <RotateCcw className="w-4 h-4 mr-1" />
                                                    Restore
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="prose prose-sm max-w-none">
                                            {item.content.substring(0, 200)}...
                                        </div>
                                    </Card>
                                ))}
                            </div>
                        </ScrollArea>
                    </TabsContent>
                </Tabs>

                {error && (
                    <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center gap-2 text-sm text-destructive mt-4"
                    >
                        <AlertCircle className="w-4 h-4" />
                        {error}
                    </motion.div>
                )}

                <DialogFooter className="border-t pt-4">
                    <div className="flex justify-between w-full">
                        <Button variant="outline" onClick={onClose} disabled={isImproving}>
                            Cancel
                        </Button>
                        <div className="flex items-center gap-2">
                            <Button
                                onClick={handleImprove}
                                disabled={isImproving || !improvementPrompt.trim()}
                                variant="secondary"
                            >
                                {isImproving ? (
                                    <>
                                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                        Improving...
                                    </>
                                ) : (
                                    <>
                                        <RotateCcw className="w-4 h-4 mr-2" />
                                        Improve
                                    </>
                                )}
                            </Button>
                            {streamedContent && (
                                <Button onClick={applyImprovement}>
                                    <Check className="w-4 h-4 mr-2" />
                                    Apply Changes
                                </Button>
                            )}
                        </div>
                    </div>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

const getTargetContent = (
    content: CourseContentPreview,
    target: string,
    targetType: string
): [string, string] => {
    const [type, ...indices] = target.split('-');
    let targetContent = '';
    let path = '';

    try {
        if (targetType === 'chapter') {
            const chapterIdx = parseInt(indices[0]);
            const chapter = content.chapters[chapterIdx];
            targetContent = `${chapter.name}\n\n${chapter.description}`;
            path = `Chapter ${chapterIdx + 1}`;
        } else if (targetType === 'topic') {
            const [chapterIdx, topicIdx] = indices.map(i => parseInt(i));
            const topic = content.chapters[chapterIdx].topics[topicIdx];
            targetContent = `${topic.name}\n\n${topic.description}\n\n${topic.content || ''}`;
            path = `Chapter ${chapterIdx + 1} > Topic ${topicIdx + 1}`;
        }
    } catch (error) {
        console.error('Error getting target content:', error);
        return ['', 'Invalid path'];
    }

    return [targetContent, path];
};

const buildImprovedContent = (
    originalContent: CourseContentPreview,
    target: string,
    targetType: string,
    improvedContent: string
): CourseContentPreview => {
    const [type, ...indices] = target.split('-');
    const newContent = { ...originalContent };

    try {
        if (targetType === 'chapter') {
            const chapterIdx = parseInt(indices[0]);
            const [name, description] = improvedContent.split('\n\n');
            newContent.chapters[chapterIdx] = {
                ...newContent.chapters[chapterIdx],
                name: name.trim(),
                description: description.trim()
            };
        } else if (targetType === 'topic') {
            const [chapterIdx, topicIdx] = indices.map(i => parseInt(i));
            const [name, description, ...contentParts] = improvedContent.split('\n\n');
            newContent.chapters[chapterIdx].topics[topicIdx] = {
                ...newContent.chapters[chapterIdx].topics[topicIdx],
                name: name.trim(),
                description: description.trim(),
                content: contentParts.join('\n\n').trim()
            };
        }
    } catch (error) {
        console.error('Error building improved content:', error);
        return originalContent;
    }

    return newContent;
};

// Utility function to generate diff view
const generateDiff = (original: string, improved: string): string => {
    const diff = require('diff');
    const differences = diff.diffWords(original, improved);

    return differences
        .map((part: any) => {
            if (part.added) {
                return `<span class="bg-green-100 dark:bg-green-900/30">${part.value}</span>`;
            }
            if (part.removed) {
                return `<span class="bg-red-100 dark:bg-red-900/30 line-through">${part.value}</span>`;
            }
            return part.value;
        })
        .join('');
};
