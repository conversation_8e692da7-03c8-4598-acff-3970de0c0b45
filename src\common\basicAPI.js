import APIConnect from "APIConnect";
import { store } from "@/store/store";
import {
  setAllSubjects,
  setAllCourses,
  setAllChapters,
  setAllTopics,
} from "components/Admin/Forms/appDetailsSlice";

const getAllSubjects = async () => {
  const getAllSubjectsData = store.dispatch(
    APIConnect.endpoints.getAllSubjectsData.initiate(undefined, {
      forceRefetch: true,
    })
  );
  try {
    const {
      data: { resultObject },
    } = await getAllSubjectsData;
    if (resultObject) {
      store.dispatch(setAllSubjects(resultObject));
      return resultObject;
    }
  } catch (err) {
    console.log("err", err);
  }
};

const getAllCourses = async () => {
  const getAllCoursesFormData = store.dispatch(
    APIConnect.endpoints.getAllCoursesFormData.initiate(undefined, {
      forceRefetch: true,
    })
  );
  try {
    const {
      data: { resultObject },
    } = await getAllCoursesFormData;
    if (resultObject) {
      store.dispatch(setAllCourses(resultObject));
      return resultObject;
    }
  } catch (err) {
    console.log("err", err);
  }
};

const getAllChapters = async () => {
  const getAllChaptersFormData = store.dispatch(
    APIConnect.endpoints.getAllChaptersFormData.initiate(undefined, {
      forceRefetch: true,
    })
  );
  try {
    const {
      data: { resultObject },
    } = await getAllChaptersFormData;
    if (resultObject) {
      store.dispatch(setAllChapters(resultObject));
      return resultObject;
    }
  } catch (err) {
    console.log("err", err);
  }
};

const getAllTopics = async () => {
  const getAllTopicsFormData = store.dispatch(
    APIConnect.endpoints.getAllTopicsFormData.initiate(undefined, {
      forceRefetch: true,
    })
  );
  try {
    const {
      data: { resultObject },
    } = await getAllTopicsFormData;
    if (resultObject) {
      store.dispatch(setAllTopics(resultObject));
      return resultObject;
    }
  } catch (err) {
    console.log("err", err);
  }
};

export { getAllChapters, getAllSubjects, getAllCourses, getAllTopics };
