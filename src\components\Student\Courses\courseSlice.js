import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  allCourses: [],
  courseDetail: {},
  currentCourseChoosen: {},
  contentRelatedTopic: {},
  bookmarksId: []
};

export const coursesSlice = createSlice({
  initialState,
  name: "courses",
  reducers: {
    courses: () => initialState,
    setStudentAllCourses: (state, action) => {
      state.allCourses = action.payload;
    },
    setStudentCoursesDetail: (state, action) => {
      state.courseDetail = action.payload;
    },
    setCurrentCourseChoosen: (state, action) => {
      state.currentCourseChoosen = action.payload;
    },
    setContentRelatedTopic: (state, action) => {
      state.contentRelatedTopic = action.payload;
    },
    setBookmarksId: (state, action) => {
      state.bookmarksId = action.payload;
    },
  },
});

export const {
  setStudentAllCourses,
  setStudentCoursesDetail,
  setCurrentCourseChoosen,
  setContentRelatedTopic,
  setBookmarksId
  
} = coursesSlice.actions;

export default coursesSlice;
