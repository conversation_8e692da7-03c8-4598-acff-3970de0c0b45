import React, { useState, useEffect } from "react";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import "suneditor/src/assets/css/suneditor.css";
import "./ContentCreation.css";
import {
  useCreateSubtopicMutation,
  useLazyGetCourseDetailsDataQuery,
} from "APIConnect";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Select from "react-select";
import { getAllCourses } from "@/common/basicAPI";
import { toast } from "@/hooks/use-toast";

const ContentTypes = [
  { value: "text", label: "Text" },
  { value: "video", label: "Video" },
  { value: "audio", label: "Audio" },
  { value: "scorm", label: "SCORM" }
];

const AssignmentTypes = [
  { value: 1, label: "Submission" },
  { value: 2, label: "Quiz" }
];

const ContentCreation = () => {
  const [getCourseDetailsData] = useLazyGetCourseDetailsDataQuery();
  const [createSubtopic] = useCreateSubtopicMutation();

  const [coursesList, setCoursesList] = useState([]);
  const [courseAllDetail, setCourseAllDetail] = useState({});
  const [chaptersList, setChaptersList] = useState([]);
  const [topicsList, setTopicsList] = useState([]);
  const [courseId, setCourseId] = useState("");
  const [chapterId, setChapterId] = useState("");
  const [topicId, setTopicId] = useState("");
  
  // New subtopic state
  const [subtopicData, setSubtopicData] = useState({
    subTopicName: "",
    topicWeight: 0,
    type: "text",
    content: "",
    contentUrl: "",
    assignment: {
      type: 1,
      title: "",
      assignmentText: "",
      fileUrl: ""
    }
  });
  const [hasAssignment, setHasAssignment] = useState(false);

  useEffect(() => {
    getCourses();
  }, []);

  const getCourses = async () => {
    try {
      const resultObject = await getAllCourses();
      if (resultObject) {
        let data = [],
          obj = {};
        resultObject?.map((item) => {
          obj = {
            value: item?.id,
            label: item?.name,
          };
          data.push(obj);
        });
        setCoursesList(data);
      }
    } catch (err) {
      console.error("err", err);
      toast({
        title: "Error fetching courses",
        description: "Please try again later",
        variant: "destructive"
      });
    }
  };

  const handleCourseChoose = async (id) => {
    try {
      const obj = {
        courseId: id,
      };
      const {
        data: { resultObject },
      } = await getCourseDetailsData(obj);
      if (resultObject && resultObject.chapters?.length) {
        setCourseAllDetail(resultObject);
        let data = [],
          obj = {};
        resultObject.chapters?.map((item) => {
          obj = {
            value: item?.id,
            label: item?.name,
          };
          data.push(obj);
        });
        setChaptersList(data);
        setTopicId("");
        setChapterId("");
      }
    } catch (err) {
      console.error("err", err);
      toast({
        title: "Error fetching course details",
        description: "Please try again later",
        variant: "destructive"
      });
    }
  };

  const handleChapterChoose = async (id) => {
    const chapter = courseAllDetail?.chapters?.find((e) => e.id === id);
    const topics = chapter?.topics;
    if (topics?.length) {
      let data = [],
        obj = {};
      topics?.map((item) => {
        obj = {
          value: item?.id,
          label: item?.name,
        };
        data.push(obj);
      });
      setTopicsList(data);
      setTopicId("");
    }
  };

  const handleSubmit = async () => {
    try {
      if (!subtopicData.subTopicName) {
        toast({
          title: "Subtopic name is required",
          variant: "destructive"
        });
        return;
      }

      if (subtopicData.topicWeight < 1 || subtopicData.topicWeight > 100) {
        toast({
          title: "Weight must be between 1 and 100",
          variant: "destructive"
        });
        return;
      }

      const topic = topicsList.find((e) => e.value === topicId);
      const payload = {
        topicId,
        courseId,
        subTopicName: subtopicData.subTopicName,
        topicWeight: subtopicData.topicWeight,
        type: subtopicData.type,
        content: subtopicData.content,
        contentUrl: subtopicData.contentUrl,
        assignment: hasAssignment ? subtopicData.assignment : undefined
      };

      await createSubtopic(payload).unwrap();
      
      toast({
        title: "Subtopic created successfully",
        variant: "default"
      });

      // Reset form
      setSubtopicData({
        subTopicName: "",
        topicWeight: 0,
        type: "text",
        content: "",
        contentUrl: "",
        assignment: {
          type: 1,
          title: "",
          assignmentText: "",
          fileUrl: ""
        }
      });
      setHasAssignment(false);
    } catch (error) {
      console.error('Error creating subtopic:', error);
      toast({
        title: "Failed to create subtopic",
        description: "Please try again later",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="content-creation">
      <div className="container space-y-6">
        <div className="topic-header">
          <h2 className="text-2xl font-bold">Create Content</h2>
        </div>

        <div className="space-y-4">
          <div>
            <Label>Course</Label>
            <Select
              options={coursesList}
              className="form-input select-input"
              value={coursesList.find((option) => option.value === courseId) || ""}
              onChange={(item) => {
                handleCourseChoose(item?.value);
                setCourseId(item?.value);
              }}
            />
          </div>

          <div>
            <Label>Chapter</Label>
            <Select
              options={chaptersList}
              className="form-input select-input"
              value={chaptersList.find((option) => option.value === chapterId) || ""}
              onChange={(item) => {
                handleChapterChoose(item?.value);
                setChapterId(item?.value);
              }}
            />
          </div>

          <div>
            <Label>Topic</Label>
            <Select
              options={topicsList}
              className="form-input select-input"
              value={topicsList.find((option) => option.value === topicId) || ""}
              onChange={(item) => setTopicId(item?.value)}
            />
          </div>

          <div>
            <Label>Subtopic Name</Label>
            <Input
              value={subtopicData.subTopicName}
              onChange={(e) => setSubtopicData(prev => ({ ...prev, subTopicName: e.target.value }))}
              placeholder="Enter subtopic name"
            />
          </div>

          <div>
            <Label>Weight (%)</Label>
            <Input
              type="number"
              min="0"
              max="100"
              value={subtopicData.topicWeight}
              onChange={(e) => setSubtopicData(prev => ({ ...prev, topicWeight: parseInt(e.target.value) || 0 }))}
              placeholder="Enter weight (0-100)"
            />
          </div>

          <div>
            <Label>Content Type</Label>
            <Select
              options={ContentTypes}
              className="form-input select-input"
              value={ContentTypes.find(type => type.value === subtopicData.type)}
              onChange={(item) => setSubtopicData(prev => ({ ...prev, type: item.value }))}
            />
          </div>

          {subtopicData.type === 'text' && (
            <div>
              <Label>Content</Label>
              <SunEditor
                setOptions={{
                  buttonList: [
                    ["undo", "redo"],
                    ["font", "fontSize", "formatBlock"],
                    ["bold", "underline", "italic", "strike"],
                    ["fontColor", "hiliteColor"],
                    ["align", "list", "table"],
                    ["link", "image", "video"],
                    ["fullScreen", "showBlocks", "codeView"],
                    ["preview", "print"],
                  ],
                  height: "70%",
                  minHeight: "100px",
                }}
                onChange={(content) => setSubtopicData(prev => ({ ...prev, content }))}
              />
            </div>
          )}

          {(subtopicData.type === 'video' || subtopicData.type === 'audio' || subtopicData.type === 'scorm') && (
            <div>
              <Label>Content URL</Label>
              <Input
                value={subtopicData.contentUrl}
                onChange={(e) => setSubtopicData(prev => ({ ...prev, contentUrl: e.target.value }))}
                placeholder={`Enter ${subtopicData.type} URL`}
              />
            </div>
          )}

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="hasAssignment"
              checked={hasAssignment}
              onChange={(e) => setHasAssignment(e.target.checked)}
            />
            <Label htmlFor="hasAssignment">Include Assignment</Label>
          </div>

          {hasAssignment && (
            <div className="space-y-4 border p-4 rounded-lg">
              <div>
                <Label>Assignment Type</Label>
                <Select
                  options={AssignmentTypes}
                  className="form-input select-input"
                  value={AssignmentTypes.find(type => type.value === subtopicData.assignment.type)}
                  onChange={(item) => setSubtopicData(prev => ({
                    ...prev,
                    assignment: { ...prev.assignment, type: item.value }
                  }))}
                />
              </div>

              <div>
                <Label>Assignment Title</Label>
                <Input
                  value={subtopicData.assignment.title}
                  onChange={(e) => setSubtopicData(prev => ({
                    ...prev,
                    assignment: { ...prev.assignment, title: e.target.value }
                  }))}
                  placeholder="Enter assignment title"
                />
              </div>

              <div>
                <Label>Assignment Text</Label>
                <Input
                  value={subtopicData.assignment.assignmentText}
                  onChange={(e) => setSubtopicData(prev => ({
                    ...prev,
                    assignment: { ...prev.assignment, assignmentText: e.target.value }
                  }))}
                  placeholder="Enter assignment description"
                />
              </div>

              <div>
                <Label>File URL (Optional)</Label>
                <Input
                  value={subtopicData.assignment.fileUrl}
                  onChange={(e) => setSubtopicData(prev => ({
                    ...prev,
                    assignment: { ...prev.assignment, fileUrl: e.target.value }
                  }))}
                  placeholder="Enter file URL"
                />
              </div>
            </div>
          )}
        </div>

        <div id="savebutton" className="button-container align-right">
          <button 
            onClick={handleSubmit}
            className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90"
          >
            Create Subtopic
          </button>
        </div>
      </div>
    </div>
  );
};

export default ContentCreation;
