import axios from 'axios';
import * as AppConstant from '../constant/AppConstant';

// Type definitions
export interface User {
  id: string;
  name: string;
  email?: string;
  picture?: string;
  role: 'Admin' | 'SchoolAdmin' | 'Teacher' | 'Student';
}

export interface Discussion {
  id: string;
  title: string;
  description: string;
  courseId?: string;
  classId?: string;
  topicId?: string;
  createdBy: string;
  createdById: string;
  createdAt: string;
  updatedAt: string;
  commentCount: number;
  voteCount: number;
  status: 'ACTIVE' | 'ARCHIVED' | 'DELETED';
  tags?: string[];
}

export interface Comment {
  id: string;
  discussionId: string;
  content: string;
  authorId: string;
  authorName: string;
  authorPictureUrl?: string;
  createdAt: string;
  updatedAt: string;
  voteCount: number;
  status: 'ACTIVE' | 'PENDING' | 'DELETED';
  evaluation?: {
    engagement: number;
    relevance: number;
    depthOfThought: number;
    evidence: number;
    overallScore: number;
  }
}

export interface CreateDiscussionPayload {
  title: string;
  description: string;
  courseId?: string;
  classId?: string;
  topicId?: string;
  tags?: string[];
}

export interface CreateCommentPayload {
  discussionId: string;
  content: string;
  userName: string;
  userPictureUrl?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export interface GetDiscussionsParams {
  page?: number;
  limit?: number;
  courseId?: string;
  classId?: string;
  topicId?: string;
  schoolId?: string;
  requiresModeration?: boolean;
  searchQuery?: string;
  filter?: string;
  sort?: string;
}

// API clients for different endpoints
export const discussionsApi = {
  // Get a list of discussions with pagination
  async getDiscussions(
    token: string,
    page: number = 1,
    limit: number = 20,
    params: GetDiscussionsParams = {}
  ): Promise<PaginatedResponse<Discussion>> {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(params.courseId && { courseId: params.courseId }),
      ...(params.classId && { classId: params.classId }),
      ...(params.topicId && { topicId: params.topicId }),
      ...(params.schoolId && { schoolId: params.schoolId }),
      ...(params.requiresModeration && { requiresModeration: params.requiresModeration.toString() }),
      ...(params.searchQuery && { searchQuery: params.searchQuery }),
      ...(params.filter && { filter: params.filter }),
      ...(params.sort && { sort: params.sort })
    });

    const response = await axios.get(
      `${AppConstant.discussionsBaseUrl}/discussions?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    
    return response.data;
  },

  // Get a single discussion by ID
  async getDiscussion(token: string, id: string): Promise<Discussion> {
    const response = await axios.get(`${AppConstant.discussionsBaseUrl}/discussions/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    
    return response.data;
  },

  // Create a new discussion
  async createDiscussion(token: string, payload: CreateDiscussionPayload): Promise<Discussion> {
    const response = await axios.post(
      `${AppConstant.discussionsBaseUrl}/discussions`,
      payload,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    
    return response.data;
  },

  // Update an existing discussion
  async updateDiscussion(token: string, id: string, payload: Partial<CreateDiscussionPayload>): Promise<Discussion> {
    const response = await axios.patch(
      `${AppConstant.discussionsBaseUrl}/discussions/${id}`,
      payload,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    
    return response.data;
  },

  // Delete a discussion
  async deleteDiscussion(token: string, id: string): Promise<void> {
    await axios.delete(`${AppConstant.discussionsBaseUrl}/discussions/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  },

  // Search discussions
  async searchDiscussions(token: string, query: string, page: number = 1, limit: number = 20): Promise<PaginatedResponse<Discussion>> {
    const queryParams = new URLSearchParams({
      q: query,
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await axios.get(
      `${AppConstant.discussionsBaseUrl}/discussions/search?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    
    return response.data;
  },
};

export const commentsApi = {
  // Get comments for a discussion with pagination
  async getComments(token: string, discussionId: string, page: number = 1, limit: number = 20): Promise<PaginatedResponse<Comment>> {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await axios.get(
      `${AppConstant.discussionsBaseUrl}/discussions/${discussionId}/comments?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    
    return response.data;
  },

  // Create a new comment
  async createComment(token: string, payload: CreateCommentPayload): Promise<Comment> {
    const response = await axios.post(
      `${AppConstant.discussionsBaseUrl}/discussions/${payload.discussionId}/comments`,
      payload,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    
    return response.data;
  },

  // Update a comment
  async updateComment(token: string, commentId: string, content: string): Promise<Comment> {
    const response = await axios.patch(
      `${AppConstant.discussionsBaseUrl}/comments/${commentId}`,
      { content },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    
    return response.data;
  },

  // Delete a comment
  async deleteComment(token: string, commentId: string): Promise<void> {
    await axios.delete(`${AppConstant.discussionsBaseUrl}/comments/${commentId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  },

  // Vote on a comment
  async voteComment(token: string, commentId: string, value: 1 | -1): Promise<Comment> {
    const response = await axios.post(
      `${AppConstant.discussionsBaseUrl}/comments/${commentId}/vote`,
      { value },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    
    return response.data;
  },
};

export const moderationApi = {
  // Get comments pending moderation with pagination (admin/teacher only)
  async getPendingModeration(token: string, page: number = 1, limit: number = 20): Promise<PaginatedResponse<Comment>> {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await axios.get(
      `${AppConstant.discussionsBaseUrl}/moderation/pending?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    
    return response.data;
  },

  // Approve a comment
  async approveComment(token: string, commentId: string): Promise<Comment> {
    const response = await axios.post(
      `${AppConstant.discussionsBaseUrl}/moderation/${commentId}/approve`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    
    return response.data;
  },

  // Reject a comment
  async rejectComment(token: string, commentId: string, reason?: string): Promise<void> {
    await axios.post(
      `${AppConstant.discussionsBaseUrl}/moderation/${commentId}/reject`,
      { reason },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
  },

  // Get moderation statistics (admin/teacher only)
  async getModerationStats(token: string): Promise<any> {
    const response = await axios.get(`${AppConstant.discussionsBaseUrl}/moderation/stats`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    
    return response.data;
  },
};

export const leaderboardApi = {
  // Get leaderboard data
  async getLeaderboard(token: string, timeRange: string = 'weekly'): Promise<any> {
    const response = await axios.get(
      `${AppConstant.discussionsBaseUrl}/leaderboard?timeRange=${timeRange}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    
    return response.data;
  },

  // Update leaderboard (admin/teacher only)
  async updateLeaderboard(token: string, timeRange: string = 'weekly'): Promise<void> {
    await axios.post(
      `${AppConstant.discussionsBaseUrl}/leaderboard/update`,
      { timeRange },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
  },
};

export const mediaApi = {
  // Upload media for discussions/comments
  async uploadMedia(token: string, file: File): Promise<string> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await axios.post(
      `${AppConstant.discussionsBaseUrl}/media/upload`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    
    return response.data.url;
  },
};