import React from 'react';
import { ChevronDown } from 'lucide-react';

interface SkillData {
  name: string;
  value: number; // 0 to 100
  color: string; // Tailwind color class
}

const defaultSkills: SkillData[] = [
  { name: 'Innovation', value: 40, color: 'bg-purple-400' },
  { name: 'Strategic\nThinking', value: 90, color: 'bg-teal-300' },
  { name: 'Subject\nKnowledge', value: 75, color: 'bg-black' },
  { name: 'Sustainability\nQuotient', value: 100, color: 'bg-blue-300' },
  { name: 'Teamwork', value: 35, color: 'bg-blue-200' },
];

const SkillsBarChart: React.FC<{ skills?: SkillData[] }> = ({ skills = defaultSkills }) => {
  const yAxisWidthClass = "w-10"; // Approx 40px, for Y-axis labels
  const yAxisPaddingRightClass = "pr-2";
  const chartVisualHeightClass = "h-52"; // Approx 208px, for the bars area
  const barWidthClass = "w-8"; // Further reduced for increased margins
  const barMarginClass = "mx-1"; // Increased margin between bars

  return (
    <div className="bg-white rounded-[10px] border border-gray-200 w-full p-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-medium text-base text-gray-700">Top 5 Skills</h3>
        <button className="text-teal-700 flex items-center text-sm font-medium">
          <span>All</span>
          <ChevronDown size={16} className="ml-1" />
        </button>
      </div>

      {/* Chart Body: Y-axis + Bars, then X-axis below */}
      <div className="flex flex-col mt-3">
        {/* Upper part: Y-axis and Bars Area */}
        <div className={`flex ${chartVisualHeightClass}`}>
          {/* Y-axis Labels */}
          <div className={`flex flex-col justify-between text-[10px] text-gray-500 font-medium h-full ${yAxisWidthClass} ${yAxisPaddingRightClass} text-right`}>
            <span>100%</span>
     
            <span>50%</span>
            <span>25%</span>
            <span>0</span>
          </div>

          {/* Bars Container (with bottom border for 0 line) */}
          <div className="flex-1 flex justify-around items-end h-full border-b border-gray-300">
            {skills.map((skill, index) => (
              <div
                key={index}
                className={`${barWidthClass} ${barMarginClass} ${skill.color} rounded-[10px] transition-all duration-300 ease-out`}
                style={{ height: `${skill.value}%` }}
                title={`${skill.name}: ${skill.value}%`}
              />
            ))}
          </div>
        </div>

        {/* Lower part: X-axis Labels Area (below Y-axis/Bars) */}
        <div className="flex mt-1.5"> {/* mt-1.5 for a bit of space above X-labels */}
          {/* Spacer for Y-axis width + padding */}
          <div className={`${yAxisWidthClass} ${yAxisPaddingRightClass}`}></div>
          
          {/* Actual X-axis Labels, aligned with bars */}
          <div className="flex-1 flex justify-around">
            {skills.map((skill, index) => (
              <div
                key={index}
                className={`${barWidthClass} ${barMarginClass} text-[8px] text-gray-700 font-medium text-center whitespace-pre-wrap leading-snug tracking-wider break-all `}
              >
                {skill.name}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkillsBarChart;
