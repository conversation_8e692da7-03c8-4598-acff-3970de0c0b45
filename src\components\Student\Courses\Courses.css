@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display&display=swap');
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');

.courses-container {
  display: flex;
  width: 100%;
}
.courses-wrapper {
  position: relative;
  max-width: 60%;
  margin: 40px auto;

  /* position: relative;
  max-width: 62%;
  margin: 40px auto; */
  /* margin: 40px 40px 40px 40px; */
  /* margin-left: 40px auto;
  margin-top: 40px auto;
  margin-bottom: 40px auto; */
  /* display: flex; */
  
  /* background-color: red; */

}

.course-welcome {
  font-style: italic; 
  font-weight: bold;
  font-size: 20px;
}

.courses-container-grid {
  border-radius: 12px;
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  padding: 20px;
  width: 100%; /* Increased width to display 4 courses initially */
  padding: 20px /* Added padding to make space for the arrows */
}

/* Hide scrollbar for all browsers */
.courses-container-grid::-webkit-scrollbar {
  display: none;
}

.courses-container-grid {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.course {
  background-color: rgb(255, 255, 255);
  margin: 10px;
  border-radius: 9px;
  max-width: 251px;
  min-width: 269px;
  max-height: 250px;
  overflow: hidden;
  text-align: left;
  cursor: pointer;
  font-family: 'DM Serif Display', serif;
  transition: transform 0.3s, opacity 0.5s ease;
  opacity: 0;
  transform: translateY(30px);
  animation: popIn 0.5s forwards ease-out;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
.course-details{
  border-radius: 0 0 9px 9px;
  /* border: 1.5px solid #703D3D; */
  border: 1.5px solid #ccc;
}


@keyframes popIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.course .imgCon {
  width: 100%;
  height: 140px;
  overflow: hidden;
}

.course img {
  width: 100%;
}

.course-name {
  font-size: 1.1em !important;
  color: #343330 !important;
  padding-left: 10px !important;
  font-weight: 100 !important;
  margin-bottom: 1px !important;
}

.course-completed {
  font-size: 1em;
  color: #343330;
  padding-left: 10px;
  font-family: 'DM Sans', sans-serif;
  font-weight: 500;
}

.progress-bar {
  background-color:#6dcecc;
  border-radius: 10px;
  overflow: hidden;
  margin-left: 10px;
  margin-top: 10px;
  margin-bottom: -10px;
  width: 95%;
  height: 7px;
}

.progress {
  height: 100%;
  background-color:
  #336b6c;
}

.course-status {
  font-weight: 700;
  margin-right: 3%;
}

.course-progress {
  font-size: 1em;
  font-weight: 700;
  color: #343330;
  margin-left: 15%;
}

.course:hover {
  transform: scale(1.05);
}

.pop-out {
  animation: popOut 0.8s ease forwards;
}

@keyframes popOut {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Circular Arrows */
.scroll-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(20, 40, 29, 1);
  color: #fff;
  border: none;
  font-size: 24px;
  cursor: pointer;
  z-index: 10;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease, opacity 0.3s ease;
  opacity: 0;
  animation: popOut 0.8s ease forwards;
}

.scroll-arrow.left {
  left: 0;
  transform: translate(-50%, -50%); /* Aligns the arrow in the center of the first course */
}

.scroll-arrow.right {
  right: 0;
  transform: translate(50%, -50%); /* Aligns the arrow in the center of the last course */
}

.scroll-arrow:hover {
  transform: scale(1.1);
}

.courses-wrapper:hover .scroll-arrow {
  opacity: 1; /* Show arrows only after fetch */
}

@media (max-width: 992px) {
  .courses-container-grid {
    padding: 10px 20px;
  }

  .course {
    margin: 5px;
  }

  .scroll-arrow {
    display: none;
  }
}


/* Circular Arrows */
.courseGif {
  position: absolute;
 
  transform: translateY(-50%);

  color: #fff;
  
margin-left: 50;
  z-index: 10;
  width: 300px;
  height: 300px;
  
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease, opacity 0.3s ease;
  opacity: 0;
  animation: popOut 0.8s ease forwards;
}

.courseGif.right {
  right: 0;
  transform: translate(50%, -50%); /* Aligns the arrow in the center of the last course */
}

.course .courseParent:hover {
  transform: scale(1.02);
}