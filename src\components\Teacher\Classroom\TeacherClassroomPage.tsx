import React, { useState } from "react";
import { useAuth0 } from "@auth0/auth0-react";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "../../ui/table";
import { ScrollArea } from "../../ui/scroll-area";
import { Input } from "../../ui/input";
import { Button } from "../../ui/button";
import { Card } from "../../ui/card";
import { useGetSchoolClassroomsQuery, useGetClassRoomTokenQuery } from "../../../APIConnect";
import { Loader2, Search, Users, Eye, GraduationCap, BookOpen, Calendar, Video } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";

interface Student {
    id: string;
    user: {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
    };
}

interface Course {
    id: string;
    name: string;
    description: string;
}

interface Classroom {
    id: string;
    name: string;
    description: string;
    students: Student[];
    course: Course;
    createdOn: string;
}

const ClassroomLoadingSkeleton = () => (
    <div className="space-y-4">
        <div className="flex items-center space-x-4">
            <div className="h-12 w-12 rounded-lg bg-muted animate-pulse" />
            <div className="space-y-2">
                <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                <div className="h-3 w-24 bg-muted animate-pulse rounded" />
            </div>
        </div>
        <div className="space-y-2">
            <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
            <div className="h-4 w-1/2 bg-muted animate-pulse rounded" />
        </div>
    </div>
);

const TeacherClassroomPage = () => {
    const navigate = useNavigate();
    const { user } = useAuth0();
    const schoolId = user?.["http://learnido-app/schoolId"] as string | undefined;
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedClassroomId, setSelectedClassroomId] = useState<string | null>(null);

    const { data: classroomsData, isLoading } = useGetSchoolClassroomsQuery(schoolId);
    const { data: tokenData, isLoading: isLoadingToken } = useGetClassRoomTokenQuery(
        { classroomId: selectedClassroomId || "", displayName: user?.name || "Teacher" },
        { skip: !selectedClassroomId }
    );

    React.useEffect(() => {
        if (tokenData?.token && selectedClassroomId) {
            navigate(`/live-session/${selectedClassroomId}`);
        }
    }, [tokenData, selectedClassroomId, navigate]);

    const filteredClassrooms = (classroomsData?.resultObject || []).filter((classroom: Classroom) => {
        const matchesSearch = searchQuery
            ? classroom.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            classroom.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            classroom.course?.name.toLowerCase().includes(searchQuery.toLowerCase())
            : true;
        return matchesSearch;
    });

    const totalStudents = filteredClassrooms.reduce((acc: number, classroom: Classroom) =>
        acc + (classroom.students?.length || 0), 0);
    const totalCourses = new Set(filteredClassrooms.map((classroom: Classroom) => classroom.course?.id)).size;

    const handleStartLiveSession = (classroomId: string) => {
        setSelectedClassroomId(classroomId);
    };

    if (isLoading) {
        return (
            <div className="container mx-auto p-6">
                <div className="flex flex-col space-y-8">
                    <Card className="p-6">
                        <ClassroomLoadingSkeleton />
                    </Card>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto p-6">
            <div className="flex flex-col space-y-8">
                <Card className="p-6">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
                        <div>
                            <h1 className="text-3xl font-semibold tracking-tight flex items-center gap-2">
                                <Users className="h-7 w-7" />
                                My Classrooms
                            </h1>
                            <p className="text-sm text-muted-foreground mt-1">Manage your classes and students</p>
                        </div>
                        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full md:w-auto">
                            <div className="relative flex-1 sm:flex-none">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search classrooms..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="pl-8"
                                />
                            </div>
                        </div>
                    </div>
                </Card>

                <div className="grid gap-4 md:grid-cols-3">
                    <Card className="p-6">
                        <div className="flex items-center space-x-4">
                            <div className="p-3 bg-primary/10 rounded-lg">
                                <Users className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                                <div className="text-2xl font-bold">{totalStudents}</div>
                                <div className="text-sm text-muted-foreground">Total Students</div>
                            </div>
                        </div>
                    </Card>
                    <Card className="p-6">
                        <div className="flex items-center space-x-4">
                            <div className="p-3 bg-primary/10 rounded-lg">
                                <BookOpen className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                                <div className="text-2xl font-bold">{totalCourses}</div>
                                <div className="text-sm text-muted-foreground">Active Courses</div>
                            </div>
                        </div>
                    </Card>
                    <Card className="p-6">
                        <div className="flex items-center space-x-4">
                            <div className="p-3 bg-primary/10 rounded-lg">
                                <GraduationCap className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                                <div className="text-2xl font-bold">{filteredClassrooms.length}</div>
                                <div className="text-sm text-muted-foreground">Total Classrooms</div>
                            </div>
                        </div>
                    </Card>
                </div>

                <Card className="p-0">
                    <ScrollArea className="h-[calc(100vh-480px)] rounded-md">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Classroom</TableHead>
                                    <TableHead>Course</TableHead>
                                    <TableHead>Students</TableHead>
                                    <TableHead>Created On</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredClassrooms.map((classroom: Classroom) => (
                                    <TableRow
                                        key={classroom.id}
                                        className="group hover:bg-muted/50 transition-colors"
                                    >
                                        <TableCell>
                                            <div className="flex items-center space-x-3">
                                                <div className="h-10 w-10 rounded-lg bg-muted flex items-center justify-center">
                                                    <Users className="h-5 w-5 text-muted-foreground" />
                                                </div>
                                                <div>
                                                    <div className="font-medium">{classroom.name}</div>
                                                    {classroom.description && (
                                                        <div className="text-sm text-muted-foreground">{classroom.description}</div>
                                                    )}
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell>{classroom.course?.name}</TableCell>
                                        <TableCell>{classroom.students?.length || 0} students</TableCell>
                                        <TableCell>{new Date(classroom.createdOn).toLocaleDateString()}</TableCell>
                                        <TableCell>
                                            <div className="flex justify-end space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleStartLiveSession(classroom.id)}
                                                    className="flex items-center gap-1"
                                                    disabled={isLoadingToken}
                                                >
                                                    {isLoadingToken && selectedClassroomId === classroom.id ? (
                                                        <Loader2 className="h-4 w-4 animate-spin mr-1" />
                                                    ) : (
                                                        <Video className="h-4 w-4 mr-1" />
                                                    )}
                                                    Start Live
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    onClick={() => navigate(`/teacher/classroom/${classroom.id}`)}
                                                    className="hover:bg-muted hover:text-foreground"
                                                >
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                                {filteredClassrooms.length === 0 && (
                                    <TableRow>
                                        <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                                            No classrooms found
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </ScrollArea>
                </Card>
            </div>
        </div>
    );
};

export default TeacherClassroomPage;
