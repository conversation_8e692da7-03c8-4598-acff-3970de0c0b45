import React from "react";
import SunEditor from "suneditor-react";
import "suneditor/dist/css/suneditor.min.css";
import 'suneditor/src/assets/css/suneditor.css'
import "./ContentMedia.css";
import aIcon from "./assets/icon.svg";
import backIcon from "./assets/back.svg";
import { useNavigate } from "react-router-dom";
import Overview from "./assets/Overview.svg";
import Notes from "./assets/Notes.svg";
import QA from "./assets/QA.svg";
import Transcript from "./assets/Transcript.svg";
import SavedNotes from "./assets/SavedNotes.svg";
import { useDispatch } from "react-redux";

const contentMediaOption = [
  {
    name: "Overview",
    imgSrc: Overview,
  },
  {
    name: "Notes",
    imgSrc: Notes,
  },
  {
    name: "Q&A",
    imgSrc: QA,
  },

  {
    name: "Transcript",
    imgSrc: Transcript,
  },
  {
    name: "Saved notes",
    imgSrc: SavedNotes,
  },
];

const ContentMedia = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const handleContinue = () => {
    //dispatch(setStudentCoursesDetail({}))
    navigate("/contentView");
  };
  return (
    <div className="content-media">
      <div className="content-media-container">
        {/* <div className="content-media-back-button">
          <img src={backIcon} alt="Back" className="back-button-icon" />
          <span className="back-button-text">Back</span>
        </div> */}
        {/* <h2 className="content-media-heading">Learn speaking Hello!!</h2> */}
        <div className="content-media-wrapper">
          <div className="content-media-video-container">
            <iframe
              style={{height:350, width:'640px' }}
              src="https://www.youtube.com/embed/mq2DSNCzM_E"
              title="YouTube video"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            ></iframe>
            <div className="content-media-video-caption">Hello!!</div>
            {/* <button className="content-media-add-to-space-button">
              <img src={aIcon} alt="icon" className="content-media-icon" />
              <span className="content-media-button-text">Add to My Space</span>
            </button> */}
          </div>
          {/* <div className="content-media-notes-container">
            <div className="content-media-notes-incontainer">
              <SunEditor
                setOptions={{
                  buttonList: [
                    [
                      "bold",
                      "underline",
                      "italic",
                      "fontColor",
                      "align",
                      "list",
                    ],
                  ],
                  height: "100%",
                  minHeight: "100px",
                  placeholder: "Write your notes",
                }}
              />
            </div>
            <button className="content-media-save-button">Save</button>
          </div> */}
        </div>

        {/* <div
          style={{
            display: "flex",
            justifyContent: "center",
            marginBottom: "20px",
          }}
        >
          {contentMediaOption?.map((item, index) => {
            return (
              <div style={{ marginRight: "20px" }} key={index}>
                <div
                  className="icon"
                  style={{
                    borderRadius: "5px",
                    padding: "5px",
                  }}
                >
                  <img
                    src={item.imgSrc}
                    alt=""
                    style={{ marginLeft: "10px" }}
                  />
                  <div
                    style={{
                      marginLeft: "10px",
                      marginRight: "10px",
                      alignContent: "center",
                    }}
                  >
                    {item.name}
                  </div>
                </div>
              </div>
            );
          })}
        </div> */}
      </div>

      <div className="content-media-footer">
        <button
          className="content-media-continue-button"
          onClick={() => handleContinue()}
        >
          Continue
        </button>
      </div>
    </div>
  );
};

export default ContentMedia;
