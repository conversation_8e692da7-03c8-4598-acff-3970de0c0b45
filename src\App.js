import React, { Suspense, useEffect } from "react";
import { Provider, useDispatch } from "react-redux";
import { store } from "@/store/store";
import RoutesComponent from "RoutesComponent";
import { BrowserRouter as Router, useLocation } from "react-router-dom";
import Layout from "./components/Layout";
import {
  auth0Audience,
  auth0ClientID,
  auth0Connection,
  auth0Domain,
  JWTToken,
} from "./constant/AppConstant";
import {
  setIdToken,
  setUserIsRendered,
  setUserToken,
} from "./components/User/userSlice";
import { ToastContainer } from "react-toastify";
import { Auth0Provider, useAuth0 } from "@auth0/auth0-react";
import "./styles/globals.css";
import { Toaster } from "./components/ui/toaster";

function AppContent() {
  const dispatch = useDispatch();

  const location = useLocation();
  const {
    isAuthenticated,
    getAccessTokenSilently,
    user,
    isLoading,
    loginWithRedirect,
    getIdTokenClaims,
  } = useAuth0();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // If we have the code parameter, we're in the callback
        const params = new URLSearchParams(location.search);
        const hasCode = params.has("code");

        if (hasCode && isAuthenticated && user) {
          console.log("Processing callback with code...");
          const token = await getAccessTokenSilently();
          const idToken = await getIdTokenClaims();
          console.log("Token received:", !!token);

          const tokenData = {
            accessToken: token,
            idToken: idToken.__raw,
            user: user,
          };

          localStorage.setItem(JWTToken, JSON.stringify(tokenData));
          localStorage.setItem("IdToken", idToken.__raw);
          dispatch(setUserToken(tokenData.accessToken));
        } else if (!hasCode) {
          // Normal initialization
          const storedToken = localStorage.getItem(JWTToken);
          if (storedToken) {
            const parsedToken = JSON.parse(storedToken);
            dispatch(setIdToken(parsedToken.idToken));
            dispatch(setUserToken(parsedToken.accessToken));
            dispatch(setUserIsRendered(true));
            // check if token is expired
            const token = await getAccessTokenSilently();
            if (!token) {
              loginWithRedirect();
            }
          } else {
            loginWithRedirect();
          }
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
        loginWithRedirect();
      } finally {
        dispatch(setUserIsRendered(true));
      }
    };

    if (!isLoading) {
      initializeAuth();
    }
  }, [
    isAuthenticated,
    user,
    getAccessTokenSilently,
    dispatch,
    isLoading,
    location,
    getIdTokenClaims,
  ]);

  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      }
    >
      <Toaster />
      {user ? <Layout /> : <></>}
      <RoutesComponent />
    </Suspense>
  );
}

function App() {
  return (
    <Provider store={store}>
      <Router>
        <Auth0Provider
          domain={auth0Domain}
          clientId={auth0ClientID}
          authorizationParams={{
            redirect_uri: window.location.origin,
            audience: auth0Audience,
            scope: "openid profile email",
            connection: auth0Connection,
          }}
          onRedirectCallback={(appState, user) => {
            console.log("appState", appState);
            console.log("user", user);
            if (user["http://learnido-app/roleId"] == "1") {
              console.log("User is Admin");
            }
          }}
          useRefreshTokens={true}
          cacheLocation="localstorage"
        >
          <AppContent />
        </Auth0Provider>
      </Router>
    </Provider>
  );
}

export default App;
