import React, { useState } from "react";
import { useAuth0 } from "@auth0/auth0-react";
import { Badge } from "../../ui/badge";
import { Input } from "../../ui/input";
import { Textarea } from "../../ui/textarea";
import { Card } from "../../ui/card";
import { motion } from "framer-motion";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "../../ui/table";
import { ScrollArea } from "../../ui/scroll-area";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "../../ui/select";
import {
    useGetQueriesQuery,
    useUpdateReplyMutation,
    useGetSchoolClassroomsQuery
} from "../../../APIConnect";
import {
    MessageCircle,
    Search,
    Filter,
    Loader2,
    Eye,
    Send,
} from "lucide-react";
import { format } from "date-fns";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>onte<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "../../ui/dialog";
import { But<PERSON> } from "../../ui/button";
import { toast } from "../../../hooks/use-toast";

interface Reply {
    id: string;
    answerText: string;
    createdOn: string;
}

interface Query {
    id: string;
    classroomId: string;
    studentId: string;
    studentName: string;
    queryText: string;
    isAnswered: boolean;
    createdOn: string;
    updatedon: string;
    reply?: Reply;
    student: {
        user: {
            firstName: string;
            lastName: string;
        }
    };
}

interface Classroom {
    id: string;
    name: string;
}

const QueryLoadingSkeleton = () => (
    <div className="space-y-4">
        <div className="flex items-center space-x-4">
            <div className="h-12 w-12 rounded-full bg-muted animate-pulse" />
            <div className="space-y-2">
                <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                <div className="h-3 w-24 bg-muted animate-pulse rounded" />
            </div>
        </div>
        <div className="space-y-2">
            <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
            <div className="h-4 w-1/2 bg-muted animate-pulse rounded" />
        </div>
    </div>
);

const TeacherQueriesPage = () => {
    const [searchQuery, setSearchQuery] = useState("");
    const [filter, setFilter] = useState("all");
    const [selectedClassroom, setSelectedClassroom] = useState<string>("");
    const [replyText, setReplyText] = useState("");
    const [selectedQuery, setSelectedQuery] = useState<Query | null>(null);
    const { user } = useAuth0();
    const schoolId = user?.["http://learnido-app/schoolId"] as string | undefined;

    const { data: classroomsData, isLoading: isLoadingClassrooms } = useGetSchoolClassroomsQuery(schoolId);
    const { data: queriesData, isLoading: isLoadingQueries, refetch: refetchQueries } = useGetQueriesQuery({
        classroomId: selectedClassroom
    });
    const [updateReply, { isLoading: isSubmittingReply }] = useUpdateReplyMutation();

    const handleSubmitReply = async (queryId: string, replyText: string) => {
        if (!replyText.trim()) {
            toast({
                title: "Reply cannot be empty",
                variant: "destructive",
            });
            return;
        }

        try {
            await updateReply({
                queryId,
                answerText: replyText,
            }).unwrap();

            toast({
                title: "Reply submitted successfully",
                variant: "default",
            });

            setReplyText("");
            refetchQueries();
        } catch (error) {
            console.error('Error submitting reply:', error);
            toast({
                title: "Failed to submit reply",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const filteredQueries = queriesData?.resultObject?.filter((query: Query) => {
        const matchesSearch = searchQuery
            ? query.queryText.toLowerCase().includes(searchQuery.toLowerCase()) ||
            query.reply?.answerText?.toLowerCase().includes(searchQuery.toLowerCase())
            : true;
        const matchesFilter =
            filter === "all" ||
            (filter === "answered" && query.isAnswered) ||
            (filter === "unanswered" && !query.isAnswered);
        return matchesSearch && matchesFilter;
    });

    const QueryDetailsDialog = ({ query }: { query: Query }) => {
        const [reply, setReply] = useState(query.reply?.answerText || "");
        return (
            <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-semibold mb-2">Question</h3>
                    <p className="text-gray-700">{query.queryText}</p>
                    <div className="mt-2 text-sm text-gray-500">
                        Asked on {format(new Date(query.createdOn), "MMM d, yyyy")}
                    </div>
                </div>

                {query.isAnswered && query.reply ? (
                    <div className="bg-green-50 p-4 rounded-lg">
                        <h3 className="font-semibold mb-2">Answer</h3>
                        <p className="text-gray-700">{query.reply.answerText}</p>
                        <div className="mt-2 text-sm text-gray-500">
                            Answered on {format(new Date(query.updatedon), "MMM d, yyyy")}
                        </div>
                    </div>
                ) : (
                    <div className="space-y-4">
                        <h3 className="font-semibold">Reply to Query</h3>
                        <Textarea
                            placeholder="Type your reply here..."
                            value={reply}
                            onChange={(e) => setReply(e.target.value)}
                            className="min-h-[100px]"
                            autoFocus={false}
                        />
                        <div className="flex justify-end">
                            <Button
                                onClick={() => {
                                    // setReplyText(reply);
                                    handleSubmitReply(query.id, reply)
                                }}
                                disabled={isSubmittingReply}
                            >
                                {isSubmittingReply && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                                <Send className="w-4 h-4 mr-2" />
                                Submit Reply
                            </Button>
                        </div>
                    </div>
                )}
            </div>
        )
    };

    if (isLoadingClassrooms) {
        return (
            <div className="flex items-center justify-center h-[calc(100vh-200px)]">
                <Loader2 className="w-8 h-8 animate-spin text-primary" />
            </div>
        );
    }

    return (
        <div className="container mx-auto p-6">
            <div className="flex flex-col space-y-8">
                <Card className="p-6">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
                        <div>
                            <h1 className="text-3xl font-semibold tracking-tight flex items-center gap-2">
                                <MessageCircle className="h-7 w-7" />
                                Student Queries
                            </h1>
                            <p className="text-sm text-muted-foreground mt-1">View and respond to student questions</p>
                        </div>
                        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full md:w-auto">
                            <Select value={selectedClassroom} onValueChange={setSelectedClassroom}>
                                <SelectTrigger className="w-[200px]">
                                    <SelectValue placeholder="Select classroom" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="ALL">All Classrooms</SelectItem>
                                    {classroomsData?.resultObject?.map((classroom: Classroom) => (
                                        <SelectItem key={classroom.id} value={classroom.id}>
                                            {classroom.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <div className="relative flex-1 sm:flex-none">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search queries..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="pl-8"
                                />
                            </div>
                            <Select value={filter} onValueChange={setFilter}>
                                <SelectTrigger className="w-[180px]">
                                    <div className="flex items-center">
                                        <Filter className="h-4 w-4 mr-2" />
                                        <SelectValue placeholder="Filter" />
                                    </div>
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Queries</SelectItem>
                                    <SelectItem value="answered">Answered</SelectItem>
                                    <SelectItem value="unanswered">Pending</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </Card>

                <Card className="p-0">
                    <ScrollArea className="h-[calc(100vh-280px)] rounded-md">
                        {isLoadingQueries ? (
                            <div className="flex justify-center items-center h-32">
                                <Loader2 className="w-8 h-8 animate-spin" />
                            </div>
                        ) : (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Student</TableHead>
                                        <TableHead>Query</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Date</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredQueries?.map((query: Query) => (
                                        <TableRow
                                            key={query.id}
                                            className="group hover:bg-muted/50 transition-colors"
                                        >
                                            <TableCell>
                                                <div className="flex items-center space-x-3">
                                                    <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                                                        <MessageCircle className="h-5 w-5 text-muted-foreground" />
                                                    </div>
                                                    <div>
                                                        <div className="font-medium">{query.studentName}</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            Student
                                                        </div>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell className="max-w-md">
                                                <p className="truncate">{query.queryText}</p>
                                            </TableCell>
                                            <TableCell>
                                                <Badge
                                                    variant={query.reply ? "default" : "destructive"}
                                                >
                                                    {query.reply ? "Answered" : "Pending"}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                {format(new Date(query.createdOn), "MMM d, yyyy")}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex justify-end space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                                    <Dialog>
                                                        <DialogTrigger asChild>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => {
                                                                    setSelectedQuery(query);
                                                                    setReplyText(query.reply?.answerText || "");
                                                                }}
                                                                className="hover:bg-muted hover:text-foreground"
                                                            >
                                                                <Eye className="h-4 w-4" />
                                                            </Button>
                                                        </DialogTrigger>
                                                        <DialogContent className="max-w-2xl" autoFocus={false}>
                                                            <DialogHeader>
                                                                <DialogTitle className="flex items-center gap-2">
                                                                    <MessageCircle className="h-5 w-5" />
                                                                    Query Details
                                                                </DialogTitle>
                                                            </DialogHeader>
                                                            <motion.div
                                                                initial={{ opacity: 0, y: 20 }}
                                                                animate={{ opacity: 1, y: 0 }}
                                                                transition={{ duration: 0.3 }}
                                                            >
                                                                <QueryDetailsDialog query={query} />
                                                            </motion.div>
                                                        </DialogContent>
                                                    </Dialog>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                    {(!filteredQueries || filteredQueries.length === 0) && (
                                        <TableRow>
                                            <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                                                No queries found
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        )}
                    </ScrollArea>
                </Card>
            </div>
        </div>
    );
};

export default TeacherQueriesPage;
