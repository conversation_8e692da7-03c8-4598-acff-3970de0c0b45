import { useState, useEffect } from 'react';
import { useGetDiscussionsQuery } from '../services/discussionsAPIjs';

interface Comment {
  id: string;
  content: string;
  authorName: string;
  authorPictureUrl: string;
  createdAt: string;
  sentimentScore?: number;
  accuracyScore?: number;
  evaluation?: {
    engagement: number;
    relevance: number;
    depthOfThought: number;
    evidence: number;
    overallScore: number;
    peerInteraction: number;
  };
}

interface Discussion {
  id: string;
  title: string;
  description: string;
  createdAt: string;
  comments: Comment[];
}

interface UserScore {
  id: string;
  name: string;
  profilePicture?: string;
  commentCount: number;
  averageScore: number;
  lastActive: Date;
  totalEngagement: number;
  scoreBreakdown: {
    engagement: number;
    relevance: number;
    depthOfThought: number;
    evidence: number;
    peerInteraction: number;
  };
}

interface Pagination {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export function useDiscussionForum(page: number = 1, limit: number = 10) {
  const [discussions, setDiscussions] = useState<Discussion[]>([]);
  const [userScores, setUserScores] = useState<UserScore[]>([]);
  
  // Fetch discussions using RTK Query
  const { 
    data: discussionsData, 
    isLoading,
    error
  } = useGetDiscussionsQuery({ page, limit });

  // Process discussions data when it arrives
  useEffect(() => {
    if (discussionsData?.items) {
      // Map the discussions and process them
      const processedDiscussions = discussionsData.items.map((discussion: any) => {
        // For now, we're using mock comments since our API doesn't return them directly
        // In a real app, you'd make another API call to get comments for each discussion
        
        // Return the discussion with processed data
        return {
          ...discussion,
          comments: [] // This would be populated from a separate API call in a real app
        };
      });
      
      setDiscussions(processedDiscussions);
      
      // Extract unique users and their scores
      // This is a simplified simulation - in a real app, 
      // you'd likely have a separate API endpoint for this
      const users: Record<string, UserScore> = {};
      
      // Mock user scores based on the existing data
      // In a real implementation, this would come from an API
      setUserScores([
        {
          id: 'user-1',
          name: 'Jamie Rodriguez',
          profilePicture: '',
          commentCount: 12,
          averageScore: 8.2,
          lastActive: new Date(Date.now() - 3600000), // 1 hour ago
          totalEngagement: 85,
          scoreBreakdown: {
            engagement: 8.5,
            relevance: 8.2,
            depthOfThought: 7.8,
            evidence: 7.3,
            peerInteraction: 9.0
          }
        },
        {
          id: 'user-2',
          name: 'Alex Chen',
          profilePicture: '',
          commentCount: 8,
          averageScore: 7.8,
          lastActive: new Date(Date.now() - 86400000), // 1 day ago
          totalEngagement: 72,
          scoreBreakdown: {
            engagement: 7.2,
            relevance: 8.0,
            depthOfThought: 8.5,
            evidence: 7.5,
            peerInteraction: 7.8
          }
        },
        {
          id: 'user-3',
          name: 'Morgan Smith',
          profilePicture: '',
          commentCount: 15,
          averageScore: 8.3,
          lastActive: new Date(Date.now() - 7200000), // 2 hours ago
          totalEngagement: 90,
          scoreBreakdown: {
            engagement: 8.7,
            relevance: 8.5,
            depthOfThought: 7.9,
            evidence: 8.0,
            peerInteraction: 8.4
          }
        }
      ]);
    }
  }, [discussionsData]);

  // For demo purposes, let's add some sample discussion data if none exists
  useEffect(() => {
    if (!isLoading && (!discussionsData?.items || discussionsData.items.length === 0)) {
      const mockDiscussions: Discussion[] = [
        {
          id: 'disc-1',
          title: 'Artificial Intelligence Ethics',
          description: 'Discussion about the ethical implications of AI in modern society',
          createdAt: new Date().toISOString(),
          comments: [
            {
              id: 'comment-1',
              content: 'I believe AI ethics should be a mandatory course for all computer science degrees. The implications of AI on society are too important to ignore.',
              authorName: 'Jamie Rodriguez',
              authorPictureUrl: '',
              createdAt: new Date().toISOString(),
              sentimentScore: 0.75,
              accuracyScore: 0.82,
              evaluation: {
                engagement: 8,
                relevance: 9,
                depthOfThought: 7,
                evidence: 6,
                overallScore: 7.5,
                peerInteraction: 8
              }
            },
            {
              id: 'comment-2',
              content: 'While I agree with the sentiment, I think we need to be careful not to stifle innovation with too many regulations. There needs to be a balance.',
              authorName: 'Alex Chen',
              authorPictureUrl: '',
              createdAt: new Date().toISOString(),
              sentimentScore: 0.65,
              accuracyScore: 0.78,
              evaluation: {
                engagement: 7,
                relevance: 8,
                depthOfThought: 9,
                evidence: 7,
                overallScore: 7.8,
                peerInteraction: 6
              }
            }
          ]
        },
        {
          id: 'disc-2',
          title: 'Climate Change Solutions',
          description: 'Discussing practical solutions to address climate change',
          createdAt: new Date().toISOString(),
          comments: [
            {
              id: 'comment-3',
              content: 'Carbon capture technology shows a lot of promise, but we need more government funding to scale it effectively.',
              authorName: 'Morgan Smith',
              authorPictureUrl: '',
              createdAt: new Date().toISOString(),
              sentimentScore: 0.68,
              accuracyScore: 0.85,
              evaluation: {
                engagement: 8,
                relevance: 9,
                depthOfThought: 8,
                evidence: 8,
                overallScore: 8.3,
                peerInteraction: 7
              }
            }
          ]
        }
      ];
      
      setDiscussions(mockDiscussions);
    }
  }, [isLoading, discussionsData]);

  return {
    discussions,
    userScores,
    pagination: discussionsData?.pagination,
    isLoading,
    error
  };
}