import React from 'react';

interface RadialProgressChartProps {
  percentage: number;
  label: string;
  color: string; // e.g., 'text-green-500' or 'stroke-green-500'
}

const RadialProgressChart: React.FC<RadialProgressChartProps> = ({ percentage, label, color }) => {
  const radius = 14.9155; // <PERSON>sen to make circumference 100
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className="p-2 rounded-lg flex flex-col items-center text-center">
      <div className="w-20 h-20 relative mb-1">
        <svg viewBox="0 0 36 36" className="w-full h-full transform -rotate-90">
          {/* Background Circle */}
          <circle
            cx="18"
            cy="18"
            r={radius}
            fill="none"
            stroke="#e5e7eb" // gray-200
            strokeWidth="6"
          />
          {/* Progress Circle */}
          <circle
            cx="18"
            cy="18"
            r={radius}
            fill="none"
            stroke="currentColor" // Use Tailwind color class on parent
            strokeWidth="6"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className={color} // Apply color class here
          />
        </svg>
        {/* Percentage Text */}
        <div className={`absolute inset-0 flex items-center justify-center text-sm font-medium ${color}`}>
          {percentage}%
        </div>
      </div>
      {/* Label */}
      <span className="text-xs font-medium text-gray-700">{label}</span>
    </div>
  );
};

export default RadialProgressChart;
