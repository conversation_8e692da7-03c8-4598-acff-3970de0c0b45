import { useState, useEffect, useCallback } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import axios from 'axios';
import * as AppConstant from '../constant/AppConstant';

// Define the interface for score breakdown
interface ScoreBreakdown {
  engagement: number;
  relevance: number;
  depthOfThought: number;
  evidence: number;
  peerInteraction: number;
}

// Define the interface for a commenter
export interface Commenter {
  id: string;
  name: string;
  profilePicture?: string;
  commentCount: number;
  averageScore: number;
  lastActive: Date;
  scoreBreakdown: ScoreBreakdown;
  badges?: string[];
}

interface CommenterResponse {
  commenters: Commenter[];
  totals: {
    commenters: number;
    comments: number;
    averageScore: number;
  };
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

interface CommenterParams {
  timeRange?: 'daily' | 'weekly' | 'monthly' | 'alltime';
  courseId?: string;
  classId?: string;
  searchQuery?: string;
  sortBy?: 'name' | 'comments' | 'score';
  sortDir?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * Custom hook for fetching commenter stats with evaluation metrics
 * @param timeRange The time period to fetch stats for (daily, weekly, monthly, alltime)
 * @param params Additional optional parameters for filtering and sorting
 * @returns Object containing commenters data, loading state, and error state
 */
export function useCommenterStats(
  timeRange: 'daily' | 'weekly' | 'monthly' | 'alltime' = 'weekly', 
  params: Omit<CommenterParams, 'timeRange'> = {}
) {
  const { getAccessTokenSilently } = useAuth0();
  const [commenters, setCommenters] = useState<Commenter[]>([]);
  const [totals, setTotals] = useState<{ commenters: number; comments: number; averageScore: number }>({
    commenters: 0,
    comments: 0,
    averageScore: 0
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Extract individual params to use as dependencies instead of the whole object
  const { courseId, classId, searchQuery, sortBy, sortDir, page, limit } = params;

  // Create a stable fetchCommenterStats function with useCallback
  const fetchCommenterStats = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Get auth token
      const token = await getAccessTokenSilently();
      
      // Build query params
      const queryParams = new URLSearchParams({
        timeRange,
        ...(courseId && { courseId }),
        ...(classId && { classId }),
        ...(searchQuery && { searchQuery }),
        ...(sortBy && { sortBy }),
        ...(sortDir && { sortDir }),
        ...(page && { page: page.toString() }),
        ...(limit && { limit: limit.toString() })
      });
      
      // Fetch data from the API
      const response = await axios.get(
        `${AppConstant.discussionsBaseUrl}/commenters?${queryParams.toString()}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      
      const data = response.data as CommenterResponse;
      
      // Process the received data
      const processedCommenters = data.commenters.map(commenter => ({
        ...commenter,
        // Convert ISO string to Date object for lastActive
        lastActive: new Date(commenter.lastActive)
      }));
      
      setCommenters(processedCommenters);
      setTotals(data.totals);
      setIsLoading(false);
    } catch (err) {
      console.error('Error fetching commenter stats:', err);
      setError('Failed to load commenter statistics');
      setIsLoading(false);
      
      // Fallback to mock data in development environment
      if (process.env.NODE_ENV === 'development') {
        console.log('Using mock data as fallback in development environment');
        const mockData = generateMockCommenterData();
        setCommenters(mockData);
      }
    }
  }, [
    timeRange, 
    courseId, 
    classId, 
    searchQuery, 
    sortBy, 
    sortDir, 
    page, 
    limit, 
    getAccessTokenSilently
  ]);

  // Use the stable callback in useEffect
  useEffect(() => {
    fetchCommenterStats();
  }, [fetchCommenterStats]);

  // Add a manual refetch function that consumers can call
  return { 
    commenters, 
    totals, 
    isLoading, 
    error,
    refetch: fetchCommenterStats
  };
}

/**
 * Generate mock commenter data for development purposes
 * This function is only used as fallback when the API is unavailable in development
 */
function generateMockCommenterData(): Commenter[] {
  return [
    {
      id: 'user123',
      name: 'Jamie Rodriguez',
      profilePicture: '',
      commentCount: 24,
      averageScore: 8.7,
      lastActive: new Date(Date.now() - 3600000), // 1 hour ago
      scoreBreakdown: {
        engagement: 9.2,
        relevance: 8.5,
        depthOfThought: 8.3,
        evidence: 8.1,
        peerInteraction: 9.4
      },
      badges: ['top-contributor', 'insightful']
    },
    {
      id: 'student456',
      name: 'Alex Chen',
      profilePicture: '',
      commentCount: 18,
      averageScore: 7.9,
      lastActive: new Date(Date.now() - 86400000), // 1 day ago
      scoreBreakdown: {
        engagement: 7.8,
        relevance: 8.2,
        depthOfThought: 8.5,
        evidence: 7.2,
        peerInteraction: 7.8
      },
      badges: ['rising-star']
    },
    {
      id: 'user789',
      name: 'Morgan Smith',
      profilePicture: '',
      commentCount: 32,
      averageScore: 9.1,
      lastActive: new Date(Date.now() - 7200000), // 2 hours ago
      scoreBreakdown: {
        engagement: 9.3,
        relevance: 9.0,
        depthOfThought: 9.2,
        evidence: 8.8,
        peerInteraction: 9.2
      },
      badges: ['expert', 'helpful']
    },
    {
      id: 'student234',
      name: 'Taylor Johnson',
      profilePicture: '',
      commentCount: 8,
      averageScore: 6.4,
      lastActive: new Date(Date.now() - 432000000), // 5 days ago
      scoreBreakdown: {
        engagement: 6.8,
        relevance: 6.2,
        depthOfThought: 5.9,
        evidence: 6.5,
        peerInteraction: 6.6
      }
    },
    {
      id: 'user567',
      name: 'Jordan Lee',
      profilePicture: '',
      commentCount: 12,
      averageScore: 7.2,
      lastActive: new Date(Date.now() - 172800000), // 2 days ago
      scoreBreakdown: {
        engagement: 7.5,
        relevance: 7.0,
        depthOfThought: 7.2,
        evidence: 6.8,
        peerInteraction: 7.5
      }
    },
    {
      id: 'student890',
      name: 'Casey Martinez',
      profilePicture: '',
      commentCount: 15,
      averageScore: 8.3,
      lastActive: new Date(Date.now() - 14400000), // 4 hours ago
      scoreBreakdown: {
        engagement: 8.5,
        relevance: 8.2,
        depthOfThought: 8.4,
        evidence: 8.0,
        peerInteraction: 8.4
      },
      badges: ['insightful']
    },
    {
      id: 'user012',
      name: 'Riley Cooper',
      profilePicture: '',
      commentCount: 5,
      averageScore: 5.8,
      lastActive: new Date(Date.now() - 604800000), // 7 days ago
      scoreBreakdown: {
        engagement: 5.9,
        relevance: 5.7,
        depthOfThought: 5.5,
        evidence: 5.6,
        peerInteraction: 6.3
      }
    }
  ];
}