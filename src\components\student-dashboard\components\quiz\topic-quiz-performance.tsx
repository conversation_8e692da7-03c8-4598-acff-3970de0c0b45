import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Target, Frown } from "lucide-react";
import { useGetTopicQuizPerformanceQuery } from "@/APIConnect";
import { useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Tooltip, TooltipProvider } from "@/components/ui/tooltip";
// import {  } from "@radix-ui/react-tooltip";

const getMotivationalMessage = (percentage: number) => {
    if (percentage >= 90) return { message: "Outstanding! You're mastering this topic!", icon: <PartyPopper className="w-5 h-5" /> };
    if (percentage >= 80) return { message: "Excellent work! Keep up the great effort!", icon: <Target className="w-5 h-5" /> };
    if (percentage >= 70) return { message: "Good job! You're making solid progress!", icon: <Star className="w-5 h-5" /> };
    if (percentage >= 60) return { message: "Nice work! Keep pushing forward!", icon: <ArrowUp className="w-5 h-5" /> };
    return { message: "Keep practicing! You've got this!", icon: <Trophy className="w-5 h-5" /> };
};

const getPerformanceColor = (percentage: number) => {
    if (percentage >= 90) return { color: "bg-green-500", textColor: "text-green-500" };
    if (percentage >= 70) return { color: "bg-blue-500", textColor: "text-blue-500" };
    if (percentage >= 50) return { color: "bg-yellow-500", textColor: "text-yellow-500" };
    return { color: "bg-red-500", textColor: "text-red-500" };
};

export function TopicQuizPerformance({
    topicId,
    topicName,
}: {
    topicId: string;
    topicName: string;
}) {
    const {
        isLoading,
        data: stats,
        error,
        refetch
    } = useGetTopicQuizPerformanceQuery(topicId);

    useEffect(() => {
        if (topicId) {
            void refetch();
        }
    }, [topicId, refetch]);

    useEffect(() => {
        if (error) {
            console.log(error);
        }
    }, [error]);

    if (error) {
        return (
            <Card className="w-full">
                <CardContent className="p-6">
                    <div className="flex flex-col items-center justify-center h-40 space-y-4">
                        <Frown className="w-12 h-12 text-muted-foreground" />
                        <p className="text-muted-foreground">Failed to load quiz performance</p>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => refetch()}
                            className="flex items-center gap-2"
                        >
                            <RefreshCcw className="w-4 h-4" />
                            Try Again
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (isLoading || !stats) {
        return (
            <Card className="w-full">
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <Skeleton className="h-6 w-[150px]" />
                        <Skeleton className="h-6 w-[100px]" />
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Skeleton className="h-[100px]" />
                        <Skeleton className="h-[100px]" />
                    </div>
                    <div className="space-y-2">
                        <div className="flex justify-between">
                            <Skeleton className="h-4 w-[100px]" />
                            <Skeleton className="h-4 w-[50px]" />
                        </div>
                        <Skeleton className="h-2 w-full" />
                    </div>
                    <Skeleton className="h-[60px]" />
                </CardContent>
            </Card>
        );
    }

    const totalMarks = stats.score; // Assuming score is out of 100, adjust if different
    const earnedPercentage = stats.percentage;

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
        >
            <Card className="w-full">
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>{topicName}</span>
                        <Badge variant="secondary" className="text-sm">
                            Quiz Performance
                        </Badge>
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                    <motion.div
                        className="grid grid-cols-1 md:grid-cols-2 gap-6 relative"

                    >
                        {earnedPercentage > 80 && (
                            <motion.div
                                initial={{ opacity: 1, scale: 1 }}
                                animate={{ opacity: 0, scale: 1.1 }}
                                transition={{ duration: 2 }}
                                className="absolute -top-16 right-0 w-screen z-[999]"
                            >
                                <img
                                    src="https://lernido-bucket.s3.amazonaws.com/animations/flying_lr.gif"
                                    alt="High performance"
                                    className="object-contain"
                                />
                            </motion.div>
                        )}
                        <Card className="p-4 hover:shadow-lg transition-all duration-300 hover:scale-105">
                            <div className="flex items-center space-x-4">
                                <div className="p-2 rounded-full bg-primary/10">
                                    <Trophy className="w-6 h-6 text-primary" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Total Score</p>
                                    <h3 className="text-2xl font-bold">{totalMarks}</h3>
                                </div>
                            </div>
                        </Card>

                        <Card className="p-4 hover:shadow-lg transition-all duration-300 hover:scale-105">
                            <div className="flex items-center space-x-4">
                                <div className="p-2 rounded-full bg-primary/10">
                                    <Star className="w-6 h-6 text-primary" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Percentage</p>
                                    <h3 className="text-2xl font-bold">{earnedPercentage}%</h3>
                                </div>
                            </div>
                        </Card>
                    </motion.div>

                    <div className="space-y-2 relative">
                        <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span className="font-medium">{earnedPercentage}%</span>
                        </div>
                        <div className="relative">
                            <TooltipProvider>
                                <Tooltip>
                                    <Progress
                                        value={earnedPercentage}
                                        className={`h-3 ${getPerformanceColor(earnedPercentage).color} transition-all duration-500`}
                                    />
                                </Tooltip>
                            </TooltipProvider>

                        </div>
                    </div>

                    <motion.div
                        className={`bg-primary/5 p-6 rounded-lg ${getPerformanceColor(earnedPercentage).textColor}`}
                        initial={{ scale: 0.95, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ delay: 0.4 }}
                    >
                        <div className="flex items-center justify-center gap-3">
                            {getMotivationalMessage(earnedPercentage).icon}
                            <p className="text-center text-sm font-medium">
                                {getMotivationalMessage(earnedPercentage).message}
                            </p>
                        </div>
                    </motion.div>

                    <motion.div
                        className="flex items-center justify-center gap-2 text-sm text-muted-foreground"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 5 }}
                    >
                        <ArrowUp className="w-4 h-4" />
                        <span>Keep learning to improve your score!</span>
                    </motion.div>

                    {earnedPercentage > 0 && (
                        <motion.div
                            initial={{ scale: 0, opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0, scale: 1 }}
                            transition={{ delay: 3, bounce: 0.5 }}
                        // className="absolute -right-8 -top-4"
                        >
                            <img
                                src="https://lernido-bucket.s3.amazonaws.com/animations/solar_power_boost.gif"
                                alt="Progress boost"
                                className="object-contain repeat-1"

                            />
                        </motion.div>
                    )}
                </CardContent>
            </Card>
        </motion.div>
    );
}
