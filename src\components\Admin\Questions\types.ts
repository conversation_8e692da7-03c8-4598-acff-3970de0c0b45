export interface ResourceMetadata {
  resourceSource: string;
  resourcePath: string;
  resourceName: string;
  resourceTitle: string;
  resourceDescription: string;
}

export interface Answer {
  answerText: string;
  matchText?: string;
  answerImageMetadata: ResourceMetadata | null;
  answerVideoMetadata: ResourceMetadata | null;
  answerAudioMetadata: ResourceMetadata | null;
}

export type QuestionType =
  | "SINGLECHOICE"
  | "MULTICHOICE"
  | "TEXTINPUT"
  | "BINARY"
  | "MATCH"
  | "SOUNDBASED";

export type CorrectAnswerType = {
  SINGLECHOICE: number;
  MULTICHOICE: string[];
  TEXTINPUT: string;
  BINARY: boolean;
  MATCH: Array<[number, number]>;
  SOUNDBASED: string;
};

export interface Question {
  id: string;
  anteriorId: string;
  questionText: string;
  mark: number;
  answers: Answer[];
  type: QuestionType;
  correctAnswer: CorrectAnswerType[QuestionType] | null;
  explanation: string;
  level: number;
  active: boolean;
  topicId: string;
  createdBy: string;
  questionImageMetadata: ResourceMetadata | null;
  questionVideoMetadata: ResourceMetadata | null;
  questionAudioMetadata: ResourceMetadata | null;
  explanationImageMetadata: ResourceMetadata | null;
  explanationVideoMetadata: ResourceMetadata | null;
  explanationAudioMetadata: ResourceMetadata | null;
}

export interface Course {
  id: string;
  name: string;
  chapters: Chapter[];
}

export interface Chapter {
  id: string;
  name: string;
  topics: Topic[];
}

export interface Topic {
  id: string;
  name: string;
}
