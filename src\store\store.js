import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from '@reduxjs/toolkit/query';
import testSlice from "@/reducers/testSlice";
import topNavSlice from "components/TopNav/topNavSlice";
import coursesSlice from "components/Student/Courses/courseSlice";
import appDetailsSlice from "components/Admin/Forms/appDetailsSlice";
import contentViewSlice from "components/Student/ContentView/contentViewSlice";
import questionSlice from "components/Admin/QuestionCreation/questionSlice";
import quizSlice from "components/Student/Quiz/quizSlice";
import userSlice from "components/User/userSlice";
import APIConnect from "APIConnect";
import discussionsAPI from '@/services/discussionsAPIjs';
import { liveKitApi } from "@/APIConnect";

export const store = configureStore({
  reducer: {
    test: testSlice.reducer,
    [APIConnect.reducerPath]: APIConnect.reducer,
    [discussionsAPI.reducerPath]: discussionsAPI.reducer,
    topNav: topNavSlice.reducer,
    courses: coursesSlice.reducer,
    appDetails: appDetailsSlice.reducer,
    contentView: contentViewSlice.reducer,
    questions: questionSlice.reducer,
    quiz: quizSlice.reducer,
    user: userSlice.reducer,

    [liveKitApi.reducerPath]: liveKitApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
      .concat(APIConnect.middleware)
      .concat(discussionsAPI.middleware)
      .concat(liveKitApi.middleware),
});

setupListeners(store.dispatch);

export default store;
