import { Menu, PanelLeftClose, PanelLeftOpen } from "lucide-react"
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import NotesDialog from "../notes-dialog"
import BookmarksDialog from "../bookmarks-dialog"
import MySpaceDialog from "../my-space-dialog"

interface TopBarProps {
    isLeftPaneVisible: boolean
    onToggle: () => void
    onMenuClick: () => void
    isMobile: boolean
    handleCourseClickById: (courseId: string) => Promise<void>
    handleTopicSelectById: (topicId: string) => void
    handleSubTopicSelectById?: (subTopicId: string) => void
}

export function TopBar({
    isLeftPaneVisible,
    onToggle,
    onMenuClick,
    isMobile,
    handleCourseClickById,
    handleTopicSelectById,
}: TopBarProps) {
    return (
        <div className="sticky top-0 z-50 flex items-center justify-between p-4 bg-card/80 backdrop-blur-sm border-b">
            <div className="flex items-center gap-2">
                {!isMobile ? (
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={onToggle}
                        className="md:flex hover:bg-accent/50 transition-colors"
                    >
                        {isLeftPaneVisible ? (
                            <PanelLeftClose className="h-5 w-5" />
                        ) : (
                            <PanelLeftOpen className="h-5 w-5" />
                        )}
                    </Button>
                ) : (
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={onMenuClick}
                        className="flex items-center justify-center w-10 h-10 rounded-lg hover:bg-accent/50 transition-colors"
                    >
                        <Menu className="h-6 w-6" />
                    </Button>
                )}
            </div>
            <div className={cn(
                "flex items-center gap-3",
                isMobile ? "md:hidden" : "hidden md:flex"
            )}>
                <NotesDialog iconOnly={isMobile} />
                <BookmarksDialog
                    iconOnly={isMobile}
                    onCourseSelect={(courseId) => handleCourseClickById(courseId)}
                    onTopicSelect={(topicId) => handleTopicSelectById(topicId)}
                />
                <MySpaceDialog iconOnly={isMobile} />
            </div>
        </div>
    )
}
