import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Discussion } from './types'
import { DiscussionList } from './discussion-list'
import { useGetDiscussionsQuery } from '@/services/discussionsAPIjs'

interface DiscussionTabsProps {
  discussions: Discussion[]
  isLoading: boolean
  onDiscussionClick: (discussion: Discussion) => void
  filter: string
  searchQuery: string
}

export function DiscussionTabs({
  discussions,
  isLoading,
  onDiscussionClick,
  filter,
  searchQuery,
}: DiscussionTabsProps) {
  const { 
    data: trendingData, 
    isLoading: isTrendingLoading,
    error: trendingError,
    refetch: refetchTrending
  } = useGetDiscussionsQuery({
    page: 1,
    limit: 20,
    sort: 'trending',
    filter,
    searchQuery,
  })

  const { 
    data: followingData, 
    isLoading: isFollowingLoading,
    error: followingError,
    refetch: refetchFollowing
  } = useGetDiscussions<PERSON><PERSON>y({
    page: 1,
    limit: 20,
    following: true,
    filter,
    searchQuery,
  })

  return (
    <Tabs defaultValue="recent" className="space-y-6">
      <TabsList>
        <TabsTrigger value="recent">Recent</TabsTrigger>
        <TabsTrigger value="trending">Trending</TabsTrigger>
        <TabsTrigger value="following">Following</TabsTrigger>
      </TabsList>

      <TabsContent value="recent" className="space-y-4">
        <DiscussionList
          discussions={discussions}
          onDiscussionClick={onDiscussionClick}
          isLoading={isLoading}
        />
      </TabsContent>

      <TabsContent value="trending">
        <DiscussionList
          discussions={trendingData?.items || []}
          onDiscussionClick={onDiscussionClick}
          isLoading={isTrendingLoading}
          error={trendingError}
          onRetry={refetchTrending}
        />
      </TabsContent>

      <TabsContent value="following">
        <DiscussionList
          discussions={followingData?.items || []}
          onDiscussionClick={onDiscussionClick}
          isLoading={isFollowingLoading}
          error={followingError}
          onRetry={refetchFollowing}
        />
      </TabsContent>
    </Tabs>
  )
}