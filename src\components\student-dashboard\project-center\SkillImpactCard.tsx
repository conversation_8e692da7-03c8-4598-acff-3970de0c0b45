import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { SkillImpact } from '@/types/projectCenter';

interface SkillImpactCardProps extends SkillImpact {}

const SkillImpactCard: React.FC<SkillImpactCardProps> = ({
  title,
  value,
  iconSrc,
  iconBgColor,
  skills
}) => {
  return (
    <Card className="rounded-2xl shadow-[0px_0px_4px_0px_rgba(0,0,0,0.25)] border-lime-500 border-[0.5px]">
      <CardContent className="p-[18px]">
        <div className="flex justify-between items-start mb-4">
          <div>
            <p className="text-zinc-600 text-sm font-normal">{title}</p>
            <p className="text-zinc-800 text-4xl font-semibold">{value}</p>
          </div>
          <div className={`${iconBgColor} rounded-full flex items-center justify-center w-16 h-16 overflow-hidden`}>
            <img src={iconSrc} alt={title} className="w-10 h-10 object-contain" />
          </div>
        </div>
        <div className="space-y-2">
          {skills.map((skill, index) => (
            <button 
              key={index} 
              className="w-full h-8 bg-white rounded-[10px] border border-slate-600 flex justify-between items-center px-2 hover:bg-slate-50 active:bg-slate-100 transition-colors cursor-pointer"
              onClick={() => console.log(`Clicked on ${skill.name}`)}
            >
              <span className="text-slate-600 text-[12px] font-medium capitalize ">
                {skill.name}
              </span>
              
                <svg width="21" height="13" viewBox="0 0 21 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M14.5 0.5L16.79 2.79L11.91 7.67L7.91 3.67L0.5 11.09L1.91 12.5L7.91 6.5L11.91 10.5L18.21 4.21L20.5 6.5V0.5H14.5Z" fill="#6DB600"/>
                </svg>
                
             
            </button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default SkillImpactCard; 