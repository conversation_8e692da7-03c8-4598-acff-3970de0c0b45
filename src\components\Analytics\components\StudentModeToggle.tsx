import { motion } from 'framer-motion';
import { Gamepad2, Monitor } from 'lucide-react';
import { Switch } from '../../ui/switch';

interface StudentModeToggleProps {
    isStudentMode: boolean;
    onToggle: (value: boolean) => void;
}

export const StudentModeToggle = ({ isStudentMode, onToggle }: StudentModeToggleProps) => {
    return (
        <motion.div
            className="flex items-center gap-3 bg-card p-2 rounded-full shadow-lg"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
        >
            <motion.div
                className="flex items-center gap-2"
                animate={{ opacity: isStudentMode ? 0.5 : 1 }}
            >
                <Monitor className="w-5 h-5" />
            </motion.div>

            <Switch
                checked={isStudentMode}
                onCheckedChange={onToggle}
                className="data-[state=checked]:bg-gradient-to-r from-pink-500 to-violet-500"
            />

            <motion.div
                className="flex items-center gap-2"
                animate={{ opacity: isStudentMode ? 1 : 0.5 }}
            >
                <Gamepad2 className="w-5 h-5" />
            </motion.div>
        </motion.div>
    );
};
