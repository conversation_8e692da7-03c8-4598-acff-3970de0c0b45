import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  setStudentAllCourses,
  setStudentCoursesDetail,
  setCurrentCourseChoosen,
} from "./courseSlice";
import {
  useLazyGetAllCourseDataQuery,
  useLazyGetCourseDetailsDataQuery,
} from "APIConnect";

import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import Chapters from "./Chapters";

import course1 from "./assets/course1.svg";
import course2 from "./assets/course2.svg";
import course3 from "./assets/course3.svg";
import { ArrowRightIcon } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

const Courses = () => {
  const dispatch = useDispatch();
  const [getCourseDetailsData] = useLazyGetCourseDetailsDataQuery();
  const [getAllCoursesData, { isLoading }] = useLazyGetAllCourseDataQuery();
  const allCourses = useSelector((state) => state.courses.allCourses);
  const [selectedCourse, setSelectedCourse] = useState(null);

  const courseImages = [course1, course2, course3];

  useEffect(() => {
    dispatch(setStudentCoursesDetail({}));
    dispatch(setCurrentCourseChoosen({}));
    getAllCourses();
  }, []);

  const getAllCourses = async () => {
    try {
      const id = "153898a5-fc2d-4630-aaee-00662a79c989";
      const { data } = await getAllCoursesData(id);
      if (data) {
        dispatch(setStudentAllCourses(data));
      }
    } catch (err) {
      console.error("Error fetching courses:", err);
    }
  };

  const handleCourseClick = async (course) => {
    setSelectedCourse(course);
    try {
      dispatch(setCurrentCourseChoosen(course));
      const { data } = await getCourseDetailsData({ courseId: course?.id });
      if (data?.resultObject) {
        dispatch(setStudentCoursesDetail(data.resultObject));
      }
    } catch (err) {
      console.error("Error fetching course details:", err);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white p-4 md:p-8">
        <Skeleton className="h-[40px] w-[250px] rounded-xl mb-6" />
        <div className="flex flex-row space-x-4">
          <div>
            <Skeleton className="h-[125px] w-[250px] rounded-xl" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>

          <div>
            <Skeleton className="h-[125px] w-[250px] rounded-xl" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-4 md:p-8">
      <h2 className="text-2xl font-bold text-[#2E645B] mb-6">
        👋 Selamat Datang Alex!
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
        {allCourses?.map((course, index) => (
          <Card
            key={index}
            className="bg-white shadow-lg transition-all duration-300 ease-in-out transform hover:scale-105 rounded-xl overflow-hidden"
          >
            <div className="relative h-40 overflow-hidden">
              <img
                src={courseImages[index % courseImages.length]}
                alt={`Course ${index + 1}`}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-4">
              <h3 className="text-lg font-semibold text-black mb-2">
                {course.name}
              </h3>
              <Progress value={course.courseProgress} className="h-2 mb-2" />
              <div className="flex justify-between items-center text-sm text-black mb-4">
                <span>1/12 Course completed</span>
                <span className="font-bold">{course.courseProgress}%</span>
              </div>
              <div className="flex flex-row items-end justify-end">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant={"expandIcon"}
                      Icon={ArrowRightIcon}
                      iconPlacement="right"
                      className="rounded-lg"
                      onClick={() => handleCourseClick(course)}
                    >
                      ✨ Continue
                      {/* <span className="absolute inset-0 bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span> */}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                      <DialogTitle>{selectedCourse?.name} Chapters</DialogTitle>
                    </DialogHeader>
                    <Chapters />
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default Courses;
