import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

const environmentalMessages = [
    "🌲 Save trees, read online!",
    "🌍 Every bit counts, go green!",
    "💡 Turn off lights when not in use!",
    "🚴 Ride a bike, reduce emissions!",
    "🌱 Plant a seed today!",
    "🍃 Fresh air, our need!",
    "🐠 Keep oceans clean!",
    "🦋 Protect our wildlife!",
    "⚡️ Save energy, save Earth!",
    "♻️ Recycle, reuse, reduce!"
];

const getRandomMessage = () => {
    const randomIndex = Math.floor(Math.random() * environmentalMessages.length);
    return environmentalMessages[randomIndex];
};

const EnvironmentalMessage = React.memo(() => {
    const message = useMemo(() => getRandomMessage(), []);

    return (
        <motion.div
            className="mt-4 p-6 bg-green-50 rounded-md"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            whileHover={{ scale: 1.02 }}
        >
            <motion.p
                className="text-xl md:text-2xl text-green-800 text-center font-bold"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.5 }}
            >
                {message}
            </motion.p>
        </motion.div>
    );
});

EnvironmentalMessage.displayName = 'EnvironmentalMessage';

export default EnvironmentalMessage;
