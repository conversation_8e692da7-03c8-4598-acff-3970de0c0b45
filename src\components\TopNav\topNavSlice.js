import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  topNavSelection: 'Home',
  studentPerformance: {},
};

export const testSlice = createSlice({
  initialState,
  name: "topNav",
  reducers: {
    topNav: () => initialState,
    setTopNavSelection: (state, action) => {
      state.topNavSelection = action.payload;
    },
    setStudentPerformance: (state, action) => {
      state.studentPerformance = action.payload;
    },
  },
});

export const { setTopNavSelection, setStudentPerformance } = testSlice.actions;

export default testSlice;
