import React, { useState, useEffect } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { useGetStudentDashboardQuery, useLazyGetAllCourseDataQuery, useLazyGetBookmarksQuery } from '@/APIConnect'; // Import both query hooks
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Skeleton } from "@/components/ui/skeleton";
import {
    Menu, Search, Plus, Calendar, ChevronLeft, ChevronRight, ChevronDown, BookOpen, X, MessageCircle, // Kept icons
    NotebookText, Star as StarIcon, Quote, Megaphone, Bot, CalendarDays, // Added icons
    Grid, Users, CheckCircle, Hash, MessageSquare, Archive // Re-added icons needed for Sidebar/Main content before refactor, Added Archive
} from 'lucide-react';
import { cn } from '@/lib/utils';
import NotesDialog from './components/notes-dialog';
import { BookmarkDialog } from './components/bookmarks/bookmark-dialog';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import ChatWindow from '@/components/chat/ChatWindow';
import HubItem from './components/HubItem';
import WorkspaceItem from './components/WorkspaceItem';
import CourseCard from './components/CourseCard';
import RightStatsPanel from './components/RightStatsPanel';
import BookmarksDialog from './components/bookmarks-dialog';

// Define interfaces based on the API response
interface Course {
    id: string;
    name: string;
    description: string;
    imageUrl: string; // Added imageUrl
    courseProgress: number;
    classroomId?: string; // Optional if not always present
}

interface BookmarkCollection {
    id: string;
    bookmarkCollectionName: string;
}

// Interfaces for API's announcement data structure
interface NotificationObject {
    senderId: string;
    notifyString: string;
    autoGenerated: boolean;
    id: string; // This is the ID of the notification content itself
    createdOn: string;
    updatedon: string;
}

interface StudentNotification { // This is what 'sn' represents
    studentId: string;
    notificationId: string; // Refers to NotificationObject.id
    isRead: boolean;
    notificationType: string;
    notificationObject: NotificationObject;
    id: string; // This is the unique ID for the student's notification entry
    createdOn: string;
    updatedon: string;
}

// Interface for the frontend's desired announcement structure
interface Announcement {
    id: string;
    notificationType: string;
    isRead: boolean;
    createdOn: string;
    title?: string;
    content?: string;
}

interface Note {
    id: string;
    title: string;
    description: string;
    createdAt: Date;
    color: string;
    courseId?: string;
    topicId?: string;
}

// Add Bookmark type for preview
interface BookmarkPreview {
    id: string;
    courseName: string;
    chapterName?: string;
    topicName?: string;
}

// MyConsole content types
type ConsoleContentType = 'My Space' | 'My Notes' | 'My Bookmarks';

const NewDashboardPage = () => {
    const { user } = useAuth0();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const studentId = user?.sub?.replace("auth0|", "");

    // Bookmarks preview hook (move to top level)
    const [getBookmarks, { data: bookmarksData }] = useLazyGetBookmarksQuery();
    useEffect(() => { getBookmarks({}); }, [getBookmarks]);
    const bookmarksList = bookmarksData?.resultObject || [];
    const bookmarkPreviews = bookmarksList.slice(0, 3).map((b: any) => `${b.courseName || ''}${b.chapterName ? ' / ' + b.chapterName : ''}${b.topicName ? ' / ' + b.topicName : ''}`);

    // Try both API methods to ensure we get courses
    const { data: dashboardData, isLoading: isDashboardLoading, error: dashboardError, refetch } = useGetStudentDashboardQuery(
        { studentId },
        { skip: !studentId }
    );

    // Fallback to the getAllCourseData method used in enhanced-learning-page
    const [getAllCoursesData, { data: allCoursesData, isLoading: isCoursesLoading }] = useLazyGetAllCourseDataQuery();

    const [courses, setCourses] = useState<Course[]>([]);
    const [bookmarks, setBookmarks] = useState<BookmarkCollection[]>([]);
    const [notes, setNotes] = useState<Note[]>([]);
    const [announcements, setAnnouncements] = useState<Announcement[]>([]);
    const [activeAnnouncementTab, setActiveAnnouncementTab] = useState<'Classroom' | 'Student'>('Classroom');
    const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
    const [isBookmarkDialogOpen, setIsBookmarkDialogOpen] = useState(false);
    const [bookmarkCollections, setBookmarkCollections] = useState<BookmarkCollection[]>([]);
        const [isChatOpen, setIsChatOpen] = useState(false); // State to control chat visibility
    const [isMobileRightPanelOpen, setIsMobileRightPanelOpen] = useState(false);
    const [activeConsoleTab, setActiveConsoleTab] = useState<ConsoleContentType>('My Space');

    // Sample courses for debugging - remove in production
    const sampleCourses = [
        {
            id: 'course-1',
            name: 'Introduction to Mathematics',
            description: 'Learn the fundamentals of mathematics including algebra, geometry, and calculus',
            courseProgress: 75
        },
        {
            id: 'course-2',
            name: 'Advanced Physics',
            description: 'Explore the world of physics from classical mechanics to quantum theory',
            courseProgress: 30
        },
        {
            id: 'course-3',
            name: 'Web Development Fundamentals',
            description: 'Master the basics of HTML, CSS, and JavaScript to build dynamic websites',
            courseProgress: 60
        }
    ];

    // Fetch all courses data if user is available
    useEffect(() => {
        if (user) {
            try {
                // Using the hardcoded ID from enhanced-learning-page.tsx to ensure compatibility
                const id = '153898a5-fc2d-4630-aaee-00662a79c989';
                console.log("Fetching courses with ID:", id);

                // First try to get courses directly
                // getAllCoursesData(id)
                //     .then(response => {
                //         console.log("Course API Response:", response);
                //         if (response?.data) {
                //             const fetchedCourses = response.data;
                //             console.log("Successfully fetched courses:", fetchedCourses);

                //             if (fetchedCourses && fetchedCourses.length > 0) {
                //                 // Directly set courses here to ensure they're loaded
                //                 setCourses(fetchedCourses.map((course: any) => ({
                //                     id: course.id,
                //                     name: course.name,
                //                     description: course.description || '',
                //                     courseProgress: course.courseProgress || 0,
                //                     classroomId: course.classroomId // Assuming classroomId is part of course data
                //                 })));
                //             }
                //             // Remove fallback to sample data to ensure we only show real data
                //         } else {
                //             console.warn("Course API response did not contain expected data structure:", response);
                //         }
                //     })
                //     .catch(error => {
                //         console.error("Error fetching courses:", error);
                //     });
            } catch (error) {
                console.error("Exception in course fetching logic:", error);
            }
        }
    }, [user, getAllCoursesData]);

    // Process dashboard data when available
    useEffect(() => {
        console.log("Dashboard data received:", dashboardData);
        if (dashboardData) {
            console.log("Setting courses from dashboard data:", dashboardData.courses);
            if (dashboardData.courses && dashboardData.courses.length > 0) {
                // setCourses(dashboardData.resultObject.courses);
            }
            setBookmarks(dashboardData.bookmarks || []);
            setBookmarkCollections(dashboardData.bookmarks || []);
            setNotes(dashboardData.notes || []);
            if (dashboardData.announcements && dashboardData.announcements.studentNotifications) {
                const transformedAnnouncements: Announcement[] = dashboardData.announcements.studentNotifications.map((sn: StudentNotification) => ({
                    id: sn.id, // Using the 'id' from the StudentNotification object
                    notificationType: sn.notificationType,
                    isRead: sn.isRead,
                    createdOn: sn.createdOn,
                    title: sn.notificationType === 'CLASSROOM' ? 'Classroom Update' : 'Student Notification', // Generating a title
                    content: sn.notificationObject.notifyString, // Mapping notifyString to content
                }));
                setAnnouncements(transformedAnnouncements);
            } else {
                setAnnouncements([]);
            }
            setCourses(dashboardData.courses || []);
        }
    }, [dashboardData]);

    // Placeholder handlers
    const handleCreateBookmarkCollection = async (name: string) => {
        console.log("Create collection:", name);
    };
    const handleBookmark = async (collectionId: string) => {
        console.log("Bookmark in:", collectionId);
    };
    const handleUnbookmark = async () => {
        console.log("Unbookmark");
    };

    // Course navigation handler - moved inside component for proper scope
    const handleCourseNavigation = (courseId: string, classroomId?: string) => {
        console.log("Navigating to course:", courseId);
        navigate(`/courses?courseId=${courseId}${classroomId ? `&classroomId=${classroomId}` : ''}`);
    };

    // Determine if we're loading
    const isLoading = isDashboardLoading || isCoursesLoading;

    // Debug output for troubleshooting
    console.log("Current state:", {
        courses,
        isLoading,
        isDashboardLoading,
        isCoursesLoading,
        allCoursesDataExists: !!allCoursesData,
        dashboardDataExists: !!dashboardData
    });

    // Show loading skeleton while loading
    if (isLoading && !courses.length) {
        return <DashboardSkeleton />;
    }

    // Show error if both API calls fail
    if (dashboardError && !courses.length) {
        return (
            <div className="flex items-center justify-center h-screen text-red-600">
                Error loading dashboard data. Please try again later.
                <Button onClick={() => refetch()} className="ml-4">Retry</Button>
            </div>
        );
    }

    const filteredAnnouncements = announcements.filter(ann => {
        if (activeAnnouncementTab === 'Classroom') {
            return ann.notificationType === 'CLASSROOM';
        } else {
            return ann.notificationType !== 'CLASSROOM';
        }
    });

    console.log("Rendering with courses:", courses);

    const chatContext = JSON.stringify({
        enrolledCourses: courses,
        progress: courses.map(course => ({ id: course.id, progress: course.courseProgress })),
    });

    return (
        <div className="flex h-screen overflow-hidden bg-gray-50  relative mb-4">
            {/* Mobile Sidebar Trigger */}
            <div className="lg:hidden fixed top-11 left-3 z-40">
                <Sheet open={isMobileSidebarOpen} onOpenChange={setIsMobileSidebarOpen}>
                    <SheetTrigger asChild>
                        <Button variant="default">
                            Dashboard
                        </Button>
                    </SheetTrigger>
                    <SheetContent hideCloseButton side="left" className="w-[320px] sm:w-[380px] p-0">
                        <SidebarContent 
                            notes={notes}
                            bookmarks={bookmarkCollections}
                            onClose={() => setIsMobileSidebarOpen(false)}
                            onTriggerBookmarkDialog={() => setIsBookmarkDialogOpen(true)}
                        />
                    </SheetContent>
                </Sheet>
            </div>

            {/* Desktop Sidebar */}
            <aside className="hidden lg:flex w-64 border-r flex-col bg-white overflow-y-auto flex-shrink-0">
                <SidebarContent
                    notes={notes}
                    bookmarks={bookmarkCollections}
                    onTriggerBookmarkDialog={() => setIsBookmarkDialogOpen(true)}
                />
            </aside>

            {/* Main Content */}
            <main className="flex-1 overflow-hidden flex flex-col">
                <ScrollArea className="h-full">
                    <div className="p-2 md:p-4 lg:p-6 pt-8 lg:pt-6">
                        {/* Welcome Header */}
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-1">
                            {/* <h2 className="text-2xl md:text-3xl font-bold text-gray-800">
                                <span className="text-gray-600 font-normal text-lg md:text-xl">Welcome Back</span> <span className="font-bold">{user?.name || 'Prakhar'}!</span>
                            </h2> */}
                        </div>
                        {/* Quote of the day */}
                        <div className="bg-[linear-gradient(90.2deg,rgba(56,108,141,0.2)_0%,rgba(3,102,100,0.2)_12.17%,rgba(14,61,53,0.2)_86.96%,rgba(6,11,11,0.2)_105.19%)] p-3 md:p-4 rounded-xl mb-6 border border-gray-200 mt-6">
                            <div className="flex items-start gap-2 text-gray-700 mb-1">
                                <Quote size={18} className="mt-0.5 flex-shrink-0" />
                                <span className="font-semibold text-base md:text-lg">Quote of the Day</span>
                            </div>
                            <p className="text-gray-600 font-medium pl-7 text-sm md:text-base">"Learn to understand nature, and then protect it."</p>
                        </div>
                        {/* Courses and Announcements Section - Side by Side */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 mb-6">
                            {/* Courses Section */}
                            <div className="flex flex-col">
                                <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-3 gap-1">
                                    <div>
                                        <h3 className="text-xl font-semibold text-gray-800">Current Enrolled Courses</h3>
                                        <p className="text-gray-500 text-sm">Total Courses - {courses?.length || 0}</p>
                                    </div>
                                    <div className="flex items-center gap-2 mt-2 md:mt-0">
                                        <Button variant="outline" size="icon" className="p-1.5 rounded-md h-7 w-7 border-gray-300 text-gray-600 hover:bg-gray-100">
                                            <ChevronLeft size={18} />
                                        </Button>
                                        <Button variant="outline" size="icon" className="p-1.5 rounded-md h-7 w-7 border-gray-300 text-gray-600 hover:bg-gray-100">
                                            <ChevronRight size={18} />
                                        </Button>
                                        <div className="relative ml-1">
                                            <Button variant="outline" className="flex items-center gap-1 rounded-md py-1 px-2.5 h-7 text-sm border-gray-300 text-gray-600 hover:bg-gray-100">
                                                <span>Recent</span>
                                                <ChevronDown size={16} />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                                {/* Course Cards Grid - Adjusted for potentially larger cards */}
                                {courses && courses.length > 0 ? (
                                    <div className="grid grid-cols-1 gap-4"> {/* Always one column for larger cards */}
                                        {courses.map((course) => (
                                            <CourseCard
                                                key={course.id}
                                                id={course.id}
                                                name={course.name}
                                                classroomId={course.classroomId}
                                                imageUrl={course.imageUrl} // Pass the actual imageUrl from the course object
                                                lessonCount={10} // Example, replace with actual data
                                                lessonsCompleted={Math.round(10 * (course.courseProgress / 100))}
                                                courseProgress={course.courseProgress}
                                                timeLeft={`${Math.round(Math.random() * 5 + 1)} hrs left`} // Example, replace
                                                onClick={handleCourseNavigation}
                                            // Add a prop for size if CourseCard supports it, or adjust its internal styling
                                            />
                                        ))}
                                    </div>
                                ) : (
                                    <div className="text-center text-gray-500 py-8 border rounded-lg bg-gray-50 text-sm h-full flex items-center justify-center min-h-[200px]">
                                        {isLoading ? 'Loading courses...' : 'You are not enrolled in any courses yet.'}
                                    </div>
                                )}
                            </div>

                            {/* Announcements Section (moved here) */}
                            <Card className="flex flex-col border border-gray-200 rounded-xl overflow-hidden shadow-sm">
                                <CardHeader className="p-3 border-b border-gray-200 bg-gray-50">
                                    <CardTitle className="flex items-center gap-2 text-gray-700 text-lg font-semibold">
                                        <Megaphone size={20} className="text-primary" />
                                        <span>Announcements</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="p-3 flex-1 flex flex-col">
                                    <div className="flex mb-2.5 bg-gray-100 rounded-md p-0.5">
                                        <button
                                            className={cn(
                                                "flex-1 py-1.5 text-center text-xs font-medium rounded-[5px] transition-colors",
                                                activeAnnouncementTab === 'Classroom' ? 'bg-white text-primary shadow-sm' : 'text-gray-600 hover:text-gray-800'
                                            )}
                                            onClick={() => setActiveAnnouncementTab('Classroom')}
                                        >
                                            Classroom
                                        </button>
                                        <button
                                            className={cn(
                                                "flex-1 py-1.5 text-center text-xs font-medium rounded-[5px] transition-colors",
                                                activeAnnouncementTab === 'Student' ? 'bg-white text-primary shadow-sm' : 'text-gray-600 hover:text-gray-800'
                                            )}
                                            onClick={() => setActiveAnnouncementTab('Student')}
                                        >
                                            Student
                                        </button>
                                    </div>
                                    <ScrollArea className="h-[280px] pr-2 flex-1">
                                        {filteredAnnouncements.length > 0 ? (
                                            <ul className="space-y-2.5">
                                                {filteredAnnouncements.map(ann => (
                                                    <li key={ann.id} className="text-sm p-2.5 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 transition-colors">
                                                        <div className="flex justify-between items-start mb-0.5">
                                                            <p className="font-semibold text-gray-800 pr-2 text-sm">{ann.title || 'Notification'}</p>
                                                            {!ann.isRead && <span className="text-xs text-white bg-blue-500 px-1.5 py-0.5 rounded-full flex-shrink-0 font-medium">New</span>}
                                                        </div>
                                                        <p className="text-gray-600 mt-0.5 text-xs leading-relaxed">{ann.content || 'Details unavailable.'}</p>
                                                        <p className="text-gray-400 mt-1.5 text-xs">{new Date(ann.createdOn).toLocaleDateString()}</p>
                                                    </li>
                                                ))}
                                            </ul>
                                        ) : (
                                            <div className="h-full flex items-center justify-center text-gray-500 text-sm">
                                                No New Notifications
                                            </div>
                                        )}
                                    </ScrollArea>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Bottom Section - Upcoming Live Lecture and My Console */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
                            {/* Upcoming Live Lecture */}
                            <Card className="flex flex-col border border-gray-200 rounded-xl overflow-hidden shadow-sm min-h-[220px]">
                                <CardHeader className="p-3 border-b border-gray-200 bg-gray-50">
                                    <CardTitle className="text-lg font-semibold text-center text-gray-700">Upcoming Live Lecture</CardTitle>
                                </CardHeader>
                                <CardContent className="p-3 flex flex-col gap-2.5 justify-between flex-1">
                                    <div className="w-full h-24 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
                                        <CalendarDays size={36} className="text-gray-400" />
                                    </div>
                                    <div className="flex flex-col">
                                        <h4 className="font-semibold text-base text-gray-800 mb-1">Types Of Noun</h4>
                                        <div className='flex flex-row justify-between items-center'>
                                            <p className="text-xs text-gray-500">1/10 lessons</p>
                                            <span className="text-xs text-primary font-semibold">2 hrs</span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* My Console Section */}
                            <Card className="flex flex-col border border-gray-200 rounded-xl overflow-hidden shadow-sm min-h-[220px]">
                                <CardHeader className="p-3 border-b border-gray-200 bg-gray-50">
                                    <CardTitle className="flex items-center gap-2 text-gray-700 text-lg font-semibold">
                                        <span>My Console</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="p-3 flex-1 flex flex-col">
                                    <div className="flex mb-2.5 bg-gray-100 rounded-md p-0.5">
                                        {(['My Space', 'My Notes', 'My Bookmarks'] as ConsoleContentType[]).map((tabName) => (
                                            <button
                                                key={tabName}
                                                className={cn(
                                                    "flex-1 py-1.5 text-center text-xs font-medium rounded-[5px] transition-colors",
                                                    activeConsoleTab === tabName ? 'bg-white text-primary shadow-sm' : 'text-gray-600 hover:text-gray-800'
                                                )}
                                                onClick={() => setActiveConsoleTab(tabName)}
                                            >
                                                {tabName}
                                            </button>
                                        ))}
                                    </div>
                                    <ScrollArea className="h-[150px] pr-2 flex-1">
                                        {activeConsoleTab === 'My Space' && (
                                            <div className="text-gray-500 text-sm flex items-center justify-center h-full">My Space Content (e.g., files, resources)</div>
                                        )}
                                        {activeConsoleTab === 'My Notes' && (
                                            notes.length > 0 ? (
                                                <ul className="space-y-1.5">
                                                    {notes.slice(0, 4).map((note) => (
                                                        <li key={note.id} className="text-sm text-gray-700 truncate p-2 border-b border-gray-100 hover:bg-gray-50 rounded transition-colors">
                                                            {note.title}
                                                        </li>
                                                    ))}
                                                    {notes.length > 4 && <li className="text-xs text-blue-600 hover:underline cursor-pointer p-1.5 text-center"><NotesDialog triggerText="View All Notes" buttonVariant="link" className="p-0 h-auto text-xs" /></li>}
                                                </ul>
                                            ) : (
                                                <div className="text-gray-500 text-sm flex items-center justify-center h-full">No notes yet. <NotesDialog triggerText="Create one?" buttonVariant="link" className="ml-1 p-0 h-auto text-xs text-blue-600 hover:underline" /></div>
                                            )
                                        )}
                                        {activeConsoleTab === 'My Bookmarks' && (
                                            bookmarkPreviews.length > 0 ? (
                                                <ul className="space-y-1.5">
                                                    {bookmarkPreviews.slice(0, 4).map((preview: string, idx: number) => (
                                                        <li key={idx} className="text-sm text-gray-700 truncate p-2 border-b border-gray-100 hover:bg-gray-50 rounded transition-colors">{preview}</li>
                                                    ))}
                                                    {bookmarksList.length > 4 && <li className="text-xs text-blue-600 hover:underline cursor-pointer p-1.5 text-center"><BookmarksDialog triggerText="View All Bookmarks" buttonVariant="link" className="p-0 h-auto text-xs" /></li>}
                                                </ul>
                                            ) : (
                                                <div className="text-gray-500 text-sm flex items-center justify-center h-full">No bookmarks yet. <BookmarksDialog triggerText="Create one?" buttonVariant="link" className="ml-1 p-0 h-auto text-xs text-blue-600 hover:underline" /></div>
                                            )
                                        )}
                                    </ScrollArea>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </ScrollArea>
            </main>

            {/* New Right Stats Panel - Pass chat toggle function */}
                        {/* Right Panel - Desktop */}
            <div className="hidden lg:flex w-80 border-l flex-col bg-white overflow-y-auto flex-shrink-0">
                <RightStatsPanel onOpenChat={() => setIsChatOpen(true)} />
            </div>

            {/* Mobile Right Panel Trigger */}
            <div className="lg:hidden fixed top-1/2 right-0 transform -translate-y-1/2 z-40">
                <Sheet open={isMobileRightPanelOpen} onOpenChange={setIsMobileRightPanelOpen}>
                    <SheetTrigger asChild>
                        <Button variant="outline" size="icon" className="shadow-md rounded-l-full rounded-r-none w-6 h-12 flex items-center justify-center mt-5">
                            <ChevronLeft size={20} />
                        </Button>
                    </SheetTrigger>
                    <SheetContent side="right" className="w-[320px] sm:w-[380px] p-0 pt-10">
                        <RightStatsPanel onOpenChat={() => setIsChatOpen(true)} />
                    </SheetContent>
                </Sheet>
            </div>

            {/* Floating Chat Window - Keep this, but remove the trigger button */}
            {/* The trigger will be moved inside RightStatsPanel */}
            {/* <Button
                onClick={() => setIsChatOpen(true)}
                className="fixed bottom-6 right-6 z-50 bg-teal-700 hover:bg-teal-800 text-white rounded-full h-14 flex items-center justify-center shadow-lg text-bold"
                // size="icon"
                aria-label="Ask AI Mentor"
            >
               <div className='text-white flex flex-row space-x-2'> <MessageCircle size={24} className='mr-2' /> Ask AI Mentor</div>
            </Button> */}

            <ChatWindow
                context={chatContext}
                isVisible={isChatOpen}
                onClose={() => setIsChatOpen(false)}
            />
        </div>
    );
};

// SidebarContent component
const SidebarContent = ({
    notes: notesData,
    bookmarks,
    onClose,
    onTriggerBookmarkDialog
}: {
    notes: Note[],
    bookmarks: BookmarkCollection[],
    onClose?: () => void,
    onTriggerBookmarkDialog: () => void
}) => {
    // --- Bookmarks Preview Logic ---
    const [isBookmarksDialogOpen, setIsBookmarksDialogOpen] = React.useState(false);
    const [getBookmarks, { data: bookmarksData }] = useLazyGetBookmarksQuery();
    React.useEffect(() => { getBookmarks({}); }, [getBookmarks]);
    const bookmarksList = bookmarksData?.resultObject || [];
    // Show the latest 3 bookmarks as preview (course/topic/chapter names)
    const bookmarkPreviews = bookmarksList.slice(0, 3).map((b: any) => `${b.courseName || ''}${b.chapterName ? ' / ' + b.chapterName : ''}${b.topicName ? ' / ' + b.topicName : ''}`);
    // Notes preview: latest 3 note titles
    const notePreviews = notesData.slice(0, 3).map(n => n.title);
    // My Space preview: placeholder file names (replace with real data if available)
    const mySpacePreviews = [
        'Document1.pdf',
        'Image2.png',
        'Notes.txt',
    ];
    return (
        <ScrollArea className="h-full">
            <div className="p-4 flex flex-col gap-4">
                {/* Close button for mobile */}
                {onClose && (
                    <div className="flex justify-end lg:hidden mb-2">
                        <Button variant="ghost" size="icon" onClick={onClose}>
                            <X size={20} />
                        </Button>
                    </div>
                )}
                {/* Hubs */}
                <div className="">
                    <div className="space-y-2">
                        {[
                            { iconSrc: '/icons/dashboard-left-sidebar/vyudi_learn.png', title: 'Vyudi Learn', bgColor: 'bg-blue-100', href: '/vyudi-learn', description: "", displayStyle: "imageOnly" },
                            //Hardcoding the project ID for now-use the below commented line for dynamic project ID
                            { iconSrc: '/icons/dashboard-left-sidebar/impact_studio.png', title: 'Impact Studio', bgColor: 'bg-pink-100', href: '/project-center/685642275457ae4f39ea30cf' },
                            // { iconSrc: '/icons/dashboard-left-sidebar/project-center.png', title: 'Project Center', description: '5 Courses', bgColor: 'bg-orange-100', href: '/project-center', displayStyle: 'standard' as const },
                            { iconSrc: '/icons/dashboard-left-sidebar/global_gateway.png', title: 'Global Gateway', bgColor: 'bg-green-100', href: '#' },
                            { iconSrc: '/icons/dashboard-left-sidebar/future_path.png', title: 'Future Path', bgColor: 'bg-yellow-100', href: '#' },
                        ].map(hub => (
                            <HubItem
                                key={hub.title}
                                iconSrc={hub.iconSrc}
                                title={hub.title}
                                description={hub.description ?? ""}
                                iconBgColorClass={hub.bgColor}
                                href={hub.href}
                                displayStyle={hub.displayStyle as 'imageOnly' | 'standard' | undefined}
                            />
                        ))}
                    </div>
                </div>
                {/* Workspace */}
                {/* <div className="px-2 mt-6">
                     <div className="flex items-center gap-2 mb-2">
                         <img src="/icons/dashboard-left-sidebar/workspace-icon.png" alt="Workspace" className="w-4 h-4" />
                         <h2 className="text-md text-primary font-semibold text-gray-500 uppercase">Work Space</h2>
                     </div>
                     <div className="space-y-3">
                        <WorkspaceItem
                            title="My Space"
                            icon={<Archive size={18} className="text-green-600" />}
                            itemCount={mySpacePreviews.length}
                            textPreviews={mySpacePreviews}
                        />
                     </div>
                </div> */}
            </div>
        </ScrollArea>
    );
};

// Skeleton Loader Component
const DashboardSkeleton = () => (
    <div className="flex h-screen overflow-hidden bg-gray-50">
        {/* Sidebar Skeleton */}
        <aside className="hidden lg:flex w-48 border-r p-2 flex-col gap-2 bg-white">
            <Skeleton className="h-7 w-20 mb-2" />
            <Skeleton className="h-5 w-12 mb-1" />
            <div className="space-y-1">
                {[...Array(4)].map((_, i) => (
                    <div key={i} className="border rounded-lg p-1 flex items-center gap-1">
                        <Skeleton className="w-6 h-6 rounded-lg flex-shrink-0" />
                        <div className="flex-1">
                            <Skeleton className="h-3 w-3/4 mb-1" />
                            <Skeleton className="h-2 w-1/2" />
                        </div>
                    </div>
                ))}
            </div>
            <Skeleton className="h-5 w-14 mt-2 mb-1" />
            <Skeleton className="h-12 w-full mb-1" />
            <Skeleton className="h-12 w-full mb-1" />
            <Skeleton className="h-12 w-full" />
        </aside>
        {/* Main Content Skeleton */}
        <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8 pt-20 lg:pt-8">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-2">
                <Skeleton className="h-8 w-64" /> {/* Welcome */}
            </div>
            <Skeleton className="h-16 w-full mb-6" /> {/* Quote */}
            <div className="flex justify-between items-center mb-4">
                <div>
                    <Skeleton className="h-6 w-48 mb-1" /> {/* Courses Title */}
                    <Skeleton className="h-4 w-32" /> {/* Courses Count */}
                </div>
                <div className="flex items-center gap-4">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <Skeleton className="h-8 w-24 rounded-full" />
                </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6 mb-6">
                {[...Array(3)].map((_, i) => (
                    <Card key={i} className="overflow-hidden">
                        <Skeleton className="h-40 w-full" />
                        <CardContent className="p-4">
                            <Skeleton className="h-5 w-3/4 mb-2" />
                            <Skeleton className="h-3 w-full mb-1" />
                            <Skeleton className="h-1.5 w-full" />
                        </CardContent>
                    </Card>
                ))}
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Skeleton className="h-48 w-full" /> {/* Live Lecture */}
                <Skeleton className="h-48 w-full" /> {/* My Console / Announcements in new layout */}
            </div>
        </main>
        {/* Right Stats Skeleton */}
        <aside className="hidden xl:block w-72 border-l p-4 space-y-6 bg-white">
            <Skeleton className="h-6 w-32 mb-2" /> {/* Scores Title */}
            <div className="grid grid-cols-2 gap-4">
                {[...Array(4)].map((_, i) => <Skeleton key={i} className="h-24 w-full rounded-lg" />)}
            </div>
            <Skeleton className="h-6 w-24 mb-2" /> {/* Leaderboard Title */}
            <Skeleton className="h-32 w-full rounded-lg" /> {/* Leaderboard Content */}
            <Skeleton className="h-12 w-full rounded-lg" /> {/* AI Mentor */}
        </aside>
    </div>
);


export default NewDashboardPage;
