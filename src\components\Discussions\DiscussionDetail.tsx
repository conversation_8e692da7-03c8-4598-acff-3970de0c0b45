import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth0 } from '@auth0/auth0-react';
import { 
  useGetDiscussionByIdQuery, 
  useGetDiscussionCommentsQuery, 
  useCreateCommentMutation, 
  useVoteCommentMutation 
} from '../../services/discussionsAPIjs';

import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import { Avatar, AvatarImage, AvatarFallback } from '../ui/avatar';
import { Textarea } from '../ui/textarea';
import { Separator } from '../ui/separator';
import { Skeleton } from '../ui/skeleton';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { 
  ThumbsUp, 
  ThumbsDown, 
  MessageCircle, 
  ArrowLeft, 
  Clock, 
  Send,
  AlertCircle,
  Loader2,
  Star
} from 'lucide-react';
import { toast } from '../../hooks/use-toast';
import { formatDistanceToNow } from 'date-fns';
import { CustomPagination } from './CustomPagination';
import { motion, AnimatePresence } from 'framer-motion';

export function DiscussionDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [page, setPage] = useState<number>(1);
  const [newComment, setNewComment] = useState('');
  const { user } = useAuth0();
  
  // Use RTK Query hooks
  const { 
    data: discussion, 
    isLoading: discussionLoading, 
    error: discussionError 
  } = useGetDiscussionByIdQuery(id || '');
  
  const { 
    data: commentsData, 
    isLoading: commentsLoading, 
    error: commentsError,
    refetch: refetchComments
  } = useGetDiscussionCommentsQuery({ 
    discussionId: id || '', 
    page, 
    limit: 10
  });
  
  const [createComment, { isLoading: commentCreating }] = useCreateCommentMutation();
  const [voteComment] = useVoteCommentMutation();

  // Extract comments and pagination from the response
  const comments = commentsData?.items || [];
  const pagination = commentsData?.pagination;

  const handleCreateComment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newComment.trim()) {
      toast({
        title: "Error",
        description: "Comment cannot be empty",
        variant: "destructive",
      });
      return;
    }
    
    // Prepare user information from Auth0
    const userName = user?.name || user?.nickname || 'Anonymous';
    const userPictureUrl = user?.picture;
    
    try {
      await createComment({
        discussionId: id || '',
        content: newComment,
        userName,
        userPictureUrl
      }).unwrap();
      
      toast({
        title: "Success",
        description: "Your comment has been submitted and is pending review.",
      });
      setNewComment('');
      refetchComments();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit your comment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleVote = async (commentId: string, value: 1 | -1) => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to vote on comments.",
        variant: "destructive",
      });
      return;
    }
    
    try {
      await voteComment({ id: commentId, value }).unwrap();
      refetchComments();
      
      toast({
        title: "Vote recorded",
        description: value === 1 ? "Upvote recorded successfully." : "Downvote recorded successfully.",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to register your vote. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const getInitials = (name?: string): string => {
    if (!name) return '?';
    return name.split(' ')
      .map(part => part.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
  };

  if (discussionLoading) {
    return (
      <div className="container mx-auto py-6 space-y-6 animate-pulse">
        <div className="flex items-center space-x-2 mb-4">
          <Skeleton className="h-8 w-8 rounded-full" />
          <Skeleton className="h-6 w-24" />
        </div>
        
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/3" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-full my-1" />
            <Skeleton className="h-4 w-full my-1" />
            <Skeleton className="h-4 w-3/4 my-1" />
          </CardContent>
        </Card>
        
        <div className="mt-8">
          <Skeleton className="h-6 w-40 mb-4" />
          <Skeleton className="h-24 w-full mb-6" />
          
          {[1, 2, 3].map((i) => (
            <Card key={i} className="mb-4">
              <CardHeader className="pb-2">
                <div className="flex items-start space-x-2">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-40" />
                    <Skeleton className="h-3 w-24 mt-1" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <Skeleton className="h-4 w-full my-1" />
                <Skeleton className="h-4 w-3/4 my-1" />
              </CardContent>
              <CardFooter>
                <Skeleton className="h-4 w-24" />
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (discussionError || !discussion) {
    return (
      <div className="container mx-auto py-6">
        <Button 
          variant="outline" 
          className="mb-4" 
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" /> Back
        </Button>
        
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4 mr-2" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {discussionError instanceof Error 
              ? discussionError.message 
              : "Failed to load discussion. Please try again later."}
          </AlertDescription>
        </Alert>
        
        <Button onClick={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Button 
          variant="outline" 
          className="mb-4 group" 
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="h-4 w-4 mr-2 transition-transform group-hover:-translate-x-1" /> 
          Back to Discussions
        </Button>
        
        <Card className="mb-6 hover:shadow-md transition-all">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-2xl">{discussion.title}</CardTitle>
                <CardDescription>
                  Started by {discussion.createdBy} • 
                  <span className="ml-1">
                    {formatDistanceToNow(new Date(discussion.createdAt), { addSuffix: true })}
                  </span>
                </CardDescription>
              </div>
              <Badge variant="secondary" className="text-xs">
                {discussion.commentCount} comments
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm max-w-none">
              <p className="whitespace-pre-wrap">{discussion.description}</p>
            </div>
          </CardContent>
          <CardFooter className="text-sm text-gray-500 justify-between">
            <div className="flex items-center space-x-4">
              <span className="flex items-center">
                <MessageCircle className="h-4 w-4 mr-1" />
                {discussion.commentCount} Responses
              </span>
              <span className="flex items-center">
                <ThumbsUp className="h-4 w-4 mr-1" />
                {discussion.voteCount} Votes
              </span>
            </div>
            <span className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              Created {formatDistanceToNow(new Date(discussion.createdAt), { addSuffix: true })}
            </span>
          </CardFooter>
        </Card>
        
        <Separator className="my-8" />
        
        <h2 className="text-xl font-bold mb-4 flex items-center">
          <MessageCircle className="h-5 w-5 mr-2" />
          Comments {pagination?.total ? `(${pagination.total})` : ''}
        </h2>
        
        {user && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Card className="mb-6 hover:shadow-sm transition-all">
              <CardContent className="pt-6">
                <form onSubmit={handleCreateComment}>
                  <div className="flex items-start space-x-4">
                    <Avatar className="w-10 h-10">
                      {user?.picture ? (
                        <AvatarImage src={user.picture} alt={user.name || 'User'} />
                      ) : (
                        <AvatarFallback className="bg-primary text-primary-foreground">
                          {(user?.name ? getInitials(user.name) : 'A')}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <div className="flex-1">
                      <Textarea
                        placeholder="Share your thoughts..."
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        className="min-h-[100px] mb-2 resize-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-all"
                      />
                      <div className="flex justify-between items-center">
                        <p className="text-sm text-gray-500">
                          Comments are moderated and will appear after approval.
                        </p>
                        <Button 
                          type="submit" 
                          disabled={commentCreating || !newComment.trim()}
                          className="transition-all"
                        >
                          {commentCreating ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Submitting...
                            </>
                          ) : (
                            <>
                              <Send className="h-4 w-4 mr-2" />
                              Submit
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        )}
        
        {commentsLoading && comments.length === 0 ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader className="pb-2">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div>
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24 mt-1" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full my-1" />
                  <Skeleton className="h-4 w-full my-1" />
                  <Skeleton className="h-4 w-2/3 my-1" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : comments.length === 0 ? (
          <Card className="text-center py-8 border-dashed border-2">
            <CardContent>
              <div className="flex flex-col items-center text-gray-500">
                <MessageCircle className="h-12 w-12 mb-3 opacity-30" />
                <p className="font-medium mb-2">No comments yet</p>
                <p className="text-sm">Be the first to share your thoughts!</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            <AnimatePresence>
              {comments.map((comment:any, index:number) => (
                <motion.div
                  key={comment.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <Card className="hover:shadow-sm transition-all">
                    <CardHeader className="pb-2">
                      <div className="flex items-start space-x-3">
                        <Avatar className="w-10 h-10">
                          {comment.authorPictureUrl ? (
                            <AvatarImage src={comment.authorPictureUrl} alt={comment.authorName} />
                          ) : (
                            <AvatarFallback className="bg-primary text-primary-foreground">
                              {getInitials(comment.authorName)}
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <div>
                          <p className="font-medium flex items-center gap-1">
                            {comment.authorName}
                            {/* Optional badge for teachers/admins */}
                            {comment.authorId && ['1', '2', '3'].includes(comment.authorId) && (
                              <Badge variant="outline" className="ml-1 text-xs py-0 h-5">
                                <Star className="h-3 w-3 mr-1 fill-yellow-400 stroke-yellow-400" />
                                Staff
                              </Badge>
                            )}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                          </p>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-2 pt-0">
                      <div className="prose prose-sm max-w-none">
                        <p className="whitespace-pre-wrap">{comment.content}</p>
                      </div>
                      {comment.status === 'PENDING' && (
                        <Badge variant="outline" className="mt-2 animate-pulse">
                          Pending Review
                        </Badge>
                      )}
                      {comment.evaluation && (
                        <div className="mt-4 grid grid-cols-2 gap-2 text-xs text-gray-500">
                          <div>Engagement: {comment.evaluation.engagement}/10</div>
                          <div>Relevance: {comment.evaluation.relevance}/10</div>
                          <div>Depth: {comment.evaluation.depthOfThought}/10</div>
                          <div>Evidence: {comment.evaluation.evidence}/10</div>
                        </div>
                      )}
                    </CardContent>
                    <CardFooter className="pt-0">
                      <div className="flex items-center space-x-4">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-xs flex items-center hover:bg-green-50 hover:text-green-600 transition-colors"
                          onClick={() => handleVote(comment.id, 1)}
                        >
                          <ThumbsUp className="h-4 w-4 mr-1" />
                          Upvote {comment.voteCount > 0 ? `(${comment.voteCount})` : ''}
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-xs flex items-center hover:bg-red-50 hover:text-red-600 transition-colors"
                          onClick={() => handleVote(comment.id, -1)}
                        >
                          <ThumbsDown className="h-4 w-4 mr-1" />
                          Downvote
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
        
        {commentsError && (
          <Alert variant="destructive" className="mt-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {commentsError instanceof Error 
                ? commentsError.message 
                : "Failed to load comments. Please try again."}
            </AlertDescription>
          </Alert>
        )}
        
        {pagination && pagination.pages > 1 && (
          <motion.div 
            className="flex justify-center mt-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <CustomPagination
              currentPage={pagination.page}
              totalPages={pagination.pages}
              onPageChange={handlePageChange}
            />
          </motion.div>
        )}
      </motion.div>
    </div>
  );
}