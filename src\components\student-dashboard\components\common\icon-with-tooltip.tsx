import React from 'react'
import {
    Toolt<PERSON>,
    TooltipContent,
    TooltipTrigger,
} from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

interface IconWithTooltipProps {
    icon: React.ReactNode
    text: string
    side?: "top" | "right" | "bottom" | "left"
    className?: string
}

export const IconWithTooltip = ({
    icon,
    text,
    side = "bottom",
    className
}: IconWithTooltipProps) => {
    return (
        <Tooltip>
            <TooltipTrigger asChild>
                <div className={cn("cursor-help", className)}>
                    {icon}
                </div>
            </TooltipTrigger>
            <TooltipContent side={side} className="bg-popover text-popover-foreground">
                <p className="text-sm">{text}</p>
            </TooltipContent>
        </Tooltip>
    )
}
