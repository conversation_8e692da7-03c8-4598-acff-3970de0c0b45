import React from 'react';

interface SkillBarProps {
  name: string;
  percentage: number;
  color: string;
}

interface LeastDevelopedSkillProps {
  title: string;
}

// Mock data for the component
const skillBars: SkillBarProps[] = [
  { name: 'Innovation', percentage: 25, color: 'bg-purple-400' },
  { name: 'Strategic Thinking', percentage: 100, color: 'bg-teal-300' },
  { name: 'Subject Knowledge', percentage: 50, color: 'bg-gray-800' },
  { name: 'Sustainability Quotient', percentage: 100, color: 'bg-blue-400' },
  { name: 'Teamwork', percentage: 25, color: 'bg-blue-300' },
];

const leastDevelopedSkills: string[] = [
  'Experiential, Social and Cultural Awareness',
  'Presentation and Communication'
];

const SkillBar: React.FC<SkillBarProps> = ({ name, percentage, color }) => {
  return (
    <div className="flex flex-col items-center h-full ">
      <div className="flex items-end justify-center h-full">
        <div 
          className={`md:w-14 w-8 ${color} rounded-t-[14px] transition-all duration-500 ease-out rounded-[14px]`}
          style={{ height: `${percentage * 2.7}px` }}
        ></div>
      </div>
      
    </div>
  );
};

const LeastDevelopedSkill: React.FC<LeastDevelopedSkillProps> = ({ title }) => {
  return (
    <div className="bg-gray-100 rounded-[10px] py-3 sm:py-4 px-4 sm:px-6 mb-2 sm:mb-4">
      <p className="text-xs sm:text-sm text-gray-700 font-medium leading-relaxed">{title}</p>
    </div>
  );
};

interface TopSkillTagsProps {
  className?: string;
}

const TopSkillTags: React.FC<TopSkillTagsProps> = ({ className = '' }) => {
  return (
    <div className={`bg-white rounded-2xl border border-[#97C48A] shadow-sm p-6 ${className}`}>
      <div className="flex flex-col lg:flex-row gap-4 sm:gap-8">
        {/* Left side - Skill Bars */}
        <div className="lg:w-[65%]">
          <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4">Top Skill Tags</h3>
          <div className="flex flex-col">
            {/* Chart with Y-axis */}
            <div className="flex h-[240px] sm:h-[290px] mt-3">
              {/* Y-axis labels */}
              <div className="flex flex-col justify-between mr-2 sm:mr-4 text-[10px] sm:text-xs text-gray-500 font-medium">
                <span>100%</span>
                <span>50%</span>
                <span>25%</span>
                <span>0</span>
              </div>
              
              {/* Chart area */}
              <div className="flex-1 h-full ">
                {/* Bars */}
                <div className="flex justify-around items-end h-full ">
                  {skillBars.map((skill, index) => (
                    <SkillBar key={index} {...skill} />
                  ))}
                </div>
              </div>
            </div>
            
            {/* X-axis labels (below the chart) */}
            <div className="flex justify-around w-full mt-2 sm:mt-3 pl-6 sm:pl-8">
              {skillBars.map((skill, index) => (
                <div key={index} className="text-[10px] sm:text-xs text-gray-700 font-medium text-center" style={{ width: '60px', maxWidth: '100%' }}>
                  {skill.name}
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Right side - Least Developed */}
        <div className="w-full lg:w-[35%]">
          <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4">Least Developed</h3>
          <div className="space-y-2 sm:space-y-3">
            {leastDevelopedSkills.map((skill, index) => (
              <LeastDevelopedSkill key={index} title={skill} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopSkillTags;
