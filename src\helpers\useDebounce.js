import { useState, useEffect } from "react";

export default function useDebounce(value, delay = 500) {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Testing the debounce
// import { useMemo, useState } from 'react';

// const debounce = (fn ,delay) => {
//   let timeout = -1;
//   return (...args) => {
//     if (timeout !== -1) {
//       clearTimeout(timeout);
//     }
//     timeout = setTimeout(fn, delay, ...args);
//   };
// };

// export function useStateDebounced(initialValue, delay) {
//   const [inputValue, _setInputValue] = useState(initialValue);

//   const [debouncedInputValue, setDebouncedInputValue] = useState(
//     initialValue
//   );

//   const memoizedDebounce = useMemo(
//     () =>
//       debounce((value) => {
//         setDebouncedInputValue(value);
//       }, delay),
//     [delay]
//   );

//   const setInputValue = (value) => {
//     if (value) {
//       _setInputValue((p) => {
//         const mutated = value(p);
//         memoizedDebounce(mutated);
//         return mutated;
//       });
//     } else {
//       _setInputValue(value);
//       memoizedDebounce(value);
//     }
//   };

//   return [inputValue, debouncedInputValue, setInputValue] ;
// }
