import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Pagination, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Loader2, Pencil, Trash2 } from 'lucide-react';
import { Question, Course } from './types';
import {
    useGetAllQuestionsQuery,
    useLazyGetAdminCoursesQuery,
    useLazyGetCourseDetailsDataQuery,
    useDeleteQuestionMutation,
} from 'src/APIConnect';
import CourseSelector from './CourseSelector';
import { stripHtmlTags } from '@/lib/utils';

const QuestionsList = () => {
    const navigate = useNavigate();

    const [courses, setCourses] = useState<Course[]>([]);
    const [selectedCourse, setSelectedCourse] = useState<string>('');
    const [selectedChapter, setSelectedChapter] = useState<string>('');
    const [selectedTopic, setSelectedTopic] = useState<string>('');
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize] = useState(10);
    const [totalQuestions, setTotalQuestions] = useState(0);

    const [getAllCourseData] = useLazyGetAdminCoursesQuery();
    const [getCourseDetails] = useLazyGetCourseDetailsDataQuery();
    const [deleteQuestion] = useDeleteQuestionMutation();

    const { data: questionsData, isLoading: isLoadingQuestions, refetch } = useGetAllQuestionsQuery(
        {
            topicId: selectedTopic,
            pageNumber: currentPage,
            pageSize: pageSize
        },
        { skip: !selectedTopic }
    );

    useEffect(() => {
        if (questionsData?.totalCount) {
            setTotalQuestions(questionsData.totalCount);
        }
    }, [questionsData]);

    useEffect(() => {
        const fetchAllCourseData = async () => {
            try {
                const { data: coursesData } = await getAllCourseData('');
                if (coursesData?.resultObject) {
                    const coursesWithDetails = await Promise.all(
                        coursesData.resultObject.map(async (course: Course) => {
                            const { data: detailsData } = await getCourseDetails({ courseId: course.id });
                            if (detailsData?.resultObject) {
                                return {
                                    ...course,
                                    chapters: detailsData.resultObject.chapters
                                };
                            }
                            return course;
                        })
                    );
                    setCourses(coursesWithDetails);
                }
            } catch (error) {
                console.error('Error fetching course data:', error);
            }
        };
        fetchAllCourseData();
    }, [getAllCourseData, getCourseDetails]);

    const handleCourseSelect = (courseId: string) => {
        setSelectedCourse(courseId);
        setSelectedChapter('');
        setSelectedTopic('');
    };

    const handleChapterSelect = (chapterId: string) => {
        setSelectedChapter(chapterId);
        setSelectedTopic('');
    };

    const handleTopicSelect = (topicId: string) => {
        setSelectedTopic(topicId);
    };

    const handleEdit = (question: Question) => {
        navigate(`/admin/questions/${question.id}?courseId=${selectedCourse}&chapterId=${selectedChapter}&topicId=${selectedTopic}`);
    };

    const handleDelete = async (id: string) => {
        try {
            await deleteQuestion(id).unwrap();
            refetch();
        } catch (error) {
            console.error('Error deleting question:', error);
        }
    };

    const filteredQuestions = (questionsData?.resultObject || []).filter((question: Question) => {
        const matchesSearch = searchQuery
            ? question.questionText.toLowerCase().includes(searchQuery.toLowerCase())
            : true;
        return matchesSearch;
    });

    return (
        <div className="container mx-auto p-4">
            <div className="flex flex-col space-y-6">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
                    <h1 className="text-2xl font-bold">Questions</h1>
                    {selectedTopic && (
                        <Button
                            onClick={() =>
                                navigate(
                                    `/admin/questions/create?courseId=${selectedCourse}&chapterId=${selectedChapter}&topicId=${selectedTopic}`
                                )
                            }
                        >
                            Create New Question
                        </Button>
                    )}
                </div>

                <CourseSelector
                    courses={courses}
                    selectedCourse={selectedCourse}
                    selectedChapter={selectedChapter}
                    selectedTopic={selectedTopic}
                    onCourseSelect={handleCourseSelect}
                    onChapterSelect={handleChapterSelect}
                    onTopicSelect={handleTopicSelect}
                />

                {isLoadingQuestions ? (
                    <div className="flex justify-center items-center h-64">
                        <Loader2 className="w-8 h-8 animate-spin" />
                    </div>
                ) : (
                    <>
                        <Input
                            placeholder="Search questions..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                        <div className="flex flex-col space-y-4">
                            <ScrollArea className="h-[calc(100vh-400px)] mt-4">
                                {filteredQuestions.length > 0 ? (
                                    <div className="space-y-4">
                                        {filteredQuestions.map((question: Question) => (
                                            <Card key={question.id}>
                                                <CardContent className="p-4 flex items-center justify-between">
                                                    <div>
                                                        <h4 className="font-medium">{stripHtmlTags(question.questionText)}</h4>
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            onClick={() => handleEdit(question)}
                                                        >
                                                            <Pencil className="w-4 h-4" />
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            onClick={() => handleDelete(question.id)}
                                                        >
                                                            <Trash2 className="w-4 h-4" />
                                                        </Button>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        ))}
                                    </div>
                                ) : (
                                    <p className="text-center text-muted-foreground py-4">
                                        No questions found.
                                    </p>
                                )}
                            </ScrollArea>

                            {filteredQuestions.length > 0 && (
                                <Pagination>
                                    <PaginationContent>
                                        <PaginationItem>
                                            <div
                                                className={currentPage === 1 ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
                                                onClick={currentPage === 1 ? undefined : () => setCurrentPage(prev => Math.max(1, prev - 1))}
                                            >
                                                <PaginationPrevious />
                                            </div>
                                        </PaginationItem>
                                        <PaginationItem>
                                            <span className="text-sm">
                                                Page {currentPage} of {Math.ceil(totalQuestions / pageSize)}
                                            </span>
                                        </PaginationItem>
                                        <PaginationItem>
                                            <div
                                                className={currentPage >= Math.ceil(totalQuestions / pageSize) ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
                                                onClick={currentPage >= Math.ceil(totalQuestions / pageSize) ? undefined : () => setCurrentPage(prev => prev + 1)}
                                            >
                                                <PaginationNext />
                                            </div>
                                        </PaginationItem>
                                    </PaginationContent>
                                </Pagination>
                            )}
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};

export default QuestionsList;
