import { useState, useEffect } from 'react'
import * as z from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useCreateDiscussionMutation } from '@/services/discussionsAPIjs'
import { toast } from '@/hooks/use-toast'

// Updated schema to require at least one of courseId, classId, topicId, moduleId, or subtopicId
const formSchema = z.object({
  title: z.string().min(5, 'Title must be at least 5 characters'),
  description: z.string().min(20, 'Description must be at least 20 characters'),
  courseId: z.string().optional(),
  topicId: z.string().optional(),
  moduleId: z.string().optional(), // Added moduleId (ChapterId)
  subtopicId: z.string().optional(), // Added subtopicId
}).refine(data => {
  // At least one of these fields must be provided
  return data.courseId ||  data.topicId || data.moduleId || data.subtopicId;
}, {
  message: "Please select at least one of: Course, Topic, Module, or Subtopic",
  path: ["courseId"], // This will show the error below the courseId field
});

interface NewDiscussionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  courses: Array<{ id: string; name: string }>
  classrooms: Array<{ id: string; name: string }>
  topics: Array<{ id: string; name: string }>
  modules?: Array<{ id: string; name: string }> // Added modules (chapters)
  subtopics?: Array<{ id: string; name: string }> // Added subtopics
  onCourseSelect: (courseId: string) => void
  onClassSelect?: (classId: string) => void
  onTopicSelect?: (topicId: string) => void
  onModuleSelect?: (moduleId: string) => void // Added module select handler
  onSubtopicSelect?: (subtopicId: string) => void // Added subtopic select handler
}

export function NewDiscussionDialog({
  open,
  onOpenChange,
  courses,
  classrooms,
  topics,
  modules,
  subtopics,
  onCourseSelect,
  onClassSelect,
  onTopicSelect,
  onModuleSelect,
  onSubtopicSelect,
}: NewDiscussionDialogProps) {
  const [createDiscussion, { isLoading }] = useCreateDiscussionMutation()
  
  // State for tracking loading states
  const [isCoursesLoading, setIsCoursesLoading] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      description: '',
    },
  })

  // Reset form selections when dialog opens/closes
  useEffect(() => {
    if (!open) {
      form.reset({
        title: '',
        description: '',
        courseId: undefined,
        topicId: undefined,
        moduleId: undefined,
        subtopicId: undefined,
      });
    }
  }, [open, form]);

  const handleCourseChange = (value: string) => {
    form.setValue('courseId', value)
    
    // Clear dependent selections
    form.setValue('moduleId', undefined)
    form.setValue('topicId', undefined)
    form.setValue('subtopicId', undefined)
    
    onCourseSelect(value)
  }

  const handleClassChange = (value: string) => {
    if (onClassSelect) {
      onClassSelect(value)
    }
  }

  const handleModuleChange = (value: string) => {
    form.setValue('moduleId', value)
    
    // Clear dependent selections
    form.setValue('topicId', undefined)
    form.setValue('subtopicId', undefined)
    
    if (onModuleSelect) {
      onModuleSelect(value)
    }
  }

  const handleTopicChange = (value: string) => {
    form.setValue('topicId', value)
    
    // Clear dependent selection
    form.setValue('subtopicId', undefined)
    
    if (onTopicSelect) {
      onTopicSelect(value)
    }
  }

  const handleSubtopicChange = (value: string) => {
    form.setValue('subtopicId', value)
    if (onSubtopicSelect) {
      onSubtopicSelect(value)
    }
  }

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      await createDiscussion(values).unwrap()
      toast({
        title: "Success",
        description: "Discussion created successfully",
      })
      form.reset()
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create discussion. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Discussion</DialogTitle>
          <DialogDescription>
            Start a new discussion thread. Be specific and provide context to get better responses.
            Select at least one of: Course, Topic, Module, or Subtopic.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="What's your question?" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Provide more details about your question..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="courseId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Course</FormLabel>
                    <Select
                      onValueChange={handleCourseChange}
                      value={field.value}
                      disabled={courses.length === 0}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={courses.length === 0 ? "Loading courses..." : "Select course"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {courses.length === 0 ? (
                          <SelectItem value="loading" disabled>
                            Loading courses...
                          </SelectItem>
                        ) : (
                          courses.map((course) => (
                            <SelectItem key={course.id} value={course.id}>
                              {course.name}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="moduleId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Module</FormLabel>
                    <Select
                      onValueChange={handleModuleChange}
                      value={field.value}
                      disabled={!form.getValues('courseId') || !modules || modules.length === 0}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue 
                            placeholder={
                              !form.getValues('courseId') 
                                ? "Select course first" 
                                : !modules || modules.length === 0 
                                  ? "Loading modules..." 
                                  : "Select module"
                            } 
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {!modules || modules.length === 0 ? (
                          <SelectItem value="loading" disabled>
                            {!form.getValues('courseId') ? "Select course first" : "Loading modules..."}
                          </SelectItem>
                        ) : (
                          modules.map((module) => (
                            <SelectItem key={module.id} value={module.id}>
                              {module.name}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="topicId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Topic</FormLabel>
                    <Select
                      onValueChange={handleTopicChange}
                      value={field.value}
                      disabled={!form.getValues('moduleId') || !topics || topics.length === 0}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue 
                            placeholder={
                              !form.getValues('moduleId') 
                                ? "Select module first" 
                                : !topics || topics.length === 0 
                                  ? "Loading topics..." 
                                  : "Select topic"
                            } 
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {!topics || topics.length === 0 ? (
                          <SelectItem value="loading" disabled>
                            {!form.getValues('moduleId') ? "Select module first" : "Loading topics..."}
                          </SelectItem>
                        ) : (
                          topics.map((topic) => (
                            <SelectItem key={topic.id} value={topic.id}>
                              {topic.name}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="subtopicId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subtopic</FormLabel>
                    <Select
                      onValueChange={handleSubtopicChange}
                      value={field.value}
                      disabled={!form.getValues('topicId') || !subtopics || subtopics.length === 0}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue 
                            placeholder={
                              !form.getValues('topicId') 
                                ? "Select topic first" 
                                : !subtopics || subtopics.length === 0 
                                  ? "Loading subtopics..." 
                                  : "Select subtopic"
                            } 
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {!subtopics || subtopics.length === 0 ? (
                          <SelectItem value="loading" disabled>
                            {!form.getValues('topicId') ? "Select topic first" : "Loading subtopics..."}
                          </SelectItem>
                        ) : (
                          subtopics.map((subtopic) => (
                            <SelectItem key={subtopic.id} value={subtopic.id}>
                              {subtopic.name}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Creating...' : 'Create Discussion'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}