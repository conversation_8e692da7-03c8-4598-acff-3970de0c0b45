import React from 'react';

interface CertificateCardProps {
  certificate: {
    title: string;
    image: string;
  };
}

const CertificateCard: React.FC<CertificateCardProps> = ({ certificate }) => {
  return (
    <div className="bg-white rounded-[10px] flex flex-col sm:flex-row items-center p-4 gap-4 border-2 border-[#347468]">
      <img src={certificate.image} alt={certificate.title} className="w-full sm:w-40 h-auto sm:h-28 object-cover rounded-[10px]" />
      <div className="flex-1 text-center sm:text-left">
        <h3 className="text-md font-semibold text-black">{certificate.title}</h3>
      </div>
    </div>
  );
};

export default CertificateCard;
