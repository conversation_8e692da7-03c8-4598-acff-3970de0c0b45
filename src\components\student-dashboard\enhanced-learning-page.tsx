import { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuth0 } from '@auth0/auth0-react'
import { useDispatch } from 'react-redux'
import { ScrollArea } from '../ui/scroll-area'
import { Sheet, SheetContent } from '../ui/sheet'
import { Button } from '../ui/button'
import { useOnlineStatus } from '../../hooks/use-online-status'
import { cn } from '../../lib/utils'
import { MessageCircle } from 'lucide-react'
import {
  useLazyGetAllCourseDataQuery,
  useLazyGetCourseDetailsDataQuery,
  useLazyGetBookmarkCollectionQuery,
  useCreateBookmarkCollectionMutation,
  useLazyGetBookmarksQuery,
  useDeleteBookmarkMutation,
  useCreateBookmarkMutation,
  useLazyGetContentRelatedTopicDataQuery,
  useLazyGetContentByIdQuery,
  useGetClassRoomTokenQuery
} from '@/APIConnect'
import { setContentRelatedTopic, setCurrentCourseChoosen, setStudentAllCourses, setStudentCoursesDetail } from '../Student/Courses/courseSlice'

// Components
import { TopBar } from './components/navigation/top-bar'
import { SidebarContent } from './components/sidebar/sidebar-content'
import { MainContent } from './components/content/main-content'
import MobileFAB from './components/mobile-fab'
import OfflineBanner from './components/common/offline-banner'
import ChatWindow from '@/components/chat/ChatWindow'

// Types
import { Topic, SubTopic, ResultObject, UserStats, Chapter, Bookmark } from './types'

const EnhancedLearningPageComponent = () => {
  const isOnline = useOnlineStatus()
  const [allCourses, setAllCourses] = useState<any[]>([])
  const [selectedCourse, setSelectedCourse] = useState<ResultObject | null>(null)
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null)
  const [selectedSubTopic, setSelectedSubTopic] = useState<SubTopic | null>(null)
  const [openChapters, setOpenChapters] = useState<string[]>([])
  const [openTopics, setOpenTopics] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCourseLoading, setIsCourseLoading] = useState(false)
  const [isChatOpen, setIsChatOpen] = useState(false) // State to control chat visibility
  const calculateUserStats = (): UserStats => {
    return {
      totalCoursesFinished: allCourses.filter(c => c.completed).length,
      totalChaptersCompleted: selectedCourse?.chapters
        .filter(ch => !ch.id.includes('Quiz') && ch.completed).length || 0,
      totalQuizzesCompleted: selectedCourse?.chapters
        .filter(ch => ch.id.includes('Quiz') && ch.completed).length || 0
    }
  }

  const [userStats, setUserStats] = useState<UserStats>(calculateUserStats())

  useEffect(() => {
    setUserStats(calculateUserStats())
  }, [selectedCourse, allCourses])
  const [isMobile, setIsMobile] = useState(false)
  const [currentCourseName, setCurrentCourseName] = useState<string | null>(null)
  const [isLeftPaneVisible, setIsLeftPaneVisible] = useState(true)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [loadingType, setLoadingType] = useState<string | null>(null)
  const [isContentLoading, setIsContentLoading] = useState(false)
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([])
  const [bookmarkCollections, setBookmarkCollections] = useState<any[]>([])
  const [isTopicBookmarked, setIsTopicBookmarked] = useState(false)
  const [isBookmarkDialogOpen, setIsBookmarkDialogOpen] = useState(false)
  const [liveClassroomId, setLiveClassroomId] = useState<string | null>(null)

  const history = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()
  const { user } = useAuth0()

  const [getAllCoursesData] = useLazyGetAllCourseDataQuery()
  const [getCourseDetailsData] = useLazyGetCourseDetailsDataQuery()
  const [getCollections] = useLazyGetBookmarkCollectionQuery()
  const [createCollection] = useCreateBookmarkCollectionMutation()
  const [getBookmarks] = useLazyGetBookmarksQuery()
  const [createBookmark] = useCreateBookmarkMutation()
  const [deleteBookmark] = useDeleteBookmarkMutation()
  const [getContentRelatedTopicData] = useLazyGetContentRelatedTopicDataQuery()
  const [getContentById] = useLazyGetContentByIdQuery()
  const { data: liveSessionToken, isLoading: isLoadingLiveSession } = useGetClassRoomTokenQuery(
    { classroomId: liveClassroomId || "", displayName: user?.name || "Student" },
    { skip: !liveClassroomId }
  )

  // Prefetch content functions
  const prefetchTopicContent = async (topicId: string) => {
    try {
      const { data } = await getContentRelatedTopicData(topicId)
      if (data?.resultObject?.contentUrl) {
        if ('serviceWorker' in navigator) {
          const registration = await navigator.serviceWorker.ready
          registration.active?.postMessage({
            type: 'CACHE_TOPIC_CONTENT',
            topicId,
            contentUrl: data.resultObject.contentUrl
          })
        }
      }
    } catch (error) {
      console.error('Failed to prefetch topic content:', error)
    }
  }
  
  const prefetchSubTopicContent = async (subTopicId: string) => {
    try {
      const { data } = await getContentRelatedTopicData(subTopicId)
      if (data?.resultObject?.contentUrl) {
        if ('serviceWorker' in navigator) {
          const registration = await navigator.serviceWorker.ready
          registration.active?.postMessage({
            type: 'CACHE_SUBTOPIC_CONTENT',
            subTopicId,
            contentUrl: data.resultObject.contentUrl
          })
        }
      }
    } catch (error) {
      console.error('Failed to prefetch subtopic content:', error)
    }
  }

  const prefetchAllContent = async (courses: any[]) => {
    try {
      for (const course of courses) {
        const { data } = await getCourseDetailsData({ courseId: course.id })
        if (data?.resultObject) {
          for (const chapter of data.resultObject.chapters) {
            if (chapter.topics && Array.isArray(chapter.topics)) {
              for (const topic of chapter.topics) {
                // Cache the topic content
                await prefetchTopicContent(topic.id)
                
                // Cache subtopic content if available
                if (topic.subTopics && Array.isArray(topic.subTopics)) {
                  for (const subTopic of topic.subTopics) {
                    await prefetchSubTopicContent(subTopic.id)
                  }
                }
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to prefetch all content:', error)
    }
  }

  // Effects
  useEffect(() => {
    const fetchCourses = async () => {
      if (user?.email) {
        try {
          const id = '153898a5-fc2d-4630-aaee-00662a79c989'
          const { data } = await getAllCoursesData(id)
          if (data.StatusCode === 400) {
            console.log("No courses found")
          } else {
            setAllCourses(data)
            setStudentAllCourses(data)
            prefetchAllContent(data)
          }
        } catch (err) {
          console.error('Error fetching courses:', err)
        } finally {
          // setTimeout(() => {
          setIsLoading(false)
          // }, 7000);
          // setIsLoading(false)
        }
      }
    }
    fetchCourses()
  }, [user, getAllCoursesData])

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth < 1024)
    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  useEffect(() => {
    const loadBookmarkCollections = async () => {
      try {
        const { data } = await getCollections({})
        if (data?.resultObject) {
          setBookmarkCollections(data.resultObject)
        }
      } catch (error) {
        console.error('Error loading bookmark collections:', error)
      }
    }
    loadBookmarkCollections()
    // const interval = setInterval(() => {
    if (selectedCourse) loadBookmarkCollections()
    // }, 10000)
    // return () => clearInterval(interval)
  }, [getCollections, selectedCourse])

  useEffect(() => {
    const checkIfBookmarked = async () => {
      if (selectedTopic) {
        try {
          const { data } = await getBookmarks({})
          if (data?.resultObject) {
            setIsTopicBookmarked(
              data.resultObject.some((b: any) => b.topicId === selectedTopic.id)
            )
            setBookmarks(data.resultObject)
          }
        } catch (error) {
          console.error('Error checking bookmark status:', error)
        }
      }
    }
    checkIfBookmarked()
  }, [selectedTopic, getBookmarks])

  useEffect(() => {
    if (liveSessionToken?.token && liveClassroomId) {
      history(`/live-session/${liveClassroomId}`);
    }
  }, [liveSessionToken, liveClassroomId, history]);

  // URL handling
  useEffect(() => {
    const nav = async () => {
      const searchParams = new URLSearchParams(location.search)
      const courseId = searchParams.get('courseId')
      console.log("Course ID from URL:", courseId);
      if (courseId && !selectedCourse && allCourses.length > 0) {
        await handleCourseClickById(courseId)
      }
    }
    nav()
  }, [location.search,selectedCourse, allCourses])

  // Effects for URL handling
  useEffect(() => {
    if (selectedCourse) {
      const searchParams = new URLSearchParams(location.search)
      const topicId = searchParams.get('topicId')
      const subTopicId = searchParams.get('subTopicId')
      
      if (topicId && !selectedTopic) {
        handleTopicSelectById(topicId)
      }
      
      if (subTopicId && !selectedSubTopic) {
        handleSubTopicSelectById(subTopicId)
      }
    }
  }, [selectedCourse, location.search, selectedTopic, selectedSubTopic])

  useEffect(() => {
    const searchParams = new URLSearchParams()
    if (selectedCourse) searchParams.set('courseId', selectedCourse.id)
    if (selectedTopic) searchParams.set('topicId', selectedTopic.id)
    if (selectedSubTopic) searchParams.set('subTopicId', selectedSubTopic.id)
    
    const url = searchParams.toString()
    if (url) {
      history(`/enhanced-learning?${url}`, {replace: true });
    }
  }, [selectedCourse, selectedTopic, selectedSubTopic, history])

  // Handlers
  const handleCourseClickById = async (courseId: string) => {
    const course = allCourses.find(c => c.id === courseId)
    if (course) await handleCourseClick(course)
  }

  // Handle TopBar compatibility with SubTopics
  const handleTopicSelectById = (topicId: string) => {
    const chapter = selectedCourse?.chapters.find(ch =>
      ch.topics.some(t => t.id === topicId)
    )
    const topic = chapter?.topics.find(t => t.id === topicId)
    if (topic) {
      handleTopicSelect(topic)
      
      // If this topic has subtopics, expand it
      if (topic.subTopics && topic.subTopics.length > 0) {
        setOpenTopics(prev =>
          prev.includes(topic.id) ? prev : [...prev, topic.id]
        )
      }
    }
  }

  const handleSubTopicSelectById = (subTopicId: string) => {
    // Skip if no course is selected
    if (!selectedCourse) return;
    
    // Find the subtopic in the course structure
    for (const chapter of selectedCourse?.chapters || []) {
      for (const topic of chapter.topics) {
        const subTopic = topic.subTopics?.find(st => st.id === subTopicId);
        if (subTopic) {
          console.log("Found subtopic in URL, loading content:", subTopic.subTopicName);
          // Set the parent topic first, then handle the subtopic
          setSelectedTopic(topic);
          // Process the subtopic with a slight delay to ensure parent topic is set
          setTimeout(() => {
            handleSubTopicSelect(subTopic);
          }, 100);
          return;
        }
      }
    }
  }

  const handleBackToStats = () => {
    setSelectedSubTopic(null)
    setSelectedTopic(null)
    setSelectedCourse(null)
    setCurrentCourseName(null)
    dispatch(setContentRelatedTopic({}))
    dispatch(setCurrentCourseChoosen({}))
    setIsLeftPaneVisible(true)
    history('/enhanced-learning')
  }

  const handleCourseClick = async (course: ResultObject) => {
    setLoadingType('courseLoad')
    setIsTransitioning(true)
    setIsCourseLoading(true)

    try {
      const { data } = await getCourseDetailsData({ courseId: course.id })
      if (data?.resultObject) {
        const courseData = data.resultObject
        let chapters = courseData.chapters
        let sortedSequence = []

        // Sort chapters
        let rootChapter = chapters.find((chapter: Chapter) =>
          chapter.anteriorId === chapter.id
        )
        for (let i = 0; i < chapters.length; i++) {
          sortedSequence.push(rootChapter)
          rootChapter = chapters.find((chapter: Chapter) =>
            chapter.anteriorId === rootChapter.id && chapter.id !== rootChapter.id
          )
        }

        // Sort topics within chapters
        sortedSequence = sortedSequence.map((chapter: Chapter) => {
          let topics = chapter.topics
          let sortedTopics = []
          let rootTopic = topics.find((topic: Topic) =>
            topic.anteriorId === topic.id
          )
          for (let j = 0; j < topics.length; j++) {
            // Make sure subTopics array exists for each topic
            const topicWithSubTopics = {
              ...rootTopic!,
              subTopics: rootTopic?.subTopics || []
            };
            sortedTopics.push(topicWithSubTopics)
            rootTopic = topics.find((topic: Topic) =>
              topic.anteriorId === rootTopic!.id && topic.id !== rootTopic!.id
            )
          }
          return { ...chapter, topics: sortedTopics }
        })

        const updatedCourseData = {
          ...courseData,
          chapters: sortedSequence
        }

        setStudentCoursesDetail(updatedCourseData)
        dispatch(setCurrentCourseChoosen(courseData))
        setSelectedCourse(updatedCourseData)
      }
    } catch (err) {
      console.error('Error fetching course details:', err)
    } finally {
      // setTimeout(() => {
      setIsCourseLoading(false)
      setIsTransitioning(false)
      setLoadingType(null)
      // }, 1500);



      if (window.innerWidth < 768) setIsLeftPaneVisible(false)
    }
  }

  const handleTopicSelect = async (topic: Topic) => {
    try {
      setIsContentLoading(true)
      setSelectedTopic(topic)
      setCurrentCourseName(selectedCourse?.name ?? "")
      if (isMobile) setIsSheetOpen(false)
      setIsBookmarkDialogOpen(false) // Close bookmark dialog when topic is selected
      dispatch(setContentRelatedTopic(topic))
    } catch (error) {
      console.error('Error loading topic:', error)
    } finally {
      setTimeout(() => {
        setIsContentLoading(false)
      }, 2000)
    }
  }

  const handleSubTopicSelect = async (subTopic: SubTopic) => {
    try {
      setIsContentLoading(true);
      setSelectedSubTopic(subTopic);
      
      // Find parent topic for context
      const parentTopic = selectedCourse?.chapters
        .flatMap(ch => ch.topics)
        .find(topic => topic.subTopics?.some(st => st.id === subTopic.id));
      
      if (parentTopic) {
        setSelectedTopic(parentTopic);
        
        // Ensure the parent topic is expanded
        if (!openTopics.includes(parentTopic.id)) {
          setOpenTopics(prev => [...prev, parentTopic.id]);
        }
        
        // Find the chapter and ensure it's expanded too
        const parentChapter = selectedCourse?.chapters.find(
          ch => ch.topics.some(t => t.id === parentTopic.id)
        );
        
        if (parentChapter && !openChapters.includes(parentChapter.id)) {
          setOpenChapters(prev => [...prev, parentChapter.id]);
        }
        
        // Fetch the content for the subtopic
        const { data } = await getContentById(subTopic.id);
        
        if (data?.resultObject) {
          // Update Redux state with both parent topic and subtopic content
          dispatch(setContentRelatedTopic({
            ...parentTopic,
            currentSubTopic: subTopic,
            // Include content from the subtopic
            type: data.resultObject.type,
            content: data.resultObject.content,
            contentUrl: data.resultObject.contentUrl
          }));
        } else {
          // If no content is found, still update with the basic information
          dispatch(setContentRelatedTopic({
            ...parentTopic,
            currentSubTopic: subTopic
          }));
        }
      }
      
      // Update URL
      const searchParams = new URLSearchParams(location.search);
      searchParams.set('courseId', selectedCourse?.id || '');
      searchParams.set('topicId', parentTopic?.id || '');
      searchParams.set('subTopicId', subTopic.id);
      history(`/enhanced-learning?${searchParams.toString()}`, { replace: true });
      
      setCurrentCourseName(selectedCourse?.name ?? "");
      if (isMobile) setIsSheetOpen(false);
      setIsBookmarkDialogOpen(false);
    } catch (error) {
      console.error('Error loading subtopic:', error);
    } finally {
      setTimeout(() => {
        setIsContentLoading(false);
      }, 2000);
    }
  }
  
  // Make the function globally available for the subtopic cards to use
  useEffect(() => {
    // @ts-ignore - Adding a custom property to the window object
    window.handleSubTopicSelect = handleSubTopicSelect;
    
    // Cleanup
    return () => {
      // @ts-ignore - Removing the custom property
      delete window.handleSubTopicSelect;
    };
  }, [selectedCourse, openTopics, openChapters]);

  const handleQuizSelect = (quiz: any) => {
    if (!isOnline) return
    setSelectedTopic(null)
    // setSelectedQuiz({
    //   ...quiz,
    //   courseId: selectedCourse?.id
    // })
    if (isMobile) setIsSheetOpen(false)
  }

  const handleCreateBookmarkCollection = async (name: string) => {
    try {
      await createCollection({ name })
      const { data } = await getCollections({})
      if (data?.resultObject) {
        setBookmarkCollections(data.resultObject)
      }
    } catch (error) {
      console.error('Error creating collection:', error)
    }
  }

  const handleBookmark = async (collectionId: string) => {
    if (!selectedTopic) return
    try {
      await createBookmark({
        bookmarkCollectionId: collectionId,
        topicId: selectedTopic.id
      })
      setIsTopicBookmarked(true)
      setIsBookmarkDialogOpen(false)
    } catch (error) {
      console.error('Error creating bookmark:', error)
    }
  }

  const handleUnbookmark = async () => {
    const bookmarkId = bookmarks.find(b => b.topicId === selectedTopic?.id)?.id
    if (!bookmarkId) return
    try {
      await deleteBookmark({ id: bookmarkId })
      setIsTopicBookmarked(false)
      setIsBookmarkDialogOpen(false)
    } catch (error) {
      console.error('Error deleting bookmark:', error)
    }
  }

  const handleJoinLiveSession = (classroomId: string) => {
    setLiveClassroomId(classroomId);
  };

  const isChapterLocked = (chapterId: string): boolean => {
    if (!selectedCourse) return true
    
    // Find the chapter
    const chapter = selectedCourse.chapters.find(ch => ch.id === chapterId)
    if (!chapter) return true
    
    // First chapter is always unlocked
    const chapterIndex = selectedCourse.chapters.findIndex(ch => ch.id === chapterId)
    if (chapterIndex === 0) return false
    
    // Check if previous chapter is completed
    const previousChapter = selectedCourse.chapters[chapterIndex - 1]
    return !previousChapter.completed
  }

  const isTopicLocked = (topicId: string): boolean => {
    if (!selectedCourse) return true

    const initialQuiz = selectedCourse.chapters.find(ch => ch.id === 'initialQuiz')
    if (initialQuiz && !initialQuiz.completed) return true

    const chapter = selectedCourse.chapters.find(ch =>
      ch.topics.some(topic => topic.id === topicId)
    )
    if (!chapter || isChapterLocked(chapter.id)) return true

    const topicIndex = chapter.topics.findIndex(t => t.id === topicId)
    if (topicIndex === 0) return false

    const previousTopic = chapter.topics[topicIndex - 1]
    return !previousTopic.completed
  }

  const isSubTopicLocked = (subTopicId: string): boolean => {
    // For now, leverage the same logic as topic locking
    // This can be enhanced as needed for subtopic-specific requirements
    if (!selectedCourse) return true
    
    const topic = selectedCourse.chapters
      .flatMap(ch => ch.topics)
      .find(topic => topic.subTopics?.some(subTopic => subTopic.id === subTopicId))
    
    if (!topic) return true
    
    return isTopicLocked(topic.id)
  }

  const chatContext = JSON.stringify({
    currentCourse: selectedCourse,
    currentTopic: selectedTopic,
  });

  return (
    <div className="flex flex-col lg:flex-row h-screen overflow-hidden bg-background">
      {/* Desktop Sidebar */}
      {!isMobile && (
        <SidebarContent
          isVisible={isLeftPaneVisible}
          isMobile={isMobile}
          isTransitioning={isTransitioning}
          loadingType={loadingType}
          selectedCourse={selectedCourse}
          selectedSubTopic={selectedSubTopic}
          openChapters={openChapters}
          openTopics={openTopics}
          userName={user?.name ?? ""}
          allCourses={allCourses}
          isCourseLoading={isCourseLoading}
          isOnline={isOnline}
          onBackClick={() => {
            setSelectedCourse(null)
            dispatch(setCurrentCourseChoosen({}))
            dispatch(setContentRelatedTopic({}))
          }}
          onCourseClick={handleCourseClick}
          onSubTopicSelect={handleSubTopicSelect}
          onChapterToggle={(chapterId) => {
            setOpenChapters(prev =>
              prev.includes(chapterId)
                ? prev.filter(id => id !== chapterId)
                : [...prev, chapterId]
            )
          }}
          onTopicToggle={(topicId) => {
            setOpenTopics(prev =>
              prev.includes(topicId)
                ? prev.filter(id => id !== topicId)
                : [...prev, topicId]
            )
          }}
          isChapterLocked={isChapterLocked}
          isTopicLocked={isTopicLocked}
          isSubTopicLocked={isSubTopicLocked}
          onJoinLiveSession={handleJoinLiveSession}
          isLoadingLiveSession={isLoadingLiveSession}
        />
      )}

      {/* Mobile Navigation Drawer */}
      {isMobile && (
        <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen} modal={true}>
          <SheetContent
            side="left"
            className="fixed inset-y-0 left-0 w-[85%] sm:w-80 p-0 overflow-y-auto border-r shadow-xl"
          >
            <button
              onClick={() => setIsSheetOpen(false)}
              className="absolute top-4 right-4 p-2 rounded-full bg-secondary hover:bg-secondary/80 transition-colors z-50"
              aria-label="Close sidebar"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4">
                <path d="M18 6 6 18" /><path d="m6 6 12 12" />
              </svg>
            </button>
            <SidebarContent
              isVisible={true}
              isMobile={isMobile}
              isTransitioning={isTransitioning}
              loadingType={loadingType}
              selectedCourse={selectedCourse}
              selectedSubTopic={selectedSubTopic}
              openChapters={openChapters}
              openTopics={openTopics}
              userName={user?.name ?? ""}
              allCourses={allCourses}
              isCourseLoading={isCourseLoading}
              isOnline={isOnline}
              onBackClick={() => {
                setSelectedCourse(null)
                dispatch(setCurrentCourseChoosen({}))
                dispatch(setContentRelatedTopic({}))
              }}
              onCourseClick={handleCourseClick}
              onSubTopicSelect={handleSubTopicSelect}
              onChapterToggle={(chapterId) => {
                setOpenChapters(prev =>
                  prev.includes(chapterId)
                    ? prev.filter(id => id !== chapterId)
                    : [...prev, chapterId]
                )
              }}
              onTopicToggle={(topicId) => {
                setOpenTopics(prev =>
                  prev.includes(topicId)
                    ? prev.filter(id => id !== topicId)
                    : [...prev, topicId]
                )
              }}
              isChapterLocked={isChapterLocked}
              isTopicLocked={isTopicLocked}
              isSubTopicLocked={isSubTopicLocked}
              onJoinLiveSession={handleJoinLiveSession}
              isLoadingLiveSession={isLoadingLiveSession}
            />
          </SheetContent>
        </Sheet>
      )}

      {/* Main Content Area */}
      <div
        className={cn(
          "flex flex-col flex-1 h-screen overflow-hidden transition-all duration-300 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
          !isLeftPaneVisible && !isMobile && "w-full"
        )}
      >
        <TopBar
          isLeftPaneVisible={isLeftPaneVisible}
          onToggle={() => setIsLeftPaneVisible(!isLeftPaneVisible)}
          onMenuClick={() => setIsSheetOpen(true)}
          isMobile={isMobile}
          handleCourseClickById={handleCourseClickById}
          handleTopicSelectById={handleTopicSelectById}
          handleSubTopicSelectById={handleSubTopicSelectById}
        />

        <ScrollArea className="flex-1">
          <div className="p-4 md:p-6 space-y-6">
            <MainContent
              isLoading={isLoading}
              isContentLoading={isContentLoading}
              selectedTopic={selectedTopic}
              selectedSubTopic={selectedSubTopic}
              currentCourseName={currentCourseName}
              userStats={userStats}
              isOnline={isOnline}
              bookmarkCollections={bookmarkCollections}
              isTopicBookmarked={isTopicBookmarked}
              onBackToStats={handleBackToStats}
              onCreateBookmarkCollection={handleCreateBookmarkCollection}
              onBookmark={handleBookmark}
              onUnbookmark={handleUnbookmark}
              onQuizComplete={() => handleCourseClick(selectedCourse!)}
            />
          </div>
        </ScrollArea>
      </div>

      {/* {isMobile && <MobileFAB />} */}
      <OfflineBanner />

       {/* Floating Chat Button */}
       <Button
                onClick={() => setIsChatOpen(true)}
                className="fixed bottom-6 right-6 z-50 bg-teal-700 hover:bg-teal-800 text-white rounded-full h-14 flex items-center justify-center shadow-lg text-bold"
                // size="icon"
                aria-label="Ask AI Mentor"
            >
               <div className='text-white flex flex-row space-x-2'> <MessageCircle size={24} className='mr-2' /> Ask AI Mentor</div>
            </Button>

      {/* Floating Chat Window */}
      <ChatWindow 
        context={chatContext} 
        isVisible={isChatOpen} 
        onClose={() => setIsChatOpen(false)} 
      />
    </div>
  )
}

export default EnhancedLearningPageComponent
