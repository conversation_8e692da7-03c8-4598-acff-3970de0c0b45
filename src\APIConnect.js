import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import {
  students,
  performance,
  course,
  courses,
  subjects,
  chapters,
  topics,
  coursesContent,
  chapter,
  topic,
  quiz,
  questions,
  courseid,
  submit,
} from "./constant/AppConstant";

export const baseUrl =
  "https://nexo-dev-fbf0cvc7bhehbuft.australiacentral-01.azurewebsites.net/";

// Custom fetch base query with offline support
const customFetchBaseQuery = fetchBaseQuery({
  baseUrl: baseUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().user.userToken;
    if (token) {
      headers.set("authorization", `Bearer ${token}`);
    }
    return headers;
  },
});

const liveKitBaseQuery = fetchBaseQuery({
  baseUrl:
    "https://learnido-livekit-bdbbh2gdfdh2g2ah.australiacentral-01.azurewebsites.net/api/v1/",
  prepareHeaders: (headers, { getState }) => {
    const token = getState().user.userToken;
    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }
    return headers;
  },
});

// Enhanced base query with offline support
const baseQueryWithOfflineSupport = async (args, api, extraOptions) => {
  try {
    // Attempt online request first
    const result = await customFetchBaseQuery(args, api, extraOptions);

    // If the request was successful, cache it
    if (result.data) {
      try {
        const cache = await caches.open("learnido-api-v1");
        const request = new Request(baseUrl + args.url, {
          method: args.method || "GET",
          headers: args.headers || {},
          body: args.body ? JSON.stringify(args.body) : undefined,
        });
        const response = new Response(JSON.stringify(result.data), {
          headers: { "Content-Type": "application/json" },
        });
        await cache.put(request, response);
      } catch (cacheError) {
        console.warn("Failed to cache API response:", cacheError);
      }
    }

    return result;
  } catch (error) {
    if (
      args.method === "POST" ||
      args.method === "PUT" ||
      args.method === "DELETE"
    ) {
      throw error;
    }
    // If offline or other request fails, try to get from cache
    try {
      const cache = await caches.open("learnido-api-v1");
      const cachedRequest = new Request(baseUrl + args.url);
      const cachedResponse = await cache.match(cachedRequest);

      if (cachedResponse) {
        const data = await cachedResponse.json();
        return { data };
      }

      // If no cached data, return error with offline flag
      return {
        error: {
          status: "OFFLINE",
          data: { message: "You are offline and no cached data is available" },
        },
      };
    } catch (cacheError) {
      // If cache access fails, return original error
      return {
        error: {
          status: error.status,
          data: error.data,
        },
      };
    }
  }
};

const APIConnect = createApi({
  reducerPath: "API",
  baseQuery: baseQueryWithOfflineSupport,
  endpoints: (builder) => ({
    getTopics: builder.query({
      query: () => ({
        url: "Topics",
        method: "GET",
      }),
    }),
    // ... existing endpoints remain the same ...
    getClassroomAnalytics: builder.query({
      query: (params) => ({
        url: `/Analytic/classroom/${params.classroomId}`,
        method: "GET",
      }),
    }),
    getSchools: builder.query({
      query: () => ({
        url: "Schools",
        method: "GET",
      }),
    }),

    getSchoolById: builder.query({
      query: (id) => ({
        url: `Schools/${id}`,
        method: "GET",
      }),
    }),

    createSchool: builder.mutation({
      query: (params) => ({
        url: "Schools",
        method: "POST",
        body: params,
      }),
    }),

    updateSchool: builder.mutation({
      query: (params) => ({
        url: `Schools/${params.id}`,
        method: "PUT",
        body: params,
      }),
    }),

    deleteSchool: builder.mutation({
      query: (params) => ({
        url: `Schools?Id=${params.Id}`,
        method: "DELETE",
      }),
    }),
    // Add content management endpoints
    getAllContent: builder.query({
      query: () => ({
        url: "CoursesContent",
        method: "GET",
      }),
    }),

    getStudentDashboard: builder.query({
      query: (studentId) => ({
        url: `Students/dashboard`,
        method: "GET",
      }),
    }),

    getContentById: builder.query({
      query: (id) => ({
        url: `CoursesContent/${id}`,
        method: "GET",
      }),
    }),

    getContentByTopicId: builder.query({
      query: (topicId) => ({
        url: `CoursesContent/topic/${topicId}`,
        method: "GET",
      }),
    }),

    createContent: builder.mutation({
      query: (params) => ({
        url: "CoursesContent",
        method: "POST",
        body: params,
      }),
    }),

    updateContent: builder.mutation({
      query: (params) => ({
        url: `CoursesContent/${params.id}`,
        method: "PUT",
        body: params,
      }),
    }),

    deleteContent: builder.mutation({
      query: (id) => ({
        url: `CoursesContent?id=${id}`,
        method: "DELETE",
      }),
    }),

    // Add notification endpoints
    getCourseById: builder.query({
      query: (id) => ({
        url: `Courses/${id}`,
        method: "GET",
      }),
    }),

    getAdminCourses: builder.query({
      query: () => ({
        url: "Courses",
        method: "GET",
      }),
    }),

    createCourse: builder.mutation({
      query: (params) => ({
        url: "Courses",
        method: "POST",
        body: params,
      }),
    }),

    updateCourse: builder.mutation({
      query: (params) => ({
        url: `Courses/${params.id}`,
        method: "PUT",
        body: params,
      }),
    }),

    deleteCourse: builder.mutation({
      query: (params) => ({
        url: `Courses?id=${params.id}`,
        method: "DELETE",
      }),
    }),

    getSubjects: builder.query({
      query: () => ({
        url: "Subjects",
        method: "GET",
      }),
    }),
    getClassroomNotifications: builder.query({
      query: ({ classroomId }) => ({
        url: `/Notifications/classroomNotification/${classroomId}`,
        method: "GET",
      }),
    }),
    getSchoolNotifications: builder.query({
      query: () => ({
        url: "/Notifications/schoolNotification",
        method: "GET",
      }),
    }),
    getStudentNotifications: builder.query({
      query: () => ({
        url: "/notifications/studentNotifications",
        method: "GET",
      }),
    }),
    // Add new Q&A endpoints
    getQueries: builder.query({
      query: (params) => ({
        url: params.classroomId
          ? `QueryReply?classroomId=${params.classroomId}`
          : "QueryReply",
        method: "GET",
      }),
    }),
    createQuery: builder.mutation({
      query: (params) => ({
        url: "QueryReply",
        method: "POST",
        body: {
          classroomId: params.classroomId,
          queryText: params.queryText,
          studentId: params.studentId,
        },
      }),
    }),
    updateQuery: builder.mutation({
      query: ({ queryId, queryText }) => ({
        url: `QueryReply/query/${queryId}`,
        method: "PUT",
        body: {
          queryText,
        },
      }),
    }),
    updateReply: builder.mutation({
      query: ({ queryId, answerText }) => ({
        url: `QueryReply/reply/${queryId}`,
        method: "PUT",
        body: {
          answerText,
        },
      }),
    }),

    getTestData: builder.query({
      query: () => ({
        url: "todos",
      }),
    }),
    getBookmarks: builder.query({
      query: (params) => ({
        url: `Bookmark`,
        method: "GET",
      }),
    }),
    createBookmark: builder.mutation({
      query: (params) => {
        const body = {
          topicId: params.topicId
        };

        // Only include bookmarkCollectionId if it's provided
        if (params.bookmarkCollectionId) {
          body.bookmarkCollectionId = params.bookmarkCollectionId;
        }

        return {
          url: `Bookmark`,
          method: "POST",
          body: body,
        };
      },
    }),
    deleteBookmark: builder.mutation({
      query: (params) => ({
        url: `Bookmark/${params.id}`,
        method: "DELETE",
      }),
    }),

    // Submissions API endpoints
    getSubmissionById: builder.query({
      query: (id) => ({
        url: `Submissions/${id}`,
        method: "GET",
      }),
    }),

    getStudentSubmission: builder.query({
      query: ({ classroomId, assignmentId }) => ({
        url: `Submissions/studentSubmission?classroomId=${classroomId}&assignmentId=${assignmentId}`,
        method: "GET",
      }),
    }),

    createSubmission: builder.mutation({
      query: (params) => ({
        url: `Submissions`,
        method: "POST",
        body: {
          classroomId: params.classroomId,
          assignmentId: params.assignmentId,
          fileUrl: params.fileUrl || "",
          additionalFiles: params.additionalFiles || [],
          selfReflectiveAnswers: params.selfReflectiveAnswers || []
        },
      }),
    }),

    updateSubmission: builder.mutation({
      query: (params) => ({
        url: `Submissions`,
        method: "PUT",
        body: {
          classroomId: params.classroomId,
          assignmentId: params.assignmentId,
          fileUrl: params.fileUrl || "",
          additionalFiles: params.additionalFiles || [],
          selfReflectiveAnswers: params.selfReflectiveAnswers || []
        },
      }),
    }),
    getBookmarkCollection: builder.query({
      query: (params) => ({
        url: `BookmarkCollection`,
        method: "GET",
      }),
    }),
    createBookmarkCollection: builder.mutation({
      query: (params) => ({
        url: `BookmarkCollection`,
        method: "POST",
        body: {
          bookmarkCollectionName: params.name,
        },
      }),
    }),
    editBookmarkCollection: builder.mutation({
      query: (params) => ({
        url: `BookmarkCollection/${params.id}`,
        method: "PUT",
        body: {
          bookmarkCollectionName: params.name,
        },
      }),
    }),
    deleteBookmarkCollection: builder.mutation({
      query: (id) => ({
        url: `BookmarkCollection/${id}`,
        method: "DELETE",
      }),
    }),
    // Student Note API endpoints (updated for new structure)
    getStudentAllNotes: builder.query({
      query: () => ({
        url: `studentnote/student`,
        method: "GET",
      }),
    }),
    getStudentNoteById: builder.query({
      query: (id) => ({
        url: `studentnote/${id}`,
        method: "GET",
      }),
    }),
    getStudentNotesByCourse: builder.query({
      query: (courseId) => ({
        url: `studentnote/course/${courseId}`,
        method: "GET",
      }),
    }),
    getStudentNotesByTopic: builder.query({
      query: (topicId) => ({
        url: `studentnote/topic/${topicId}`,
        method: "GET",
      }),
    }),
    createNewStudentNote: builder.mutation({
      query: (params) => ({
        url: `studentnote`,
        method: "POST",
        body: params,
      }),
    }),
    updateStudentNote: builder.mutation({
      query: (params) => ({
        url: `studentnote/${params.id}`,
        method: "PUT",
        body: { ...params, id: undefined },
      }),
    }),
    deleteStudentNote: builder.mutation({
      query: (id) => ({
        url: `studentnote/${id}`,
        method: "DELETE",
      }),
    }),
    // Subtopic endpoints
    getSubtopics: builder.query({
      query: (topicId) => ({
        url: `CoursesContent/topic/${topicId}`,
        method: "GET",
      }),
    }),

    getSubtopicById: builder.query({
      query: (id) => ({
        url: `CoursesContent/subtopicId/${id}`,
        method: "GET",
      }),
    }),

    createSubtopic: builder.mutation({
      query: (params) => ({
        url: "CoursesContent",
        method: "POST",
        body: params,
      }),
    }),

    updateSubtopic: builder.mutation({
      query: (params) => ({
        url: `CoursesContent/${params.id}`,
        method: "PUT",
        body: params,
      }),
    }),

    deleteSubtopic: builder.mutation({
      query: (id) => ({
        url: `CoursesContent/${id}`,
        method: "DELETE",
      }),
    }),

    updateSubtopicWeights: builder.mutation({
      query: ({ topicId, weights }) => ({
        url: `CoursesContent/topic/${topicId}/weight`,
        method: "PUT",
        body: weights,
      }),
    }),

    getQuizQuestions: builder.query({
      query: (quizId) => ({
        url: `Quizzes/${quizId}`,
      }),
    }),

    getSubtopicContent: builder.query({
      query: (subtopicId) => ({
        url: `CoursesContent/${subtopicId}`,
      }),
    }),

    getQuestionById: builder.query({
      query: (questionId) => ({
        url: `Questions/${questionId}`,
      }),
    }),
    getStudentPerformanceData: builder.query({
      query: (id) => ({
        url: `${students}/${id}/${performance}`,
      }),
    }),
    getTopicQuizPerformance: builder.query({
      query: (id) => ({
        url: `Students/performance/topic/${id}`,
      }),
    }),
    getAllCourseData: builder.query({
      query: (id) => ({
        url: `${students}/${course}`,
      }),
    }),
    getCourseDetailsData: builder.query({
      query: (obj) => ({
        url: `${courses}/${obj.courseId}`,
      }),
    }),
    getAllSubjectsData: builder.query({
      query: () => ({
        url: `${subjects}`,
      }),
    }),
    getAllCoursesFormData: builder.query({
      query: () => ({
        url: `${courses}`,
      }),
    }),
    getAllChaptersFormData: builder.query({
      query: () => ({
        url: `${chapters}`,
      }),
    }),
    getAllTopicsFormData: builder.query({
      query: () => ({
        url: `${topics}`,
      }),
    }),
    getContentViewData: builder.query({
      query: () => ({
        url: `${coursesContent}`,
      }),
    }),
    getStudentQuiz: builder.query({
      query: (val) => ({
        url: val.getPracticeQuiz
          ? `${students}/practiceQuiz/?chapterId=${val.courseId}`
          : `${students}/${quiz}?${courseid}=${val.courseId}`,
      }),
    }),
    getContentRelatedTopicData: builder.query({
      query: (id) => ({
        url: `${coursesContent}/${topic}/${id}`,
      }),
    }),
    getQuestionTypeData: builder.query({
      query: () => ({
        url: `${coursesContent}/${topic}`,
      }),
    }),
    getPracticeQuiz: builder.query({
      query: (val) => ({
        url: `${students}/practiceQuiz/?chapterId=${val.courseId}`,
      }),
    }),
    saveCourseData: builder.mutation({
      query: (params) => ({
        url: `${courses}`,
        method: "POST",
        body: params,
      }),
    }),
    saveChaptersData: builder.mutation({
      query: (params) => ({
        url: `${chapters}`,
        method: "POST",
        body: params,
      }),
    }),
    saveTopicsData: builder.mutation({
      query: (params) => ({
        url: `${topics}`,
        method: "POST",
        body: params,
      }),
    }),
    updateTopicsData: builder.mutation({
      query: (params) => ({
        url: `${topics}/${params.id}`,
        method: "PUT",
        body: params,
      }),
    }),
    updateChaptersData: builder.mutation({
      query: (params) => ({
        url: `${chapters}/${params.id}`,
        method: "PUT",
        body: params,
      }),
    }),
    saveCoursesContent: builder.mutation({
      query: (params) => ({
        url: `${coursesContent}`,
        method: "POST",
        body: params,
      }),
    }),
    saveQuestionData: builder.mutation({
      query: (params) => ({
        url: `${questions}`,
        method: "POST",
        body: params,
      }),
    }),
    saveStudentQuiz: builder.mutation({
      query: (params) => ({
        url: `${students}/${quiz}/${params.quizId}/${submit}`,
        method: "PUT",
        body: { questionSet: params.quizzes, timeTaken: params.timeTaken },
      }),
    }),

    deleteCourseData: builder.mutation({
      query: (params) => ({
        url: `${courses}?id=${params.id}`,
        method: "DELETE",
      }),
    }),
    deleteChapterData: builder.mutation({
      query: (id) => ({
        url: `${chapters}?id=${id}`,
        method: "DELETE",
      }),
    }),
    deleteTopicData: builder.mutation({
      query: (id) => ({
        url: `${topics}?id=${id}`,
        method: "DELETE",
      }),
    }),
    getClassroomAndChapterAnalytics: builder.query({
      query: (params) => ({
        url: `Analytic/classroom/${params.classroomId}/chapter/${params.chapterId}`,
        method: "GET",
      }),
    }),
    // Add Questions management endpoints
    getAllQuestions: builder.query({
      query: (params) => ({
        url: `Questions?TopicId=${params.topicId}`,
        method: "GET",
      }),
    }),
    getQuestionById: builder.query({
      query: (params) => ({
        url: `Questions/${params.id}`,
        method: "GET",
      }),
    }),
    createQuestion: builder.mutation({
      query: (params) => ({
        url: "Questions",
        method: "POST",
        body: params,
      }),
    }),
    updateQuestion: builder.mutation({
      query: (params) => ({
        url: `Questions/${params.id}`,
        method: "PUT",
        body: params,
      }),
    }),
    deleteQuestion: builder.mutation({
      query: (id) => ({
        url: `Questions/${id}`,
        method: "DELETE",
      }),
    }),
    // // Existing endpoints...
    // getClassroomNotifications: builder.query({
    //   query: () => ({
    //     url: "Notifications/classroomNotification",
    //     method: "GET",
    //   }),
    // }),

    getAdminCourses: builder.query({
      query: () => ({
        url: "Courses",
        method: "GET",
      }),
    }),
    // Add these inside endpoints: (builder) => ({ ... })
    getSchoolClassrooms: builder.query({
      query: (schoolId) => ({
        url: `Schools/${schoolId}/classrooms`,
        method: "GET",
      }),
    }),

    getClassroomById: builder.query({
      query: (id) => ({
        url: `Classrooms/${id}`,
        method: "GET",
      }),
    }),

    createClassroom: builder.mutation({
      query: (params) => ({
        url: `Schools/${params.schoolId}/classrooms`,
        method: "POST",
        body: params,
      }),
    }),

    updateClassroom: builder.mutation({
      query: (params) => ({
        url: `Classrooms/${params.id}`,
        method: "PUT",
        body: params,
      }),
    }),

    deleteClassroom: builder.mutation({
      query: (id) => ({
        url: `Classrooms/${id}`,
        method: "DELETE",
      }),
    }),

    getUsers: builder.query({
      query: () => ({
        url: "/Users",
        method: "GET",
      }),
    }),

    getUserById: builder.query({
      query: (id) => ({
        url: `/Users/<USER>
        method: "GET",
      }),
    }),

    createUser: builder.mutation({
      query: (body) => ({
        url: "/Users",
        method: "POST",
        body,
      }),
    }),

    updateUser: builder.mutation({
      query: (user) => ({
        url: `/Users`,
        method: "PATCH",
        body: user,
      }),
    }),

    deleteUser: builder.mutation({
      query: ({ Id }) => ({
        url: `/Users?Id=${Id}`,
        method: "DELETE",
      }),
    }),

    // Assignment scoring endpoint
    submitAssignmentScore: builder.mutation({
      query: (params) => ({
        url: "CourseContent/scoring",
        method: "PUT",
        body: params,
      }),
    }),

    // Classroom announcement endpoints
    getAnnouncements: builder.query({
      query: ({ type, id }) => ({
        url: `/Notifications/${type}/${id}`,
        method: "GET",
      }),
    }),
    createAnnouncement: builder.mutation({
      query: ({ type, classroomId, ...params }) => ({
        url: `/Notifications/${type}`,
        method: "POST",
        body: {
          classroomId,
          ...params,
        },
      }),
    }),

    // Classroom analytics endpoints
    // getClassroomAnalytics: builder.query({
    //   query: (classroomId) => ({
    //     url: `Classrooms/${classroomId}/analytics`,
    //     method: "GET",
    //   }),
    // }),
    getClassParticipation: builder.query({
      query: ({ classroomId, period }) => ({
        url: `Classrooms/${classroomId}/participation?period=${period}`,
        method: "GET",
      }),
    }),
    getClassPerformance: builder.query({
      query: ({ classroomId, period }) => ({
        url: `Classrooms/${classroomId}/performance?period=${period}`,
        method: "GET",
      }),
    }),

    // Student progress endpoints
    getStudentProgress: builder.query({
      query: ({ classroomId, studentId }) => ({
        url: `Classrooms/${classroomId}/students/${studentId}/progress`,
        method: "GET",
      }),
    }),
    removeStudentFromClassroom: builder.mutation({
      query: ({ classroomId, studentId }) => ({
        url: `Classrooms/${classroomId}/student/${studentId}`,
        method: "DELETE",
      }),
    }),

    // Topic management endpoints
    updateTopicEndDate: builder.mutation({
      query: ({ topicId, endDate }) => ({
        url: `Topics/${topicId}/endDate`,
        method: "PUT",
        body: { endDate },
      }),
    }),

    getSchoolCourses: builder.query({
      query: (schoolId) => ({
        url: `Schools/${schoolId}/courses`,
        method: "GET",
      }),
    }),

    addSchoolCourse: builder.mutation({
      query: (params) => ({
        url: `Schools/course`,
        method: "PUT",
        body: params,
      }),
    }),

    getSubjects: builder.query({
      query: () => ({
        url: "Subjects",
        method: "GET",
      }),
    }),

    getSubjectById: builder.query({
      query: (id) => ({
        url: `Subjects/${id}`,
        method: "GET",
      }),
    }),

    createSubject: builder.mutation({
      query: (params) => ({
        url: "Subjects",
        method: "POST",
        body: params,
      }),
    }),

    updateSubject: builder.mutation({
      query: (params) => ({
        url: `Subjects/${params.id}`,
        method: "PUT",
        body: params,
      }),
    }),

    deleteSubject: builder.mutation({
      query: (id) => ({
        url: `Subjects?id=${id}`,
        method: "DELETE",
      }),
    }),

    addQuestionToFinalOrInitialCourseQuiz: builder.mutation({
      query: (params) => ({
        url: `/SpecialQuizQuestions/addQuestion`,
        method: "PUT",
        body: params,
      }),
    }),
    removeQuestionFromFinalOrInitialCourseQuiz: builder.mutation({
      query: (params) => ({
        url: `/SpecialQuizQuestions/removeQuestion`,
        method: "PUT",
        body: params,
      }),
    }),

    listCourseInitailOrFinalQuizQuestions: builder.query({
      query: (params) => ({
        url: `/SpecialQuizQuestions/${params.courseId}/${
          params.isFinal === true ? "final" : "initial"
        }`,
        method: "GET",
      }),
    }),
    getStudentAnalytics: builder.query({
      query: ({ studentId, classroomId }) => ({
        url: `/Analytic/student/${studentId}/classroom/${classroomId}`,
        method: "GET",
      }),
    }),

    addStudentToClassroom: builder.mutation({
      query: ({ classroomId, studentId }) => ({
        url: `Classrooms/${classroomId}/student/${studentId}`,
        method: "PUT",
        // body: { studentId },
      }),
    }),
    // ... rest of the endpoints remain the same ...
    getStudentProjectDashboard: builder.query({
      query: () => ({
        url: `StudentGroup/student-dashboard`,
        method: "GET",
      }),
    }),

    // Project API endpoints
    getProjectById: builder.query({
      query: (projectId) => ({
        url: `Project/${projectId}`,
        method: "GET",
      }),
    }),

    getProjectAssignments: builder.query({
      query: (projectId) => ({
        url: `Project/${projectId}/assignments`,
        method: "GET",
      }),
    }),

    // Group API endpoints
    getGroupById: builder.query({
      query: (groupId) => ({
        url: `StudentGroup/${groupId}`,
        method: "GET",
      }),
    }),

    getGroupsByStudentId: builder.query({
      query: (studentId) => ({
        url: `StudentGroup/student/${studentId}`,
        method: "GET",
      }),
    }),

    // Chat API endpoints
    getGroupChatHistory: builder.query({
      query: ({ groupId, page = 1, pageSize = 20 }) => ({
        url: `StudentGroup/${groupId}/chat?page=${page}&pageSize=${pageSize}`,
        method: "GET",
      }),
    }),

    sendGroupMessage: builder.mutation({
      query: ({ groupId, message, type = "TEXT" }) => ({
        url: `StudentGroup/${groupId}/message`,
        method: "POST",
        body: { message, type },
      }),
    }),

    // Submission API endpoints
    getAssignmentSubmission: builder.query({
      query: ({ groupId, assignmentId }) => ({
        url: `StudentGroup/${groupId}/assignment/${assignmentId}`,
        method: "GET",
      }),
    }),

    submitAssignment: builder.mutation({
      query: ({ groupId, assignmentId, fileUrl }) => ({
        url: `StudentGroup/${groupId}/submission/${assignmentId}`,
        method: "POST",
        body: { fileUrl },
      }),
    }),

    // User API endpoints
    getUserById: builder.query({
      query: (userId) => ({
        url: `Users/${userId}`,
        method: "GET",
      }),
    }),
    getCalendarEvents: builder.query({
      query: ({ start, end }) => ({
        url: `event/calendar?start=${start}&end=${end}`,
        method: "GET",
      }),
    }),
  }),
});

export const liveKitApi = createApi({
  reducerPath: "liveKitApi",
  baseQuery: liveKitBaseQuery,
  endpoints: (builder) => ({
    getClassRoomToken: builder.query({
      query: ({ classroomId, displayName }) =>
        `token/get_class_room_token?classroom_id=${classroomId}&display_name=${displayName}`,
    }),
    getBreakoutRoomToken: builder.query({
      query: ({ classroomId, displayName, breakoutRoomId }) =>
        `token/get_breakout_room_token?classroom_id=${classroomId}&display_name=${displayName}&breakout_room_id=${breakoutRoomId}`,
    }),
    getBreakoutRoomList: builder.query({
      query: (classroomId) =>
        `room/breakout_room_list?classroom_id=${classroomId}`,
    }),
    createBreakoutRoom: builder.mutation({
      query: ({ classroomId, breakoutRoomName }) => ({
        url: "room/create_breakout_room",
        method: "POST",
        body: {
          classroom_id: classroomId,
          breakout_room_name: breakoutRoomName,
        },
      }),
    }),
    deleteBreakoutRoom: builder.mutation({
      query: ({ classroomId, breakoutRoomId }) => ({
        url: `room/delete_breakout_room?classroom_id=${classroomId}&breakout_room_id=${breakoutRoomId}`,
        method: "DELETE",
      }),
    }),
  }),
});

export const {
  // Add content management exports
  useGetAllContentQuery,
  useGetContentByIdQuery,
  useGetContentByTopicIdQuery,
  useLazyGetContentByIdQuery,
  useLazyGetContentByTopicIdQuery,
  useCreateContentMutation,
  useUpdateContentMutation,
  useDeleteContentMutation,
  // Add Questions management exports
  useLazyGetAllQuestionsQuery,
  useGetAllQuestionsQuery,
  useCreateQuestionMutation,
  useUpdateQuestionMutation,
  useDeleteQuestionMutation,
  useLazyGetAdminCoursesQuery,
  useGetQuestionByIdQuery,
  // Existing exports...
  useLazyGetTestDataQuery,
  useLazyGetAllCourseDataQuery,
  useLazyGetStudentPerformanceDataQuery,
  useLazyGetCourseDetailsDataQuery,
  useLazyGetAllSubjectsDataQuery,
  useGetAllCourseDataQuery,
  useLazyGetAllCoursesFormDataQuery,
  useLazyGetAllChaptersFormDataQuery,
  useLazyGetAllTopicsFormDataQuery,
  useLazyGetStudentQuizQuery,
  useLazyGetContentViewDataQuery,
  useLazyGetContentRelatedTopicDataQuery,
  useLazyGetBookmarksQuery,
  useGetBookmarksQuery,
  useCreateBookmarkMutation,
  useDeleteBookmarkMutation,
  useGetBookmarkCollectionQuery,
  useCreateBookmarkCollectionMutation,

  // Submissions API hooks
  useGetSubmissionByIdQuery,
  useLazyGetStudentSubmissionQuery,
  useCreateSubmissionMutation,
  useUpdateSubmissionMutation,
  useGetClassroomNotificationsQuery,
  useLazyGetClassroomNotificationsQuery,
  useLazyGetBookmarkCollectionQuery,
  useEditBookmarkCollectionMutation,
  useDeleteBookmarkCollectionMutation,
  useLazyGetQuestionTypeDataQuery,
  useLazyGetTopicQuizPerformanceQuery,
  useSaveCourseDataMutation,
  useSaveChaptersDataMutation,
  useSaveTopicsDataMutation,
  useUpdateTopicsDataMutation,
  useUpdateChaptersDataMutation,
  useSaveCoursesContentMutation,
  useSaveQuestionDataMutation,
  useSaveStudentQuizMutation,
  useDeleteCourseDataMutation,
  useDeleteChapterDataMutation,
  useDeleteTopicDataMutation,
  useLazyGetQuizQuestionsQuery,
  useGetSubtopicContentQuery,
  useLazyGetSubtopicContentQuery,
  useLazyGetQuestionByIdQuery,
  useLazyGetSchoolClassroomsQuery,
  useGetStudentAllNotesQuery,
  useGetStudentNoteByIdQuery,
  useGetStudentNotesByCourseQuery,
  useGetStudentNotesByTopicQuery,
  useCreateNewStudentNoteMutation,
  useUpdateStudentNoteMutation,
  useDeleteStudentNoteMutation,
  useGetTopicQuizPerformanceQuery,
  useGetPracticeQuizQuery,
  useLazyGetPracticeQuizQuery,
  useGetAnnouncementsQuery,
  useGetSchoolNotificationsQuery,
  useGetStudentNotificationsQuery,
  useLazyGetAnnouncementsQuery,
  useLazyGetSchoolNotificationsQuery,
  useLazyGetStudentNotificationsQuery,
  useGetQueriesQuery,
  useCreateQueryMutation,
  useGetAdminCoursesQuery,
  useGetCourseByIdQuery,
  useCreateCourseMutation,
  useUpdateCourseMutation,
  useDeleteCourseMutation,
  useGetSubjectsQuery,
  useGetSchoolsQuery,
  useGetSchoolByIdQuery,
  useCreateSchoolMutation,
  useUpdateSchoolMutation,
  useDeleteSchoolMutation,
  useGetSchoolClassroomsQuery,
  useGetClassroomByIdQuery,
  useCreateClassroomMutation,
  useUpdateClassroomMutation,
  useGetSchoolCoursesQuery,
  useAddSchoolCourseMutation,
  useDeleteClassroomMutation,
  useGetUsersQuery,
  useGetUserByIdQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useUpdateQueryMutation,
  useUpdateReplyMutation,
  useGetClassroomAnnouncementsQuery,
  useCreateAnnouncementMutation,
  useGetClassroomAnalyticsQuery,
  useGetClassParticipationQuery,
  useGetClassPerformanceQuery,
  useGetStudentProgressQuery,
  useAddStudentToClassroomMutation,
  useRemoveStudentFromClassroomMutation,
  useUpdateTopicEndDateMutation,
  useGetClassroomAndChapterAnalyticsQuery,
  useGetSubjectByIdQuery,
  useCreateSubjectMutation,
  useUpdateSubjectMutation,
  useDeleteSubjectMutation,
  useListCourseInitailOrFinalQuizQuestionsQuery,
  useAddQuestionToFinalOrInitialCourseQuizMutation,
  useRemoveQuestionFromFinalOrInitialCourseQuizMutation,
  useLazyGetClassroomAnalyticsQuery,
  useGetStudentAnalyticsQuery,
  useLazyGetTopicsQuery,
  useGetSubtopicsQuery,
  useGetSubtopicByIdQuery,
  useCreateSubtopicMutation,
  useUpdateSubtopicMutation,
  useDeleteSubtopicMutation,
  useUpdateSubtopicWeightsMutation,
  useSubmitAssignmentScoreMutation,
  useGetStudentDashboardQuery,
  useLazyGetStudentDashboardQuery,
  useLazyGetStudentAllNotesQuery,
  useLazyGetStudentNoteByIdQuery,
  useLazyGetStudentNotesByCourseQuery,
  useLazyGetStudentNotesByTopicQuery,
  useGetStudentProjectDashboardQuery,
  useLazyGetStudentProjectDashboardQuery,
  // Project API hooks
  useGetProjectByIdQuery,
  useLazyGetProjectByIdQuery,
  useGetProjectAssignmentsQuery,
  useLazyGetProjectAssignmentsQuery,
  // Group API hooks
  useGetGroupByIdQuery,
  useLazyGetGroupByIdQuery,
  useGetGroupsByStudentIdQuery,
  useLazyGetGroupsByStudentIdQuery,
  // Chat API hooks
  useGetGroupChatHistoryQuery,
  useLazyGetGroupChatHistoryQuery,
  useSendGroupMessageMutation,
  // Submission API hooks
  useGetAssignmentSubmissionQuery,
  useLazyGetAssignmentSubmissionQuery,
  useSubmitAssignmentMutation,
  // User API hooks (useGetUserByIdQuery already exported above)
  useLazyGetUserByIdQuery,
  useGetCalendarEventsQuery,
} = APIConnect;
export const {
  useGetClassRoomTokenQuery,
  useGetBreakoutRoomTokenQuery,
  useGetBreakoutRoomListQuery,
  useCreateBreakoutRoomMutation,
  useDeleteBreakoutRoomMutation,
  
} = liveKitApi;
export default APIConnect;
