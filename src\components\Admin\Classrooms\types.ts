export interface Classroom {
  id: string;
  name: string;
  description: string;
  courseId: string;
  schoolId: string;
  teacherIds: string[];
  studentIds: string[];
  createdOn: string;
}

export interface ClassCreationRequest {
  name: string;
  description: string;
  courseId: string;
  schoolId: string;
  teacherIds?: string[];
  studentIds?: string[];
  createdOn: string;
}

export interface ClassroomEditorProps {
  classroom?: Classroom;
  onSave: () => void;
  onCancel: () => void;
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  userRoleId: number;
}
