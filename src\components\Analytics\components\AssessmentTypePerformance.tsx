import React from 'react';
import { motion } from 'framer-motion';

// Animation variants
const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.5 } },
};

// Define the performance levels
export type PerformanceLevel = 'Good' | 'Average' | 'Poor';

// Define interfaces for the data structure
interface AssessmentType {
  name: string;
  level: PerformanceLevel;
}

interface TestResult {
  name: string;
  level: PerformanceLevel;
}

interface AssessmentTypePerformanceProps {
  className?: string;
}

// Map performance levels to colors
const performanceColorMap: Record<PerformanceLevel, string> = {
  'Good': 'bg-[#97C48A] text-[#2D2D2D]',
  'Average': 'bg-[#FFDE78] text-[#2D2D2D]',
  'Poor': 'bg-[#FE5B50] text-white',
};

// Sample data for assessments in the exact order shown in the image
const assessments: AssessmentType[] = [
  { name: 'Assessment-A', level: 'Good' },
  { name: 'Assessment-B', level: 'Good' },
  { name: 'Assessment-C', level: 'Average' },
  { name: 'Assessment-D', level: 'Average' },
  { name: 'Assessment-E', level: 'Poor' },
  { name: 'Assessment-F', level: 'Poor' },
];

// Sample data for tests by column as shown in the image
const testColumns: TestResult[][] = [
  // Column 1 - Mostly A-Tests (Good)
  [
    { name: 'A-Test', level: 'Good' },
    { name: 'A-Test', level: 'Good' },
    { name: 'A-Test', level: 'Good' }
  ],
  // Column 2 - Mostly B-Tests (Average)
  [
    { name: 'B-Test', level: 'Average' },
    { name: 'B-Test', level: 'Average' },
    { name: 'B-Test', level: 'Average' }
  ],
  // Column 3 - Mostly C-Tests (Poor)
  [
    { name: 'C-Test', level: 'Poor' },
    { name: 'C-Test', level: 'Average' },
    { name: 'A-Test', level: 'Good' }
  ],
  // Column 4 - Mostly A-Tests (Good)
  [
    { name: 'A-Test', level: 'Good' },
    { name: 'A-Test', level: 'Good' },
    { name: 'A-Test', level: 'Good' }
  ],
  // Column 5 - Mostly B-Tests (Average)
  [
    { name: 'B-Test', level: 'Average' },
    { name: 'B-Test', level: 'Average' },
    { name: 'A-Test', level: 'Good' }
  ],
  // Column 6 - Mostly C-Tests (Poor)
  [
    { name: 'C-Test', level: 'Poor' },
    { name: 'C-Test', level: 'Average' },
    { name: 'C-Test', level: 'Average' }
  ],
];

// Reusable box component with consistent dimensions
const AssessmentBox: React.FC<{ text: string; level: PerformanceLevel; isHeader?: boolean }> = ({ 
  text, 
  level,
  isHeader = false 
}) => {
  return (
    <div 
      className={`
        rounded-[4px] h-8 sm:h-10 min-w-[80px] sm:min-w-[90px] flex items-center justify-center
        ${isHeader ? 'px-2 sm:px-3' : 'px-1 sm:px-2'} 
        text-[10px] sm:text-xs md:text-sm font-medium whitespace-nowrap overflow-hidden text-ellipsis
        ${performanceColorMap[level]}
      `}
    >
      {text}
    </div>
  );
};

const AssessmentTypePerformance: React.FC<AssessmentTypePerformanceProps> = ({ className = '' }) => {
  return (
    <motion.div
      className={`bg-white rounded-2xl border border-[#97C48A] shadow-sm p-4 sm:p-6 flex flex-col sm:flex-row ${className}`}
      variants={fadeIn}
      initial="initial"
      animate="animate"
    >
      {/* Main content - header and assessment boxes */}
      <div className="w-full sm:w-[90%] sm:pr-4">
        <h3 className="text-lg font-semibold text-[#2D2D2D] mb-3 sm:mb-6">Assessment Type Performance</h3>
        
        {/* Scrollable container for mobile */}
        <div className="overflow-x-auto pb-2 -mx-4 px-4 sm:overflow-visible sm:pb-0 sm:mx-0 sm:px-0">
          <div className="min-w-[600px] sm:min-w-0">
            {/* Assessment types header */}
            <div className="grid grid-cols-6 gap-2 mb-4">
              {assessments.map((assessment, index) => (
                <AssessmentBox 
                  key={index}
                  text={assessment.name}
                  level={assessment.level}
                  isHeader={true}
                />
              ))}
            </div>
            
            {/* Test results grid */}
            <div className="space-y-3">
              {/* Generate 3 rows of tests */}
              {[0, 1, 2].map(rowIndex => (
                <div key={rowIndex} className="grid grid-cols-6 gap-2">
                  {testColumns.map((column, colIndex) => (
                    <AssessmentBox
                      key={colIndex}
                      text={column[rowIndex].name}
                      level={column[rowIndex].level}
                    />
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      
      {/* Legend - horizontal on mobile, vertical on desktop */}
      <div className="flex flex-row sm:flex-col justify-center items-start gap-4 sm:gap-0 mt-4 sm:mt-0 sm:w-[10%] sm:pl-4 sm:ml-4 p-2 pt-4 sm:pt-2">
        {Object.entries(performanceColorMap).map(([level, colorClass]) => (
          <div key={level} className="flex items-center sm:mb-4 w-auto sm:w-full">
            <div className={`w-4 h-4 rounded-[4px] ${colorClass.split(' ')[0]}`}></div>
            <span className="ml-3 text-sm text-gray-700">{level}</span>
          </div>
        ))}
      </div>
    </motion.div>
  );
};

export default AssessmentTypePerformance;
