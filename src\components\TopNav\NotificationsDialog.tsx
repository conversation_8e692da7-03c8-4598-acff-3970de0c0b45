import React, { useEffect, useState } from 'react';
import { Bell } from 'lucide-react';
import {
    <PERSON><PERSON>,
    Dialog<PERSON>ontent,
    Di<PERSON>Header,
    <PERSON><PERSON><PERSON><PERSON>le,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useGetSchoolNotificationsQuery } from '@/APIConnect';
import { Badge } from '@/components/ui/badge';

interface Notification {
    id: string;
    senderId: string;
    schoolId: string;
    notifyString: string;
    autoGenerated: boolean;
}

export function NotificationsDialog() {
    const [isOpen, setIsOpen] = useState(false);
    const { data: notificationsData, refetch } = useGetSchoolNotificationsQuery({});
    const [notifications, setNotifications] = useState<Notification[]>([]);

    useEffect(() => {
        if (notificationsData?.resultObject) {
            setNotifications(notificationsData.resultObject);
        }
    }, [notificationsData]);

    // Refetch notifications every minute
    useEffect(() => {
        const interval = setInterval(() => {
            refetch();
        }, 60000);

        return () => clearInterval(interval);
    }, [refetch]);

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" size="icon" className="relative">
                    <Bell className="h-5 w-5" />
                    {notifications.length > 0 && (
                        <Badge
                            variant="destructive"
                            className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
                        >
                            {notifications.length}
                        </Badge>
                    )}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>School Notifications</DialogTitle>
                </DialogHeader>
                <ScrollArea className="h-[400px] w-full pr-4">
                    {notifications.length > 0 ? (
                        <div className="space-y-4">
                            {notifications.map((notification) => (
                                <div
                                    key={notification.id}
                                    className="flex flex-col space-y-1 rounded-lg border p-4 shadow-sm transition-colors hover:bg-muted/50"
                                >
                                    <p className="text-sm text-foreground">
                                        {notification.notifyString}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                        {notification.autoGenerated ? 'System Generated' : 'Manual Notification'}
                                    </p>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
                            <Bell className="h-8 w-8 mb-2" />
                            <p>No new notifications</p>
                        </div>
                    )}
                </ScrollArea>
            </DialogContent>
        </Dialog>
    );
}
