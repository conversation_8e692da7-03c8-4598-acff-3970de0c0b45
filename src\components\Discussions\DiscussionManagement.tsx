import React, { useState } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { usePendingModeration, useModerationStats, useUpdateLeaderboard } from '../../hooks/useDiscussions';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '../ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Alert, AlertDescription } from '../ui/alert';
import { Skeleton } from '../ui/skeleton';
import { ModerationQueue } from './ModerationQueue';
import { StudentEngagementStats } from './StudentEngagementStats';
import { RefreshCw, AlertCircle, Search, ArrowUpDown, User, MessageSquare, BarChart3 } from 'lucide-react';
import { toast } from '../../hooks/use-toast';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Progress } from '../ui/progress';
import { useCommenterStats } from '../../hooks/useCommenterStats';

// Define proper types for stats object
interface ModerationStats {
  counts?: {
    pending: number;
    approved: number;
    rejected: number;
    total: number;
  };
  averages?: {
    accuracyScore: number;
    sentimentScore: number;
    engagementScore: number;
  };
}

export function DiscussionManagement() {
  const { user } = useAuth0();
  const [timeRange, setTimeRange] = useState<'daily' | 'weekly' | 'monthly' | 'alltime'>('weekly');
  const { stats, loading: statsLoading, error: statsError, refetch: refetchStats } = useModerationStats();
  const { updateLeaderboard, loading: updatingLeaderboard } = useUpdateLeaderboard();
  const { commenters, isLoading: commentersLoading, error: commentersError } = useCommenterStats(timeRange);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'comments' | 'score'>('score');
  const [sortDir, setSortDir] = useState<'asc' | 'desc'>('desc');
  
  // Get user role
  const roleId = user!["http://learnido-app/roleId"];
  
  // Check if user is allowed to access this page
  const canAccessManagement = ['1','2','3'].includes(roleId);

  // Safely type-cast stats to our defined interface
  const typedStats = stats as ModerationStats | undefined;

  const handleUpdateLeaderboard = async () => {
    const success = await updateLeaderboard(timeRange);
    if (success) {
      toast({
        title: "Leaderboard Updated",
        description: `Successfully updated the ${timeRange} leaderboard.`,
      });
    } else {
      toast({
        title: "Update Failed",
        description: "Failed to update the leaderboard. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Filter and sort commenters
  const filteredCommenters = commenters
    ? commenters.filter((commenter:any) => 
        commenter.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        commenter.id.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : [];

  const sortedCommenters = [...filteredCommenters].sort((a, b) => {
    let result = 0;
    
    if (sortBy === 'name') {
      result = a.name.localeCompare(b.name);
    } else if (sortBy === 'comments') {
      result = a.commentCount - b.commentCount;
    } else if (sortBy === 'score') {
      result = a.averageScore - b.averageScore;
    }
    
    return sortDir === 'asc' ? result : -result;
  });

  // Toggle sort direction or change sort field
  const toggleSort = (field: 'name' | 'comments' | 'score') => {
    if (sortBy === field) {
      setSortDir(sortDir === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDir('desc'); // Default to descending when changing fields
    }
  };

  // Helper function to get initials for avatar
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Helper function to get score color class
  const getScoreColorClass = (score: number) => {
    if (score >= 8) return 'bg-green-50 text-green-700 border-green-200';
    if (score >= 6) return 'bg-amber-50 text-amber-700 border-amber-200';
    return 'bg-red-50 text-red-700 border-red-200';
  };

  if (!canAccessManagement) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          You do not have permission to access this page.
        </AlertDescription>
      </Alert>
    );
  }

  

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl space-y-8 mb-12">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Discussion Management</h1>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => refetchStats()}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {statsLoading ? (
          <>
            {[1, 2, 3].map((i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-5 w-32" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-10 w-16" />
                  <Skeleton className="h-4 w-24 mt-2" />
                </CardContent>
              </Card>
            ))}
          </>
        ) : statsError ? (
          <Alert variant="destructive" className="col-span-3">
            <AlertDescription>{statsError}</AlertDescription>
          </Alert>
        ) : (
          <>
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Pending Moderation</CardTitle>
                <CardDescription>Comments awaiting review</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold">{typedStats?.counts?.pending || 0}</p>
                <p className="text-sm text-gray-500 mt-1">
                  {(typedStats?.counts?.pending || 0) > 0 
                    ? 'Requires attention' 
                    : 'No pending items'}
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Approved Comments</CardTitle>
                <CardDescription>Total approved comments</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold">{typedStats?.counts?.approved || 0}</p>
                <p className="text-sm text-gray-500 mt-1">
                  {typedStats?.counts?.total ? 
                    `${Math.round(((typedStats.counts.approved || 0) / typedStats.counts.total) * 100)}% of total` 
                    : 'No comments yet'}
                </p>
              </CardContent>
            </Card>

            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Average Engagement</CardTitle>
                <CardDescription>Based on AI evaluation</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold">
                  {typedStats?.averages?.accuracyScore !== undefined ? 
                    `${(typedStats.averages.accuracyScore * 10).toFixed(1)}/10` 
                    : 'N/A'}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Student comment quality
                </p>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      <Tabs defaultValue="moderation" className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-8">
          <TabsTrigger value="moderation">Moderation Queue</TabsTrigger>
          <TabsTrigger value="commenters">Commenters Performance</TabsTrigger>
          <TabsTrigger value="engagement">Student Engagement</TabsTrigger>
        </TabsList>
        
        <TabsContent value="moderation" className="space-y-6">
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <ModerationQueue />
          </div>
        </TabsContent>
        
        <TabsContent value="commenters" className="space-y-6">
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
              <div className="relative w-full max-w-sm">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Search commenters..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              <div className="flex flex-wrap space-x-2">
                <Button 
                  variant={timeRange === 'daily' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange('daily')}
                >
                  Daily
                </Button>
                <Button 
                  variant={timeRange === 'weekly' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange('weekly')}
                >
                  Weekly
                </Button>
                <Button 
                  variant={timeRange === 'monthly' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange('monthly')}
                >
                  Monthly
                </Button>
                <Button 
                  variant={timeRange === 'alltime' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange('alltime')}
                >
                  All Time
                </Button>
              </div>
            </div>
            
            {commentersLoading ? (
              <div className="space-y-4">
                {[1, 2, 3, 4].map((i) => (
                  <Card key={i} className="w-full">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-4">
                        <Skeleton className="h-12 w-12 rounded-full" />
                        <div className="space-y-2 flex-1">
                          <Skeleton className="h-5 w-1/3" />
                          <Skeleton className="h-4 w-1/2" />
                        </div>
                        <Skeleton className="h-8 w-16" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : commentersError ? (
              <Alert variant="destructive">
                <AlertDescription>{commentersError}</AlertDescription>
              </Alert>
            ) : (
              <div className="overflow-hidden rounded-lg border">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[60px]">#</TableHead>
                        <TableHead className="w-[280px]">
                          <div className="flex items-center cursor-pointer" onClick={() => toggleSort('name')}>
                            <span>Commenter</span>
                            {sortBy === 'name' && (
                              <ArrowUpDown className="ml-2 h-3 w-3" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center cursor-pointer" onClick={() => toggleSort('comments')}>
                            <span>Comments</span>
                            {sortBy === 'comments' && (
                              <ArrowUpDown className="ml-2 h-3 w-3" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center cursor-pointer" onClick={() => toggleSort('score')}>
                            <span>Avg. Score</span>
                            {sortBy === 'score' && (
                              <ArrowUpDown className="ml-2 h-3 w-3" />
                            )}
                          </div>
                        </TableHead>
                        <TableHead>Score Breakdown</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {sortedCommenters.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-10 text-gray-500">
                            No commenters found for the selected time period
                          </TableCell>
                        </TableRow>
                      ) : (
                        sortedCommenters.map((commenter, index) => (
                          <TableRow key={commenter.id} className="hover:bg-gray-50">
                            <TableCell className="font-medium">{index + 1}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <Avatar>
                                  <AvatarImage src={commenter.profilePicture || ''} />
                                  <AvatarFallback>{getInitials(commenter.name)}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{commenter.name}</div>
                                  <div className="text-xs text-gray-500">{commenter.id}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <MessageSquare className="h-4 w-4 text-gray-500" />
                                <span>{commenter.commentCount}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className={getScoreColorClass(commenter.averageScore)}>
                                {commenter.averageScore.toFixed(1)}/10
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="space-y-3 max-w-xs">
                                <div>
                                  <div className="flex justify-between text-xs mb-1">
                                    <span>Engagement</span>
                                    <span>{commenter.scoreBreakdown.engagement.toFixed(1)}</span>
                                  </div>
                                  <Progress value={commenter.scoreBreakdown.engagement * 10} className="h-1" />
                                </div>
                                <div>
                                  <div className="flex justify-between text-xs mb-1">
                                    <span>Relevance</span>
                                    <span>{commenter.scoreBreakdown.relevance.toFixed(1)}</span>
                                  </div>
                                  <Progress value={commenter.scoreBreakdown.relevance * 10} className="h-1" />
                                </div>
                                <div>
                                  <div className="flex justify-between text-xs mb-1">
                                    <span>Depth</span>
                                    <span>{commenter.scoreBreakdown.depthOfThought.toFixed(1)}</span>
                                  </div>
                                  <Progress value={commenter.scoreBreakdown.depthOfThought * 10} className="h-1" />
                                </div>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="engagement" className="space-y-6">
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
              <div className="space-x-2">
                <Button 
                  variant={timeRange === 'daily' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange('daily')}
                >
                  Daily
                </Button>
                <Button 
                  variant={timeRange === 'weekly' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange('weekly')}
                >
                  Weekly
                </Button>
                <Button 
                  variant={timeRange === 'monthly' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange('monthly')}
                >
                  Monthly
                </Button>
                <Button 
                  variant={timeRange === 'alltime' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange('alltime')}
                >
                  All Time
                </Button>
              </div>
              
              <Button 
                variant="outline"
                size="sm"
                onClick={handleUpdateLeaderboard}
                disabled={updatingLeaderboard}
              >
                {updatingLeaderboard ? 'Updating...' : 'Update Leaderboard'}
              </Button>
            </div>
            
            <StudentEngagementStats timeRange={timeRange} />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default DiscussionManagement;