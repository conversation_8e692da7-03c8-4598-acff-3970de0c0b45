import { createSlice } from "@reduxjs/toolkit";
const initialState = {
  questionTypes: [
    {
      label: "Multi-Choice",
      value: "MULTICHOICE",
    },
    {
      label: "Single-Choice",
      value: "SINGLECHOICE",
    },
    {
      label: "Fill in the blanks",
      value: "TEXTINPUT",
    },
    {
      label: "Match",
      value: "MATCH",
    },
    {
      label: "True/False",
      value: "BINARY",
    },
  ],
  questionLevel: 0,
  tempQuestionLevel: 0,
  questions: {
    // For test
    // courseId: "502a207b-2e5b-424f-91b8-59f476cda012",
    // chapterId: "80054c7f-2d82-408b-aa2a-df3b9e0c2d0b",
    // topicId: "f1d5dd56-10b5-4b0d-86c9-67998cd4d904",
    courseId: "",
    chapterId: "",
    topicId: "",
    questionText: "",
    explanation: "",
    answers: [],
    questionType: "MULTICHOICE",
    correctAnswer: null,
    singleChoiceAns: "",
    questionText2: ''
  },
};

export const questionSlice = createSlice({
  initialState,
  name: "questions",
  reducers: {
    setQuestionsValue: (state, action) => {
      const key = Object.keys(action.payload)?.[0];
      state.questions[key] = action.payload[key];
    },
    setQuestionsValueMany: (state, action) => {
      Object.keys(action.payload).forEach(function (key, index) {
        state.questions[key] = action.payload[key];
      });
    },
    setQuestionLevel: (state, action) => {
      state.questionLevel = action.payload;
    },
    setTempQuestionLevel: (state, action) => {
      state.tempQuestionLevel = action.payload;
    },
    setQuestionTypeData: (state, action) => {
      state.questionTypes = action.payload;
    },
    setResetQuestionValue: (state, action) => {
      const updatedData = {
        ...state.questions,
        topicId: "",
        questionText: "",
        explanation: "",
        answers: [],
        singleChoiceAns: "",
        correctAnswer: null,
        questionText2: ''
      };
      state.questions = updatedData;
    },
  },
});

export const {
  setQuestionsValue,
  setQuestionLevel,
  setResetQuestionValue,
  setTempQuestionLevel,
  setQuestionTypeData,
  setQuestionsValueMany,
} = questionSlice.actions;

export default questionSlice;
