import { useEffect, useState, use<PERSON><PERSON>back, useMemo } from 'react';
import { useGetPracticeQuizQuery, useLazyGetPracticeQuizQuery, useLazyGetStudentQuizQuery, useSaveStudentQuizMutation } from '@/APIConnect';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import { Question, QuizState, QuestionType, QuizAnswer } from '@/types/quiz';
import { useAuth0 } from '@auth0/auth0-react';
import InteractiveMatchTheFollowing from '../match-the-following';
import QuizStartDialog from './quiz-start-dialog';
import QuizResultDialog from './quiz-result-dialog';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from "@/lib/utils";
import { <PERSON><PERSON>, <PERSON>c<PERSON>ff, <PERSON>ader2, Square } from "lucide-react";
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';


interface Quiz {
    id: string;
    title: string;
    questionSet?: any[]; // Add support for pre-fetched questions
}

// Helper component to safely render HTML content
const HTMLContent = ({ content, className }: { content: string, className?: string }) => {
    return (
        <div
            className={className}
            dangerouslySetInnerHTML={{ __html: content }}
        />
    );
};

// Media components
const MediaRenderer = ({ metadata }: { metadata: any }) => {
    if (!metadata?.url) return null;

    const { url, type } = metadata;

    switch (type) {
        case 'image':
            return (
                <div className="my-4 rounded-lg overflow-hidden">
                    <img
                        src={url}
                        alt="Question media"
                        className="w-full h-auto object-contain max-h-[400px]"
                    />
                </div>
            );
        case 'video':
            return (
                <div className="my-4 rounded-lg overflow-hidden">
                    <video
                        controls
                        className="w-full max-h-[400px]"
                    >
                        <source src={url} type="video/mp4" />
                        Your browser does not support the video tag.
                    </video>
                </div>
            );
        case 'audio':
            return (
                <div className="my-4">
                    <audio controls className="w-full">
                        <source src={url} type="audio/mpeg" />
                        Your browser does not support the audio element.
                    </audio>
                </div>
            );
        default:
            return null;
    }
};

const QUIZ_TIME_LIMIT = 30 * 60; // 30 minutes in seconds

const QuizRenderer = ({ quiz, courseId, onQuizSuccessfullyCompleted, onQuizCancelled }: { quiz: Quiz, courseId?: string, onQuizSuccessfullyCompleted: () => void, onQuizCancelled?: () => void }) => {
    const [getStudentQuiz, { data: apiQuizData, isLoading: apiLoading, error: quizLoadError }] = useLazyGetStudentQuizQuery();
    const [showStartDialog, setShowStartDialog] = useState(true);
    const [showResultDialog, setShowResultDialog] = useState(true);

    // Use pre-fetched quiz data if available, otherwise use API data - memoized to prevent infinite loops
    const quizData = useMemo(() => {
        return quiz.questionSet ? { resultObject: quiz } : apiQuizData;
    }, [quiz.questionSet, apiQuizData]);

    // Helper function to get questions from either structure - memoized to prevent infinite loops
    const getQuestions = useCallback(() => {
        return quizData?.resultObject?.questionSet || quizData?.questionSet || [];
    }, [quizData]);
    const [isLoading, setIsLoading] = useState(false);
    const [elapsedTime, setElapsedTime] = useState(0);
    const [isInitialized, setIsInitialized] = useState(false);
    const [quizState, setQuizState] = useState<QuizState>({
        status: 'pre-start',
        currentQuestion: -1,
        timeLeft: QUIZ_TIME_LIMIT,
        score: 0,
        percentage: 0,
        answers: {},
        completed: null,
        timeTaken: 0,
        questionStartTime: Date.now(),
        questionTimeTaken: {}
    });

    const [saveStudentQuiz] = useSaveStudentQuizMutation();
    const { user } = useAuth0();
    const [quizScore, setQuizScore] = useState(0);
    const [quizPercentage, setQuizPercentage] = useState(0);
    const [error, setError] = useState<string | null>(null);
    const [isProcessing, setIsProcessing] = useState(false);

    // Speech Recognition setup
    const {
        transcript,
        listening,
        resetTranscript,
        browserSupportsSpeechRecognition,
        isMicrophoneAvailable
    } = useSpeechRecognition();

    // Update elapsed time every second
    useEffect(() => {
        if (quizState.status === 'in-progress') {
            const timer = setInterval(() => {
                const timeSpent = Math.floor((Date.now() - quizState.questionStartTime) / 1000);
                setElapsedTime(timeSpent);
            }, 1000);

            return () => clearInterval(timer);
        }
    }, [quizState.status, quizState.questionStartTime]);

    useEffect(() => {
        if (transcript) {
            const questions = getQuestions();
            const currentQuestion = questions[quizState.currentQuestion];
            if (currentQuestion) {
                handleAnswerChange(transcript, 'SOUNDBASED', currentQuestion.id);
                setIsProcessing(false);
            }
        }
    }, [transcript]);

    useEffect(() => {
        if (quizData && !isInitialized) {
            // Check for questions in both possible structures
            const questions = getQuestions();
            if (!questions?.length) {
                throw new Error('No questions available for this quiz');
            }

            // Load existing student answers from multiple sources
            const existingAnswers: Record<string, QuizAnswer> = {};

            // First, try to load from localStorage (for in-progress quizzes)
            const storageKey = `quiz_answers_${quiz.id}`;
            try {
                const savedAnswers = localStorage.getItem(storageKey);
                if (savedAnswers) {
                    const parsedAnswers = JSON.parse(savedAnswers);
                    Object.keys(parsedAnswers).forEach(questionId => {
                        existingAnswers[questionId] = parsedAnswers[questionId];
                    });
                    console.log('Loaded answers from localStorage:', existingAnswers);
                }
            } catch (error) {
                console.warn('Failed to load answers from localStorage:', error);
            }

            // Then, load from question objects (this will override localStorage if present)
            questions.forEach((question: any) => {
                if (question.studentAnswer !== null && question.studentAnswer !== undefined) {
                    existingAnswers[question.id] = {
                        questionId: question.id,
                        type: question.type,
                        studentAnswer: question.studentAnswer
                    };
                    console.log(`Loaded answer from database for question ${question.id}:`, question.studentAnswer);
                }
            });

            setQuizState(prev => ({
                ...prev,
                status: 'pre-start', // Keep it at pre-start to show the start button
                currentQuestion: 0,
                timeLeft: QUIZ_TIME_LIMIT,
                answers: existingAnswers, // Load existing answers
                timeTaken: 0,
                questionStartTime: Date.now(),
                questionTimeTaken: {}
            }));
            setIsInitialized(true);
        }
    }, [quizData, isInitialized, getQuestions]);

    const updateQuestionTime = (currentQuestionId: string) => {
        const timeSpent = Math.floor((Date.now() - quizState.questionStartTime) / 1000);
        return {
            ...quizState.questionTimeTaken,
            [currentQuestionId]: (quizState.questionTimeTaken[currentQuestionId] || 0) + timeSpent
        };
    };

    const startQuiz = async () => {
        console.log('Starting quiz:', quiz.id);
        try {
            setIsLoading(true);
            setError(null);

            // If we have pre-fetched quiz data, skip API call and start quiz
            if (quiz.questionSet) {
                console.log('Using pre-fetched quiz data');
                setQuizState(prev => ({
                    ...prev,
                    status: 'in-progress',
                    questionStartTime: Date.now()
                }));
                return;
            }

            // Otherwise, fetch from API
            console.log('Fetching quiz data from API');
            if (quiz.id.endsWith("-quiz")) {
                await getStudentQuiz({ courseId: quiz.id.replace("-quiz", "").replace("topicQuiz-", ""), getPracticeQuiz: true });
            } else if (courseId) {
                console.log('Starting quiz:', quiz.id, "courseId:", courseId);
                await getStudentQuiz({ courseId: courseId ?? "" })
            }
        } catch (error) {
            console.log('Failed to start quiz:', error);
            setError(error instanceof Error ? error.message : 'Failed to start quiz');
            console.error('Failed to start quiz:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const startRecording = () => {
        if (!browserSupportsSpeechRecognition) {
            setError('Your browser does not support speech recognition.');
            return;
        }
        if (!isMicrophoneAvailable) {
            setError('Please allow microphone access to use speech recognition.');
            return;
        }
        resetTranscript();
        setIsProcessing(true);
        SpeechRecognition.startListening({ continuous: false, language: 'en-US' });
    };

    const stopRecording = () => {
        SpeechRecognition.stopListening();
        setIsProcessing(false);
    };

    // Memoize match props to prevent unnecessary re-renders
    const getMatchProps = useMemo(() => (answerObject: Record<string, string>) => {
        const leftItems = Object.keys(answerObject).map((key) => ({ id: key, text: key }));
        const rightItems = Object.values(answerObject).map((value) => ({ id: value, text: value }));
        return {
            question: "Match the following",
            leftItems,
            rightItems
        };
    }, []);

    // Memoize match handler to prevent unnecessary re-renders
    const handleMatch = useCallback((connections: { left: string; right: string }[], questionId: string) => {
        // Convert array of connections to a mapping object
        const connectionMap = connections.reduce((acc, conn) => {
            acc[conn.left] = conn.right;
            return acc;
        }, {} as Record<string, string>);
        handleAnswerChange(connectionMap, 'MATCH', questionId);
    }, []);

    // Create stable memoized onMatch callbacks for each question
    const onMatchCallbacks = useMemo(() => {
        const callbacks: Record<string, (connections: { left: string; right: string }[]) => void> = {};
        return {
            get: (questionId: string) => {
                if (!callbacks[questionId]) {
                    callbacks[questionId] = (connections: { left: string; right: string }[]) =>
                        handleMatch(connections, questionId);
                }
                return callbacks[questionId];
            }
        };
    }, [handleMatch]);

    const renderQuestion = (question: Question) => {
        const questionId = question.id;
        const currentAnswer = quizState.answers[questionId]?.studentAnswer;

        switch (question.type) {
            case 'SINGLECHOICE':
                return (
                    <RadioGroup
                        value={currentAnswer?.toString()}
                        onValueChange={(value) => handleAnswerChange(value, 'SINGLECHOICE', questionId)}
                        className="space-y-3"
                    >
                        {question.answers.map((option, index) => (
                            <div
                                key={index}
                                onClick={() => handleAnswerChange(index.toString(), 'SINGLECHOICE', questionId)}
                                className={cn(
                                    "flex items-center space-x-3 p-4 rounded-lg border transition-all",
                                    "hover:bg-accent/50 cursor-pointer",
                                    currentAnswer?.toString() === index.toString()
                                        ? "border-primary bg-primary/10"
                                        : "bg-background"
                                )}
                            >
                                <div className="flex items-center space-x-3 flex-1">
                                    <RadioGroupItem
                                        value={index.toString()}
                                        id={`option-${questionId}-${index}`}
                                        className="w-5 h-5"
                                    />
                                    <label
                                        className="text-sm font-medium leading-none cursor-pointer flex-grow"
                                        htmlFor={`option-${questionId}-${index}`}
                                    >
                                        <HTMLContent content={option.answerText} />
                                        {option.answerImageMetadata && (
                                            <MediaRenderer metadata={{ type: 'image', url: option.answerImageMetadata.resourceSource }} />
                                        )}
                                        {option.answerVideoMetadata && (
                                            <MediaRenderer metadata={{ type: 'video', url: option.answerVideoMetadata.resourceSource }} />
                                        )}
                                        {option.answerAudioMetadata && (
                                            <MediaRenderer metadata={{ type: 'audio', url: option.answerAudioMetadata.resourceSource }} />
                                        )}
                                    </label>
                                </div>
                                {currentAnswer?.toString() === index.toString() && (
                                    <motion.div
                                        initial={{ scale: 0 }}
                                        animate={{ scale: 1 }}
                                        className="w-2 h-2 bg-primary rounded-full"
                                    />
                                )}
                            </div>
                        ))}
                    </RadioGroup>
                );

            case 'MULTICHOICE':
                return question.answers.map((option, index) => (
                    <div
                        key={index}
                        onClick={() => handleAnswerChange(!(Array.isArray(currentAnswer) && currentAnswer.includes(index)), 'MULTICHOICE', questionId, index)}
                        className={cn(
                            "flex items-center space-x-3 p-4 rounded-lg border transition-all",
                            "hover:bg-accent/50 cursor-pointer",
                            Array.isArray(currentAnswer) && currentAnswer.includes(index)
                                ? "border-primary bg-primary/10"
                                : "bg-background"
                        )}
                    >
                        <div className="flex items-center space-x-3 flex-1">
                            <Checkbox
                                checked={Array.isArray(currentAnswer) && currentAnswer.includes(index)}
                                onCheckedChange={(checked) => handleAnswerChange(checked, 'MULTICHOICE', questionId, index)}
                                id={`option-${questionId}-${index}`}
                                className="w-5 h-5"
                            />
                            <label
                                className="text-sm font-medium leading-none cursor-pointer flex-grow"
                                htmlFor={`option-${questionId}-${index}`}
                            >
                                <HTMLContent content={option.answerText} />
                                {option.answerImageMetadata && (
                                    <MediaRenderer metadata={{ type: 'image', url: option.answerImageMetadata.resourceSource }} />
                                )}
                                {option.answerVideoMetadata && (
                                    <MediaRenderer metadata={{ type: 'video', url: option.answerVideoMetadata.resourceSource }} />
                                )}
                                {option.answerAudioMetadata && (
                                    <MediaRenderer metadata={{ type: 'audio', url: option.answerAudioMetadata.resourceSource }} />
                                )}
                            </label>
                        </div>
                        {Array.isArray(currentAnswer) && currentAnswer.includes(index) && (
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="w-2 h-2 bg-primary rounded-full"
                            />
                        )}
                    </div>
                ));

            case 'SOUNDBASED':
                return (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="space-y-4"
                    >
                        <div className="space-y-4">
                            <div className="flex items-center gap-4">
                                <div className="flex-1 p-3 bg-muted rounded-lg">
                                    {currentAnswer ? (
                                        <p className="text-sm">{currentAnswer}</p>
                                    ) : (
                                        <p className="text-sm text-muted-foreground">
                                            {listening ? "Listening..." : isProcessing ? "Processing..." : "Click record to speak your answer"}
                                        </p>
                                    )}
                                </div>
                                <div className="flex gap-2">
                                    {isProcessing && (
                                        <motion.div
                                            initial={{ opacity: 0, scale: 0.95 }}
                                            animate={{ opacity: 1, scale: 1 }}
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            <Button
                                                type="button"
                                                variant="destructive"
                                                size="icon"
                                                onClick={() => {
                                                    setIsProcessing(false);
                                                    resetTranscript();
                                                }}
                                                className="w-10 h-10"
                                            >
                                                <Square className="h-5 w-5" />
                                            </Button>
                                        </motion.div>
                                    )}
                                    <motion.div
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        <Button
                                            type="button"
                                            variant={listening ? "destructive" : "default"}
                                            size="icon"
                                            onClick={listening ? stopRecording : startRecording}
                                            className={cn(
                                                "w-10 h-10",
                                                listening && "animate-pulse"
                                            )}
                                            disabled={isProcessing && !listening}
                                        >
                                            {listening ? (
                                                <MicOff className="h-5 w-5" />
                                            ) : isProcessing ? (
                                                <Loader2 className="h-5 w-5 animate-spin" />
                                            ) : (
                                                <Mic className="h-5 w-5" />
                                            )}
                                        </Button>
                                    </motion.div>
                                </div>
                            </div>
                        </div>
                        {currentAnswer && (
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                    resetTranscript();
                                    handleAnswerChange("", 'SOUNDBASED', questionId);
                                }}
                                className="text-xs"
                            >
                                Clear Answer
                            </Button>
                        )}
                    </motion.div>
                );

            case 'TEXTINPUT':
                return (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                    >
                        <Input
                            type="text"
                            value={currentAnswer?.toString() || ''}
                            onChange={(e) => handleAnswerChange(e.target.value, 'TEXTINPUT', questionId)}
                            placeholder="Type your answer here..."
                            className="mt-2"
                        />
                    </motion.div>
                );

            case 'BINARY':
                return (
                    <RadioGroup
                        value={currentAnswer?.toString()}
                        onValueChange={(value) => handleAnswerChange(value, 'BINARY', questionId)}
                        className="space-y-3"
                    >
                        {['true', 'false'].map((value, index) => (
                            <motion.div
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-accent cursor-pointer"
                                key={value}
                            >
                                <RadioGroupItem value={value} id={`${value}-${questionId}`} />
                                <label
                                    className="text-sm font-medium leading-none cursor-pointer flex-grow"
                                    htmlFor={`${value}-${questionId}`}
                                >
                                    {value.charAt(0).toUpperCase() + value.slice(1)}
                                </label>
                            </motion.div>
                        ))}
                    </RadioGroup>
                );

            case 'MATCH':
                const answerObject = question.correctAnswer as Record<string, string>;
                const matchProps = getMatchProps(answerObject);

                return (
                    <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                    >
                        <InteractiveMatchTheFollowing
                            {...matchProps}
                            onMatch={onMatchCallbacks.get(questionId)}
                        />
                    </motion.div>
                );
            default:
                return null;
        }
    };

    const handleAnswerChange = (value: any, type: QuestionType, questionId: string, index?: number) => {
        setQuizState(prev => {
            const updatedAnswers: Record<string, QuizAnswer> = { ...prev.answers };
            const currentAnswer: QuizAnswer = updatedAnswers[questionId] || { questionId, type, studentAnswer: undefined };

            switch (type) {
                case "SINGLECHOICE":
                    currentAnswer.studentAnswer = parseInt(value);
                    break;
                case "MULTICHOICE":
                    const currentAnswers = Array.isArray(currentAnswer.studentAnswer) ? currentAnswer.studentAnswer : [];
                    if (value && index !== undefined) {
                        currentAnswer.studentAnswer = [...currentAnswers, index];
                    } else if (index !== undefined) {
                        currentAnswer.studentAnswer = currentAnswers.filter(ans => ans !== index);
                    }
                    break;
                case "TEXTINPUT":
                case "SOUNDBASED":
                    currentAnswer.studentAnswer = value;
                    break;
                case "BINARY":
                    currentAnswer.studentAnswer = value === 'true';
                    break;
                case 'MATCH':
                    currentAnswer.studentAnswer = value;
                    break;
            }

            updatedAnswers[questionId] = currentAnswer;

            // Save to localStorage for persistence during quiz
            const storageKey = `quiz_answers_${quiz.id}`;
            try {
                localStorage.setItem(storageKey, JSON.stringify(updatedAnswers));
            } catch (error) {
                console.warn('Failed to save answer to localStorage:', error);
            }

            return { ...prev, answers: updatedAnswers };
        });
    };

    const handleSubmit = async () => {
        if (!user?.sub) {
            setError('User not authenticated');
            return;
        }

        try {
            setIsLoading(true);
            const questions = getQuestions();
            const currentQuestionId = questions[quizState.currentQuestion].id;
            const updatedTimeTaken = updateQuestionTime(currentQuestionId);
            const totalTimeTaken = Object.values(updatedTimeTaken).reduce((a: number, b: number) => a + b, 0);

            const answers = Object.values(quizState.answers)
                .filter(answer => answer.studentAnswer !== undefined)
                .map((answer) => {
                    const timeTaken = updatedTimeTaken[answer.questionId] || 0;
                    if (answer.type === 'MULTICHOICE') {
                        const question = questions.find((q: any) => q.id === answer.questionId);
                        if (question) {
                            const answerText = (answer.studentAnswer as number[]).map((index) => question.answers[index].answerText);
                            return { ...answer, studentAnswer: answerText, correctAnswer: question.correctAnswer, mark: question.mark, answers: question.answers, timeTaken };
                        }
                    } else if (answer.type === 'MATCH') {
                        const connections = answer.studentAnswer as any;
                        const connectionArray = Object.keys(connections).map((key) => {
                            return { [connections[key].left]: connections[key].right };
                        });
                        const finalAnswer: Record<string, string> = {};
                        for (let index = 0; index < connectionArray.length; index++) {
                            const element = connectionArray[index];
                            finalAnswer[Object.keys(element)[0] as string] = Object.values(element)[0] as string;
                        }
                        const question = questions.find((q: any) => q.id === answer.questionId);
                        return { ...answer, studentAnswer: finalAnswer, correctAnswer: question.correctAnswer, mark: question.mark, answers: question.answers, timeTaken: timeTaken * 1000 };
                    }
                    const question = questions.find((q: any) => q.id === answer.questionId);
                    if (question) {
                        return { ...answer, correctAnswer: question.correctAnswer, mark: question.mark, answers: question.answers, timeTaken: timeTaken * 1000 };
                    }
                    return { ...answer, timeTaken };
                });

            let response = null;
            // For pre-fetched quiz data, handle as practice quiz
            if (quizData.type === 'PRACTICE' || quiz.questionSet) {
                let score = 0;
                let totalMarks = 0;
                answers.forEach((answer) => {
                    const question = questions.find((q: any) => q.id === answer.questionId);
                    if (question) {
                        totalMarks += question.mark;
                        if (question.correctAnswer === answer.studentAnswer) {
                            score += question.mark;
                        }
                    }
                });
                const percentage = (score / totalMarks) * 100;
                response = { score, percentage, completed: percentage >= 50 };
            } else {
                // This will be handled in the database save section below
                response = { score: 0, percentage: 0, completed: false }; // Temporary response
            }

            const r = response;
            if (r?.completed === true || r?.completed === false) {
                // Save student answers back to BOTH data structures
                console.log('Current quiz state answers:', quizState.answers);
                console.log('Quiz object structure:', { hasQuestionSet: !!quiz.questionSet, hasResultObject: !!quizData?.resultObject });

                // Save to both quiz.questionSet AND quizData.resultObject.questionSet
                const saveAnswersToQuestions = (questions: any[]) => {
                    questions.forEach((question: any) => {
                        const userAnswer = quizState.answers[question.id];
                        if (userAnswer) {
                            question.studentAnswer = userAnswer.studentAnswer;
                            question.timeTaken = updatedTimeTaken[question.id] || 0;
                            console.log(`Saved answer for ${question.id}:`, userAnswer.studentAnswer);
                        }
                    });
                };

                // Save to quiz.questionSet (local copy)
                if (quiz.questionSet) {
                    console.log('Saving to quiz.questionSet');
                    saveAnswersToQuestions(quiz.questionSet);
                }

                // Save to quizData.resultObject.questionSet (API data copy)
                if (quizData?.resultObject?.questionSet) {
                    console.log('Saving to quizData.resultObject.questionSet');
                    saveAnswersToQuestions(quizData.resultObject.questionSet);
                }

                // Save answers to database using the existing saveStudentQuiz API
                try {
                    if (!quiz.questionSet) {
                        // For non-practice quizzes, use the existing API to save answers
                        const saveResponse = await saveStudentQuiz({
                            quizId: quiz.id,
                            quizzes: answers,
                            timeTaken: totalTimeTaken * 1000
                        }).unwrap();

                        console.log('Quiz answers saved to database via saveStudentQuiz API');

                        // Update the response to use the API response
                        response = saveResponse;
                    } else {
                        console.log('Practice quiz - answers saved locally only');
                    }
                } catch (error) {
                    console.error('Failed to save answers to database:', error);
                }

                // Clear localStorage since quiz is completed
                const storageKey = `quiz_answers_${quiz.id}`;
                try {
                    localStorage.removeItem(storageKey);
                    console.log('Cleared quiz answers from localStorage');
                } catch (error) {
                    console.warn('Failed to clear localStorage:', error);
                }

                // Persist the answers (saved to database)
                console.log('Quiz answers saved to both data structures and database');

                setQuizScore(r.score);
                setQuizPercentage(r.percentage);
                setQuizState(prev => ({
                    ...prev,
                    status: 'completed',
                    score: r.score,
                    percentage: r.percentage,
                    completed: r.completed,
                    timeTaken: totalTimeTaken,
                    questionTimeTaken: updatedTimeTaken
                }));

                if (r.completed === true) {
                    onQuizSuccessfullyCompleted();
                }
            } else {
                throw new Error('Invalid response from server');
            }
        } catch (err) {
            setError('Failed to submit quiz. Please try again.');
            console.error('Quiz submission error:', err);
        } finally {
            setIsLoading(false);
        }
    };

    const handleQuestionChange = (newQuestionIndex: number) => {
        const questions = getQuestions();
        const currentQuestionId = questions[quizState.currentQuestion].id;
        const updatedTimeTaken = updateQuestionTime(currentQuestionId);

        setQuizState(prev => ({
            ...prev,
            currentQuestion: newQuestionIndex,
            questionStartTime: Date.now(),
            questionTimeTaken: updatedTimeTaken
        }));
    };

    if (apiLoading || isLoading) {
        return (
            <Card className="w-full max-w-3xl mx-auto">
                <CardHeader>
                    <CardTitle className="flex items-center gap-4">
                        <span>Loading Quiz...</span>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className='flex flex-col justify-center items-center space-y-4'>
                        <Progress value={100} className="w-full animate-pulse" />
                        <img
                            src="https://lernido-bucket.s3.amazonaws.com/animations/eye_rolling.gif"
                            alt="Loading"
                            className="object-contain"
                        />
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (error) {
        return (
            <Card className="w-full max-w-3xl mx-auto">
                <CardHeader>
                    <CardTitle className="text-destructive">Error</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-destructive mb-4">{error}</p>
                    <Button onClick={() => setError(null)} variant="outline">Try Again</Button>
                </CardContent>
            </Card>
        );
    }

    if (quizState.status === 'pre-start') {
        return (
            <>
                <QuizStartDialog
                    isOpen={showStartDialog}
                    onClose={() => {
                        console.log('Quiz start dialog closed');
                        setShowStartDialog(false);
                        // Call the cancel callback to navigate to content area
                        if (onQuizCancelled) {
                            onQuizCancelled();
                        }
                    }}
                    onStartQuiz={() => {
                        setShowStartDialog(false);
                        startQuiz();
                    }}
                    quizTitle={quiz.title}
                />
                {!showStartDialog && (
                    <div className="flex items-center justify-center min-h-[400px]">
                        <p className="text-gray-500">Quiz dialog closed</p>
                    </div>
                )}
            </>
        );
    }

    if (quizState.status === 'completed') {
        const isSuccess = quizState.completed === true;
        return (
            <>
                <QuizResultDialog
                    isOpen={showResultDialog}
                    onClose={() => {
                        console.log('Quiz result dialog closed');
                        setShowResultDialog(false);
                    }}
                    onRetry={() => {
                        setShowResultDialog(false);
                        startQuiz();
                    }}
                    onNext={() => {
                        setShowResultDialog(false);
                        onQuizSuccessfullyCompleted();
                    }}
                    score={Math.round(quizScore)}
                    totalQuestions={getQuestions().length}
                    percentage={Math.round(quizPercentage)}
                    timeTaken={quizState.timeTaken}
                    passed={isSuccess}
                />
                {!showResultDialog && (
                    <div className="flex items-center justify-center min-h-[400px]">
                        <p className="text-gray-500">Quiz completed</p>
                    </div>
                )}
            </>
        );
    }

    const questions = getQuestions();
    const currentQuestion = questions[quizState.currentQuestion];

    if (!currentQuestion) {
        return (
            <Card className="w-full max-w-3xl mx-auto">
                <CardHeader>
                    <CardTitle className="text-destructive">Error</CardTitle>
                </CardHeader>
                <CardContent>
                    <p>No questions found</p>
                </CardContent>
            </Card>
        );
    }

    return (
        <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
        >
            <Card className="w-full max-w-3xl mx-auto border-none">
                <CardHeader className="space-y-4">
                    <div className="flex justify-between items-center">
                        <CardTitle className="text-2xl">Question {quizState.currentQuestion + 1}/{questions.length}</CardTitle>
                        <div className="text-sm text-muted-foreground space-x-2">
                            <span>Time spent:</span>
                            <span>
                                {Math.floor(elapsedTime / 60)}m{' '}
                                {elapsedTime % 60}s
                            </span>
                        </div>
                    </div>
                    <div className="space-y-4">
                        <HTMLContent
                            content={currentQuestion.questionText}
                            className="text-lg font-medium"
                        />
                        {currentQuestion.questionImageMetadata && (
                            <MediaRenderer metadata={{ type: "image", url: currentQuestion.questionImageMetadata.resourceSource }} />
                        )}
                        {currentQuestion.questionVideoMetadata && (
                            <MediaRenderer metadata={{ type: "video", url: currentQuestion.questionVideoMetadata.resourceSource }} />
                        )}
                        {currentQuestion.questionAudioMetadata && (
                            <MediaRenderer metadata={{
                                type: "audio",
                                url: currentQuestion.questionAudioMetadata.resourceSource
                            }} />
                        )}
                        {currentQuestion.explanation && (
                            <motion.div
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="text-sm text-muted-foreground bg-muted p-3 rounded-lg"
                            >
                                <HTMLContent content={currentQuestion.explanation} />
                            </motion.div>
                        )}
                    </div>
                    <Progress
                        value={(quizState.currentQuestion + 1) / questions.length * 100}
                        className="h-2"
                    />
                </CardHeader>
                <CardContent>
                    <AnimatePresence mode="wait">
                        <motion.div
                            key={currentQuestion.id}
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -20 }}
                            className="space-y-6"
                        >
                            {renderQuestion(currentQuestion)}
                            <div className="flex justify-end gap-3 pt-6">
                                {quizState.currentQuestion > 0 && (
                                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                                        <Button
                                            variant="outline"
                                            onClick={() => handleQuestionChange(quizState.currentQuestion - 1)}
                                        >
                                            Previous
                                        </Button>
                                    </motion.div>
                                )}
                                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                                    {quizState.currentQuestion + 1 < questions.length ? (
                                        <Button
                                            onClick={() => handleQuestionChange(quizState.currentQuestion + 1)}
                                        >
                                            Next Question
                                        </Button>
                                    ) : (
                                        <Button
                                            onClick={handleSubmit}
                                            disabled={isLoading}
                                            className="bg-green-600 hover:bg-green-700"
                                        >
                                            {isLoading ? 'Submitting...' : 'Submit Quiz'}
                                        </Button>
                                    )}
                                </motion.div>
                            </div>
                        </motion.div>
                    </AnimatePresence>
                </CardContent>
            </Card>
        </motion.div>
    );
};

export default QuizRenderer;
