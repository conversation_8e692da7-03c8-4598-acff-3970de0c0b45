import React, { useState, useEffect, useRef } from 'react';
import { sendMessageToChatbot, getChatHistory } from '@/services/chatbotService';
import { useAuth0 } from '@auth0/auth0-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { useLazyGetBookmarksQuery, useLazyGetStudentAllNotesQuery } from '@/APIConnect';
import ReactMarkdown from 'react-markdown'; // Import ReactMarkdown
import { Loader2, Loader2Icon, Send, X } from 'lucide-react'; // Import an icon for the close button

// Define types for better clarity (adjust based on actual API response)
type Bookmark = { id: string; name: string; topicId: string };
type Note = { id: string; title: string; content: string };


const ChatWindow = ({ context: parentContext, isVisible, onClose }: { context: string, isVisible: boolean, onClose: () => void }) => {
  const { user, getAccessTokenSilently } = useAuth0();
  const studentId = user?.sub?.replace("auth0|", "");
  const [messages, setMessages] = useState<{ sender: string; text: string }[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);
  const [notes, setNotes] = useState<Note[]>([]);
  const chatEndRef = useRef<HTMLDivElement>(null);

  const [getBookmarks] = useLazyGetBookmarksQuery();
  const [getNotes] = useLazyGetStudentAllNotesQuery();

  useEffect(() => {
    const fetchInitialData = async () => {
      if (!studentId) return;
      // Reset messages when becoming visible if needed, or just fetch
      // setMessages([{ sender: 'ai', text: 'Loading history...' }]); // Optional loading state
      try {
        const token = await getAccessTokenSilently();

        // Fetch History
        const history = await getChatHistory(token);
        if (history && Array.isArray(history) && history.length > 0) {
          const formattedMessages = history.flatMap((item: any) => [
            { sender: 'user', text: item.message },
            { sender: 'ai', text: item.response },
          ]);
          setMessages(formattedMessages);
        } else {
          setMessages([{ sender: 'ai', text: 'Hello! How can I help you learn today?' }]);
        }

        // Fetch Bookmarks
        const bookmarkData = await getBookmarks({ studentId }).unwrap();
        if (bookmarkData?.resultObject) setBookmarks(bookmarkData.resultObject || []);

        // Fetch Notes
        const noteData = await getNotes(studentId).unwrap();
        if (noteData?.resultObject) {
            const fetchedNotes = noteData.resultObject.flatMap((item: any) =>
                item.notes.map((note: any) => ({ ...note, id: item.id, folderId: item.folderId })) || []
            );
            setNotes(fetchedNotes);
        }

      } catch (error) {
        console.error('Error fetching initial chat data:', error);
        // Avoid overwriting existing messages if history fails but initial message is there
        if (messages.length === 0 || (messages.length === 1 && messages[0].text.includes('Loading'))) {
           setMessages([{ sender: 'ai', text: 'Sorry, I couldn\'t load previous data. How can I help?' }]);
        }
      }
    };

    if (isVisible && studentId) { // Fetch data only when visible and studentId is available
        fetchInitialData();
    }
    // Clear messages when hidden? Optional: depends on desired behavior
    // else {
    //   setMessages([]);
    // }
  }, [studentId, getAccessTokenSilently, getBookmarks, getNotes, isVisible]);


  useEffect(() => {
    // Scroll to bottom when messages change or visibility changes to true
    if (isVisible) {
        chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isVisible]);

  const sendMessage = async () => {
    if (!input.trim() || !studentId) return;

    const userMessage = { sender: 'user', text: input };
    const currentInput = input; // Capture input before clearing
    setInput('');

    // Optimistically update UI
    setMessages((prev) => {
        const updatedMessages = prev.length === 1 && (prev[0].text.includes('Hello!') || prev[0].text.includes('Sorry'))
            ? [userMessage]
            : [...prev, userMessage];
        // Add AI thinking placeholder immediately after user message
        return [...updatedMessages, { sender: 'ai', text: '...' }];
    });

    setIsLoading(true);

    // Prepare comprehensive context
    const comprehensiveContext = JSON.stringify({
      userDetails: {
        id: studentId,
        name: user?.name,
        email: user?.email,
      },
      currentViewContext: JSON.parse(parentContext || '{}'),
      studentBookmarks: bookmarks.slice(0, 5).map(b => ({ name: b.name, topicId: b.topicId })), // Limit context size
      studentNotes: notes.slice(0, 3).map(n => ({ title: n.title })), // Limit context size
    });

    const aiMessageIndex = messages.length + 1; // Index where the actual AI response will go (after user msg + placeholder)

    try {
      const token = await getAccessTokenSilently();
      const response = await sendMessageToChatbot(currentInput, comprehensiveContext, true, token);

      if (!response.body) throw new Error("Response body is null");

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let aiResponseText = '';
      let firstChunk = true;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        aiResponseText += chunk;

        setMessages((prev) => {
          const updatedMessages = [...prev];
          const targetIndex = updatedMessages.length - 1; // Always update the last message (the placeholder)
          if (updatedMessages[targetIndex]?.sender === 'ai') {
              updatedMessages[targetIndex] = {
                  sender: 'ai',
                  // Replace '...' on first chunk, append otherwise
                  text: firstChunk ? chunk : updatedMessages[targetIndex].text.replace('...', '') + chunk
              };
              firstChunk = false;
          }
          return updatedMessages;
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages((prev) => {
         const updatedMessages = [...prev];
         const targetIndex = updatedMessages.length - 1;
         // Replace the placeholder with error message
         if(updatedMessages[targetIndex]?.sender === 'ai') {
            updatedMessages[targetIndex] = { sender: 'ai', text: 'Error: Unable to fetch response.' };
         }
         return updatedMessages;
      });
    } finally {
      setIsLoading(false);
    }
  };


  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-full max-w-md h-[70vh] max-h-[600px] bg-white border rounded-lg shadow-xl flex flex-col transition-all duration-300 ease-in-out">
      {/* Header */}
      <div className="flex justify-between items-center p-3 border-b bg-gray-50 rounded-t-lg flex-shrink-0">
        <p className="text-sm font-semibold text-gray-700">AI Mentor</p>
        <Button variant="ghost" size="icon" onClick={onClose} className="h-6 w-6">
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Chat Area */}
      <ScrollArea className="flex-1 overflow-y-auto p-4">
        {messages.map((msg, index) => (
          <div
            key={index}
            className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'} mb-3`}
          >
            <div
              className={`max-w-[85%] px-3 py-2 rounded-lg shadow-sm text-sm ${
                msg.sender === 'user'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-800'
              }`}
            >
              {msg.sender === 'ai' ? (
                <ReactMarkdown
                  components={{
                    p: ({node, ...props}) => <p className="mb-1 last:mb-0" {...props} />,
                    // Add styling for other markdown elements if needed
                    // e.g., ul, ol, li, code, pre
                  }}
                >
                  {msg.text}
                </ReactMarkdown>
              ) : (
                msg.text // Render user messages as plain text
              )}
            </div>
          </div>
        ))}
        {/* No separate loading indicator needed as '...' serves that purpose */}
        <div ref={chatEndRef} />
      </ScrollArea>

      {/* Input Area */}
      <div className="flex items-center gap-2 p-3 border-t flex-shrink-0">
        <input
          type="text"
          className="flex-1 border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 transition duration-150"
          placeholder="Ask me anything..."
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && !isLoading && sendMessage()}
          disabled={isLoading}
        />
        <Button onClick={sendMessage} disabled={isLoading || !input.trim()} size="icon" className="h-9 w-9 flex-shrink-0">
          {isLoading ? (
           <Loader2Icon className="h-4 w-4 animate-spin" /> 
          ) : (
            <Send
                className="h-4 w-4 text-white"
               />
          )}
        </Button>
      </div>
    </div>
  );
};

export default ChatWindow;
