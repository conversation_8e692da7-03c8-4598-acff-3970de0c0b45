import { motion } from "framer-motion"
import { LoadingAnimation } from "../common/loading-animation"
import { TopicHeader } from "../topic/topic-header"
import UserStats from "../stats/user-stats"
import { NotificationsList } from "../notifications/notifications-list"
import { BookmarkDialog } from "../bookmarks/bookmark-dialog"
import EnhancedContentRenderer from "./enhanced-content-renderer"
import QuizRenderer from "../quiz/quiz-renderer"
import { TopicQuizPerformance } from "../quiz/topic-quiz-performance"
import EnvironmentalMessage from "../common/environmental-message"
import OfflineFeatureNotice from "../common/offline-feature-notice"
import { Topic, SubTopic, UserStats as UserStatsType } from "../../types"
import { 
    Card, 
    CardHeader, 
    CardContent 
} from "@/components/ui/card"
import { 
    CheckCircle,
    Video,
    FileAudio,
    ScrollText,
    FileText,
    FileQuestion
} from "lucide-react"
import { cn } from "@/lib/utils"

interface MainContentProps {
    isLoading: boolean
    isContentLoading: boolean
    selectedTopic: Topic | null
    selectedSubTopic: SubTopic | null
    currentCourseName: string | null
    userStats: UserStatsType
    isOnline: boolean
    bookmarkCollections: Array<{
        id: string
        bookmarkCollectionName: string
    }>
    isTopicBookmarked: boolean
    onBackToStats: () => void
    onCreateBookmarkCollection: (name: string) => Promise<void>
    onBookmark: (collectionId: string) => Promise<void>
    onUnbookmark: () => Promise<void>
    onQuizComplete: () => void
}

export function MainContent({
    isLoading,
    isContentLoading,
    selectedTopic,
    selectedSubTopic,
    currentCourseName,
    userStats,
    isOnline,
    bookmarkCollections,
    isTopicBookmarked,
    onBackToStats,
    onCreateBookmarkCollection,
    onBookmark,
    onUnbookmark,
    onQuizComplete,
}: MainContentProps) {
    console.log("selectedTopicId", selectedTopic?.id);
    console.log("selectedSubTopicId", selectedSubTopic?.id);
    if (isLoading) {
        return <LoadingAnimation type="contentLoad" />
    }

    // Display content for the selected subtopic
    if (selectedSubTopic?.id) {
        return (
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-4"
            >
                <TopicHeader
                    courseName={currentCourseName}
                    topicName={`${selectedTopic?.name} > ${selectedSubTopic.subTopicName}`}
                    onBackClick={onBackToStats}
                />
                
                <motion.div
                    layout
                    className="bg-card rounded-lg shadow-sm border transition-all duration-200"
                >
                    {isContentLoading ? (
                        <LoadingAnimation type="topicContent" />
                    ) : (
                        <div className="p-6">
                            <EnhancedContentRenderer
                                key={selectedSubTopic.id}
                                defaultTopicId={selectedSubTopic.id}
                            />
                            <EnvironmentalMessage />
                        </div>
                    )}
                </motion.div>
            </motion.div>
        )
    }

    if (selectedTopic) {
        return (
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-4"
            >
                <div className="flex items-center justify-between">
                    <TopicHeader
                        courseName={currentCourseName}
                        topicName={selectedTopic.name}
                        onBackClick={onBackToStats}
                    />
                    <BookmarkDialog
                        isOpen={false}
                        setIsOpen={() => {}}
                        isBookmarked={isTopicBookmarked}
                        collections={bookmarkCollections}
                        onCreateCollection={onCreateBookmarkCollection}
                        onBookmark={onBookmark}
                        onUnbookmark={onUnbookmark}
                    />
                </div>
                
                {selectedTopic.subTopics && selectedTopic.subTopics.length > 0 ? (
                    <div className="bg-card rounded-lg shadow-sm border p-6">
                        <h3 className="text-xl font-medium mb-4">Select a subtopic to begin</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {selectedTopic.subTopics.map(subTopic => (
                                <Card
                                    key={subTopic.id}
                                    className={cn(
                                        "hover:bg-accent/50 transition-colors cursor-pointer",
                                        subTopic.completed && "border-green-200"
                                    )}
                                    onClick={() => {
                                        // Update URL parameters
                                        const params = new URLSearchParams(window.location.search);
                                        params.set('topicId', selectedTopic.id);
                                        params.set('subTopicId', subTopic.id);
                                        window.history.replaceState(null, '', `?${params.toString()}`);
                                        
                                        // Call the handler directly if it was passed as a prop
                                        if (window.handleSubTopicSelect) {
                                            window.handleSubTopicSelect(subTopic);
                                        } else {
                                            console.error("handleSubTopicSelect function not available globally");
                                            // Fallback to page reload
                                            window.location.reload();
                                        }
                                    }}
                                >
                                    <CardHeader className="pb-2">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                {subTopic.contentType === 'video' && <Video className="h-4 w-4 text-secondary" />}
                                                {subTopic.contentType === 'audio' && <FileAudio className="h-4 w-4 text-secondary" />}
                                                {subTopic.contentType === 'scorm' && <ScrollText className="h-4 w-4 text-secondary" />}
                                                {subTopic.contentType === 'text' && <FileText className="h-4 w-4 text-secondary" />}
                                                {subTopic.contentType === 'quiz' && <FileQuestion className="h-4 w-4 text-secondary" />}
                                                <h4 className="text-sm font-medium">{subTopic.subTopicName}</h4>
                                            </div>
                                            {subTopic.completed && <CheckCircle className="h-4 w-4 text-green-500" />}
                                        </div>
                                    </CardHeader>
                                    {subTopic.content && (
                                        <CardContent className="pb-3 pt-0">
                                            <p className="text-xs text-muted-foreground line-clamp-2">{subTopic.content}</p>
                                        </CardContent>
                                    )}
                                </Card>
                            ))}
                        </div>
                    </div>
                ) : (
                    <motion.div
                        layout
                        className="bg-card rounded-lg shadow-sm border transition-all duration-200"
                    >
                        {isContentLoading ? (
                            <LoadingAnimation type="topicContent" />
                        ) : (
                            <div className="p-6">
                                <EnhancedContentRenderer
                                    key={selectedTopic.id}
                                    defaultTopicId={selectedTopic.id}
                                />
                                <EnvironmentalMessage />
                            </div>
                        )}
                    </motion.div>
                )}
            </motion.div>
        )
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-8 max-w-7xl mx-auto w-full"
        >
            {isOnline ? (
                <div className="grid gap-8">
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        <UserStats stats={userStats} />
                    </motion.div>
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                    >
                        <NotificationsList />
                    </motion.div>
                </div>
            ) : (
                <OfflineFeatureNotice message="Stats and notifications are not available offline" />
            )}
            <motion.div
                className="flex items-center justify-center gap-4 mt-24"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{
                    type: "spring",
                    stiffness: 200,
                    damping: 20,
                    delay: 0.2
                }}
            >
                <motion.img
                    src="/icons/eco-light.png"
                    alt="Eco Light"
                    className="w-12 h-12"
                    animate={{
                        scale: [1, 1.05, 1],
                        opacity: [0.8, 1, 0.8]
                    }}
                    transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                    }}
                />
                <h2 className="text-2xl font-medium text-center bg-gradient-to-r from-primary via-primary/80 to-secondary bg-clip-text text-transparent">
                    Select a topic to begin learning
                </h2>
            </motion.div>
            <EnvironmentalMessage />
        </motion.div>
    )
}
