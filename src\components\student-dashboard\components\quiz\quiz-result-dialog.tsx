import React from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';

interface QuizResultDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onRetry: () => void;
  onNext: () => void;
  score: number;
  totalQuestions: number;
  percentage: number;
  timeTaken: number; // in seconds
  passed: boolean;
}

const QuizResultDialog: React.FC<QuizResultDialogProps> = ({
  isOpen,
  onClose,
  onRetry,
  onNext,
  score,
  totalQuestions,
  percentage,
  timeTaken,
  passed
}) => {
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="p-0 border-0 bg-transparent shadow-none max-w-none fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[95vw] max-w-[769px] h-[90vh] max-h-[647px] min-h-[500px] opacity-100"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        {/* Main Dialog Container */}
        <div className="bg-white relative w-full h-full opacity-100 rounded-[40px] sm:rounded-[40px] rounded-[20px] shadow-[0px_0px_4px_0px_#00000040] overflow-hidden">
          {/* Title */}
          <div className="absolute text-black w-full px-4 sm:w-[153px] sm:px-0 h-auto sm:h-9 top-6 sm:top-12 left-1/2 sm:left-[308px] -translate-x-1/2 sm:translate-x-0  font-semibold text-xl sm:text-[28px] leading-tight sm:leading-[100%] tracking-[0%] text-black opacity-100 text-center sm:text-left">
            Quiz Ended
          </div>

          {/* Main Image */}
          <div className="absolute w-[280px] sm:w-[357px] h-[180px] sm:h-[238px] top-16 sm:top-[108px] left-1/2 sm:left-[206px] -translate-x-1/2 sm:translate-x-0 opacity-100">
            <img
              src="/icons/Quizz-assignment-icons/end.png"
              alt="Quiz End"
              className="w-full h-full object-contain"
            />
          </div>

          {/* Bottom Info Box */}
          <div className="absolute flex flex-col sm:flex-row items-center w-[calc(100%-2rem)] sm:w-[701px] h-auto sm:h-[102px] min-h-[80px] top-[260px] sm:top-[386px] left-4 sm:left-[34px] bg-[#3474681A] rounded-2xl p-3 sm:p-4 gap-3 sm:gap-2 opacity-100">
            {/* Icon */}
            <div className="flex-shrink-0 w-[50px] sm:w-[70px] h-[50px] sm:h-[70px] opacity-100">
              <img
                src="/icons/Quizz-assignment-icons/end-quiz.png"
                alt="Quiz End Icon"
                className="w-full h-full object-contain"
              />
            </div>

            {/* Text */}
            <div className="flex items-center w-full sm:w-[591px]  font-medium text-sm sm:text-lg leading-[120%] tracking-[0%] text-[#343330] opacity-100 text-center sm:text-left">
              <span>
                Great job—
                <span className="font-bold italic text-sm sm:text-lg leading-[100%] tracking-[0%] text-[#343330]">
                  {score} out of {totalQuestions}
                </span>
                {' '}correct! You can go ahead and continue making smart choices, just like we do for a better planet.
              </span>
            </div>
          </div>

          {/* Buttons Container */}
          <div className="absolute flex flex-col sm:flex-row w-[calc(100%-2rem)] sm:w-[210px] h-auto sm:h-[50px] bottom-4 sm:bottom-auto sm:top-[549px] left-4 sm:left-[511px] gap-3 sm:gap-6 opacity-100">
            {/* Back Button */}
            <button
              onClick={onRetry}
              className="w-full sm:w-[94px] h-[45px] sm:h-[50px] rounded-3xl  font-medium text-lg sm:text-xl leading-none tracking-[0%] capitalize border border-[#347468] bg-white text-[#347468] opacity-100 flex items-center justify-center cursor-pointer p-0 hover:bg-[#347468] hover:text-white transition-colors"
            >
              Back
            </button>

            {/* Next Button */}
            <button
              onClick={onNext}
              className="w-full sm:w-[92px] h-[45px] sm:h-[50px] rounded-3xl font-medium text-lg sm:text-xl leading-none tracking-[0%] capitalize bg-[#347468] border border-[#347468] text-white opacity-100 flex items-center justify-center cursor-pointer p-0 hover:bg-[#2a5d54] transition-colors"
            >
              Next
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QuizResultDialog;
