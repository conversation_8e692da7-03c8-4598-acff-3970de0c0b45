import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    ChevronRight,
    Lock,
    CloudOff,
    CheckCircle,
    Folder,
    ScrollText,
    FileText,
    BookOpen,
    FileQuestion,
    Sparkles,
    Circle,
    Video,
    Play,
    FileAudio,
    Zap,
    Star,
    Lightbulb
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Chapter, Topic, SubTopic } from '../../types';
import { Progress } from '@/components/ui/progress';
import { 
    useLazyGetContentRelatedTopicDataQuery, 
    useGetSubtopicsQuery,
    useLazyGetContentByIdQuery 
} from '@/APIConnect';
import { useOnlineStatus } from '@/hooks/use-online-status';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '@/components/ui/tooltip';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface TopicListProps {
    isLoading: boolean;
    chapters: Chapter[];
    openChapters: string[];
    openTopics: string[]; // Track which topics are expanded
    selectedSubTopic: SubTopic | null;
    onChapterToggle: (chapterId: string) => void;
    onTopicToggle: (topicId: string) => void; // Toggle topic expansion
    onSubTopicSelect: (subTopic: SubTopic) => void;
    isChapterLocked: (chapterId: string) => boolean;
    isTopicLocked: (topicId: string) => boolean;
    isSubTopicLocked: (subTopicId: string) => boolean;
}

interface CacheStatus {
    [key: string]: {
        status: 'uncached' | 'caching' | 'cached' | 'error';
        error?: string;
    };
}

const TopicList = ({
    isLoading,
    chapters,
    openChapters,
    openTopics,
    selectedSubTopic,
    onChapterToggle,
    onTopicToggle,
    onSubTopicSelect,
    isChapterLocked,
    isTopicLocked,
    isSubTopicLocked,
}: TopicListProps) => {
    // API hooks for fetching data
    const [getContentRelatedTopicData] = useLazyGetContentRelatedTopicDataQuery();
    const [getContentById] = useLazyGetContentByIdQuery();
    
    // Use specific topic for the query and skip it initially
    const [currentTopicForSubtopics, setCurrentTopicForSubtopics] = useState<string | null>(null);
    const { data: subtopicsData, isLoading: isLoadingSubtopics } = useGetSubtopicsQuery(
        currentTopicForSubtopics || '', 
        { skip: !currentTopicForSubtopics }
    );
    
    // State for storing fetched subtopics for each topic
    const [subtopicsMap, setSubtopicsMap] = useState<Record<string, SubTopic[]>>({});
    const [subtopicContentMap, setSubtopicContentMap] = useState<Record<string, any>>({});
    const [cacheStatus, setCacheStatus] = useState<CacheStatus>({});
    const isOnline = useOnlineStatus();

    // Function to fetch subtopics for a topic
    const fetchSubtopicsForTopic = (topicId: string) => {
        setCurrentTopicForSubtopics(topicId);
    };

    // Function to fetch content for a subtopic
    const fetchSubtopicContent = async (subTopicId: string) => {
        try {
            const result = await getContentById(subTopicId);
            if (result.data?.resultObject) {
                setSubtopicContentMap(prev => ({
                    ...prev,
                    [subTopicId]: result.data.resultObject
                }));
                return result.data.resultObject;
            }
        } catch (error) {
            console.error('Failed to fetch subtopic content:', error);
        }
        return null;
    };

    // Cache management functions
    const checkContentCached = async (contentUrl: string) => {
        if ('caches' in window) {
            const cache = await caches.open('learnido-scorm-v1');
            const response = await cache.match(contentUrl);
            return !!response;
        }
        return false;
    };

    const prefetchSubTopicContent = async (subTopicId: string) => {
        try {
            setCacheStatus(prev => ({
                ...prev,
                [subTopicId]: { status: 'caching' }
            }));

            // First, fetch the content information for the subtopic
            const contentData = await fetchSubtopicContent(subTopicId);
            
            if (contentData?.contentUrl) {
                const contentUrl = contentData.contentUrl;
                const isCached = await checkContentCached(contentUrl);

                if (!isCached && 'serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.ready;
                    await registration.active?.postMessage({
                        type: 'CACHE_SUBTOPIC_CONTENT',
                        subTopicId,
                        contentUrl
                    });
                }

                setCacheStatus(prev => ({
                    ...prev,
                    [subTopicId]: { status: 'cached' }
                }));
            }
        } catch (error) {
            console.error('Failed to prefetch sub-topic content:', error);
            setCacheStatus(prev => ({
                ...prev,
                [subTopicId]: {
                    status: 'error',
                    error: error instanceof Error ? error.message : 'Failed to cache content'
                }
            }));
        }
    };

    // Check cache status for opened chapters and topics
    useEffect(() => {
        const checkOpenedContent = async () => {
            for (const chapterId of openChapters) {
                const chapter = chapters.find(ch => ch.id === chapterId);
                const chapterIndex = chapters.findIndex(ch => ch.id === chapterId);
                const isFirstChapter = chapterIndex === 0;
                
                // First chapter is always accessible regardless of lock status
                if (chapter && (isFirstChapter || !isChapterLocked(chapterId))) {
                    for (const topic of chapter.topics) {
                        // Topics in first chapter are always accessible
                        const topicAccessible = isFirstChapter || !isTopicLocked(topic.id);
                        
                        if (topicAccessible && openTopics.includes(topic.id)) {
                            // Fetch subtopics if not already loaded
                            if (!subtopicsMap[topic.id]) {
                                setCurrentTopicForSubtopics(topic.id);
                            }
                            
                            // Check cache status for subtopics
                            const subTopics = subtopicsMap[topic.id] || topic.subTopics || [];
                            for (const subTopic of subTopics) {
                                // Subtopics in first chapter are always accessible
                                const subtopicAccessible = isFirstChapter || !isSubTopicLocked(subTopic.id);
                                
                                if (subtopicAccessible && !cacheStatus[subTopic.id]) {
                                    await prefetchSubTopicContent(subTopic.id);
                                }
                            }
                        }
                    }
                }
            }
        };

        if (isOnline) {
            checkOpenedContent();
        }
    }, [openChapters, openTopics, chapters, isOnline, cacheStatus, subtopicsMap, isChapterLocked, isTopicLocked, isSubTopicLocked]);

    // Effect to update subtopicsMap when data is loaded
    useEffect(() => {
        if (currentTopicForSubtopics && subtopicsData?.resultObject) {
            // Map API response to SubTopic interface
            const mappedSubtopics = subtopicsData.resultObject.map((subTopic : SubTopic) => ({
                id: subTopic.id,
                name: subTopic.subTopicName,
                contentType: mapContentType(subTopic.type ?? 0),
                completed: false, // Set default value or get from another source if available
                locked: false,    // Set default value or get from another source if available
                // Add other required properties with default values
            }));
            
            setSubtopicsMap(prev => ({
                ...prev,
                [currentTopicForSubtopics]: mappedSubtopics
            }));
        }
    }, [currentTopicForSubtopics, subtopicsData]);
    
    // Helper function to map content type numbers to strings
    const mapContentType = (typeNum: number): string => {
        // This mapping should match your application's content type definitions
        switch (typeNum) {
            case 1: return 'text';
            case 2: return 'video';
            case 3: return 'audio';
            case 4: return 'scorm';
            case 5: return 'quiz';
            default: return 'text';
        }
    };

    const renderCacheStatus = (subTopicId: string) => {
        const status = cacheStatus[subTopicId];
        if (!status) return null;

        switch (status.status) {
            case 'caching':
                return null;
            case 'cached':
                return (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <CheckCircle className="h-4 w-4 text-secondary ml-2" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Content cached for offline use</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                );
            case 'error':
                return (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <CloudOff className="h-4 w-4 text-destructive ml-2" />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>{status.error || 'Failed to cache content'}</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                );
            default:
                return null;
        }
    };

    const getChapterIcon = (chapter: Chapter) => {
        return <Folder className="h-4 w-4" />;
    };

    const getTopicIcon = (index: number) => {
        const icons = [
            <ScrollText className="h-4 w-4" />,
            <Lightbulb className="h-4 w-4" />,
            <Star className="h-4 w-4" />,
            <Zap className="h-4 w-4" />,
            <Sparkles className="h-4 w-4" />
        ];
        return icons[index % icons.length] || <ScrollText className="h-4 w-4" />;
    };
    
    const getSubTopicIcon = (subTopic: SubTopic) => {
        // Icon based on content type
        switch (subTopic.contentType) {
            case 'video': return <Video className="h-4 w-4" />;
            case 'audio': return <FileAudio className="h-4 w-4" />;
            case 'text': return <FileText className="h-4 w-4" />;
            case 'scorm': return <ScrollText className="h-4 w-4" />;
            case 'quiz': return <FileQuestion className="h-4 w-4" />;
            default: return <Circle className="h-4 w-4" />;
        }
    };

    if (isLoading) {
        return (
            <div className="space-y-4 p-4">
                {[1, 2, 3].map((i) => (
                    <Card key={i} className="p-4 space-y-3">
                        <Skeleton className="h-6 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                    </Card>
                ))}
            </div>
        );
    }

    // Helper function to calculate chapter completion based on subtopics
    const getChapterCompletion = (chapter: Chapter) => {
        // Calculate direct topic completion
        const totalTopics = chapter.topics.length;
        
        // Calculate sub-topic completion
        let totalSubTopics = 0;
        let completedSubTopics = 0;
        
        chapter.topics.forEach(topic => {
            if (topic.subTopics && topic.subTopics.length > 0) {
                totalSubTopics += topic.subTopics.length;
                completedSubTopics += topic.subTopics.filter(subTopic => subTopic.completed).length;
            }
        });
        
        // Calculate completion percentage based on sub-topics
        const completionPercentage = totalSubTopics > 0 
            ? (completedSubTopics / totalSubTopics) * 100 
            : 0;

        return {
            totalTopics,
            completedTopics: Math.round(completedSubTopics / (totalSubTopics || 1) * totalTopics),
            completionPercentage,
            isCompleted: completionPercentage === 100
        };
    };
    
    // Helper function to check if a chapter should be unlocked
    const shouldChapterBeUnlocked = (chapter: Chapter, index: number): boolean => {
        // First chapter is always unlocked
        if (index === 0) return true;
        
        // Get the previous chapter
        const previousChapter = chapters[index - 1];
        if (!previousChapter) return false;
        
        // Check if previous chapter is completed
        const previousChapterCompletion = getChapterCompletion(previousChapter);
        return previousChapterCompletion.isCompleted;
    };

    return (
        <div className="space-y-3">
            {chapters.map((chapter, chapterIndex) => {
                const hasTopics = chapter.topics && chapter.topics.length > 0;
                // Override the locked status based on our progression logic
                const isChapterUnlocked = shouldChapterBeUnlocked(chapter, chapterIndex);
                const isLocked = isChapterLocked(chapter.id) || !isChapterUnlocked;
                const isChapterOpen = openChapters.includes(chapter.id);
                const completion = getChapterCompletion(chapter);
                
                return (
                    <Card
                        key={chapter.id}
                        className={cn(
                            'overflow-hidden transition-colors',
                            isLocked && 'opacity-60'
                        )}
                    >
                        <button
                            className={cn(
                                'w-full px-4 py-3 flex items-center justify-between',
                                'hover:bg-muted/50 transition-colors',
                                isLocked && 'cursor-not-allowed'
                            )}
                            onClick={() => !isLocked && onChapterToggle(chapter.id)}
                            disabled={isLocked}
                        >
                            <div className="flex items-start gap-3">
                                <div className="bg-secondary text-secondary-foreground rounded-lg p-2">
                                    {getChapterIcon(chapter)}
                                </div>
                                <div className="text-left">
                                    <h3 className="text-sm font-medium">{chapter.name}</h3>
                                    {hasTopics && (
                                        <p className="text-xs text-muted-foreground mt-1">
                                            {chapter.topics.length} topics • {Math.round(completion.completionPercentage)}% complete
                                        </p>
                                    )}
                                </div>
                            </div>
                            <div className="flex items-center">
                                {isLocked && <Lock className="h-4 w-4 text-muted-foreground mr-2" />}
                                <ChevronRight className={cn(
                                    "h-4 w-4 transition-transform",
                                    isChapterOpen && "rotate-90"
                                )} />
                            </div>
                        </button>

                        <AnimatePresence>
                            {hasTopics && isChapterOpen && !isLocked && (
                                <motion.div
                                    initial={{ height: 0 }}
                                    animate={{ height: "auto" }}
                                    exit={{ height: 0 }}
                                    transition={{ duration: 0.2 }}
                                    className="overflow-hidden border-t"
                                >
                                    <div className="bg-muted/30 py-1">
                                        {chapter.topics.map((topic, topicIndex) => {
                                            // Override the locked status for topics
                                            const isFirstChapter = chapterIndex === 0;
                                            const baseLocked = isTopicLocked(topic.id);
                                            // Topics in first chapter are always unlocked 
                                            // Topics in other chapters follow the original locking logic
                                            const topicIsLocked = isFirstChapter ? false : baseLocked;
                                            
                                            const isTopicOpen = openTopics.includes(topic.id);
                                            // Check both API-fetched subtopics and props
                                            const apiSubTopics = subtopicsMap[topic.id] || [];
                                            const hasSubTopics = (apiSubTopics.length > 0) || (topic.subTopics && topic.subTopics.length > 0);
                                            // Log for debugging
                                            console.log('Topic:', topic.name, 'API subtopics:', apiSubTopics.length, 'Prop subtopics:', topic.subTopics?.length || 0);
                                            
                                            return (
                                                <div key={topic.id} className="px-4 py-1">
                                                    <button
                                                        className={cn(
                                                            'w-full flex items-center justify-between px-2 py-2 rounded-md',
                                                            'hover:bg-muted/50 transition-colors',
                                                            topicIsLocked && 'opacity-50 cursor-not-allowed'
                                                        )}
                                                        onClick={() => !topicIsLocked && onTopicToggle(topic.id)}
                                                        disabled={topicIsLocked}
                                                    >
                                                        <div className="flex items-center gap-2">
                                                            {getTopicIcon(topicIndex)}
                                                            <span className="text-sm">{topic.name}</span>
                                                        </div>
                                                        <div className="flex items-center">
                                                            {topicIsLocked ? (
                                                                <Lock className="h-4 w-4 text-muted-foreground mr-2" />
                                                            ) : topic.completed && (
                                                                <CheckCircle className="h-4 w-4 text-secondary mr-2" />
                                                            )}
                                                            <ChevronRight className={cn(
                                                                "h-4 w-4 transition-transform",
                                                                isTopicOpen && "rotate-90"
                                                            )} />
                                                        </div>
                                                    </button>

                                                    <AnimatePresence>
                                                        {hasSubTopics && isTopicOpen && !topicIsLocked && (
                                                            <motion.div
                                                                initial={{ height: 0 }}
                                                                animate={{ height: "auto" }}
                                                                exit={{ height: 0 }}
                                                                transition={{ duration: 0.15 }}
                                                                className="overflow-hidden pl-6 mt-1 space-y-0.5"
                                                            >
                                                                {/* Show loading state when fetching subtopics */}
                                                                {isLoadingSubtopics && currentTopicForSubtopics === topic.id && (
                                                                    <div className="py-2 flex items-center justify-center">
                                                                        <Skeleton className="h-5 w-5 rounded-full animate-pulse" />
                                                                        <Skeleton className="h-4 w-24 ml-2" />
                                                                    </div>
                                                                )}
                                                                
                                                                {/* Render subtopics from API if available, otherwise fall back to props */}
                                                                {(subtopicsMap[topic.id] || topic.subTopics || []).map((subTopic) => {
                                                                    // Override the locked status for subtopics
                                                                    const baseSubTopicLocked = isSubTopicLocked(subTopic.id);
                                                                    // Subtopics in first chapter are always unlocked
                                                                    const subTopicIsLocked = isFirstChapter ? false : baseSubTopicLocked;
                                                                    
                                                                    const isSelected = selectedSubTopic?.id === subTopic.id;
                                                                    // Get content type from our mapped data or from the original content information
                                                                    const contentType = subTopic.contentType || 
                                                                        (subtopicContentMap[subTopic.id]?.type && mapContentType(subtopicContentMap[subTopic.id].type));
                                                                    
                                                                    // For new API response format, we might need to use different property names
                                                                    const subtopicName = subTopic.subTopicName || subTopic!.subTopicName;

                                                                    return (
                                                                        <Button
                                                                            key={subTopic.id}
                                                                            variant={isSelected ? "secondary" : "ghost"}
                                                                            className={cn(
                                                                                'w-full justify-start gap-2 px-2 py-1.5 h-auto text-sm',
                                                                                subTopicIsLocked && 'opacity-50 cursor-not-allowed'
                                                                            )}
                                                                            onClick={() => {
                                                                                if (!subTopicIsLocked) {
                                                                                    // Fetch content if not already loaded
                                                                                    if (!subtopicContentMap[subTopic.id]) {
                                                                                        fetchSubtopicContent(subTopic.id);
                                                                                    }
                                                                                    onSubTopicSelect(subTopic);
                                                                                }
                                                                            }}
                                                                            disabled={subTopicIsLocked}
                                                                        >
                                                                            {getSubTopicIcon({...subTopic, contentType})}
                                                                            <span className="truncate flex-1">{subtopicName}</span>
                                                                            {renderCacheStatus(subTopic.id)}
                                                                            {subTopicIsLocked ? (
                                                                                <Lock className="h-3.5 w-3.5 text-muted-foreground" />
                                                                            ) : subTopic.completed && (
                                                                                <CheckCircle className="h-3.5 w-3.5 text-secondary" />
                                                                            )}
                                                                        </Button>
                                                                    );
                                                                })}
                                                            </motion.div>
                                                        )}
                                                    </AnimatePresence>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </motion.div>
                            )}
                        </AnimatePresence>                        </Card>
                );
            })}
        </div>
    );
};

export default TopicList;
