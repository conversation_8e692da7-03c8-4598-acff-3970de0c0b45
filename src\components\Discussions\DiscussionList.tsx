import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useAuth0 } from '@auth0/auth0-react';
import { useDiscussions, useCreateDiscussion } from '../../hooks/useDiscussions';

import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../ui/dialog';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Skeleton } from '../ui/skeleton';
import { Alert, AlertDescription } from '../ui/alert';
import { MessageCircle, ThumbsUp, Clock, Plus, AlertCircle } from 'lucide-react';
import { toast } from '../../hooks/use-toast';
import { formatDistanceToNow } from 'date-fns';
import { CustomPagination } from './CustomPagination';

export function DiscussionList() {
  const navigate = useNavigate();
  const location = useLocation();
  const { courseId, classId, topicId } = useParams();
  const { user } = useAuth0();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [newDiscussion, setNewDiscussion] = useState({ title: '', description: '' });
  const [initialPage] = useState(1);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const { createDiscussion, loading: creating } = useCreateDiscussion();
  
  // Get user role from Auth0
  const userRole = user?.["http://learnido-app/roleId"];
  console.log('User Role:', userRole);
  const isTeacher = userRole === "3";
  const isSchoolAdmin = userRole === "2";
  const isAdmin = userRole === "1";
  
  // Check if user can create discussions (non-student roles)
  const canCreateDiscussion = ['1', '2', '3'].includes(userRole || '');

  // Determine the context based on path
  const isTeacherPath = location.pathname.includes('/teacher/discussions');
  const isSchoolAdminPath = location.pathname.includes('/school-admin/discussions');
  
  // Extract school ID for school admin and teacher
  const schoolId = user?.["http://learnido-app/schoolId"];
  
  // Customize params based on user role and path
  const customParams = {
    page: currentPage,
    limit: 20,
    courseId,
    classId,
    topicId,
    // Filter by school for school admin and teacher
    ...(isSchoolAdmin || isTeacher ? { schoolId } : {}),
    // Only show discussions that need moderation for admin views
    ...(isTeacherPath || isSchoolAdminPath ? { requiresModeration: true } : {})
  };
  
  const { discussions, pagination, loading, error, refetch } = useDiscussions(
    currentPage,
    20,
    customParams
  );

  const handleOpenDiscussion = (id: string) => {
    navigate(`/discussions/${id}`);
  };

  const handleCreateDiscussion = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newDiscussion.title.trim() || !newDiscussion.description.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }
    
    const result = await createDiscussion({
      ...newDiscussion,
      courseId,
      classId,
      topicId,
      ...(schoolId ? { schoolId } : {})
    });
    
    if (result) {
      toast({
        title: "Success",
        description: "Discussion created successfully!",
      });
      setDialogOpen(false);
      setNewDiscussion({ title: '', description: '' });
      refetch();
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    refetch(page);
  };

  // Define page title based on path
  let pageTitle = "Discussions";
  if (isTeacherPath) {
    pageTitle = "Classroom Discussions";
  } else if (isSchoolAdminPath) {
    pageTitle = "School Discussions";
  } else if (courseId) {
    pageTitle = "Course Discussions";
  } else if (classId) {
    pageTitle = "Classroom Discussions";
  } else if (topicId) {
    pageTitle = "Topic Discussions";
  }

  if (loading && discussions && discussions.length === 0) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">{pageTitle}</h1>
          <Skeleton className="h-10 w-40" />
        </div>
        
        <div className="grid gap-4 md:grid-cols-2">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <Skeleton className="h-5 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent className="pb-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full mt-2" />
              </CardContent>
              <CardFooter>
                <Skeleton className="h-4 w-24 mr-2" />
                <Skeleton className="h-4 w-24" />
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">{pageTitle}</h1>
          <p className="text-gray-500">
            {isTeacherPath ? 'Manage classroom discussions' :
             isSchoolAdminPath ? 'Manage school discussions' :
             courseId ? 'Course Discussions' : 
             classId ? 'Classroom Discussions' : 
             topicId ? 'Topic Discussions' : 'All Discussions'}
          </p>
        </div>
        
        {canCreateDiscussion && (
          <Button onClick={() => setDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Discussion
          </Button>
        )}
      </div>

      {discussions?.length === 0 ? (
        <Card className="text-center py-12 my-8">
          <CardContent>
            <div className="flex flex-col items-center justify-center text-gray-500">
              <MessageCircle className="w-16 h-16 mb-4" />
              <h3 className="text-xl font-medium">No discussions yet</h3>
              <p className="mt-2">
                {canCreateDiscussion 
                  ? 'Start a discussion to get the conversation going!' 
                  : 'There are no discussions available at the moment.'}
              </p>
              {canCreateDiscussion && (
                <Button 
                  onClick={() => setDialogOpen(true)} 
                  variant="outline"
                  className="mt-4"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Start Discussion
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {discussions?.map((discussion) => (
              <Card 
                key={discussion.id} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleOpenDiscussion(discussion.id)}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="text-xl">{discussion.title}</CardTitle>
                  <CardDescription>
                    {discussion.createdBy}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <p className="line-clamp-2 text-gray-600">
                    {discussion.description}
                  </p>
                </CardContent>
                <CardFooter className="text-sm text-gray-500 justify-between">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <MessageCircle className="h-4 w-4 mr-1" />
                      {discussion.commentCount}
                    </span>
                    <span className="flex items-center">
                      <ThumbsUp className="h-4 w-4 mr-1" />
                      {discussion.voteCount}
                    </span>
                  </div>
                  <span className="flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    {formatDistanceToNow(new Date(discussion.createdAt), { addSuffix: true })}
                  </span>
                </CardFooter>
              </Card>
            ))}
          </div>

          {pagination && pagination.pages > 1 && (
            <div className="flex justify-center mt-8">
              <CustomPagination
                currentPage={pagination.page}
                totalPages={pagination.pages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}

      {/* Create Discussion Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <form onSubmit={handleCreateDiscussion}>
            <DialogHeader>
              <DialogTitle>Create New Discussion</DialogTitle>
              <DialogDescription>
                Start a new discussion topic here. Click save when you're done.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  placeholder="Enter a descriptive title"
                  value={newDiscussion.title}
                  onChange={(e) => setNewDiscussion({ ...newDiscussion, title: e.target.value })}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Provide details about your discussion topic"
                  value={newDiscussion.description}
                  onChange={(e) => setNewDiscussion({ ...newDiscussion, description: e.target.value })}
                  required
                  className="min-h-[120px]"
                />
              </div>
            </div>
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={creating}>
                {creating ? 'Creating...' : 'Create Discussion'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}