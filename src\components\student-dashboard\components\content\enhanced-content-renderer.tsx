import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import Scorm<PERSON>esson from '@/components/scorm-content';
import { useLazyGetContentByIdQuery, useLazyGetContentRelatedTopicDataQuery } from '@/APIConnect';
import { Loader2Icon } from 'lucide-react';
import { useOnlineStatus } from '@/hooks/use-online-status';

interface EnhancedContentRendererProps {
    defaultTopicId: string | null;
}

const EnhancedContentRenderer = ({ defaultTopicId }: EnhancedContentRendererProps) => {
    const [getContentRelatedTopicData, getContentRelatedTopicDataResult] =
        useLazyGetContentByIdQuery();
    
    // Get content data from Redux store
    const contentRelatedTopic = useSelector((state: any) => state.courses.contentRelatedTopic);
    
    const [isCaching, setIsCaching] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const isOnline = useOnlineStatus();
    
    // Define content type mapping to match backend API
    // Backend enum: html=1, audio=2, video=3, scorm=4, document=5, livelecture=6
    const mapContentTypeFromNumber = (typeNum: number): string => {
        switch(typeNum) {
            case 1: return 'HTML'; // html = 1
            case 2: return 'AUDIO_URL'; // audio = 2
            case 3: return 'VIDEO_URL'; // video = 3
            case 4: return 'SCORM'; // scorm = 4
            case 5: return 'DOCUMENT'; // document = 5
            case 6: return 'HTML'; // livelecture = 6 (mapping to HTML for now)
            default: return 'HTML';
        }
    };

    // Cache the content URL in the service worker
    const cacheContent = async (contentUrl: string, contentId: string) => {
        if ('serviceWorker' in navigator && contentUrl) {
            try {
                setIsCaching(true);
                const registration = await navigator.serviceWorker.ready;
                
                // Determine whether this is a topic or subtopic based on context
                const messageType = defaultTopicId?.includes('subtopic-') 
                    ? 'CACHE_SUBTOPIC_CONTENT' 
                    : 'CACHE_TOPIC_CONTENT';
                
                await registration.active?.postMessage({
                    type: messageType,
                    topicId: contentId,
                    subTopicId: contentId,
                    contentUrl
                });
                console.log('Content cached successfully');
            } catch (error) {
                console.error('Failed to cache content:', error);
            } finally {
                setIsCaching(false);
            }
        }
    };

    // Check if we have content in Redux store
    const hasReduxContent = contentRelatedTopic && 
                           Object.keys(contentRelatedTopic).length > 0 && 
                           (contentRelatedTopic.content || contentRelatedTopic.contentUrl ||
                           (contentRelatedTopic.currentSubTopic && 
                           (contentRelatedTopic.currentSubTopic.content || contentRelatedTopic.currentSubTopic.contentUrl)));

    // Fetch content from API if not in Redux
    useEffect(() => {
        if (!hasReduxContent && defaultTopicId) {
            setIsLoading(true);
            console.log('Fetching content from API for:', defaultTopicId);
            getContentRelatedTopicData(defaultTopicId)
                .finally(() => setIsLoading(false));
        }
    }, [defaultTopicId, hasReduxContent]);

    // Render content from Redux store
    const renderReduxContent = () => {
        // Check if we're rendering a subtopic, and if so, get its content from the currentSubTopic property
        const isSubTopic = defaultTopicId?.includes('subtopic-') || contentRelatedTopic.currentSubTopic;

        // Log for debugging
        console.log("Rendering content for:", isSubTopic ? "SubTopic" : "Topic", defaultTopicId);
        console.log("Redux content structure:",
            isSubTopic ? "Using subtopic content" : "Using topic content",
            isSubTopic ? contentRelatedTopic.currentSubTopic : contentRelatedTopic);

        // Get the appropriate content source based on whether it's a subtopic or topic
        const contentSource = isSubTopic && contentRelatedTopic.currentSubTopic
            ? contentRelatedTopic.currentSubTopic
            : contentRelatedTopic;

        const type = contentSource.type || 0;
        const contentType = mapContentTypeFromNumber(type);
        const data = {
            content: contentSource.content,
            contentUrl: contentSource.contentUrl
        };

        switch (contentType) {
            case 'SCORM':
                // SCORM URL can be in either content field (from upload endpoint) or contentUrl field
                const scormUrl = data.content || data.contentUrl;
                return (
                    <div>
                        {isCaching && (
                            <div className="mb-4 flex items-center justify-center text-primary">
                                <Loader2Icon className="w-4 h-4 animate-spin mr-2" />
                                <span className="text-sm">Caching content for offline use...</span>
                            </div>
                        )}
                        {scormUrl ? (
                            <ScormLesson url={scormUrl} />
                        ) : (
                            <div className="flex items-center justify-center h-[400px] bg-gray-100 rounded-lg">
                                <p className="text-gray-600">No SCORM content available</p>
                            </div>
                        )}
                    </div>
                );
            case 'DOCUMENT':
                // Document rendering (PDF, DOC, etc.) - URL comes from content or contentUrl field
                const documentUrl = data.content || data.contentUrl;
                return (
                    <div className="w-full h-full bg-white relative overflow-hidden">
                        {documentUrl ? (
                            <>
                                <object
                                    data={`${documentUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`}
                                    type="application/pdf"
                                    className="w-full h-full"
                                    style={{
                                        height: 'calc(100vh - 120px)',
                                        minHeight: '800px',
                                        border: 'none',
                                        outline: 'none'
                                    }}
                                    onContextMenu={(e) => e.preventDefault()}
                                >
                                    <iframe
                                        src={`${documentUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`}
                                        className="w-full h-full border-0"
                                        title="Document Content"
                                        style={{
                                            height: 'calc(100vh - 120px)',
                                            minHeight: '800px',
                                            border: 'none',
                                            outline: 'none'
                                        }}
                                        onContextMenu={(e) => e.preventDefault()}
                                        sandbox="allow-same-origin allow-scripts"
                                        scrolling="auto"
                                        frameBorder="0"
                                    />
                                </object>
                                <style dangerouslySetInnerHTML={{
                                    __html: `
                                        object[type="application/pdf"] {
                                            -webkit-appearance: none;
                                            -moz-appearance: none;
                                            appearance: none;
                                        }
                                        iframe {
                                            -webkit-appearance: none;
                                            -moz-appearance: none;
                                            appearance: none;
                                        }
                                    `
                                }} />
                            </>
                        ) : (
                            <div className="flex items-center justify-center h-[400px] bg-gray-100 rounded-lg">
                                <p className="text-gray-600">No document content available</p>
                            </div>
                        )}
                    </div>
                );
            case 'HTML':
                return <div dangerouslySetInnerHTML={{ __html: data.content }} />;
            case 'VIDEO_URL':
                if (!isOnline && data.contentUrl?.includes('youtube')) {
                    return (
                        <div className="flex flex-col items-center justify-center h-[600px] bg-gray-100 rounded-lg">
                            <p className="text-lg text-gray-600">
                                YouTube videos are not available offline
                            </p>
                        </div>
                    );
                }
                if (data.contentUrl?.includes('youtube')) {
                    const videoId = data.contentUrl.split('v=')[1] || 
                                    data.contentUrl.split('embed/')[1];
                    return (
                        <iframe
                            src={`https://www.youtube.com/embed/${videoId}`}
                            className="w-full h-[600px] rounded-lg shadow-md"
                            title="learning content"
                            allowFullScreen
                        />
                    );
                }
                return (
                    <video
                        src={data.contentUrl}
                        controls
                        className="w-full h-[600px] rounded-lg shadow-md"
                        title="learning content"
                    />
                );
            case 'AUDIO_URL':
                return <audio src={data.contentUrl} controls className="w-full" />;
            default:
                return <p>Unsupported content type</p>;
        }
    };

    // Render content from API
    const renderApiContent = () => {
        const resultObject = getContentRelatedTopicDataResult.data?.resultObject;
        if (!resultObject) return null;

        const type = resultObject.type || 0;
        const contentType = mapContentTypeFromNumber(type);

        switch (contentType) {
            case 'SCORM':
                // SCORM URL can be in either content field (from upload endpoint) or contentUrl field
                const scormUrl = resultObject.content || resultObject.contentUrl;
                return (
                    <div>
                        {isCaching && (
                            <div className="mb-4 flex items-center justify-center text-primary">
                                <Loader2Icon className="w-4 h-4 animate-spin mr-2" />
                                <span className="text-sm">Caching content for offline use...</span>
                            </div>
                        )}
                        {scormUrl ? (
                            <ScormLesson url={scormUrl} />
                        ) : (
                            <div className="flex items-center justify-center h-[400px] bg-gray-100 rounded-lg">
                                <p className="text-gray-600">No SCORM content available</p>
                            </div>
                        )}
                    </div>
                );
            case 'DOCUMENT':
                // Document rendering (PDF, DOC, etc.) - URL comes from content or contentUrl field
                const documentUrl = resultObject.content || resultObject.contentUrl;
                return (
                    <div className="w-full h-full bg-white relative overflow-hidden">
                        {documentUrl ? (
                            <>
                                <object
                                    data={`${documentUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`}
                                    type="application/pdf"
                                    className="w-full h-full"
                                    style={{
                                        height: 'calc(100vh - 120px)',
                                        minHeight: '800px',
                                        border: 'none',
                                        outline: 'none'
                                    }}
                                    onContextMenu={(e) => e.preventDefault()}
                                >
                                    <iframe
                                        src={`${documentUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`}
                                        className="w-full h-full border-0"
                                        title="Document Content"
                                        style={{
                                            height: 'calc(100vh - 120px)',
                                            minHeight: '800px',
                                            border: 'none',
                                            outline: 'none'
                                        }}
                                        onContextMenu={(e) => e.preventDefault()}
                                        sandbox="allow-same-origin allow-scripts"
                                        scrolling="auto"
                                        frameBorder="0"
                                    />
                                </object>
                                <style dangerouslySetInnerHTML={{
                                    __html: `
                                        object[type="application/pdf"] {
                                            -webkit-appearance: none;
                                            -moz-appearance: none;
                                            appearance: none;
                                        }
                                        iframe {
                                            -webkit-appearance: none;
                                            -moz-appearance: none;
                                            appearance: none;
                                        }
                                    `
                                }} />
                            </>
                        ) : (
                            <div className="flex items-center justify-center h-[400px] bg-gray-100 rounded-lg">
                                <p className="text-gray-600">No document content available</p>
                            </div>
                        )}
                    </div>
                );
            case 'HTML':
                return <div dangerouslySetInnerHTML={{ __html: resultObject.content }} />;
            case 'VIDEO_URL':
                if (!isOnline && resultObject.contentUrl?.includes('youtube')) {
                    return (
                        <div className="flex flex-col items-center justify-center h-[600px] bg-gray-100 rounded-lg">
                            <p className="text-lg text-gray-600">
                                YouTube videos are not available offline
                            </p>
                        </div>
                    );
                }
                if (resultObject.contentUrl?.includes('youtube')) {
                    const videoId = resultObject.contentUrl.split('v=')[1] || 
                                   resultObject.contentUrl.split('embed/')[1];
                    return (
                        <iframe
                            src={`https://www.youtube.com/embed/${videoId}`}
                            className="w-full h-[600px] rounded-lg shadow-md"
                            title="learning content"
                            allowFullScreen
                        />
                    );
                }
                return (
                    <video
                        src={resultObject.contentUrl}
                        controls
                        className="w-full h-[600px] rounded-lg shadow-md"
                        title="learning content"
                    />
                );
            case 'AUDIO_URL':
                return <audio src={resultObject.contentUrl} controls className="w-full" />;
            default:
                return <p>Unsupported content type</p>;
        }
    };

    // Loading state
    if (isLoading || getContentRelatedTopicDataResult.isLoading) {
        return (
            <div className='w-full h-[600px] flex items-center justify-center'>
                <Loader2Icon className='w-10 h-10 animate-spin text-primary' />
            </div>
        );
    }

    // Content debugging
    console.log("Redux content:", contentRelatedTopic);
    console.log("API content:", getContentRelatedTopicDataResult.data?.resultObject);

    // Main render
    return (
        <div className='w-full h-full'>
            {!isOnline && (
                <div className="mb-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <p className="text-yellow-800">
                        You are currently offline. Content that has been previously cached will be available.
                    </p>
                </div>
            )}
            
            {/* Render content from Redux if available, otherwise from API */}
            {hasReduxContent ? renderReduxContent() : renderApiContent()}
        </div>
    );
};

export default EnhancedContentRenderer;
