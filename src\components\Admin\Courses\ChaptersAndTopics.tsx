import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion";
import { Plus, Pencil, Trash2, ChevronRight, FileQuestion, Loader2, BarChart2, RefreshCw } from 'lucide-react';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
    DialogFooter,
} from "@/components/ui/dialog";
import { toast } from '@/hooks/use-toast';
import { Module, Topic, Subtopic } from './types';
import {
    useSaveChaptersDataMutation,
    useSaveTopicsDataMutation,
    useDeleteChapterDataMutation,
    useDeleteTopicDataMutation,
    useUpdateChaptersDataMutation,
    useLazyGetAllQuestionsQuery,
    useUpdateTopicsDataMutation,
    useCreateSubtopicMutation,
    useUpdateSubtopicMutation,
    useDeleteSubtopicMutation,
    useUpdateSubtopicWeightsMutation,
} from '@/APIConnect';
import { Label } from '@/components/ui/label';
import TopicContent from './TopicContent';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ChapterAnalyticsExample } from '@/components/Analytics/ChapterAnalyticsExample';
import { stripHtmlTags } from '@/lib/utils';

interface ModulesAndContentProps {
    courseId: string;
    modules: Module[];
    onUpdate: () => void;
    disabled?: boolean;
    classroomId?: string;
}

interface ModuleFormData {
    name: string;
    description: string;
    courseId: string;
}

interface TopicFormData {
    name: string;
    description: string;
    chapterId: string;
}

interface SubtopicFormData {
    subTopicName: string;
    topicWeight: number;
    type: string;
    content: string;
    contentUrl?: string;
    assignment?: {
        type: 1 | 2;
        title: string;
        assignmentText: string;
        fileUrl?: string;
    };
}

interface QuestionData {
    id: string;
    questionText: string;
}

const ModulesAndContent: React.FC<ModulesAndContentProps> = ({
    courseId,
    modules,
    onUpdate,
    disabled = true,
    classroomId
}) => {
    const navigate = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();

    const [isAnalyticsOpen, setIsAnalyticsOpen] = useState(false);
    const [selectedChapterForAnalytics, setSelectedChapterForAnalytics] = useState<string | null>(null);

    const [saveChapter] = useSaveChaptersDataMutation();
    const [saveTopic] = useSaveTopicsDataMutation();
    const [updateChapter] = useUpdateChaptersDataMutation();
    const [updateTopic] = useUpdateTopicsDataMutation();
    const [deleteChapter] = useDeleteChapterDataMutation();
    const [deleteTopic] = useDeleteTopicDataMutation();

    const [createSubtopic] = useCreateSubtopicMutation();
    const [updateSubtopic] = useUpdateSubtopicMutation();
    const [deleteSubtopic] = useDeleteSubtopicMutation();
    const [updateSubtopicWeights] = useUpdateSubtopicWeightsMutation();

    const [isChapterDialogOpen, setIsChapterDialogOpen] = useState(false);
    const [isTopicDialogOpen, setIsTopicDialogOpen] = useState(false);
    const [isSubtopicDialogOpen, setIsSubtopicDialogOpen] = useState(false);
    const [selectedChapter, setSelectedChapter] = useState<Module | null>(null);
    const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
    const [selectedSubtopic, setSelectedSubtopic] = useState<Subtopic | null>(null);

    // Initialize expandedTopicId from URL params
    const [expandedTopicId, setExpandedTopicId] = useState<string | null>(() => {
        return searchParams.get('topicId');
    });

    const [chapterFormData, setChapterFormData] = useState<ModuleFormData>({
        name: '',
        description: '',
        courseId: courseId,
    });

    const [topicFormData, setTopicFormData] = useState<TopicFormData>({
        name: '',
        description: '',
        chapterId: '',
    });

    const [subtopicFormData, setSubtopicFormData] = useState<SubtopicFormData>({
        subTopicName: '',
        topicWeight: 0,
        type: '',
        content: '',
    });

    // Update URL when selections change
    useEffect(() => {
        const newParams = new URLSearchParams(searchParams);
        if (expandedTopicId) {
            newParams.set('topicId', expandedTopicId);
            // Find and set the parent chapter ID
            const parentChapter = modules.find(chapter =>
                chapter.topics.some(topic => topic.id === expandedTopicId)
            );
            if (parentChapter) {
                newParams.set('chapterId', parentChapter.id);
            }
        } else {
            newParams.delete('topicId');
            newParams.delete('chapterId');
        }
        setSearchParams(newParams, { replace: true });
    }, [expandedTopicId, modules]);

    // Initialize selections from URL params when component mounts
    useEffect(() => {
        const topicId = searchParams.get('topicId');
        if (topicId) {
            setExpandedTopicId(topicId);
        }
    }, [searchParams]);

    // Query hooks for questions
    const [getAllQuestionsQuery] = useLazyGetAllQuestionsQuery();
    const [questionCounts, setQuestionCounts] = useState<Record<string, number>>({});
    const [questionsByTopic, setQuestionsByTopic] = useState<Record<string, QuestionData[]>>({});
    const [isRefreshingCounts, setIsRefreshingCounts] = useState(false);
    const [loadingTopicId, setLoadingTopicId] = useState<string | null>(null);

    // Fetch question counts for all topics
    const fetchQuestionCounts = async () => {
        setIsRefreshingCounts(true);
        try {
            const topicIds = modules.flatMap(chapter =>
                chapter.topics.map(topic => topic.id)
            );

            const results = await Promise.all(
                topicIds.map(async topicId => {
                    const result = await getAllQuestionsQuery({ topicId }).unwrap();
                    return { topicId, count: result?.resultObject?.length || 0 };
                })
            );

            const counts = results.reduce((acc, { topicId, count }) => {
                acc[topicId] = count;
                return acc;
            }, {} as Record<string, number>);

            setQuestionCounts(counts);
        } catch (error) {
            console.error('Error fetching question counts:', error);
        } finally {
            setIsRefreshingCounts(false);
        }
    };

    // Fetch counts on mount and when chapters change
    useEffect(() => {
        fetchQuestionCounts();
    }, [modules]);

    // Fetch questions for a specific topic
    const fetchTopicQuestions = async (topicId: string) => {
        setLoadingTopicId(topicId);
        try {
            const result = await getAllQuestionsQuery({ topicId }).unwrap();
            const questions = result?.resultObject || [];
            setQuestionsByTopic(prev => ({
                ...prev,
                [topicId]: questions
            }));
            setQuestionCounts(prev => ({
                ...prev,
                [topicId]: questions.length
            }));
        } finally {
            setLoadingTopicId(null);
        }
    };

    // Fetch questions when a topic is expanded
    useEffect(() => {
        if (expandedTopicId) {
            fetchTopicQuestions(expandedTopicId);
        }
    }, [expandedTopicId]);

    const handleAddChapter = async () => {
        try {
            if (!chapterFormData.name.trim()) {
                toast({
                    title: "Chapter name is required",
                    variant: "destructive",
                });
                return;
            }

            await saveChapter({
                ...chapterFormData,
                createdOn: new Date().toISOString(),
            }).unwrap();

            toast({
                title: "Chapter added successfully",
                variant: "default",
            });
            setIsChapterDialogOpen(false);
            setChapterFormData({ name: '', description: '', courseId });
            onUpdate();
        } catch (error) {
            console.error('Error adding chapter:', error);
            toast({
                title: "Failed to add chapter",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const handleEditChapter = async () => {
        try {
            if (!chapterFormData.name.trim()) {
                toast({
                    title: "Chapter name is required",
                    variant: "destructive",
                });
                return;
            }

            await updateChapter({
                ...chapterFormData,
                id: selectedChapter?.id,
                createdOn: selectedChapter?.createdOn || new Date().toISOString(),
            }).unwrap();

            toast({
                title: "Chapter updated successfully",
                variant: "default",
            });
            setIsChapterDialogOpen(false);
            setSelectedChapter(null);
            onUpdate();
        } catch (error) {
            console.error('Error updating chapter:', error);
            toast({
                title: "Failed to update chapter",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const handleDeleteChapter = async (chapterId: string) => {
        try {
            await deleteChapter(chapterId).unwrap();
            toast({
                title: "Chapter deleted successfully",
                variant: "default",
            });
            onUpdate();
        } catch (error) {
            console.error('Error deleting chapter:', error);
            toast({
                title: "Failed to delete chapter",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const handleAddTopic = async () => {
        try {
            if (!topicFormData.name.trim()) {
                toast({
                    title: "Topic name is required",
                    variant: "destructive",
                });
                return;
            }

            await saveTopic({
                ...topicFormData,
                createdOn: new Date().toISOString(),
            }).unwrap();

            toast({
                title: "Topic added successfully",
                variant: "default",
            });
            setIsTopicDialogOpen(false);
            setTopicFormData({ name: '', description: '', chapterId: '' });
            onUpdate();
        } catch (error) {
            console.error('Error adding topic:', error);
            toast({
                title: "Failed to add topic",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const handleEditTopic = async () => {
        try {
            if (!topicFormData.name.trim()) {
                toast({
                    title: "Topic name is required",
                    variant: "destructive",
                });
                return;
            }

            await updateTopic({
                ...topicFormData,
                id: selectedTopic?.id,
                createdOn: selectedTopic?.createdOn || new Date().toISOString(),
            }).unwrap();

            toast({
                title: "Topic updated successfully",
                variant: "default",
            });
            setIsTopicDialogOpen(false);
            setSelectedTopic(null);
            onUpdate();
        } catch (error) {
            console.error('Error updating topic:', error);
            toast({
                title: "Failed to update topic",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const handleDeleteTopic = async (topicId: string) => {
        try {
            await deleteTopic(topicId).unwrap();
            toast({
                title: "Topic deleted successfully",
                variant: "default",
            });
            onUpdate();
        } catch (error) {
            console.error('Error deleting topic:', error);
            toast({
                title: "Failed to delete topic",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const handleAddSubtopic = async (topicId: string) => {
        try {
            if (!subtopicFormData.subTopicName.trim()) {
                toast({
                    title: "Subtopic name is required",
                    variant: "destructive",
                });
                return;
            }

            // Validate weight
            const existingSubtopics = modules
                .flatMap(m => m.topics)
                .find(t => t.id === topicId)
                ?.subtopics || [];
            
            const totalWeight = existingSubtopics.reduce((sum, st) => sum + st.topicWeight, 0);
            if (totalWeight + subtopicFormData.topicWeight > 100) {
                toast({
                    title: "Total weight cannot exceed 100",
                    description: "Please adjust the weight",
                    variant: "destructive",
                });
                return;
            }

            await createSubtopic({
                ...subtopicFormData,
                topicId,
                courseId,
            }).unwrap();

            toast({
                title: "Subtopic added successfully",
                variant: "default",
            });
            setIsSubtopicDialogOpen(false);
            setSubtopicFormData({
                subTopicName: '',
                topicWeight: 0,
                type: '',
                content: '',
            });
            onUpdate();
        } catch (error) {
            console.error('Error adding subtopic:', error);
            toast({
                title: "Failed to add subtopic",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const handleEditSubtopic = async () => {
        try {
            if (!subtopicFormData.subTopicName.trim()) {
                toast({
                    title: "Subtopic name is required",
                    variant: "destructive",
                });
                return;
            }

            if (!selectedSubtopic) return;

            await updateSubtopic({
                id: selectedSubtopic.id,
                ...subtopicFormData,
            }).unwrap();

            toast({
                title: "Subtopic updated successfully",
                variant: "default",
            });
            setIsSubtopicDialogOpen(false);
            setSelectedSubtopic(null);
            onUpdate();
        } catch (error) {
            console.error('Error updating subtopic:', error);
            toast({
                title: "Failed to update subtopic",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const handleDeleteSubtopic = async (subtopicId: string) => {
        try {
            await deleteSubtopic(subtopicId).unwrap();
            toast({
                title: "Subtopic deleted successfully",
                variant: "default",
            });
            onUpdate();
        } catch (error) {
            console.error('Error deleting subtopic:', error);
            toast({
                title: "Failed to delete subtopic",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const handleUpdateSubtopicWeights = async (topicId: string, weights: { id: string; weight: number }[]) => {
        try {
            await updateSubtopicWeights({ topicId, weights }).unwrap();
            toast({
                title: "Weights updated successfully",
                variant: "default",
            });
            onUpdate();
        } catch (error) {
            console.error('Error updating weights:', error);
            toast({
                title: "Failed to update weights",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-gray-800">Modules & Content</h2>
                {!disabled && (
                    <Dialog open={isChapterDialogOpen} onOpenChange={setIsChapterDialogOpen}>
                        <DialogTrigger asChild>
                            <Button onClick={() => {
                                setSelectedChapter(null);
                                setChapterFormData({ name: '', description: '', courseId });
                            }}>
                                <Plus className="w-4 h-4 mr-2" />
                                Add Module
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>{selectedChapter ? 'Edit Chapter' : 'Add Chapter'}</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4 py-4">
                                <div className="space-y-2">
                                    <Label>Chapter Name</Label>
                                    <Input
                                        value={chapterFormData.name}
                                        onChange={(e) => setChapterFormData(prev => ({ ...prev, name: e.target.value }))}
                                        placeholder="Enter chapter name"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label>Description</Label>
                                    <Textarea
                                        value={chapterFormData.description}
                                        onChange={(e) => setChapterFormData(prev => ({ ...prev, description: e.target.value }))}
                                        placeholder="Enter chapter description"
                                    />
                                </div>
                            </div>
                            <DialogFooter>
                                <Button variant="outline" onClick={() => setIsChapterDialogOpen(false)}>
                                    Cancel
                                </Button>
                                <Button onClick={selectedChapter ? handleEditChapter : handleAddChapter}>
                                    {selectedChapter ? 'Update' : 'Add'}
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                )}
            </div>

            <Accordion
                type="single"
                collapsible
                className="space-y-4"
                value={searchParams.get('moduleId') || undefined}
                onValueChange={(value) => {
                    const newParams = new URLSearchParams(searchParams);
                    if (value) {
                        newParams.set('moduleId', value);
                    } else {
                        newParams.delete('moduleId');
                        newParams.delete('topicId');
                    }
                    setSearchParams(newParams, { replace: true });
                }}
            >
                {modules.map((module) => (
                    <AccordionItem key={module.id} value={module.id} className="border rounded-lg">
                        <AccordionTrigger className="px-4 hover:no-underline">
                            <div className="flex items-center justify-between w-full">
                                <div className="flex items-center space-x-2">
                                    <ChevronRight className="w-4 h-4" />
                                    <span>{module.name}</span>
                                </div>
                                <div className="flex items-center space-x-2" onClick={(e) => e.stopPropagation()}>

                                    <Dialog open={isAnalyticsOpen}
                                        onOpenChange={(open) => {
                                            setIsAnalyticsOpen(open);
                                            if (!open) setSelectedChapterForAnalytics(null);
                                        }}>
                                        <DialogTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => {
                                                    setSelectedChapterForAnalytics(module.id);
                                                    setIsAnalyticsOpen(true);
                                                }}
                                            >
                                                <BarChart2 className="w-4 h-4" />
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent className="max-w-4xl">
                                            <DialogHeader>
                                                <DialogTitle>Analytics for {module.name}</DialogTitle>
                                            </DialogHeader>
                                            <div className="mt-4">
                                                <ChapterAnalyticsExample
                                                    classroomId={"d8fe1f7c-9e82-4a04-8e42-fac124512169"}
                                                    chapterId={"3498359c-f08f-46a6-8263-67a7246578f9"}
                                                />
                                            </div>
                                        </DialogContent>
                                    </Dialog>

                                    {!disabled && (
                                        <>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => {
                                                    setSelectedChapter(module);
                                                    setChapterFormData({
                                                        name: module.name,
                                                        description: module.description,
                                                        courseId,
                                                    });
                                                    setIsChapterDialogOpen(true);
                                                }}
                                            >
                                                <Pencil className="w-4 h-4" />
                                            </Button>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => handleDeleteChapter(module.id)}
                                            >
                                                <Trash2 className="w-4 h-4" />
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </div>
                        </AccordionTrigger>
                        <AccordionContent className="px-4 pb-4">
                            <div className="space-y-4">
                                <div className="flex justify-between items-center">
                                    <h3 className="text-sm text-muted-foreground">Topics</h3>
                                    {!disabled && (
                                        <Dialog open={isTopicDialogOpen} onOpenChange={setIsTopicDialogOpen}>
                                            <DialogTrigger asChild>
                                                <Button
                                                    size="sm"
                                                    onClick={() => {
                                                        setSelectedTopic(null);
                                                        setTopicFormData({
                                                            name: '',
                                                            description: '',
                                                            chapterId: module.id,
                                                        });
                                                    }}
                                                >
                                                    <Plus className="w-4 h-4 mr-2" />
                                                    Add Topic
                                                </Button>
                                            </DialogTrigger>
                                            <DialogContent>
                                                <DialogHeader>
                                                    <DialogTitle>{selectedTopic ? 'Edit Topic' : 'Add Topic'}</DialogTitle>
                                                </DialogHeader>
                                                <div className="space-y-4 py-4">
                                                    <div className="space-y-2">
                                                        <Label>Topic Name</Label>
                                                        <Input
                                                            value={topicFormData.name}
                                                            onChange={(e) => setTopicFormData(prev => ({ ...prev, name: e.target.value }))}
                                                            placeholder="Enter topic name"
                                                        />
                                                    </div>
                                                    <div className="space-y-2">
                                                        <Label>Description</Label>
                                                        <Textarea
                                                            value={topicFormData.description}
                                                            onChange={(e) => setTopicFormData(prev => ({ ...prev, description: e.target.value }))}
                                                            placeholder="Enter topic description"
                                                        />
                                                    </div>
                                                </div>
                                                <DialogFooter>
                                                    <Button variant="outline" onClick={() => setIsTopicDialogOpen(false)}>
                                                        Cancel
                                                    </Button>
                                                    <Button onClick={selectedTopic ? handleEditTopic : handleAddTopic}>
                                                        {selectedTopic ? 'Update' : 'Add'}
                                                    </Button>
                                                </DialogFooter>
                                            </DialogContent>
                                        </Dialog>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    {module.topics.map((topic) => (
                                        <Card key={topic.id}>
                                            <CardContent className="p-4">
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <h4 className="font-medium text-gray-800">{topic.name}</h4>
                                                        {topic.description && (
                                                            <p className="text-sm text-muted-foreground">{topic.description}</p>
                                                        )}
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <TopicContent
                                                            topicId={topic.id}
                                                            courseId={courseId}
                                                            topicName={topic.name}
                                                            disabled={disabled}
                                                        />
                                                        {!disabled && (
                                                            <>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="icon"
                                                                    onClick={() => {
                                                                        setSelectedTopic(topic);
                                                                        setTopicFormData({
                                                                            name: topic.name,
                                                                            description: topic.description,
                                                                            chapterId: module.id,
                                                                        });
                                                                        setIsTopicDialogOpen(true);
                                                                    }}
                                                                >
                                                                    <Pencil className="w-4 h-4" />
                                                                </Button>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="icon"
                                                                    onClick={() => handleDeleteTopic(topic.id)}
                                                                >
                                                                    <Trash2 className="w-4 h-4" />
                                                                </Button>
                                                            </>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="mt-4">
                                                    <div className="flex items-center gap-2 w-full" onClick={(e) => e.stopPropagation()}>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => setExpandedTopicId(expandedTopicId === topic.id ? null : topic.id)}
                                                            className="flex-1 justify-start"
                                                        >
                                                            <FileQuestion className="w-4 h-4 mr-2" />
                                                            Questions
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => fetchTopicQuestions(topic.id)}
                                                            disabled={loadingTopicId === topic.id}
                                                        >
                                                            {loadingTopicId === topic.id ? (
                                                                <Loader2 className="w-4 h-4 animate-spin" />
                                                            ) : (
                                                                <RefreshCw className="w-4 h-4" />
                                                            )}
                                                        </Button>
                                                        <div className="px-3 py-1.5 text-sm rounded-md bg-muted">
                                                            {questionCounts[topic.id] || 0}
                                                        </div>
                                                    </div>

                                                    {expandedTopicId === topic.id && (
                                                        <div className="mt-2 space-y-2">
                                                            {!disabled && (
                                                                <Button
                                                                    onClick={() =>
                                                                        navigate(
                                                                            `/admin/questions/create?courseId=${courseId}&chapterId=${module.id}&topicId=${topic.id}`
                                                                        )
                                                                    }
                                                                    size="sm"
                                                                    className="mb-2"
                                                                >
                                                                    <Plus className="w-4 h-4 mr-2" />
                                                                    Create Question
                                                                </Button>
                                                            )}

                                                            {loadingTopicId === topic.id ? (
                                                                <div className="flex justify-center py-4">
                                                                    <Loader2 className="w-6 h-6 animate-spin" />
                                                                </div>
                                                            ) : (
                                                                <div className="space-y-2">
                                                                    {questionsByTopic[topic.id]?.map((question) => (
                                                                        <Card key={question.id}>
                                                                            <CardContent className="p-3 flex items-center justify-between">
                                                                                <div className="flex-1">
                                                                                    <p className="text-sm">{stripHtmlTags(question.questionText)}</p>
                                                                                </div>
                                                                                {!disabled && (
                                                                                    <div className="flex items-center space-x-2 ml-4">
                                                                                        <Button
                                                                                            variant="ghost"
                                                                                            size="sm"
                                                                                            onClick={() => navigate(`/admin/questions/${question.id}`)}
                                                                                        >
                                                                                            <Pencil className="w-4 h-4" />
                                                                                        </Button>
                                                                                    </div>
                                                                                )}
                                                                            </CardContent>
                                                                        </Card>
                                                                    ))}
                                                                    {(!questionsByTopic[topic.id] || questionsByTopic[topic.id].length === 0) && (
                                                                        <p className="text-sm text-muted-foreground text-center py-2">
                                                                            No questions available
                                                                        </p>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                </div>
                                                <div className="mt-4">
                                                    <div className="flex items-center justify-between mb-2">
                                                        <h5 className="text-sm font-medium">Subtopics</h5>
                                                        {!disabled && (
                                                            <Button
                                                                size="sm"
                                                                onClick={() => {
                                                                    setSelectedSubtopic(null);
                                                                    setSubtopicFormData({
                                                                        subTopicName: '',
                                                                        topicWeight: 0,
                                                                        type: '',
                                                                        content: '',
                                                                    });
                                                                    setIsSubtopicDialogOpen(true);
                                                                }}
                                                            >
                                                                <Plus className="w-4 h-4 mr-2" />
                                                                Add Subtopic
                                                            </Button>
                                                        )}
                                                    </div>

                                                    <div className="space-y-2">
                                                        {topic.subtopics?.map((subtopic) => (
                                                            <Card key={subtopic.id}>
                                                                <CardContent className="p-3">
                                                                    <div className="flex items-center justify-between">
                                                                        <div>
                                                                            <p className="font-medium">{subtopic.subTopicName}</p>
                                                                            <p className="text-sm text-muted-foreground">
                                                                                Weight: {subtopic.topicWeight}%
                                                                            </p>
                                                                        </div>
                                                                        {!disabled && (
                                                                            <div className="flex items-center space-x-2">
                                                                                <Button
                                                                                    variant="ghost"
                                                                                    size="icon"
                                                                                    onClick={() => {
                                                                                        setSelectedSubtopic(subtopic);
                                                                                        setSubtopicFormData({
                                                                                            subTopicName: subtopic.subTopicName,
                                                                                            topicWeight: subtopic.topicWeight,
                                                                                            type: subtopic.type,
                                                                                            content: subtopic.content,
                                                                                            contentUrl: subtopic.contentUrl,
                                                                                            assignment: subtopic.assignment,
                                                                                        });
                                                                                        setIsSubtopicDialogOpen(true);
                                                                                    }}
                                                                                >
                                                                                    <Pencil className="w-4 h-4" />
                                                                                </Button>
                                                                                <Button
                                                                                    variant="ghost"
                                                                                    size="icon"
                                                                                    onClick={() => handleDeleteSubtopic(subtopic.id)}
                                                                                >
                                                                                    <Trash2 className="w-4 h-4" />
                                                                                </Button>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </CardContent>
                                                            </Card>
                                                        ))}
                                                        {(!topic.subtopics || topic.subtopics.length === 0) && (
                                                            <p className="text-sm text-muted-foreground text-center py-2">
                                                                No subtopics available
                                                            </p>
                                                        )}
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                    {module.topics.length === 0 && (
                                        <p className="text-sm text-muted-foreground text-center py-4">
                                            No topics yet
                                        </p>
                                    )}
                                </div>
                            </div>
                        </AccordionContent>
                    </AccordionItem>
                ))}
                {modules.length === 0 && (
                    <p className="text-sm text-muted-foreground text-center py-4">
                        No chapters yet
                    </p>
                )}
            </Accordion>

            {/* Subtopic Dialog */}
            <Dialog open={isSubtopicDialogOpen} onOpenChange={setIsSubtopicDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{selectedSubtopic ? 'Edit Subtopic' : 'Add Subtopic'}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        <div className="space-y-2">
                            <Label>Subtopic Name</Label>
                            <Input
                                value={subtopicFormData.subTopicName}
                                onChange={(e) => setSubtopicFormData(prev => ({ ...prev, subTopicName: e.target.value }))}
                                placeholder="Enter subtopic name"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label>Weight (%)</Label>
                            <Input
                                type="number"
                                min="0"
                                max="100"
                                value={subtopicFormData.topicWeight}
                                onChange={(e) => setSubtopicFormData(prev => ({ ...prev, topicWeight: parseInt(e.target.value) || 0 }))}
                                placeholder="Enter weight (0-100)"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label>Content Type</Label>
                            <Select
                                value={subtopicFormData.type}
                                onValueChange={(value) => setSubtopicFormData(prev => ({ ...prev, type: value }))}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select content type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="text">Text</SelectItem>
                                    <SelectItem value="video">Video</SelectItem>
                                    <SelectItem value="audio">Audio</SelectItem>
                                    <SelectItem value="scorm">SCORM</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <Label>Content</Label>
                            <Textarea
                                value={subtopicFormData.content}
                                onChange={(e) => setSubtopicFormData(prev => ({ ...prev, content: e.target.value }))}
                                placeholder="Enter content"
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsSubtopicDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={selectedSubtopic ? handleEditSubtopic : () => handleAddSubtopic(selectedTopic?.id || '')}>
                            {selectedSubtopic ? 'Update' : 'Add'}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default ModulesAndContent;
