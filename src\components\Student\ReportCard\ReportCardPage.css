@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');


.reportCard-container {
    display: grid;
    /* grid-template-columns: repeat(2, 1fr);  */
    grid-gap: 20px; 
    padding: 20px;
    background-color: #fef8e7; 
    /* background-color: red;  */
    justify-content: center;
    width: 100%;
  
    
  }
  
  .reportCard-container1 {
    display: flex;
    /* display: grid;
    grid-template-columns: repeat(2, 1fr);  */
    grid-gap: 20px; 
    /* padding: 20px; */
    /* background-color: #fef8e7;  */
    /* background-color: yellow;  */
    padding: 20px;
    justify-content: center;
    width: 100%;
  }

   
  .reportCard-container2 {
    display: flex;
    /* display: grid;
    grid-template-columns: repeat(2, 1fr);  */
    /* grid-gap: 20px;  */
    /* padding: 20px; */
    /* background-color: #fef8e7;  */
    /* background-color: yellow;  */
    /* padding: 20px; */
    justify-content: center;
    width: 100%;
  }
  

   
  .reportCard-container3 {
    display: grid;
    /* display: flex; */
    /* display: grid;
    grid-template-columns: repeat(2, 1fr);  */
    grid-gap: 20px; 
    /* padding: 20px; */
    /* background-color: #fef8e7;  */
    /* background-color: yellow;  */
    /* padding: 20px; */
    justify-content: center;
    width: 100%;
  }
  

  .card1 {
    
   
    /* background-color: white; */
    /* padding: 20px;
    border-radius: 15px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); */
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .card2 {
    /* background-color: white; */
    /* padding: 20px;
    border-radius: 15px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); */
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .card3 {
    /* background-color: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); */
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .card4 {
    /* background-color: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); */
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .card5 {
    /* background-color: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); */
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .card {
    background-color: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .card img {
    max-width: 100%;
    height: auto;
    display: block;
    border-radius: 10px;
  }
  