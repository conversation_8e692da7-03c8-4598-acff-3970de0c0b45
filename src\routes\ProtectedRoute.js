import React, { useState, useEffect } from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useSelector } from "react-redux";

const ProtectedRoute = ({ component: Component, ...rest }) => {
  const userToken = useSelector((state) => state.user.userToken);
  const userIsRendered = useSelector((state) => state.user.userIsRendered);
  const [isRendered, setIsRendered] = useState(false);
  useEffect(() => {
    if (userIsRendered) {
      setIsRendered(true);
    }
  }, [userIsRendered]);

  if (!isRendered) return null;
  console.log("userToken", userToken);
  return !userToken ? <Navigate to="/login" /> : <Outlet />;
};

export default ProtectedRoute;
