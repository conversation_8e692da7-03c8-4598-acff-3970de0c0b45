export interface Bookmark {
  id: string;
  title: string;
  courseId: string;
  topicId?: string;
  quizId?: string;
  createdAt: Date;
}
export interface SubTopic {
  id: string;
  subTopicName?: string;
  topicId: string;
  contentType: 'text' | 'video' | 'audio' | 'scorm' | 'quiz' | string;
  type?: number;
  contentUrl?: string;
  content?: string;
  completed: boolean;
  weight: number;
  createdOn?: string;
  updatedOn?: string;
  quizId?: string | null; // Quiz ID if this subtopic has a quiz
  assignment?: {
    type: number; // 1=Quiz, 2=CaseStudy, 3=TeamProject, 4=SelfReflectiveJournal, etc.
    tags: Array<{
      tagId: number; // 1-10 assignment tags
    }>;
    title: string;
    assignmentText: string;
    fileUrl?: string;
    quizId?: string; // For quiz type assignments
  };
}

export interface Topic {
  anteriorId: string;
  name: string;
  description: string;
  chapterId: string;
  id: string;
  deleted: boolean;
  createdOn: string;
  updatedon: string;
  content?: any;
  completed: boolean;
  subTopics: SubTopic[];
  recommendedTime: number;
}


export interface Chapter {
  anteriorId: string;
  name: string;
  description: string;
  courseId: string;
  topics: Topic[];
  id: string;
  deleted: boolean;
  createdOn: string;
  updatedon: string;
  completed: boolean;
}

export interface ResultObject {
  totalHours?: number;
  instructorName?: string;
  objectives?: never[];
  name: string;
  description: string;
  subjectId: string;
  chapters: Chapter[];
  id: string;
  deleted: boolean;
  createdOn: string;
  updatedon: string;
  initialQuizCompleted: boolean;
  finalQuizCompleted: boolean;
}

export interface ApiResponse {
  resultObject: ResultObject;
  isError: boolean;
  errors: any[];
  recoverableErrors: any[];
}

export interface UserStats {
  totalCoursesFinished: number;
  totalChaptersCompleted: number;
  totalQuizzesCompleted: number;
}

export interface NoteDto {
  Id?: string;
  StudentId?: string;
  CourseId?: string;
  TopicId?: string;
  Title: string;
  NoteString: string;
  NoteType?: number; // 0 = AllDay, 1 = Timed
  ReminderDateTime?: string;
  ReminderId?: string;
}

export interface Note {
  title: string;
  noteString: string;
}

// Legacy interface for backward compatibility
export interface LegacyNoteDto {
  studentId: string;
  courseId: string;
  topicId: string;
  notes: Note[];
  id: string;
  createdOn: string;
  updatedOn: string;
}
