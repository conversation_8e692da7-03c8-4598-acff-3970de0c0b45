import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

interface DataPoint {
    label: string;
    value: number;
}

interface DoughnutChartProps {
    data: DataPoint[];
    title: string;
    width?: number;
    height?: number;
    studentMode?: boolean;
}

interface ArcData {
    startAngle: number;
    endAngle: number;
    padAngle: number;
    data: DataPoint;
}

export const DoughnutChart: React.FC<DoughnutChartProps> = ({
    data,
    title,
    width = 300,
    height = 300,
    studentMode = false
}) => {
    const svgRef = useRef<SVGSVGElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!data || !svgRef.current || !containerRef.current) return;

        // Clear previous chart
        d3.select(svgRef.current).selectAll('*').remove();

        // Get container width
        const containerWidth = containerRef.current.clientWidth;
        const actualWidth = width || containerWidth;
        const actualHeight = height || containerWidth;

        const radius = Math.min(actualWidth, actualHeight) / 3.3;
        const innerRadius = radius * 0.6;

        const svg = d3.select(svgRef.current)
            .attr('width', actualWidth)
            .attr('height', actualHeight)
            .attr('viewBox', `0 0 ${actualWidth} ${actualHeight}`)
            .attr('preserveAspectRatio', 'xMidYMid meet');

        // Add gradients and filters
        const defs = svg.append('defs');

        // Create gradients and patterns
        const colors = studentMode ? [
            ['hsl(217, 85%, 75%)', 'hsl(217, 85%, 65%)'], // Light Blue
            ['hsl(217, 85%, 70%)', 'hsl(217, 85%, 60%)'], // Medium Blue
            ['hsl(217, 85%, 65%)', 'hsl(217, 85%, 55%)'], // Darker Blue
            ['hsl(217, 85%, 60%)', 'hsl(217, 85%, 50%)'], // Even Darker Blue
            ['hsl(217, 85%, 55%)', 'hsl(217, 85%, 45%)']  // Darkest Blue
        ] : [
            ['hsl(217, 91%, 60%)', 'hsl(221, 83%, 53%)'], // Blue gradient
            ['hsl(271, 91%, 65%)', 'hsl(269, 80%, 40%)'], // Purple gradient
            ['hsl(189, 94%, 43%)', 'hsl(199, 89%, 48%)'], // Cyan gradient
            ['hsl(31, 90%, 50%)', 'hsl(21, 90%, 48%)'],   // Orange gradient
            ['hsl(142, 71%, 45%)', 'hsl(142, 76%, 36%)']  // Emerald gradient
        ];

        // Create wave pattern for student mode
        if (studentMode) {
            const wavePattern = defs.append('pattern')
                .attr('id', 'wave-pattern')
                .attr('width', 10)
                .attr('height', 10)
                .attr('patternUnits', 'userSpaceOnUse')
                .attr('patternTransform', 'rotate(45)');

            wavePattern.append('path')
                .attr('d', 'M0,5 Q2.5,0 5,5 T10,5')
                .attr('stroke', 'hsl(217, 85%, 65%)')
                .attr('stroke-width', 1)
                .attr('fill', 'none');
        }

        data.forEach((_, i) => {
            const gradient = defs.append('linearGradient')
                .attr('id', `segment-gradient-${i}`)
                .attr('gradientUnits', 'userSpaceOnUse')
                .attr('x1', '0%')
                .attr('y1', '0%')
                .attr('x2', '100%')
                .attr('y2', '100%');

            gradient.append('stop')
                .attr('offset', '0%')
                .attr('stop-color', colors[i % colors.length][0]);

            gradient.append('stop')
                .attr('offset', '100%')
                .attr('stop-color', colors[i % colors.length][1]);
        });

        // Glow effect
        const glow = defs.append('filter')
            .attr('id', 'segment-glow')
            .attr('height', '300%')
            .attr('width', '300%')
            .attr('x', '-100%')
            .attr('y', '-100%');

        glow.append('feGaussianBlur')
            .attr('stdDeviation', '3')
            .attr('result', 'coloredBlur');

        const feMerge = glow.append('feMerge');
        feMerge.append('feMergeNode')
            .attr('in', 'coloredBlur');
        feMerge.append('feMergeNode')
            .attr('in', 'SourceGraphic');

        // Position the chart more to the left to make room for the legend
        const chartX = actualWidth * 0.25;
        const chartY = actualHeight / 2 + 20; // Add offset for title
        const g = svg.append('g')
            .attr('transform', `translate(${chartX},${chartY})`);

        // Compute the position of each group on the pie
        const pie = d3.pie<DataPoint>()
            .value(d => d.value)
            .sort(null)
            .padAngle(0.02);

        // Arc generator with rounded corners
        const arc = d3.arc<d3.PieArcDatum<DataPoint>>()
            .innerRadius(innerRadius)
            .outerRadius(radius)
            .cornerRadius(4);

        // Add the segments with animation
        const paths = g.selectAll('path')
            .data(pie(data))
            .enter()
            .append('path')
            .attr('fill', (_, i) => `url(#segment-gradient-${i})`)
            .attr('opacity', 0.9)
            .attr('stroke', 'white')
            .attr('stroke-width', studentMode ? 3 : 2)
            .style('filter', 'url(#segment-glow)')
            .style('cursor', 'pointer')
            .each(function (d) {
                const self = this as any;
                self._current = {
                    startAngle: d.startAngle,
                    endAngle: d.startAngle,
                    padAngle: d.padAngle,
                    data: d.data
                };
            });

        // Animate the segments
        paths.transition()
            .duration(1000)
            .attrTween('d', function (d) {
                const self = this as any;
                const interpolate = d3.interpolate(self._current, d);
                self._current = interpolate(0);
                return (t: number) => arc(interpolate(t)) || '';
            });

        // Add labels with animation
        const labelArc = d3.arc<d3.PieArcDatum<DataPoint>>()
            .innerRadius(radius * 0.8)
            .outerRadius(radius * 0.8);

        const labels = g.selectAll('text')
            .data(pie(data))
            .enter()
            .append('text')
            .attr('opacity', 0)
            .attr('transform', d => `translate(${labelArc.centroid(d)})`)
            .attr('dy', '0.35em')
            .attr('text-anchor', 'middle')
            .attr('font-size', '14px')
            .attr('font-weight', 'bold')
            .attr('class', 'text-xs font-medium')
            .style('fill', 'white');

        // Add percentage labels with animation
        labels.text(d => `${Math.round((d.data.value / d3.sum(data, d => d.value)) * 100)}%`)
            .transition()
            .delay(1000)
            .duration(500)
            .attr('opacity', 1);

        // Enhanced tooltip
        const tooltip = d3.select('body')
            .append('div')
            .attr('class', 'tooltip')
            .style('position', 'absolute')
            .style('background-color', 'hsl(var(--background))')
            .style('padding', '12px')
            .style('border', '1px solid hsl(var(--border))')
            .style('border-radius', '8px')
            .style('pointer-events', 'none')
            .style('opacity', 0)
            .style('font-size', '14px')
            .style('color', 'hsl(var(--foreground))')
            .style('box-shadow', '0 4px 12px rgba(0, 0, 0, 0.1)')
            .style('transform', 'translate(-50%, -100%)');

        paths.on('mouseover', function (event: MouseEvent, d: d3.PieArcDatum<DataPoint>) {
            const segment = d3.select(this);

            segment.transition()
                .duration(200)
                .attr('opacity', 1)
                .attr('transform', () => {
                    const centroid = arc.centroid(d);
                    const scale = studentMode ? 1.05 : 1.02;
                    const x = centroid[0] * (studentMode ? 0.1 : 0.05);
                    const y = centroid[1] * (studentMode ? 0.1 : 0.05);
                    return `translate(${x},${y}) scale(${scale})`;
                })
                .style('filter', 'url(#segment-glow) brightness(1.1)');

            if (studentMode) {
                // Add subtle wave animation
                const centroid = arc.centroid(d);
                g.append('circle')
                    .attr('class', 'temp-wave')
                    .attr('cx', centroid[0])
                    .attr('cy', centroid[1])
                    .attr('r', radius * 0.2)
                    .attr('fill', 'none')
                    .attr('stroke', 'hsl(217, 85%, 75%)')
                    .attr('stroke-width', 2)
                    .attr('opacity', 0)
                    .transition()
                    .duration(1000)
                    .attr('r', radius * 0.4)
                    .attr('opacity', 0.3)
                    .transition()
                    .duration(500)
                    .attr('opacity', 0)
                    .remove();
            }

            tooltip.transition()
                .duration(200)
                .style('opacity', 1);

            tooltip.html(`
                <div class="font-medium">${d.data.label}</div>
                <div class="text-sm text-muted-foreground">Value: ${d.data.value}</div>
                <div class="text-sm text-muted-foreground">${Math.round((d.data.value / d3.sum(data, d => d.value)) * 100)}%</div>
            `)
                .style('left', (event.pageX) + 'px')
                .style('top', (event.pageY - 10) + 'px');
        })
            .on('mouseout', function () {
                const segment = d3.select(this);

                segment.transition()
                    .duration(200)
                    .attr('opacity', 0.9)
                    .attr('transform', 'translate(0,0) scale(1)')
                    .style('filter', 'url(#segment-glow)');

                // Remove any temporary animations
                g.selectAll('.temp-wave').remove();

                tooltip.transition()
                    .duration(200)
                    .style('opacity', 0);
            });

        // Enhanced legend with hover interaction
        // Title positioning
        svg.append('text')
            .attr('class', 'chart-title')
            .attr('x', actualWidth / 3.8)
            .attr('y', 30)
            .attr('text-anchor', 'middle')
            .style('font-size', '16px')
            .style('font-weight', '600')
            .style('fill', 'hsl(var(--foreground))')
            .style('opacity', 0)
            .text(title)
            .transition()
            .duration(500)
            .style('opacity', 1);

        // Enhanced legend with hover interaction
        const legendSpacing = 32;
        const legendWidth = 200;
        const legendPadding = 20;
        const legendItemHeight = 24;
        const totalLegendHeight = data.length * legendItemHeight + legendPadding * 4;

        // Position legend to the right of the chart with proper spacing
        const legendX = actualWidth * 0.5;
        const legendY = (actualHeight - totalLegendHeight) / 2 + 40; // Add offset to account for title

        const legendBlock = svg.append('g')
            .attr('transform', `translate(${legendX}, ${legendY})`);

        // Legend background
        legendBlock.append('rect')
            .attr('width', legendWidth)
            .attr('height', totalLegendHeight)
            .attr('rx', 12)
            .attr('fill', 'hsl(var(--background))')
            .attr('opacity', 0.8)
            .attr('filter', 'url(#segment-glow)');

        const legendItems = legendBlock.selectAll('.legend-item')
            .data(pie(data))
            .enter()
            .append('g')
            .attr('class', 'legend-item')
            .attr('transform', (_, i) => `translate(${legendPadding}, ${i * legendSpacing + legendPadding})`);

        // Legend item background for hover effect
        legendItems.append('rect')
            .attr('x', -legendPadding)
            .attr('y', -legendPadding / 2)
            .attr('width', legendWidth)
            .attr('height', legendSpacing)
            .attr('fill', 'transparent')
            .attr('class', 'legend-hover');

        // Color indicator with gradient
        legendItems.append('rect')
            .attr('width', 16)
            .attr('height', 16)
            .attr('rx', 4)
            .attr('fill', (_, i) => `url(#segment-gradient-${i})`);

        // Label with value
        legendItems.append('text')
            .attr('x', 24)
            .attr('y', 12)
            .style('font-size', '14px')
            .style('font-weight', '500')
            .style('fill', 'hsl(var(--foreground))')
            .text(d => {
                const percentage = Math.round((d.data.value / d3.sum(data, d => d.value)) * 100);
                return `${d.data.label} (${percentage}%)`;
            });

        // Enhanced hover interaction
        legendItems.on('mouseover', function (_, d) {
            const segment = paths.filter((p: any) => p.data.label === d.data.label);
            segment.dispatch('mouseover');

            d3.select(this).select('.legend-hover')
                .transition()
                .duration(200)
                .attr('fill', 'hsl(var(--primary)/0.1)');
        })
            .on('mouseout', function (_, d) {
                const segment = paths.filter((p: any) => p.data.label === d.data.label);
                segment.dispatch('mouseout');

                d3.select(this).select('.legend-hover')
                    .transition()
                    .duration(200)
                    .attr('fill', 'transparent');
            });

        // Handle resize
        const handleResize = () => {
            if (!containerRef.current) return;
            const newWidth = containerRef.current.clientWidth;
            const newHeight = containerRef.current.clientWidth;
            svg.attr('width', newWidth)
                .attr('height', newHeight)
                .attr('viewBox', `0 0 ${newWidth} ${newHeight}`);

            const newRadius = Math.min(newWidth, newHeight) / 2;
            const newInnerRadius = newRadius * 0.6;

            arc.innerRadius(newInnerRadius)
                .outerRadius(newRadius);

            labelArc.innerRadius(newRadius * 0.8)
                .outerRadius(newRadius * 0.8);

            const newChartX = newWidth * 0.25;
            const newChartY = newHeight / 2 + 20;
            g.attr('transform', `translate(${newChartX},${newChartY})`);

            // Update legend position
            const newLegendX = newWidth * 0.5;
            const newLegendY = (newHeight - totalLegendHeight) / 2 + 40;
            legendBlock.attr('transform', `translate(${newLegendX}, ${newLegendY})`);

            // Update title position
            svg.select('.chart-title')
                .attr('x', actualWidth / 2)
                .attr('y', 30);

            paths.attr('d', arc);
            labels.attr('transform', d => `translate(${labelArc.centroid(d)})`);
        };

        window.addEventListener('resize', handleResize);

        // Cleanup
        return () => {
            d3.select('body').selectAll('.tooltip').remove();
            window.removeEventListener('resize', handleResize);
        };
    }, [data, width, height, title, studentMode]);

    return (
        <div ref={containerRef} className="w-full h-full">
            <svg ref={svgRef} className="w-full h-full" />
        </div>
    );
};
