import React, { useState } from "react";

import Question from "components/Admin/QuestionCreation/Question";
import Answers from "components/Admin/QuestionCreation/Answers";
import Explanation from "components/Admin/QuestionCreation/Explanation";
import Review from "components/Admin/QuestionCreation/Review";
import { useSelector, useDispatch } from "react-redux";
import {
  setQuestionLevel,
  setResetQuestionValue,
  setTempQuestionLevel,
} from "components/Admin/QuestionCreation/questionSlice";
import { useSaveQuestionDataMutation } from "APIConnect";
import "./QuestionCreation.css";

const QuestionCreation = () => {
  const dispatch = useDispatch();
  const [saveQuestionData] = useSaveQuestionDataMutation();
  const questionLevel = useSelector((state) => state.questions.questionLevel);
  const questionText = useSelector(
    (state) => state.questions.questions.questionText
  );
  const questionText2 = useSelector(
    (state) => state.questions.questions.questionText2
  );
  const courseId = useSelector((state) => state.questions.questions.courseId);
  const chapterId = useSelector((state) => state.questions.questions.chapterId);
  const topicId = useSelector((state) => state.questions.questions.topicId);
  const answers = useSelector((state) => state.questions.questions.answers);
  const correctAnswer = useSelector(
    (state) => state.questions.questions.correctAnswer
  );
  const questionType = useSelector(
    (state) => state.questions.questions.questionType
  );
  const explanation = useSelector(
    (state) => state.questions.questions.explanation
  );

  const [option, setOption] = useState("question");

  const handleChooseOption = (val) => {
    setOption(val);
  };

  const handleSubmit = (val) => {
    switch (val) {
      case 1:
        handleChooseOption("answer");
        break;
      case 2:
        handleChooseOption("explanation");
        break;
      case 3:
        handleChooseOption("review");
        break;
      default:
        handleChooseOption("question");
    }
    dispatch(setQuestionLevel(val));
    dispatch(setTempQuestionLevel(val));
  };

  const handleComplete = async () => {
    const obj = {
      questionText : questionType === 'TEXTINPUT' ? questionText + ' | ' +  questionText2 :  questionText,
      answers,
      explanation,
      courseId,
      chapterId,
      topicId,
      mark: 0,
      level: 1,
      type: questionType,
      active: true,
      createdBy: "Test",
      correctAnswer,
    };
    try {
      const {
        data: { resultObject },
      } = await saveQuestionData(obj);
      if (resultObject) {
        setInitialState();
      }
    } catch (err) {
      console.log("err", err);
      setInitialState();
    }
  };

  const handleCancel = () => {
    setInitialState();
  };
  const setInitialState = () => {
    dispatch(setQuestionLevel(0));
    dispatch(setTempQuestionLevel(0));
    setOption("question");
    dispatch(setResetQuestionValue());
  };
  return (
    <div className="parentContainer">
      <button onClick={() => handleChooseOption("question")}>Question</button>
      <button
        onClick={() => handleChooseOption("answer")}
        disabled={questionLevel < 1}
      >
        Answer
      </button>
      <button
        onClick={() => handleChooseOption("explanation")}
        disabled={questionLevel < 2}
      >
        Explanation
      </button>
      <button
        onClick={() => handleChooseOption("review")}
        disabled={questionLevel < 3}
      >
        Review
      </button>

      {option === "question" ? (
        <>
          <Question
            handleQuestionSubmit={handleSubmit}
            handleCancel={handleCancel}
          />
        </>
      ) : option === "answer" ? (
        <>
          <Answers handleAnswerComplete={handleSubmit} />
        </>
      ) : option === "explanation" ? (
        <Explanation handleExplanationComplete={handleSubmit} />
      ) : (
        <Review handleReviewComplete={handleComplete} />
      )}
    </div>
  );
};

export default QuestionCreation;
