@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');



.qa-container {
    /* display: grid; */
    /* grid-template-columns: repeat(2, 1fr);  */
    grid-gap: 20px; 
    padding: 20px;
    background-color: #fef8e7; 
    /* background-color: red;  */
    /* justify-content: center; */
    justify-content: flex-end;
    /* width: 100%; */
    /* width: "100vh"; */
    
  height: 100vh;
    
  }
  
  .qa-container1 {
    background-color: #fef8e7; 
    /* width: 80%; */
    /* background-color: red;  */
    display: flex;
    /* display: grid;
    grid-template-columns: repeat(2, 1fr);  */
    grid-gap: 20px; 
    /* background-color: #fef8e7;  */
    /* padding: 20px; */
    /* background-color: #fef8e7;  */
   
    padding: 20px;
    justify-content: center;
    /* width: 10%; */
  }

  .qa-container-question {
  
    background-color: #fef8e7; 
    /* width: 80%; */
    /* background-color: red;  */
    display: flex;
    /* display: grid;
    grid-template-columns: repeat(2, 1fr);  */
    grid-gap: 20px; 
    /* background-color: #fef8e7;  */
    /* padding: 20px; */
    /* background-color: #fef8e7;  */
   
    padding: 20px;
    justify-content: flex-end;
    width: 84%;
    /* width: 0%; */
  }

  .qa-container2 {
    display: flex;
    /* display: grid;
    grid-template-columns: repeat(2, 1fr);  */
    grid-gap: 20px; 
    /* padding: 20px; */
    /* background-color: #fef8e7;  */
    width: 100px;
    padding: 20px;
    justify-content: flex-end;
  }



  .card1 {
    
   
    /* background-color: white; */
    /* padding: 20px;
    border-radius: 15px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); */
    display: flex;
    justify-content: center;
    align-items: center;
  }