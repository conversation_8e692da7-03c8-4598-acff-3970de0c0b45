import { Search, Filter } from 'lucide-react'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface DiscussionSearchProps {
  searchQuery: string
  onSearchChange: (value: string) => void
  filter: string
  onFilterChange: (value: string) => void
}

export function DiscussionSearch({
  searchQuery,
  onSearchChange,
  filter,
  onFilterChange,
}: DiscussionSearchProps) {
  return (
    <div className="flex flex-col md:flex-row gap-4">
      <div className="flex-1 relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search discussions..."
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10"
        />
      </div>
      <Select value={filter} onValueChange={onFilterChange}>
        <SelectTrigger className="w-[180px]">
          <Filter className="w-4 h-4 mr-2" />
          <SelectValue placeholder="Filter by" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Discussions</SelectItem>
          <SelectItem value="my-courses">My Courses</SelectItem>
          <SelectItem value="unanswered">Unanswered</SelectItem>
          <SelectItem value="solved">Solved</SelectItem>
        </SelectContent>
      </Select>
    </div>
  )
}