import React from 'react';
import { motion } from 'framer-motion';
import { Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import NotesDialog from './notes-dialog';
import BookmarksDialog from './bookmarks-dialog';
import MySpaceDialog from './my-space-dialog';

const MobileFAB = () => {
    return (
        <div className="fixed bottom-20 right-4 md:hidden">
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <motion.div
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <Button
                            size="icon"
                            className="h-14 w-14 rounded-full shadow-lg bg-gradient-to-r from-primary to-primary/80"
                        >
                            <motion.div
                                animate={{
                                    rotate: [0, 180, 360],
                                }}
                                transition={{
                                    duration: 4,
                                    ease: "linear",
                                    repeat: Infinity,
                                }}
                            >
                                <Sparkles className="h-6 w-6 text-white" />
                            </motion.div>
                        </Button>
                    </motion.div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48 mr-2 mb-2">
                    <DropdownMenuItem asChild>
                        <NotesDialog />
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                        <BookmarksDialog />
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                        <MySpaceDialog />
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    );
};

export default MobileFAB;
