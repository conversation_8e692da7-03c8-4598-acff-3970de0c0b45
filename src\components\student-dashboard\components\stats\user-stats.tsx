import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trophy } from 'lucide-react';
import { motion } from "framer-motion";
import { UserStats as UserStatsType } from '../../types';
import { cn } from "@/lib/utils";

interface UserStatsProps {
    stats: UserStatsType;
}

const StatCard = ({ icon, title, value, delay, trend }: {
    icon: React.ReactNode;
    title: string;
    value: number;
    delay: number;
    trend?: number;
}) => (
    <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
            duration: 0.5,
            delay,
            type: "spring",
            stiffness: 100,
            damping: 15
        }}
        className="group relative overflow-hidden rounded-xl bg-gradient-to-br from-card to-card/95 p-6 shadow-md border transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:bg-card/95"
    >
        {/* <div className="absolute right-0 top-0 h-32 w-32 translate-x-8 translate-y--8 transform opacity-[0.07] transition-transform group-hover:translate-x-6 group-hover:translate-y--6 group-hover:opacity-[0.1] rotate-12">
            <TrendingUp className="h-full w-full text-primary stroke-[1.5]" />
        </div> */}

        <div className="space-y-4">
            <div className="inline-flex items-center justify-center rounded-lg bg-gradient-to-br from-primary/15 to-primary/10 p-3 transition-all duration-300 group-hover:scale-110 group-hover:from-primary/20 group-hover:to-primary/15">
                {React.cloneElement(icon as React.ReactElement, {
                    className: "h-6 w-6 text-primary transition-transform group-hover:scale-110"
                })}
            </div>

            <div className="space-y-2">
                <motion.h3
                    className="text-sm font-medium text-muted-foreground"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: delay + 0.2 }}
                >
                    {title}
                </motion.h3>

                <motion.div
                    className="flex items-baseline gap-2"
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                        delay: delay + 0.3,
                        type: "spring",
                        stiffness: 100,
                        damping: 15
                    }}
                >
                    <span className="text-3xl font-bold bg-gradient-to-br from-primary via-primary/90 to-primary/80 bg-clip-text text-transparent">
                        {value}
                    </span>
                    <span className="text-sm text-muted-foreground/70 font-medium">completed</span>
                    {trend && (
                        <span className={cn(
                            "text-xs font-medium px-2 py-1 rounded-full transition-all",
                            trend > 0
                                ? "text-green-700 bg-green-100/80 group-hover:bg-green-100"
                                : "text-red-700 bg-red-100/80 group-hover:bg-red-100"
                        )}>
                            {trend > 0 ? '+' : ''}{trend}%
                        </span>
                    )}
                </motion.div>
            </div>
        </div>

        <motion.div
            className="absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-primary/5 via-primary/20 to-primary/5"
            whileHover={{ height: '0.375rem', opacity: 1 }}
            initial={{ opacity: 0.7 }}
            transition={{ duration: 0.2 }}
        />
    </motion.div>
);

const UserStats = ({ stats }: UserStatsProps) => {
    // Example trends - in a real app these would come from the backend
    const trends = {
        courses: 15,
        chapters: 8,
        quizzes: 12
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="flex items-center gap-3"
                >
                    <Trophy className="w-6 h-6 text-primary" />
                    <h2 className="text-xl font-semibold text-card-foreground">Your Learning Progress</h2>
                </motion.div>
                <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="text-sm font-medium text-muted-foreground/80 bg-secondary/50 px-3 py-1.5 rounded-full"
                >
                    Last 30 days
                </motion.div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
                <StatCard
                    icon={<BookOpenCheck className="h-5 w-5 text-primary" />}
                    title="Courses Finished"
                    value={stats.totalCoursesFinished}
                    delay={0}
                    trend={trends.courses}
                />
                <StatCard
                    icon={<CheckCircle className="h-5 w-5 text-primary" />}
                    title="Chapters Completed"
                    value={stats.totalChaptersCompleted}
                    delay={0.1}
                    trend={trends.chapters}
                />
                <StatCard
                    icon={<BarChart className="h-5 w-5 text-primary" />}
                    title="Quizzes Completed"
                    value={stats.totalQuizzesCompleted}
                    delay={0.2}
                    trend={trends.quizzes}
                />
            </div>
        </div>
    );
};

export default UserStats;
