export type QuestionType =
  | "SINGLECHOICE"
  | "MULTICHOICE"
  | "TEXTINPUT"
  | "BINARY"
  | "MATCH"
  | "SOUNDBASED";

export interface Answer {
  answerText: string;
  answerImageMetadata: any;
  answerVideoMetadata: any;
  answerAudioMetadata: any;
}

export interface QuizAnswer {
  questionId: string;
  type: QuestionType;
  studentAnswer: number | number[] | string | boolean;
  timeTaken?: number;
}

export interface QuizState {
  status: "pre-start" | "in-progress" | "completed";
  currentQuestion: number;
  timeLeft: number;
  score: number;
  percentage: number;
  answers: Record<string, QuizAnswer>;
  completed: boolean | null;
  timeTaken: number;
  questionStartTime: number;
  questionTimeTaken: Record<string, number>;
}

export interface Question {
  id: string;
  questionText: string;
  type: QuestionType;
  answers: Answer[];
  correctAnswer: any;
  explanation: string;
  studentAnswer: any;
  mark: number;
}

export interface QuizData {
  questionSet: Question[];
  courseId: string;
  classroomId: string;
  score: number;
  percentage: number;
  completed: boolean;
}
