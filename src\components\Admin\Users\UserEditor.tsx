import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Save, User2, Mail, Loader2, Eye, EyeOff } from 'lucide-react';
import {
    useGetUserByIdQuery,
    useCreateUserMutation,
    useUpdateUserMutation,
    useGetSchoolsQuery,
} from 'src/APIConnect';
import { toast } from "@/hooks/use-toast";
import { UserCreationRequest } from './types';
import { useAuth0 } from '@auth0/auth0-react';

interface FormState extends Omit<UserCreationRequest, 'passwordHash' | 'passwordSalt'> {
    firstName: string;
    lastName: string;
    nickName: string;
    email: string;
    userRoleId: number;
    schoolId: string;
    password?: string;
    createdOn: string;
    userRole?: {
        id: number;
        name: string;
    }
}

interface FormErrors {
    firstName?: string;
    lastName?: string;
    nickName?: string;
    email?: string;
    password?: string;
    userRoleId?: string;
    schoolId?: string;
}

const userRoles = [
    { id: 1, role: 'Admin' },
    { id: 2, role: 'SchoolAdmin' },
    { id: 3, role: 'Teacher' },
    { id: 4, role: 'Student' }
];

const UserEditor = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const isEditMode = !!id;
    const { user } = useAuth0();
    const isSchoolAdmin = user?.["http://learnido-app/roleId"] === "2";
    const schoolId = user?.["http://learnido-app/schoolId"] as string | undefined;


    const { data: userData, isLoading: isLoadingUser } = useGetUserByIdQuery(id, { skip: !id });
    const [createUser, { isLoading: isCreating }] = useCreateUserMutation();
    const [updateUser, { isLoading: isUpdating }] = useUpdateUserMutation();
    const { data: schoolsData } = useGetSchoolsQuery({});
    const schools = schoolsData?.resultObject ?? [];

    const [formData, setFormData] = useState<FormState>({
        firstName: '',
        lastName: '',
        nickName: '',
        email: '',
        userRoleId: 4, // Default to Student
        schoolId: isSchoolAdmin ? schoolId ?? "" : "", // This should be set based on context or selection
        createdOn: new Date().toISOString(),
        userRole: {
            id: 4,
            name: "Student"
        }
    });

    const [errors, setErrors] = useState<FormErrors>({});

    const [showPassword, setShowPassword] = useState(false);

    useEffect(() => {
        if (userData) {
            setFormData({
                firstName: userData.firstName,
                lastName: userData.lastName,
                nickName: userData.nickName,
                email: userData.email,
                userRoleId: userData.userRoleId,
                schoolId: userData.schoolId,
                createdOn: userData.createdOn,
                userRole: userData.userRole
            });
        }
    }, [userData]);

    const validateForm = (): boolean => {
        const newErrors: FormErrors = {};
        let isValid = true;

        if (!formData.firstName.trim()) {
            newErrors.firstName = "First name is required";
            isValid = false;
        }

        if (!formData.lastName.trim()) {
            newErrors.lastName = "Last name is required";
            isValid = false;
        }

        if (!formData.nickName.trim()) {
            newErrors.nickName = "Nickname is required";
            isValid = false;
        }

        if (!formData.email.trim()) {
            newErrors.email = "Email is required";
            isValid = false;
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = "Please enter a valid email address";
            isValid = false;
        }

        if (!isEditMode && (!formData.password || !formData.password.trim())) {
            newErrors.password = "Password is required for new users";
            isValid = false;
        } else if (!isEditMode && formData.password) {
            const passwordRegex = /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};':"\\|,.<>\/?]).{8,}$/;
            if (!passwordRegex.test(formData.password)) {
                newErrors.password = "Password must be at least 8 characters long and include an uppercase letter, a number, and a special character";
                isValid = false;
            }
        }

        if (!formData.userRoleId) {
            newErrors.userRoleId = "User role is required";
            isValid = false;
        }

        if (!formData.schoolId.trim()) {
            newErrors.schoolId = "School is required";
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            toast({
                title: "Please fix the errors",
                description: "Some fields are missing or invalid",
                variant: "destructive",
            });
            return;
        }

        try {
            const submitData = {
                ...formData,
                name: `${formData.firstName} ${formData.lastName}`,
            };
            delete submitData.userRole;

            if (isEditMode) {
                await updateUser({
                    id,
                    ...submitData,
                }).unwrap();
                toast({
                    title: "User updated successfully",
                    variant: "default",
                });
            } else {
                await createUser(submitData).unwrap();
                toast({
                    title: "User created successfully",
                    variant: "default",
                });
                navigate(isSchoolAdmin ? '/school-admin/users' : '/admin/users');
            }
        } catch (error) {
            console.error('Error saving user:', error);
            toast({
                title: "Failed to save user",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    if (isEditMode && isLoadingUser) {
        return (
            <div className="flex justify-center items-center h-screen">
                <Loader2 className="w-8 h-8 animate-spin" />
            </div>
        );
    }

    const isSubmitting = isCreating || isUpdating;

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="sticky top-0 z-10 bg-white border-b">
                <div className="container mx-auto px-4 py-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => navigate(isSchoolAdmin ? '/school-admin/users' : '/admin/users')}
                                disabled={isSubmitting}
                            >
                                <ArrowLeft className="h-5 w-5" />
                            </Button>
                            <h1 className="text-2xl font-bold flex items-center">
                                <User2 className="h-6 w-6 mr-2" />
                                {isEditMode ? 'Edit User' : 'Create User'}
                            </h1>
                        </div>
                        <Button
                            onClick={handleSubmit}
                            className="flex items-center"
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? (
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            ) : (
                                <Save className="h-4 w-4 mr-2" />
                            )}
                            {isEditMode ? 'Update' : 'Create'}
                        </Button>
                    </div>
                </div>
            </div>

            <div className="container mx-auto px-4 py-6">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center mb-6">
                            <User2 className="h-5 w-5 mr-2" />
                            <h2 className="text-xl font-semibold">User Details</h2>
                        </div>
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <Label>First Name</Label>
                                    <Input
                                        value={formData.firstName}
                                        onChange={(e) => {
                                            setFormData((prev) => ({ ...prev, firstName: e.target.value }));
                                            if (errors.firstName) {
                                                setErrors((prev) => ({ ...prev, firstName: undefined }));
                                            }
                                        }}
                                        placeholder="Enter first name..."
                                        className={errors.firstName ? "border-red-500" : ""}
                                        disabled={isSubmitting}
                                    />
                                    {errors.firstName && (
                                        <p className="text-sm text-red-500 mt-1">{errors.firstName}</p>
                                    )}
                                </div>

                                <div>
                                    <Label>Last Name</Label>
                                    <Input
                                        value={formData.lastName}
                                        onChange={(e) => {
                                            setFormData((prev) => ({ ...prev, lastName: e.target.value }));
                                            if (errors.lastName) {
                                                setErrors((prev) => ({ ...prev, lastName: undefined }));
                                            }
                                        }}
                                        placeholder="Enter last name..."
                                        className={errors.lastName ? "border-red-500" : ""}
                                        disabled={isSubmitting}
                                    />
                                    {errors.lastName && (
                                        <p className="text-sm text-red-500 mt-1">{errors.lastName}</p>
                                    )}
                                </div>

                                <div>
                                    <Label>Nickname</Label>
                                    <Input
                                        value={formData.nickName}
                                        onChange={(e) => {
                                            setFormData((prev) => ({ ...prev, nickName: e.target.value }));
                                            if (errors.nickName) {
                                                setErrors((prev) => ({ ...prev, nickName: undefined }));
                                            }
                                        }}
                                        placeholder="Enter nickname..."
                                        className={errors.nickName ? "border-red-500" : ""}
                                        disabled={isSubmitting}
                                    />
                                    {errors.nickName && (
                                        <p className="text-sm text-red-500 mt-1">{errors.nickName}</p>
                                    )}
                                </div>
                            </div>

                            <div>
                                <Label>Email</Label>
                                <div className="relative">
                                    <Mail className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        value={formData.email}
                                        onChange={(e) => {
                                            setFormData((prev) => ({ ...prev, email: e.target.value }));
                                            if (errors.email) {
                                                setErrors((prev) => ({ ...prev, email: undefined }));
                                            }
                                        }}
                                        placeholder="Enter email address..."
                                        className={`pl-8 ${errors.email ? "border-red-500" : ""}`}
                                        disabled={isSubmitting}
                                    />
                                </div>
                                {errors.email && (
                                    <p className="text-sm text-red-500 mt-1">{errors.email}</p>
                                )}
                            </div>

                            {!isEditMode && (
                                <div>
                                    <Label>Password</Label>
                                    <div className="relative">
                                        <Input
                                            type={showPassword ? "text" : "password"}
                                            value={formData.password || ''}
                                            onChange={(e) => {
                                                setFormData((prev) => ({ ...prev, password: e.target.value }));
                                                if (errors.password) {
                                                    setErrors((prev) => ({ ...prev, password: undefined }));
                                                }
                                            }}
                                            placeholder="Enter password..."
                                            className={`${errors.password ? "border-red-500" : ""} pr-10`}
                                            disabled={isSubmitting}
                                        />
                                        <button
                                            type="button"
                                            onClick={() => setShowPassword((prev) => !prev)}
                                            className="absolute right-2 top-2.5 text-muted-foreground"
                                        >
                                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                        </button>
                                    </div>
                                    {errors.password && (
                                        <p className="text-sm text-red-500 mt-1">{errors.password}</p>
                                    )}
                                    <p className="text-sm text-muted-foreground mt-1">
                                        Password must be at least 8 characters long and include an uppercase letter, a number, and a special character.
                                    </p>
                                </div>
                            )}
                            <Label>School</Label>
                            <Select
                                value={formData.schoolId}
                                onValueChange={(value) => {
                                    setFormData((prev) => ({ ...prev, schoolId: value }));
                                    if (errors.schoolId) {
                                        setErrors((prev) => ({ ...prev, schoolId: undefined }));
                                    }
                                }}
                                disabled={isSubmitting}
                            >
                                <SelectTrigger className={errors.schoolId ? "border-red-500" : ""}>
                                    <SelectValue placeholder="Select a school" />
                                </SelectTrigger>
                                <SelectContent>
                                    {schools.map((school: any) => (
                                        <SelectItem key={school.id} value={school.id}>
                                            {school.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            {errors.schoolId && (
                                <p className="text-sm text-red-500 mt-1">{errors.schoolId}</p>
                            )}
                            <div>
                                <Label>User Role</Label>
                                <Select
                                    value={formData.userRoleId.toString()}
                                    onValueChange={(value) => {
                                        setFormData((prev) => ({ ...prev, userRoleId: parseInt(value) }));
                                        if (errors.userRoleId) {
                                            setErrors((prev) => ({ ...prev, userRoleId: undefined }));
                                        }
                                    }}
                                    disabled={isSubmitting || (id != null && id !== '')}
                                >
                                    <SelectTrigger className={errors.userRoleId ? "border-red-500" : ""}>
                                        <SelectValue placeholder="Select a role" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {userRoles.filter((ur) => {
                                            if (isSchoolAdmin) {
                                                return ur.id !== 1 && ur.id !== 2;
                                            }
                                            return ur.id !== 1;
                                        }).map((role) => (
                                            <SelectItem key={role.id} value={role.id.toString()}>
                                                {role.role}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.userRoleId && (
                                    <p className="text-sm text-red-500 mt-1">{errors.userRoleId}</p>
                                )}
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div >
    );
};

export default UserEditor;
