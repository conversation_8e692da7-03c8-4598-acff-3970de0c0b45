import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle } from 'lucide-react';

// Animation variants
const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.5 } },
};

interface FeedbackListProps {
  className?: string;
}

// Mock feedback data
const feedbackItems = [
  {
    id: 1,
    title: '01: Introduction to Pronouns',
    status: 'Complete the test on time'
  },
  {
    id: 2,
    title: '02: Introduction to Pronouns',
    status: 'Complete the test on time'
  },
  {
    id: 3,
    title: '03: Introduction to Pronouns',
    status: 'Complete the test on time'
  }
];

const FeedbackList: React.FC<FeedbackListProps> = ({ className = '' }) => {
  return (
    <motion.div
      className={`bg-white rounded-2xl  border border-[#97C48A] shadow-sm p-5  ${className}`}
      variants={fadeIn}
      initial="initial"
      animate="animate"
    >
      <h3 className="text-lg font-bold text-[#2D2D2D] mb-4">Feedback</h3>
      
      <div className="space-y-2 mt-2 overflow-hidden">
        {feedbackItems.map(item => (
          <div key={item.id} className="py-1 border-b border-gray-100 last:border-0">
            <div className="font-medium text-sm text-[#2D2D2D] truncate" title={item.title}>
              {item.title}
            </div>
            <div className="flex items-center text-xs text-gray-500 mt-0.5">
              <CheckCircle className="h-3 w-3 text-green-500 mr-1 flex-shrink-0" />
              <span className="truncate">{item.status}</span>
            </div>
          </div>
        ))}
      </div>
    </motion.div>
  );
};

export default FeedbackList;
