import React from 'react';
import { WifiOff } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OfflineFeatureNoticeProps {
    className?: string;
    message?: string;
}

const OfflineFeatureNotice = ({
    className,
    message = "This feature is not available offline"
}: OfflineFeatureNoticeProps) => {
    return (
        <div
            className={cn(
                'flex flex-col items-center justify-center p-8 text-center',
                'bg-gray-50 rounded-lg border border-gray-200',
                className
            )}
        >
            <WifiOff className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-600">{message}</p>
        </div>
    );
};

export default OfflineFeatureNotice;
