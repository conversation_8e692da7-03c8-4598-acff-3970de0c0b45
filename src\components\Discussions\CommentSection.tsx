import React, { useState, useRef } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { useComments, useCreateComment, useVoteComment } from '../../hooks/useDiscussions';
import { useMediaUpload } from '../../hooks/useDiscussions';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardFooter } from '../ui/card';
import { Textarea } from '../ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Skeleton } from '../ui/skeleton';
import { Alert, AlertDescription } from '../ui/alert';
import { Loader2, Paperclip, Image, Film } from 'lucide-react';
import { toast } from '../../hooks/use-toast';
import { CustomPagination } from './CustomPagination';
import { CommentItem } from './CommentItem';

interface CommentSectionProps {
  discussionId: string;
}

export function CommentSection({ discussionId }: CommentSectionProps) {
  const [commentText, setCommentText] = useState('');
  const [page, setPage] = useState(1);
  const { comments, pagination, loading, error, refetch } = useComments(discussionId, page, 10);
  const { createComment, loading: postingComment } = useCreateComment();
  const { voteComment } = useVoteComment();
  const { uploadMedia, loading: uploadingMedia } = useMediaUpload();
  const [mediaUrls, setMediaUrls] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { user } = useAuth0();

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!commentText.trim() && mediaUrls.length === 0) {
      return;
    }
    
    // Format content with media urls if present
    let content = commentText.trim();
    if (mediaUrls.length > 0) {
      content += '\n\n' + mediaUrls.map(url => {
        // If image, wrap in markdown image syntax
        if (url.match(/\.(jpeg|jpg|gif|png)$/i)) {
          return `![Image](${url})`;
        }
        // If video, wrap in markdown video syntax (custom for this app)
        else if (url.match(/\.(mp4|webm|ogg)$/i)) {
          return `[VIDEO](${url})`;
        }
        return url;
      }).join('\n');
    }
    
    const result = await createComment({
      discussionId,
      content,
      userName: user?.name ?? "",
      userPictureUrl: user?.picture
    });
    
    if (result) {
      setCommentText('');
      setMediaUrls([]);
      refetch();
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setIsUploading(true);
      
      try {
        const urls = [];
        for (let i = 0; i < e.target.files.length; i++) {
          const file = e.target.files[i];
          const url = await uploadMedia(file);
          if (url) {
            urls.push(url);
          }
        }
        
        setMediaUrls([...mediaUrls, ...urls]);
        
        if (urls.length > 0) {
          toast({
            title: "Files uploaded",
            description: `Successfully uploaded ${urls.length} file${urls.length > 1 ? 's' : ''}`,
          });
        }
      } catch (error) {
        toast({
          title: "Upload failed",
          description: "Could not upload files. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsUploading(false);
        // Reset the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }
    }
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    refetch(newPage, 10);
  };

  const handleVote = async (commentId: string, value: 1 | -1) => {
    await voteComment(commentId, value);
    refetch();
  };

  // Extract first letter for avatar fallback
  const getInitials = (name?: string) => {
    if (!name) return '?';
    return name.charAt(0).toUpperCase();
  };

  if (loading && !comments.length) {
    return (
      <div className="space-y-4">
        <h3 className="text-xl font-semibold">Comments</h3>
        {[1, 2, 3].map((i) => (
          <Card key={i} className="mb-4">
            <CardHeader className="flex flex-row space-x-4 pb-2 pt-4">
              <Skeleton className="w-10 h-10 rounded-full" />
              <div>
                <Skeleton className="h-4 w-32 mb-2" />
                <Skeleton className="h-3 w-20" />
              </div>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-xl font-semibold">
          Comments {pagination?.total ? `(${pagination.total})` : ''}
        </h3>

        <form onSubmit={handleSubmitComment} className="space-y-4">
          <div className="flex gap-4">
            <Avatar className="w-10 h-10">
              <AvatarImage src={user?.picture} alt={user?.name} />
              <AvatarFallback>{getInitials(user?.name)}</AvatarFallback>
            </Avatar>
            <Textarea
              value={commentText}
              onChange={(e) => setCommentText(e.target.value)}
              placeholder="Write a comment..."
              disabled={postingComment || isUploading}
              className="w-full min-h-[100px]"
            />
          </div>
          
          {/* Display media preview */}
          {mediaUrls.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              {mediaUrls.map((url, index) => (
                <div key={index} className="relative group">
                  {url.match(/\.(jpeg|jpg|gif|png)$/i) ? (
                    <img 
                      src={url} 
                      alt="Preview" 
                      className="w-20 h-20 object-cover rounded border border-gray-200" 
                    />
                  ) : (
                    <div className="w-20 h-20 flex items-center justify-center bg-gray-100 rounded border border-gray-200">
                      <Film className="w-8 h-8 text-gray-400" />
                    </div>
                  )}
                  <button
                    type="button"
                    onClick={() => setMediaUrls(mediaUrls.filter((_, i) => i !== index))}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 
                              flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}
          
          <div className="flex justify-between">
            <div>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                multiple
                accept="image/*,video/*"
                onChange={handleFileSelect}
                disabled={postingComment || isUploading}
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                disabled={postingComment || isUploading}
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Paperclip className="mr-2 h-4 w-4" />
                    Add Media
                  </>
                )}
              </Button>
            </div>
            <Button
              type="submit"
              disabled={(!commentText.trim() && mediaUrls.length === 0) || postingComment || isUploading}
            >
              {postingComment ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Posting...
                </>
              ) : 'Post Comment'}
            </Button>
          </div>
        </form>
      </div>

      <div className="space-y-4">
        {comments.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500">Be the first to comment on this discussion!</p>
            </CardContent>
          </Card>
        ) : (
          <>
            {comments.map((comment) => (
              <CommentItem 
                key={comment.id} 
                comment={comment} 
                onVote={handleVote} 
              />
            ))}

            {pagination && pagination.total > pagination.limit && (
              <div className="flex justify-center mt-6">
                <CustomPagination
                  currentPage={pagination.page}
                  totalPages={pagination.pages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}