import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, Users, Loader2, School, BookOpen, Trash2, PenSquare } from 'lucide-react';
import { toast } from "@/hooks/use-toast";
import { useAuth0 } from "@auth0/auth0-react";
import {
    useGetSchoolsQuery,
    useGetSchoolByIdQuery,
    useGetSchoolClassroomsQuery,
    useDeleteClassroomMutation
} from '@/APIConnect';
import { Classroom } from './types';
import ClassroomsTab from '../Schools/ClassroomsTab';



const ClassroomsPage = () => {
    const navigate = useNavigate();
    const { user } = useAuth0();
    const roleId = user?.["http://learnido-app/roleId"] as string;
    const schoolId = user?.["http://learnido-app/schoolId"] as string;
    const isSchoolAdmin = roleId === "2";

    // For SchoolAdmin, use static school ID
    const { data: schoolData, isLoading: isLoadingSchool } = useGetSchoolByIdQuery(
        schoolId,
        { skip: !isSchoolAdmin }
    );
    const { data: schools = [], isLoading: isLoadingSchools } = useGetSchoolsQuery({}, { skip: isSchoolAdmin });
    const [deleteClassroom, { isLoading: isDeleting }] = useDeleteClassroomMutation();

    const handleDelete = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this classroom?')) {
            try {
                await deleteClassroom(id).unwrap();
                toast({
                    title: "Classroom deleted successfully",
                    variant: "default",
                });
            } catch (error) {
                console.error('Error deleting classroom:', error);
                toast({
                    title: "Failed to delete classroom",
                    description: "Please try again later",
                    variant: "destructive",
                });
            }
        }
    };

    const handleEdit = (id: string) => {
        const basePath = isSchoolAdmin ? '/school-admin' : '/admin';
        navigate(`${basePath}/classrooms/${id}`);
    };

    const handleCreate = () => {
        const basePath = isSchoolAdmin ? '/school-admin' : '/admin';
        navigate(`${basePath}/classrooms/create`);
    };

    if (isLoadingSchools || (isSchoolAdmin && isLoadingSchool)) {
        return (
            <div className="flex justify-center items-center h-screen">
                <Loader2 className="w-8 h-8 animate-spin" />
            </div>
        );
    }

    // For SchoolAdmin, create a single-item array with their school
    const schoolsList = isSchoolAdmin && schoolData
        ? [schoolData]
        : schools.resultObject || [];

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="sticky top-0 z-10 bg-white border-b">
                <div className="container mx-auto px-4 py-4">
                    <div className="flex items-center justify-between">
                        <h1 className="text-2xl font-bold flex items-center">
                            <Users className="h-6 w-6 mr-2" />
                            Classrooms
                        </h1>
                        <Button
                            onClick={handleCreate}
                            className="flex items-center"
                        >
                            <Plus className="h-4 w-4 mr-2" />
                            Create Classroom
                        </Button>
                    </div>
                </div>
            </div>

            <div className="container mx-auto px-4 py-6">
                <div className="space-y-6">
                    {schoolsList.map((school: any) => (
                        <ClassroomsTab
                            key={school.id}
                            schoolId={school.id}
                            isSchoolAdmin={isSchoolAdmin}
                        />
                    ))}
                    {schoolsList.length === 0 && (
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">No schools found</p>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </div>
    );
};

interface SchoolClassroomsProps {
    school: any;
    onDelete: (id: string) => void;
    onEdit: (id: string) => void;
    isSchoolAdmin: boolean;
}

const SchoolClassrooms = ({ school, onDelete, onEdit, isSchoolAdmin }: SchoolClassroomsProps) => {
    const { data: classroomsData = [], isLoading } = useGetSchoolClassroomsQuery(school.id);
    const classrooms = classroomsData.resultObject || [];

    if (isLoading) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="flex justify-center">
                        <Loader2 className="w-6 h-6 animate-spin" />
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardContent className="p-6">
                <div className="flex items-center mb-4">
                    <School className="h-5 w-5 mr-2" />
                    <h2 className="text-xl font-semibold">{school.name}</h2>
                </div>
                {classrooms.length === 0 ? (
                    <p className="text-center text-gray-500">No classrooms found</p>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {classrooms.map((classroom: Classroom) => (
                            <Card key={classroom.id}>
                                <CardContent className="p-4">
                                    <div className="flex items-start justify-between">
                                        <div>
                                            <h3 className="font-semibold">{classroom.name}</h3>
                                            <p className="text-sm text-gray-500 mt-1">
                                                {classroom.description}
                                            </p>
                                        </div>
                                        <div className="flex space-x-2">
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => onEdit(classroom.id)}
                                            >
                                                <PenSquare className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => onDelete(classroom.id)}
                                            >
                                                <Trash2 className="h-4 w-4 text-red-500" />
                                            </Button>
                                        </div>
                                    </div>
                                    <div className="mt-4 flex items-center text-sm text-gray-500">
                                        <BookOpen className="h-4 w-4 mr-1" />
                                        <span>Course: {classroom.courseId}</span>
                                    </div>
                                    <div className="mt-2">
                                        <div className="flex items-center text-sm text-gray-500">
                                            <Users className="h-4 w-4 mr-1" />
                                            <span>
                                                {classroom.teacherIds?.length || 0} Teacher{classroom.teacherIds?.length !== 1 ? 's' : ''},
                                                {' '}{classroom.studentIds?.length || 0} Student{classroom.studentIds?.length !== 1 ? 's' : ''}
                                            </span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}
            </CardContent>
        </Card>
    );
};

export default ClassroomsPage;
