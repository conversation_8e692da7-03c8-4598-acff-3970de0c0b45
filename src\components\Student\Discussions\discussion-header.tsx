import { Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useAuth0 } from '@auth0/auth0-react';
import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';

interface DiscussionHeaderProps {
  onNewDiscussion: () => void
}

export function DiscussionHeader({ onNewDiscussion }: DiscussionHeaderProps) {
  const {user} = useAuth0();
  const navigate = useNavigate();
  const [isTeacher, setIsTeacher] = useState(false);
  const roleId = user!['http://learnido-app/roleId'] || '4';

  useEffect(() => {
    if(!user) return;
    console.log(user);
    console.log('roleId', roleId)
    setIsTeacher(roleId === '1' || roleId === '2' || roleId === '3');
  }, [user]);

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold">Discussions</h1>
        <p className="text-muted-foreground mt-1">
          Join the conversation and learn from your peers
        </p>
      </div>

      <div className="flex flex-row space-x-2">
      { roleId == '1' &&
      <Button
        onClick={onNewDiscussion}
        className="hidden md:flex"
      >
        <Plus className="w-4 h-4 mr-2" />
        New Discussion
      </Button>
}

{
  isTeacher &&
  <Button
  onClick={() =>{ 
    // navigate to /discussions/management
    navigate('/discussions/management')
  }}
  className="hidden md:flex"
  >
    <Plus className="w-4 h-4 mr-2" />
    Manage Discussions
  </Button>
}
      </div>

    </div>
  )
}