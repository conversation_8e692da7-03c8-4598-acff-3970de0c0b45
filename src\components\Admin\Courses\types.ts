export interface Subtopic {
    id: string;
    topicId: string;
    courseId: string;
    subTopicName: string;
    topicWeight: number;
    type: string;
    content: string;
    contentUrl: string;
    assignment?: {
        type: 1 | 2; // 1 = Submission type, 2 = quiz type
        title: string;
        assignmentText: string;
        fileUrl: string;
    };
    createdOn: string;
    updateOn: string;
}

export interface Topic {
    id: string;
    name: string;
    description: string;
    chapterId: string;
    subtopics: Subtopic[];
    createdOn: string;
}

export interface Module {
    id: string;
    name: string;
    description: string;
    topics: Topic[];
    createdOn: string;
}

export interface TopicContent {
  id: string;
  topicId: string;
  courseId: string;
  topicName: string;
  type: ContentType;
  content: string;
  contentUrl?: string;
  assignment?: AssignmentRequestDto;
}

export enum ContentType {
    HTML = 1,
    AUDIO_URL = 2,
    VIDEO_URL = 3,
    SCORM = 4
}

export interface AssignmentRequestDto {
  title: string;
  assignmentText: string;
}

export interface CourseCreationRequest {
  name: string;
  description: string;
  premium: boolean;
  subjectId: string;
  createdOn: string;
}

export interface Subject {
  id: string;
  name: string;
  description: string;
}

export interface Course {
  id: string;
  name: string;
  description: string;
  chapters: Module[];
  subjectId: string;
  premium: boolean;
  createdOn: string;
}
