import React, { useState, useEffect } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { ScrollArea } from '@/components/ui/scroll-area';
import SummaryCard from './SummaryCard';
import ProjectLifecycleTracker from './ProjectLifecycleTracker';
import ProjectActivityTable from './ProjectActivityTable';
import SkillImpactCard from './SkillImpactCard';
import NotificationsAlerts from './NotificationsAlerts';
import { useGetStudentProjectDashboardQuery } from '@/APIConnect';
import {
  LifecycleStage,
  ProjectActivity,
  NotificationAlert,
  SummaryInfo,
  SkillImpact,
  ProjectCard,
  StudentProjectDashboardResponse
} from '@/types/projectCenter';
// Utility functions for data transformation
const getStatusColor = (completed: number, total: number): string => {
  if (total === 0) return 'text-gray-400';
  const ratio = completed / total;
  if (ratio >= 0.8) return 'text-lime-500';
  if (ratio >= 0.5) return 'text-yellow-500';
  return 'text-red-600';
};

const getClassAccess = (group: any): string => {
  if (group.classroomId && group.classroomId !== "") return `CLASS-${group.classroomId.slice(-3)}`;
  if (group.schoolId && group.schoolId !== "") return `SCHOOL-${group.schoolId.slice(-3)}`;
  return 'GLOBAL';
};

const transformToSummaryData = (apiResponse: any): SummaryInfo[] => {
  // Handle the real API response structure
  const dashboardData = apiResponse?.resultObject;
  if (!dashboardData || !dashboardData.groups || !Array.isArray(dashboardData.groups)) {
    return [];
  }

  const academicGroups = dashboardData.groups.filter((g: any) => g?.project?.projectType === 'ACADEMIC');
  const industryGroups = dashboardData.groups.filter((g: any) => g?.project?.projectType === 'INDUSTRY');

  return [
    {
      id: 'active-projects',
      title: 'Active Projects',
      value: dashboardData.groups.length,
      iconSrc: '/icons/project-center-icons/active-projects.png',
      iconBgColor: 'bg-lime-500',
      filterOptions: {
        academicLabel: `Academic (${academicGroups.length})`,
        industryLabel: `Industry (${industryGroups.length})`
      }
    },
    {
      id: 'students-participating',
      title: 'Students Participating',
      value: dashboardData.groups.reduce((sum: number, g: any) => sum + (g?.studentIds?.length || 0), 0),
      iconSrc: '/icons/project-center-icons/users.png',
      iconBgColor: 'bg-lime-500',
      filterOptions: {
        academicLabel: 'Current Term',
        industryLabel: 'Active Projects'
      }
    },
    {
      id: 'evaluation-projects',
      title: 'Evaluation Project',
      value: dashboardData.groups.reduce((sum: number, g: any) => sum + (g?.totalPeerReviews || 0) + (g?.totalInstructorReviews || 0), 0),
      iconSrc: '/icons/project-center-icons/evaluation-projects.png',
      iconBgColor: 'bg-lime-500',
      filterOptions: {
        academicLabel: 'Academic',
        industryLabel: 'Industry'
      }
    },
    {
      id: 'completed-projects',
      title: 'Project Completed',
      value: (dashboardData.acedemicGroupLifeCycle?.graded?.length || 0) + (dashboardData.industryGroupLifeCycle?.graded?.length || 0),
      iconSrc: '/icons/project-center-icons/projects-completed.png',
      iconBgColor: 'bg-lime-500',
      filterOptions: {
        academicLabel: 'Academic',
        industryLabel: 'Industry'
      }
    }
  ];
};

const transformToLifecycleStages = (
  apiResponse: any,
  activeTab: 'academic' | 'industry'
): LifecycleStage[] => {
  // Handle the real API response structure
  const dashboardData = apiResponse?.resultObject;
  if (!dashboardData || !dashboardData.groups || !Array.isArray(dashboardData.groups)) {
    return [];
  }

  const lifecycle = activeTab === 'academic'
    ? dashboardData.acedemicGroupLifeCycle
    : dashboardData.industryGroupLifeCycle;

  console.log('Lifecycle Data:', {
    activeTab,
    lifecycle,
    academicLifecycle: dashboardData.acedemicGroupLifeCycle,
    industryLifecycle: dashboardData.industryGroupLifeCycle,
    createdProjects: lifecycle?.created,
    createdProjectsType: typeof lifecycle?.created,
    createdProjectsLength: lifecycle?.created?.length,
    rawAcademicCreated: dashboardData.acedemicGroupLifeCycle?.created,
    rawIndustryCreated: dashboardData.industryGroupLifeCycle?.created
  });

  if (!lifecycle) {
    return [];
  }

  // Helper function to convert MongoDB ObjectId to string
  const objectIdToString = (id: any): string => {
    if (typeof id === 'string') return id;
    if (id && typeof id === 'object' && id.timestamp) {
      // Convert to hex string format to match lifecycle data
      const timestamp = id.timestamp.toString(16).padStart(8, '0');
      const machine = id.machine.toString(16).padStart(6, '0');
      const pid = id.pid.toString(16).padStart(4, '0');
      const increment = id.increment.toString(16).padStart(6, '0');
      return `${timestamp}${machine}${pid}${increment}`;
    }
    return String(id);
  };

  const getProjectsForStage = (projectIds: string[]): ProjectCard[] => {
    if (!projectIds || !Array.isArray(projectIds)) {
      return [];
    }

    console.log('Lifecycle Debug:', {
      projectIds,
      availableGroups: dashboardData.groups.map((g: any) => ({
        groupId: objectIdToString(g.id),
        projectId: objectIdToString(g.project?.id),
        projectName: g.project?.projectName,
        projectType: g.project?.projectType
      }))
    });

    return projectIds.map(projectId => {
      // Find group by matching the project.id (not projectId)
      const group = dashboardData.groups.find((g: any) => {
        const groupProjectId = objectIdToString(g?.project?.id);
        console.log(`Comparing: "${groupProjectId}" === "${projectId}"`, groupProjectId === projectId);
        return groupProjectId === projectId;
      });

      if (!group || !group.project) {
        console.log(`No group found for project ID: ${projectId}`);
        return null;
      }

      console.log(`Found group for ${projectId}:`, {
        groupId: objectIdToString(group.id),
        projectName: group.project.projectName,
        members: group.studentIds?.length || 0
      });

      return {
        id: objectIdToString(group.id),
        name: group.project.projectName,
        members: group.studentIds?.length || 0
      };
    }).filter(Boolean) as ProjectCard[];
  };

  // Test direct access to lifecycle data
  console.log('Direct lifecycle test:', {
    lifecycleCreated: lifecycle?.created,
    lifecycleCreatedType: typeof lifecycle?.created,
    lifecycleCreatedArray: Array.isArray(lifecycle?.created),
    lifecycleKeys: lifecycle ? Object.keys(lifecycle) : 'no lifecycle'
  });

  // Temporary manual test with known project IDs
  const testProjectIds = activeTab === 'academic'
    ? ['685640a68d2cbb6890848a10', '685640c58d2cbb6890848a12']
    : ['68563f5c8d2cbb6890848a0e', '68563f1a8d2cbb6890848a0c'];

  console.log('Testing with manual project IDs:', testProjectIds);

  return [
    {
      id: 'draft-created',
      title: 'Draft / Created',
      projects: getProjectsForStage(testProjectIds) // Use manual IDs for testing
    },
    {
      id: 'groups-formed',
      title: 'Groups Formed',
      projects: getProjectsForStage(lifecycle?.groupsFormed || [])
    },
    {
      id: 'pending-submissions',
      title: 'Pending Submissions',
      projects: getProjectsForStage(lifecycle?.pendingSubmissions || [])
    },
    {
      id: 'peer-review',
      title: 'Peer Review Ongoing',
      projects: getProjectsForStage(lifecycle?.peerReviewOngoing || [])
    },
    {
      id: 'final-submission',
      title: 'Final Submission',
      projects: getProjectsForStage(lifecycle?.finalSubmission || [])
    },
    {
      id: 'instructor-review',
      title: 'Instructor Review',
      projects: getProjectsForStage(lifecycle?.instructorReviewOngoing || [])
    }
  ];
};

const transformToProjectActivity = (apiResponse: any): ProjectActivity[] => {
  // Handle the real API response structure
  const dashboardData = apiResponse?.resultObject;
  if (!dashboardData || !dashboardData.groups || !Array.isArray(dashboardData.groups)) {
    return [];
  }

  // Helper function to convert MongoDB ObjectId to string
  const objectIdToString = (id: any): string => {
    if (typeof id === 'string') return id;
    if (id && typeof id === 'object' && id.timestamp) {
      // Convert to hex string format to match lifecycle data
      const timestamp = id.timestamp.toString(16).padStart(8, '0');
      const machine = id.machine.toString(16).padStart(6, '0');
      const pid = id.pid.toString(16).padStart(4, '0');
      const increment = id.increment.toString(16).padStart(6, '0');
      return `${timestamp}${machine}${pid}${increment}`;
    }
    return String(id);
  };

  return dashboardData.groups.map((group: any) => {
    if (!group || !group.project) {
      return null;
    }

    const submissionCount = group.totalSubmissions || 0;
    const submissionTotal = group.totalAssignments || 0;
    const peerReviewCount = group.totalPeerReviews || 0;
    const peerReviewTotal = group.totalAssignments || 0;
    const instructorReviewCount = group.totalInstructorReviews || 0;
    const instructorReviewTotal = group.totalAssignments || 0;

    return {
      id: objectIdToString(group.id) || '',
      projectName: group.project.projectName || 'Unknown Project',
      type: group.project.projectType === 'ACADEMIC' ? 'Academic' : 'Industry',
      classAccess: getClassAccess(group),
      members: group.studentIds?.length || 0,
      submissionStatus: {
        count: submissionCount,
        total: submissionTotal,
        statusColor: getStatusColor(submissionCount, submissionTotal)
      },
      peerReviewStatus: {
        count: peerReviewCount,
        total: peerReviewTotal,
        statusColor: getStatusColor(peerReviewCount, peerReviewTotal)
      },
      instructorReviewStatus: {
        count: instructorReviewCount,
        total: instructorReviewTotal,
        statusColor: getStatusColor(instructorReviewCount, instructorReviewTotal)
      }
    };
  }).filter(Boolean) as ProjectActivity[];
};

const ProjectCenterPage: React.FC = () => {
  const { user } = useAuth0();
  const navigate = useNavigate();

  // Get token from Redux store for debugging
  const userToken = useSelector((state: any) => state.user.userToken);

  // State for lifecycle tab
  const [activeLifecycleTab, setActiveLifecycleTab] = useState<'academic' | 'industry'>('academic');

  // Fetch real data from API - skip if no token
  const {
    data: dashboardData,
    isLoading,
    error,
    refetch
  } = useGetStudentProjectDashboardQuery(undefined, {
    skip: !userToken || !user
  });

  // Debug logging to see what we're getting from the API
  React.useEffect(() => {
    console.log('Project Center Debug Info:', {
      userToken: userToken,
      tokenType: typeof userToken,
      hasToken: !!userToken,
      user: user,
      dashboardData,
      isLoading,
      error,
      hasData: !!dashboardData,
      groupsLength: dashboardData?.resultObject?.groups?.length,
      actualGroups: dashboardData?.resultObject?.groups,
      academicLifecycle: dashboardData?.resultObject?.acedemicGroupLifeCycle,
      industryLifecycle: dashboardData?.resultObject?.industryGroupLifeCycle
    });

    // Also log the error details if there is one
    if (error) {
      console.error('API Error Details:', error);
    }
  }, [dashboardData, isLoading, error, userToken, user]);

  // Manual API test function for debugging
  React.useEffect(() => {
    const testAPI = async () => {
      if (userToken && user) {
        try {
          console.log('Testing manual API call...');
          const response = await fetch('https://nexo-dev-fbf0cvc7bhehbuft.australiacentral-01.azurewebsites.net/StudentGroup/student-dashboard', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${userToken}`,
              'Content-Type': 'application/json'
            }
          });

          console.log('Manual API Response Status:', response.status);
          console.log('Manual API Response Headers:', Object.fromEntries(response.headers.entries()));

          if (response.ok) {
            const data = await response.json();
            console.log('Manual API Response Data:', data);
          } else {
            const errorText = await response.text();
            console.error('Manual API Error Response:', errorText);
          }
        } catch (error) {
          console.error('Manual API Call Failed:', error);
        }
      }
    };

    // Only run once when component mounts and we have a token
    if (userToken && user && !dashboardData && !isLoading) {
      testAPI();
    }
  }, [userToken, user, dashboardData, isLoading]);

  // Transform API data or use defaults with proper error handling
  const summaryData: SummaryInfo[] = dashboardData && !error
    ? transformToSummaryData(dashboardData)
    : [
        {
          id: 'active-projects',
          title: 'Active Projects',
          value: '07',
          iconSrc: '/icons/project-center-icons/active-projects.png',
          iconBgColor: 'bg-lime-500',
          filterOptions: {
            academicLabel: 'Academic',
            industryLabel: 'Industry'
          }
        },
        {
          id: 'students-participating',
          title: 'Students Participating',
          value: '28',
          iconSrc: '/icons/project-center-icons/users.png',
          iconBgColor: 'bg-lime-500',
          filterOptions: {
            academicLabel: 'Current Term',
            industryLabel: 'Active Projects'
          }
        },
        {
          id: 'evaluation-projects',
          title: 'Evaluation Project',
          value: '07',
          iconSrc: '/icons/project-center-icons/evaluation-projects.png',
          iconBgColor: 'bg-lime-500',
          filterOptions: {
            academicLabel: 'Academic',
            industryLabel: 'Industry'
          }
        },
        {
          id: 'completed-projects',
          title: 'Project Completed',
          value: '07',
          iconSrc: '/icons/project-center-icons/projects-completed.png',
          iconBgColor: 'bg-lime-500',
          filterOptions: {
            academicLabel: 'Academic',
            industryLabel: 'Industry'
          }
        },
      ];

  // Transform lifecycle data or use defaults with proper error handling
  const lifecycleStages: LifecycleStage[] = dashboardData && !error
    ? transformToLifecycleStages(dashboardData, activeLifecycleTab)
    : [
        {
          id: 'draft-created',
          title: 'Draft / Created',
          projects: [
            { id: 'p1', name: 'Project 1', members: 4 },
            { id: 'p4a', name: 'Project 4', members: 4 },
            { id: 'p4b', name: 'Project 4', members: 4 }
          ]
        },
        {
          id: 'groups-formed',
          title: 'Groups Formed',
          projects: [
            { id: 'p8', name: 'Project 8', members: 4 },
            { id: 'p10a', name: 'Project 10', members: 4 },
            { id: 'p21a', name: 'Project 21', members: 4 },
            { id: 'p16a', name: 'Project 16', members: 4 }
          ]
        },
        {
          id: 'pending-submissions',
          title: 'Pending Submissions',
          projects: [
            { id: 'p8p', name: 'Project 8', members: 4 },
            { id: 'p10p', name: 'Project 10', members: 4 }
          ]
        },
        {
          id: 'peer-review',
          title: 'Peer Review Ongoing',
          projects: [
            { id: 'p8pr', name: 'Project 8', members: 4 }
          ]
        },
        {
          id: 'final-submission',
          title: 'Final Submission',
          projects: [
            { id: 'p8fs', name: 'Project 8', members: 4 },
            { id: 'p10fs', name: 'Project 10', members: 4 },
            { id: 'p21fs', name: 'Project 21', members: 4 }
          ]
        },
        {
          id: 'instructor-review',
          title: 'Instructor Review',
          projects: [
            { id: 'p8ir', name: 'Project 8', members: 4 },
            { id: 'p10ir', name: 'Project 10', members: 4 },
            { id: 'p21ir', name: 'Project 21', members: 4 },
            { id: 'p16ir', name: 'Project 16', members: 4 }
          ]
        },
      ];

  // Transform activity data or use defaults with proper error handling
  const activityData: ProjectActivity[] = dashboardData && !error
    ? transformToProjectActivity(dashboardData)
    : [
        {
          id: 'pa1',
          projectName: 'Project 1',
          type: 'Academic',
          classAccess: 'BIO202',
          members: 4,
          submissionStatus: { count: 4, total: 6, statusColor: 'text-red-600' },
          peerReviewStatus: { count: 4, total: 6, statusColor: 'text-lime-500' },
          instructorReviewStatus: { count: 4, total: 6, statusColor: 'text-lime-500' }
        },
        {
          id: 'pa2',
          projectName: 'Project 2',
          type: 'Industry',
          classAccess: 'BIO202',
          members: 4,
          submissionStatus: { count: 4, total: 6, statusColor: 'text-red-600' },
          peerReviewStatus: { count: 4, total: 6, statusColor: 'text-lime-500' },
          instructorReviewStatus: { count: 4, total: 6, statusColor: 'text-lime-500' }
        },
        {
          id: 'pa3',
          projectName: 'Project 3',
          type: 'Academic',
          classAccess: 'BIO202',
          members: 4,
          submissionStatus: { count: 4, total: 6, statusColor: 'text-lime-500' },
          peerReviewStatus: { count: 4, total: 6, statusColor: 'text-lime-500' },
          instructorReviewStatus: { count: 4, total: 6, statusColor: 'text-lime-500' }
        },
        {
          id: 'pa4',
          projectName: 'Project 4',
          type: 'Academic',
          classAccess: 'BIO202',
          members: 4,
          submissionStatus: { count: 4, total: 6, statusColor: 'text-lime-500' },
          peerReviewStatus: { count: 4, total: 6, statusColor: 'text-red-600' },
          instructorReviewStatus: { count: 4, total: 6, statusColor: 'text-lime-500' }
        },
        {
          id: 'pa5',
          projectName: 'Project 5',
          type: 'Academic',
          classAccess: 'BIO202',
          members: 4,
          submissionStatus: { count: 4, total: 6, statusColor: 'text-red-600' },
          peerReviewStatus: { count: 4, total: 6, statusColor: 'text-red-600' },
          instructorReviewStatus: { count: 4, total: 6, statusColor: 'text-lime-500' }
        },
        {
          id: 'pa6',
          projectName: 'Project 6',
          type: 'Industry',
          classAccess: 'BIO202',
          members: 4,
          submissionStatus: { count: 4, total: 6, statusColor: 'text-red-600' },
          peerReviewStatus: { count: 4, total: 6, statusColor: 'text-lime-500' },
          instructorReviewStatus: { count: 4, total: 6, statusColor: 'text-lime-500' }
        },
        {
          id: 'pa7',
          projectName: 'Project 7',
          type: 'Industry',
          classAccess: 'BIO202',
          members: 4,
          submissionStatus: { count: 4, total: 6, statusColor: 'text-red-600' },
          peerReviewStatus: { count: 4, total: 6, statusColor: 'text-red-600' },
          instructorReviewStatus: { count: 4, total: 6, statusColor: 'text-lime-500' }
        },
      ];

  // Mock data for skill impact
  const skillImpactData: SkillImpact = {
    title: 'Skill Index Impacted',
    value: '62%',
    iconSrc: '/icons/project-center-icons/skill-index-impacted.png',
    iconBgColor: 'bg-lime-500',
    skills: [
      { name: 'Employability Skills', progress: 75 },
      { name: 'Employability Skills', progress: 60 }
    ]
  };

  // Mock data for notifications
  const notificationsData: NotificationAlert[] = [
    { id: 'n1', type: 'Submission', date: '1 May 2025', title: 'Types of Noun' },
    { id: 'n2', type: 'Submission', date: '11 May 2025', title: 'English Assignment', isNew: true },
    { id: 'n3', type: 'Submission', date: '14 May 2025', title: 'Type of Pronoun' },
  ];

  // Handlers
  const handleViewProject = (project: ProjectActivity) => {
    navigate(`/project-center/${project.id}`);
  };

  const handleProjectCardClick = (project: ProjectCard) => {
    navigate(`/project-center/${project.id}`);
  };

  const handleFilterProjects = () => {
    // Implementation for filtering projects
    console.log('Filtering projects');
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading project data...</p>
        </div>
      </div>
    );
  }

  // Show error state with retry option
  if (error) {
    return (
      <div className="flex items-center justify-center h-screen bg-white">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <p className="text-gray-600 mb-4">Failed to load project data</p>
          <button
            onClick={() => refetch()}
            className="bg-lime-500 hover:bg-lime-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col lg:flex-row bg-white">
      {/* Main Content Area */}
      <main className="flex-1 p-6 space-y-6 lg:w-3/4">
        {/* Header */}
        <div className="flex flex-col lg:flex-row items-center">
          <h1 className="text-zinc-800 text-3xl font-semibold">Project Center</h1>
        </div>

        {/* Summary Cards Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
          {summaryData.map(item => (
            <SummaryCard
              key={item.id}
              id={item.id}
              title={item.title}
              value={item.value}
              iconSrc={item.iconSrc}
              iconBgColor={item.iconBgColor}
              filterOptions={item.filterOptions}
            />
          ))}
        </div>

        {/* Project Lifecycle Tracker */}
        <div className="overflow-x-auto">
          <ProjectLifecycleTracker
            stages={lifecycleStages}
            activeTab={activeLifecycleTab}
            onTabChange={setActiveLifecycleTab}
            onProjectClick={handleProjectCardClick}
          />
        </div>

        {/* Project Activity Tracker */}
        <div className="overflow-x-auto">
          <ProjectActivityTable
            activities={activityData}
            onViewProject={handleViewProject}
            onFilter={handleFilterProjects}
          />
        </div>
      </main>

      {/* Right Sidebar Area */}
      <aside className="w-full lg:w-1/4 bg-white border-l-2 border-t-2 border-neutral-200 p-4 lg:h-screen lg:sticky top-0">
        <ScrollArea className="h-full">
          <div className="space-y-6">
            {/* Skill Index Impacted */}
            <SkillImpactCard
              title={skillImpactData.title}
              value={skillImpactData.value}
              iconSrc={skillImpactData.iconSrc}
              iconBgColor={skillImpactData.iconBgColor}
              skills={skillImpactData.skills}
            />

            {/* Notifications & Alerts */}
            <NotificationsAlerts notifications={notificationsData} />
          </div>
        </ScrollArea>
      </aside>
    </div>
  );
};

export default ProjectCenterPage; 