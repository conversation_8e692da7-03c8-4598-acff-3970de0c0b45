import { discussionsBaseUrl } from '@/constant/AppConstant';

/**
 * Sends a message to the chatbot API.
 * @param {string} message - The user's message.
 * @param {string} [context] - Optional context for the chatbot.
 * @param {boolean} [stream=true] - Whether to use streaming responses.
 * @param {string} token - The authentication token.
 * @returns {Promise<Response>} The API response.
 */
export async function sendMessageToChatbot(message, context = '', stream = true, token) {
  const response = await fetch(`${discussionsBaseUrl}/chat`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ message, context, stream }),
  });

  if (!response.ok) {
    throw new Error('Failed to send message to chatbot');
  }

  return response;
}

/**
 * Retrieves the chat history from the chatbot API.
 * @param {string} token - The authentication token.
 * @returns {Promise<Object>} The chat history.
 */
export async function getChatHistory(token) {
  const response = await fetch(`${discussionsBaseUrl}/chat/history`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch chat history');
  }

  const data = await response.json();
  return data.history; // Return only the history array
}
