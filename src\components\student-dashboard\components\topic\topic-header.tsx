import { ChevronLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import TopicRibbon from "../../topic-ribbon"

interface TopicHeaderProps {
    courseName: string | null
    topicName: string
    onBackClick: () => void
    className?: string
}

export function TopicHeader({
    courseName,
    topicName,
    onBackClick,
    className
}: TopicHeaderProps) {
    return (
        <div className={cn(
            "sticky top-0 z-10 flex flex-col sm:flex-row items-start sm:items-center gap-2 bg-background/95 backdrop-blur-sm border-b px-3 py-2",
            className
        )}>
            <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 sm:px-3 -ml-2 text-muted-foreground hover:text-foreground"
                onClick={onBackClick}
            >
                <ChevronLeft className="w-4 h-4 mr-1" />
                <span className="text-sm hidden sm:inline">Back</span>
            </Button>

            <div className="flex-1 min-w-0 flex items-center">
                <TopicRibbon
                    chapterName={`${courseName} / ${topicName}`}
                />
            </div>
        </div>
    )
}
