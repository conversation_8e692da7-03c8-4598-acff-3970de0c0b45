@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');




.quiz-container {
    font-family: Arial, sans-serif;
    padding: 60px;
    margin: 0 auto;
    background-color: #FEFAE0;
    /* height: 80vh; */
    height: 65vh;
overflow-y: scroll;
    
  }
  
  .quiz-container  .maincontentview {
    width: 100%;
    height: 100%;
    background-color: #FEFAE0;
    
    }

  .quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .quiz-back-button {
    font-size: 20px;
    color: #555;
    font-family: 'DM Sans', sans-serif;
    font-weight: 500;
    color: #000;
    background: none;
    border: none;
    cursor: pointer;
  }
  
  .quiz-name {
    flex: 1;
    text-align: center;
    font-size: 20px;
    color: #333;
  }
  
  .timer {
    font-size: 20px;
    color: #555;
    font-weight: bold;
  }
  
  .progress-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }

  .progress-dot-maincontainer {
    display: flex;
    /* z-index: 2; */
  }
  
  .progress-dot {
    width: 23px;
    height: 23px;
    border-radius: 50%;
    /* margin: 0 15px; */
   
    background-color: #fff;
  margin-top: -7px;
  margin-left: -10px;
  
    /* z-index: 2; */
  }

  .test-container {
    background-color: #703D3D;
    border-radius: 8px;
    height: 5px;
  }
  
  .progress-line {
   background-color: #703D3D33;
   margin-top: 7px;
height: 8px;
border-color: #703D3D;
border-radius: 20px;
border-style: solid;
border-width: 1;
 
    /* margin: 0 15px; */
    justify-content: center;
    /* position: absolute; */
    /* height: 4px; */
    /* background-color: black; */
     /* Beige line */
  

  }

  .progress-line1 {
    margin-top: 7px;
   }

  .rogress-dot-maincontainer {
    display: flex;
    z-index: 1000;
  }

  .progress-filled {
    height: 100%;
    background-color: #6e4241; /* Dark brown filled line */
    z-index: 2;
  }
  
  .progress-dot.active {
    background-color: #703D3D;
  }
  
  .question-container {
    background-color: #703D3D;
    padding: 20px;
    border-radius: 8px;
    color: #fff;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
  }

  .options-maincontainer {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    color: #fff;
    height: 150px;
    border: 1;
    border-width: 1;
    border-color: #703D3D;
    border-top-left-radius: 0;
    border-style: solid;
    border-top-right-radius:0;
    border: none;
    overflow-y: scroll;
  }

  .test-container {
    background-color: #703D3D;
    border-radius: 8px;
  }
  
  .question-title {
    font-size: 20px;
    margin-bottom: 10px;
    font-weight: 400;
    margin-left: 25px;
  }
  
  .question-text {
    font-size: 18px;
    margin-top: -5px;
    /* margin-bottom: 10px; */
    color: #fff;
   font-weight: bold;
   margin-left: 25px;
  }
  
  .options-container {
    display: flex;
    flex-direction: column;
    margin-left: 20px;
  }
  
  .option {
    /* margin-bottom: 15px; */
    display: flex;
    align-items: center;
  }
  
  .option-label {
    margin-left: 10px;
    font-size: 16px;
    color: #000;
  }
  
  .continue-button1 {
    justify-content: flex-end;
    margin-top: -25px;
  }

  .align-right {
    text-align: right;
    }

  .continue-button {
    background-color: #703D3D;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 32px;
    cursor: pointer;
    margin-right: 80px;
    /* display: block; */
    /* margin: 0 auto; */
    /* font-weight: bold; */
    text-align: right;
   margin-top: 50px;
   font-size: 16px;
  
    /* margin-left: 100px; */
    /* margin: 40 */
    /* text-align: center;
    justify-content: center;
    align-items: center;
    align-self: center; */
  }
  
  .no-of-question {
    text-align: center;
    font-size: 20px;
  }

  .no-data {
    text-align: center;
  }

  /* Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Modal Content */
.modal-content-quiz {
  background-color: #FFFFFF;
  padding: 20px;
  border-radius: 12px;
  width: 400px;
  text-align: center;
  box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease-in-out;
}

.modal-quiz-img-container {
  justify-content: center;
}

.modal-quiz-img-container img {
  width: 100%;
}

.modal-quiz-result {
  color: black
}

@keyframes fadeIn {
  from {
      opacity: 0;
      transform: translateY(-20px);
  }
  to {
      opacity: 1;
      transform: translateY(0);
  }
}

h2 {
  font-size: 24px;
  margin-bottom: 20px;
}

.result-icon {
  /* font-size: 40px; */
  margin-bottom: 10px;
  background-color: #8D1A11;
  border-radius: 40px;
  width: 30px;
  height: 30px;
}

.fail-icon {
  color: #FFFFFF;
  width: 30px;
  height: 30px;
 align-content: center;
}

.fail-message {
  font-size: 16px;
  color: #333;
  margin-bottom: 20px;
}



 .next-topic-cont {
  display: flex;
  justify-content: center;
  margin-top: -20px;
}

.next-topic-message {
  font-size: 16px;
  color: #333;
  margin-bottom: 20px;

}
.next-topic-name-message {
  
 color: #703D3D;;
 /* text-decoration: underline; */
 cursor: pointer;
}

/* Score and Points */
.score-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  width:100%;

}

.score-box {
  font-weight: bold;
  background-color: #e3d3cf;
  padding: 20px;
  border-radius: 8px;
  width: 43%;
  text-align: center;

  border: 1px solid transparent;
 
  border-width: 1;
  border-color: #703D3D;

  padding: 10px;
  
}

.score-box h3 {
  font-size: 34px;
  margin: 10px 0;
  color: #FFFFFF;
  font-weight: bold;
}

.score-box p {
  font-size: 14px;
  color: #FFFFFF;
}

/* Buttons */
.button-container {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-bottom: 25px;
  margin-top: 10px;
}

.button-container .review-btn {

  background-color:  #FFFFFF;
  color: #703D3D; 
  border-radius: 32px;
  
  /* display: block; */
  /* margin: 0 auto; */
  font-weight: bold;
  border: solid;
  width: 100%;
  height: 40px;
}

.button-container .retry-btn {
  
  background-color:   #703D3D;
  color: #FFFFFF; 
  border-radius: 32px;

  /* display: block; */
  /* margin: 0 auto; */
  font-weight: bold;
  border: solid;
  width: 100%;
}

.review-btn {
  width: 45%;
}

.retry-btn {
  width: 45%;
  background-color: #703D3D;
}

.custom-hr {
  border: none;
  height: 1px;
  background-color: #FFFFFF;
  border-radius: 2px;
}

/* 
button {
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 16px;
  border: 1px solid transparent;
  cursor: pointer;
}

.review-btn {
  background-color: red;
  border: 1px solid #5A4038;
  color: hsl(14, 23%, 29%);
  border-width: 1;
  border-color: #703D3D;
  color: #703D3D;
  
}

.retry-btn {
  background-color: #5A4038;
  color: white;
}

button:hover {
  opacity: 0.8;
} */

.quiz-footer {
  background: #FFFFFF;
  box-shadow: 0px -4px 4px rgba(19, 109, 98, 0.1);
  padding: 20px;
  display: flex;
  justify-content: flex-end;
  position: relative;
 
}



.content-media-continue-button {
  margin-right: 60px;
  background: #703D3D;
  color: #fff;
  padding: 12px 28px;
  border: none;
  border-radius: 31px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.3s ease-in-out, transform 0.2s ease-in-out;
}

.quiz-result {
  background-color: #FEFAE0;
  height: 10vh;
}

.fill-in-blank-input {
  /* borderTopWidth:0,
  borderLeftWidth:0,
  borderRightWidth:0,
  borderTopColor:'#fff',
//  height: 40, // Adjust height to make it slim
  borderBottomWidth: 2, // Bottom border to resemble a line
  borderBottomColor: '#000', // Black color for the line
  fontSize: 18, // Size of the text
  padding: 0, // No padding to keep it clean
  caretColor: '#000' */
  outline: 0;
  border-width: 0 0 2px;
  border-color: #703D3D;
  margin-left: 10
}

.fill-in-blank-input:focus {
  border-color: fff;
  /* outline: 1px dotted #000 */
}


input[type='checkbox'] {
  accent-color: #703D3D;
}

input[type='radio'] {
  accent-color: #703D3D;
}

 .fill-in {
  border: 'none';
  border-bottom: '2px solid white';
  background-color: 'transparent';
  color: '#fff';
  font-size: '20px';
  width: '200px';
  margin-left: '20px';
  margin-right: '20px';
  outline: 'none';
}