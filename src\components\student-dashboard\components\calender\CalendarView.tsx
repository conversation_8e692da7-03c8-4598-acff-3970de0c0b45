import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, ChevronDown, StickyNote, Clock } from 'lucide-react';
import { useGetCalendarEventsQuery } from '@/APIConnect';

interface CalendarEvent {
  id: string;
  type: 'Live Lecture' | 'Submission' | 'Assignment' | 'Project Deadline' | 'Assignment Deadline' | 'All Day Note' | 'Timed Note';
  time: string;
  title: string;
  startTime: string;
  endTime: string;
}

interface CalendarViewProps {
  events?: CalendarEvent[];
}

const CalendarView: React.FC<CalendarViewProps> = ({ events = [] }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewType, setViewType] = useState<'Day' | 'Week' | 'Month'>('Day');

  // Calculate week range for API
  const getWeekRange = (date: Date) => {
    const start = new Date(date);
    const day = start.getDay();
    const diff = start.getDate() - day + (day === 0 ? -6 : 1);
    start.setDate(diff);
    const end = new Date(start);
    end.setDate(start.getDate() + 6);
    return {
      start: start.toISOString().slice(0, 10),
      end: end.toISOString().slice(0, 10),
    };
  };
  const { start, end } = getWeekRange(currentDate);
  const { data: apiData, isLoading } = useGetCalendarEventsQuery({ start, end });

  // Map API events, deadlines, and notes to UI events
  const apiEvents = (apiData?.events || []).map((ev: any) => ({
    id: ev.id,
    type: 'Live Lecture', // or map type if needed
    time: `${new Date(ev.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - ${new Date(ev.endTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`,
    title: ev.description || 'Event',
    startTime: new Date(ev.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    endTime: new Date(ev.endTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    date: new Date(ev.startTime).toISOString().slice(0, 10)
  }));

  const apiDeadlines = (apiData?.deadlines || []).map((dl: any) => ({
    id: dl.referenceId,
    type: dl.type === 'Project' ? 'Project Deadline' : 'Assignment Deadline',
    time: new Date(dl.dueDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    title: dl.assignmentTitle,
    startTime: new Date(dl.dueDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    endTime: new Date(dl.dueDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    date: new Date(dl.dueDate).toISOString().slice(0, 10)
  }));

  const apiNotes = (apiData?.notes || []).map((note: any) => {
    // Handle note reminders - only show notes that have reminders
    console.log('Processing note for calendar:', note);

    if (note.reminderDateTime && note.noteType !== 0) {
      const reminderDate = new Date(note.reminderDateTime);
      const noteEvent = {
        id: note.id,
        type: note.noteType === 1 ? 'All Day Note' : 'Timed Note',
        time: note.noteType === 1 ? 'All Day' : reminderDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        title: note.title,
        startTime: note.noteType === 1 ? '00:00' : reminderDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        endTime: note.noteType === 1 ? '23:59' : reminderDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        date: reminderDate.toISOString().slice(0, 10)
      };

      console.log('Created note event for calendar:', noteEvent);
      return noteEvent;
    }
    return null;
  }).filter(Boolean);

  const realEvents = [...apiEvents, ...apiDeadlines, ...apiNotes];

  // Filter events for the selected date
  const selectedDateStr = selectedDate.toISOString().slice(0, 10);
  const filteredEvents = realEvents.filter(e => e.date === selectedDateStr);
  const displayEvents = filteredEvents.length > 0 ? filteredEvents : [];

  // Get current week days
  const getWeekDays = () => {
    const startOfWeek = new Date(currentDate);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    startOfWeek.setDate(diff);

    const weekDays = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      weekDays.push(date);
    }
    return weekDays;
  };

  const weekDays = getWeekDays();
  const dayNames = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'long', 
      year: 'numeric' 
    });
  };

  const formatSelectedDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'long', 
      day: 'numeric',
      year: 'numeric' 
    });
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isSelected = (date: Date) => {
    return date.toDateString() === selectedDate.toDateString();
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'Live Lecture':
        return 'bg-[#347468]';
      case 'Submission':
        return 'bg-[#347468]';
      case 'Assignment':
        return 'bg-[#347468]';
      case 'Project Deadline':
        return 'bg-[#347468]';
      case 'Assignment Deadline':
        return 'bg-[#347468]';
      case 'All Day Note':
        return 'bg-blue-500';
      case 'Timed Note':
        return 'bg-purple-500';
      default:
        return 'bg-[#347468]';
    }
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'All Day Note':
        return <StickyNote className="w-3 h-3" />;
      case 'Timed Note':
        return <Clock className="w-3 h-3" />;
      default:
        return null;
    }
  };

  // Dynamically generate timeline timestamps based on event range and interval
  function parseTimeString(time: string) {
    // Handles 'HH:MM AM/PM' format
    const [h, m] = time.split(/:| /);
    let hour = parseInt(h, 10);
    const minute = parseInt(m, 10);
    const ampm = time.slice(-2);
    if (ampm === 'PM' && hour !== 12) hour += 12;
    if (ampm === 'AM' && hour === 12) hour = 0;
    return hour * 60 + minute;
  }
  function formatTime(minutes: number) {
    const h = Math.floor(minutes / 60);
    const m = minutes % 60;
    const ampm = h >= 12 ? 'PM' : 'AM';
    const hour12 = h % 12 === 0 ? 12 : h % 12;
    return `${hour12}:${m.toString().padStart(2, '0')} ${ampm}`;
  }
  let minTime = 8 * 60, maxTime = 18 * 60; // default 8:00 AM to 6:00 PM
  if (realEvents.length > 0) {
    const times = realEvents.map(e => parseTimeString(e.startTime));
    minTime = Math.min(...times, minTime);
    maxTime = Math.max(...times, maxTime);
    // Round minTime down to nearest 2 hours, maxTime up
    minTime = Math.floor(minTime / 120) * 120;
    maxTime = Math.ceil(maxTime / 120) * 120;
  }
  const interval = 120; // 2 hours
  const timelineTicks: string[] = [];
  for (let t = minTime; t <= maxTime; t += interval) {
    timelineTicks.push(formatTime(t));
  }
  const timelineHeight = 180;
  const gap = timelineTicks.length > 1 ? timelineHeight / (timelineTicks.length - 1) : 0;

  return (
    <div className="w-full max-w-md mx-auto bg-white">
      {/* Calendar Header */}
      <div className="flex items-center justify-center mb-4 w-full h-8 relative">
        {/* Previous Button */}
        <button
          onClick={() => navigateMonth('prev')}
          className="absolute left-0 flex items-center justify-center border-[0.8px] border-[#E0E0E0] rounded-full w-8 h-8"
        >
          <ChevronLeft
            className="text-gray-600 w-4 h-4"
          />
        </button>

        {/* Month/Year Display */}
        <div
          className="text-center font-semibold text-[#222222] font-inter text-base leading-5"
        >
          {formatDate(currentDate)}
        </div>

        {/* Next Button */}
        <button
          onClick={() => navigateMonth('next')}
          className="absolute right-0 flex items-center justify-center border-[0.8px] border-[#E0E0E0] rounded-full w-8 h-8"
        >
          <ChevronRight
            className="text-gray-600 w-4 h-4"
          />
        </button>
      </div>

      {/* Week Days */}
      <div className="flex justify-center mb-6 h-16 gap-2">
        {weekDays.map((date, index) => {
          const isCurrentDay = isToday(date);
          const isSelectedDay = isSelected(date);
          // Check if there is an event, deadline, or note for this date
          const dateStr = date.toISOString().slice(0, 10);
          const hasEvent = realEvents.some(event => event.date === dateStr);

          return (
            <button
              key={index}
              onClick={() => setSelectedDate(date)}
              className={`flex flex-col items-center justify-center w-[44.86px] h-16 gap-0.5 py-1 px-0.5 ${
                isSelectedDay ? 'bg-[#347468]' : 'bg-white'
              } border ${isSelectedDay ? '' : 'border-[#E0E0E0]'}`}
              style={{ borderRadius: '8px' }}
            >
              {/* Date Number */}
              <div
                className={`text-center w-[32.86px] h-6 font-inter font-normal text-xl leading-6 ${
                  isSelectedDay ? 'text-white' : 'text-[#222222]'
                }`}
              >
                {date.getDate()}
              </div>

              {/* Day Name */}
              <div
                className={`text-center w-[32.86px] h-4 font-inter font-normal text-xs leading-4 ${
                  isSelectedDay ? 'text-white' : 'text-[#717171]'
                }`}
              >
                {dayNames[index]}
              </div>

              {/* Notification Dot */}
              {hasEvent && (
                <div
                  className={`rounded-full w-1 h-1 ${
                    isSelectedDay ? 'bg-white' : 'bg-[#347468]'
                  }`}
                />
              )}
            </button>
          );
        })}
      </div>

      {/* Calendar Frame */}
      <div className="bg-white border border-[#347468] w-[392px] h-[349px] rounded-tl-2xl rounded-br-2xl rounded-bl-2xl shadow-[0px_4px_16.5px_0px_#00000040] overflow-hidden">
        {/* Date Header with Dropdown */}
        <div className="flex justify-between items-center w-[289px] h-[37px] mt-4 ml-4">
          {/* Selected Date */}
          <div className="text-[#2D2D2D] capitalize w-[117px] h-[21px] font-inter font-semibold text-base leading-none">
            {formatSelectedDate(selectedDate)}
          </div>

          {/* Day Dropdown */}
          <button className="flex items-center bg-white border border-[#347468] rounded-full w-[78px] h-[37px] gap-1 px-3 py-2">
            <span className="text-[#347468] capitalize w-[30px] h-[21px] font-inter font-semibold text-base leading-none">
              {viewType}
            </span>
            <ChevronDown className="text-[#347468] w-4 h-4 stroke-[2.5]" />
          </button>
        </div>

        {/* Horizontal Line */}
        <div className="border-t border-[#347468] w-[321px] h-0 mx-4 mt-[19px]" />

        {/* Timeline and Events - Scrollable */}
        <div className="relative mt-[37px] h-[180px] overflow-y-auto">
          {/* Side Timeline - interval ticks */}
          <div className="absolute flex flex-col w-[59px] h-[180px] justify-between top-0 left-4">
            {timelineTicks.map((time, idx) => (
              <div
                key={time}
                className="relative flex flex-col items-center"
                style={{ marginTop: idx === 0 ? 0 : gap - 18 }}
              >
                <div className="text-[#2D2D2D] text-center capitalize w-[62px] h-[18px] font-inter font-semibold text-sm leading-none whitespace-nowrap">
                  {time}
                </div>
                {/* Dotted line except for last timestamp */}
                {idx < timelineTicks.length - 1 && (
                  <div className="w-0.5" style={{ height: gap - 18 }}>
                    <div className="h-full border-r-2 border-dotted border-[#347468] mt-1 mb-1"></div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Event Cards - align to closest timeline tick */}
          {isLoading ? (
            <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-[#347468] font-inter">Loading...</div>
          ) : displayEvents.length === 0 ? (
            <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-[#347468] font-inter">No events present</div>
          ) : (
            displayEvents.map((event) => {
              // Find the closest timeline tick
              const eventMinutes = parseTimeString(event.startTime);
              let idx = 0;
              let minDiff = Infinity;
              for (let i = 0; i < timelineTicks.length; i++) {
                const tickMinutes = parseTimeString(timelineTicks[i]);
                const diff = Math.abs(eventMinutes - tickMinutes);
                if (diff < minDiff) {
                  minDiff = diff;
                  idx = i;
                }
              }
              let top = idx * gap - 24;
              if (top < 0) top = 0;
              if (top > timelineHeight - 48) top = timelineHeight - 48;
              return (
                <div
                  key={event.id}
                  className="absolute bg-white border border-[#347468] rounded-xl w-[206px] h-[48px] p-3 left-[140px] flex flex-col justify-center"
                  style={{ top: `${top}px` }}
                >
                  <div className="flex flex-col items-start gap-0.5 w-full">
                    <div className="flex flex-row items-center gap-1 w-full">
                      {getEventTypeIcon(event.type) && (
                        <span className="text-[#717171]">
                          {getEventTypeIcon(event.type)}
                        </span>
                      )}
                      <span className="text-[#717171] font-inter font-normal text-[11px] leading-4">
                        {event.type}
                      </span>
                      <span className="text-[#2D2D2D] font-['DM_Sans'] font-medium text-[11px] leading-4">
                        {event.time}
                      </span>
                    </div>
                    <span className="text-[#347468] font-inter font-medium text-sm leading-5">
                      {event.title}
                    </span>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default CalendarView;
