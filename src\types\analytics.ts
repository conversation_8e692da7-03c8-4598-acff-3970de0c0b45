export interface LevelCount {
  averagePassCount: number;
  averageTotalCount: number;
  averageTotalTimeTaken: number;
}

export interface ChapterAnalytics {
  classroomId: string;
  courseId: string;
  chapterId: string;
  topicId: string;
  averageScore: number;
  averagePercentage: number;
  averageTimeTaken: number;
  averageSuccessfulSubmissions: number;
  averageRepitionCount: number;
  averageLvl1Count: LevelCount;
  averageLvl2Count: LevelCount;
  averageLvl3Count: LevelCount;
  averageLvl4Count: LevelCount;
  averageLvl5Count: LevelCount;
  averageMultipleChoiceCount: LevelCount;
  averageSingleChoiceCount: LevelCount;
  averageTextInputCount: LevelCount;
  averageMatchCount: LevelCount;
  averageBinaryCount: LevelCount;
  averageSoundbasedCount: LevelCount;
  averagePictureMatchCount: LevelCount;
}

export interface ClassroomAnalyticsResponseDto {
  resultObject: ClassroomAnalytics[];
  isError: boolean;
  errors: any[];
  recoverableErrors: any[];
}

export interface ClassroomAnalytics {
  classroomId: string;
  courseId: string;
  chapterId: string;
  topicId: string;
  uniqueStudentCount: number;
  averageScore: number;
  averagePercentage: number;
  averageTimeTaken: number;
  averageSuccessfulSubmissions: number;
  averageRepitionCount: number;
  averageLvl1Count: LevelCount;
  averageLvl2Count: LevelCount;
  averageLvl3Count: LevelCount;
  averageLvl4Count: LevelCount;
  averageLvl5Count: LevelCount;
  averageMultipleChoiceCount: LevelCount;
  averageSingleChoiceCount: LevelCount;
  averageTextInputCount: LevelCount;
  averageMatchCount: LevelCount;
  averageBinaryCount: LevelCount;
  averageSoundbasedCount: LevelCount;
  averagePictureMatchCount: LevelCount;
  id: Id;
  createdOn: string;
  updatedon: string;
  chapterName?: string;
  topicName?: string;
}

export interface Id {
  timestamp: number;
  machine: number;
  pid: number;
  increment: number;
  creationTime: string;
}

export interface StudentAnalyticsResponseDto {
  resultObject: StudentAnalytics[];
  isError: boolean;
  errors: any[];
  recoverableErrors: any[];
}

export interface StudentLevelCount {
  passCount: number;
  totalCount: number;
  totalTimeTaken: number;
}

export interface StudentAnalytics {
  studentId: string;
  classroomId: string;
  courseId: string;
  chapterId: string;
  topicId: string;
  succcefullSubmissionDateTime: string;
  timeTakenForQuiz: number;
  score: number;
  percentage: number;
  quizRepetitionCount: number;
  level1Count: StudentLevelCount;
  level2Count: StudentLevelCount;
  level3Count: StudentLevelCount;
  level4Count: StudentLevelCount;
  level5Count: StudentLevelCount;
  multipleChoiceCount: StudentLevelCount;
  singleChoiceCount: StudentLevelCount;
  textInputCount: StudentLevelCount;
  matchCount: StudentLevelCount;
  binaryCount: StudentLevelCount;
  soundbasedCount: StudentLevelCount;
  pictureMatchCount: StudentLevelCount;
  id: Id;
  createdOn: string;
  updatedon: string;
}
