import React from 'react';
import { Button } from '../ui/button';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { motion } from 'framer-motion';

interface CustomPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  maxVisiblePages?: number;
}

export const CustomPagination: React.FC<CustomPaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  maxVisiblePages = 5
}) => {
  // Handle edge cases
  if (totalPages <= 1) return null;
  
  // Ensure current page is within valid range
  const safePage = Math.max(1, Math.min(currentPage, totalPages));
  
  // Calculate visible page range
  const getVisiblePageNumbers = () => {
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    let startPage = Math.max(1, safePage - halfVisible);
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    // Adjust if we're near the end
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);
  };
  
  const visiblePageNumbers = getVisiblePageNumbers();
  const showStartEllipsis = visiblePageNumbers[0] > 1;
  const showEndEllipsis = visiblePageNumbers[visiblePageNumbers.length - 1] < totalPages;
  
  return (
    <nav className="flex items-center space-x-1" aria-label="Pagination">
      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(safePage - 1)}
        disabled={safePage === 1}
        className="hidden sm:flex"
      >
        <ChevronLeft className="h-4 w-4" />
        <span className="sr-only">Previous</span>
      </Button>
      
      {/* First page */}
      {showStartEllipsis && (
        <>
          <Button
            variant={safePage === 1 ? "default" : "outline"}
            size="sm"
            onClick={() => onPageChange(1)}
          >
            1
          </Button>
          <span className="text-gray-400">
            <MoreHorizontal className="h-4 w-4" />
          </span>
        </>
      )}
      
      {/* Visible pages */}
      {visiblePageNumbers.map((pageNumber) => (
        <motion.div
          key={pageNumber}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          <Button
            variant={pageNumber === safePage ? "default" : "outline"}
            size="sm"
            onClick={() => onPageChange(pageNumber)}
            className={pageNumber === safePage ? "pointer-events-none" : ""}
          >
            {pageNumber}
          </Button>
        </motion.div>
      ))}
      
      {/* Last page */}
      {showEndEllipsis && (
        <>
          <span className="text-gray-400">
            <MoreHorizontal className="h-4 w-4" />
          </span>
          <Button
            variant={safePage === totalPages ? "default" : "outline"}
            size="sm"
            onClick={() => onPageChange(totalPages)}
          >
            {totalPages}
          </Button>
        </>
      )}
      
      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(safePage + 1)}
        disabled={safePage === totalPages}
        className="hidden sm:flex"
      >
        <ChevronRight className="h-4 w-4" />
        <span className="sr-only">Next</span>
      </Button>
    </nav>
  );
};