import React from 'react';
import { ChevronLeft, GraduationCap, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ResultObject } from '../../types';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface SidebarHeaderProps {
    selectedCourse: ResultObject | null;
    onBackClick: () => void;
    userName: string;
}

const SidebarHeader = ({ selectedCourse, onBackClick, userName }: SidebarHeaderProps) => {
    const initials = userName
        .split(' ')
        .map(n => n[0])
        .join('')
        .toUpperCase();

    return (
        <div className="sticky top-0 z-50 bg-card border-b">
            <div className="p-4 flex items-center gap-3 bg-gradient-to-r from-primary/5 via-primary/10 to-transparent">
                <Avatar className="h-10 w-10 border-2 border-primary/20">
                    <AvatarFallback className="bg-primary/10 text-primary">
                        {initials}
                    </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                    <h2 className="text-sm font-medium leading-none">
                        Welcome back,
                    </h2>
                    <p className="text-base font-semibold mt-1 text-ellipsis">
                        {userName.includes("@") ? userName.split("@")[0] + "..." : userName}
                    </p>
                </div>
            </div>
            {selectedCourse ? (
                <div className="p-4">
                    <Button
                        variant="ghost"
                        onClick={onBackClick}
                        className="flex items-center text-muted-foreground hover:text-primary w-full justify-start"
                    >
                        <ChevronLeft className="w-4 h-4 mr-2" />
                        <GraduationCap className="w-4 h-4 mr-2" />
                        All Courses
                    </Button>
                </div>
            ) : (
                <div className="px-4 py-3 border-b border-border/50">
                    <h3 className="text-sm font-medium flex items-center gap-2 text-muted-foreground">
                        <GraduationCap className="w-4 h-4" />
                        Available Courses
                    </h3>
                </div>
            )}
        </div>
    );
};

export default SidebarHeader;
