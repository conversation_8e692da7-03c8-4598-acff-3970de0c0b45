import { Star, Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import { useState } from "react"

interface BookmarkDialogProps {
    isOpen: boolean; // Add isOpen prop
    setIsOpen: (isOpen: boolean) => void; // Add setIsOpen prop
    isBookmarked: boolean
    collections: Array<{
        id: string
        bookmarkCollectionName: string
    }>
    onCreateCollection: (name: string) => Promise<void>
    onBookmark: (collectionId: string) => Promise<void>
    onUnbookmark: () => Promise<void>
}

export function BookmarkDialog({
    isOpen, // Use prop
    setIsOpen, // Use prop
    isBookmarked,
    collections,
    onCreateCollection,
    onBookmark,
    onUnbookmark,
}: BookmarkDialogProps) {
    const [selectedCollection, setSelectedCollection] = useState("")
    const [isCreateCollectionOpen, setIsCreateCollectionOpen] = useState(false)
    const [newCollectionName, setNewCollectionName] = useState("")

    const handleBookmarkClick = () => {
        if (isBookmarked) {
            // onUnbookmark()
        } else {
            setIsOpen(true)
        }
    }

    const handleCreateCollection = async () => {
        if (newCollectionName.trim()) {
            await onCreateCollection(newCollectionName.trim())
            setNewCollectionName("")
            setIsCreateCollectionOpen(false)
            // Reset the selected collection to show the newly created one
            setSelectedCollection("")
        }
    }

    const handleBookmark = async () => {
        if (selectedCollection) {
            try {
                // Check if topic is already bookmarked in this collection
                const isAlreadyBookmarked = collections.some(collection =>
                    collection.id === selectedCollection
                );

                if (isAlreadyBookmarked) {
                    alert("This topic is already bookmarked in this collection");
                    return;
                }

                await onBookmark(selectedCollection);
                setIsOpen(false); // Use prop setter
                setSelectedCollection("");
            } catch (error) {
                console.error('Error adding bookmark:', error);
                alert("Failed to add bookmark. Please try again.");
            }
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}> {/* Use props for open state */}
            {/* DialogTrigger might need to be handled by the parent component now */}
            {/* Or keep it if the dialog should also be triggerable by its own button */}
            {/* <DialogTrigger asChild> ... </DialogTrigger> */}
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>
                        {isBookmarked ? "Remove Bookmark" : "Add to Collection"}
                    </DialogTitle>
                </DialogHeader>
                <div className="flex flex-col space-y-4 mt-4">
                    {isBookmarked ? (
                        <div className="flex flex-col space-y-4">
                            <p className="text-sm text-muted-foreground">
                                Are you sure you want to remove this bookmark?
                            </p>
                            <Button
                                onClick={() => {
                                    onUnbookmark()
                                    setIsOpen(false) // Use prop setter
                                }}
                                className="bg-destructive hover:bg-destructive/90 text-white transition-colors"
                            >
                                Remove Bookmark
                            </Button>
                        </div>
                    ) : (
                        <>
                            <div className="flex space-x-2">
                                <Select
                                    value={selectedCollection}
                                    onValueChange={setSelectedCollection}
                                >
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Select Collection" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {collections.map((collection) => (
                                            <SelectItem
                                                key={collection.id}
                                                value={collection.id}
                                            >
                                                {collection.bookmarkCollectionName}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>

                                <Dialog
                                    open={isCreateCollectionOpen}
                                    onOpenChange={setIsCreateCollectionOpen}
                                >
                                    <DialogTrigger asChild>
                                        <Button variant="outline" size="icon">
                                            <Plus className="h-4 w-4" />
                                        </Button>
                                    </DialogTrigger>
                                    <DialogContent>
                                        <DialogHeader>
                                            <DialogTitle>Create New Collection</DialogTitle>
                                        </DialogHeader>
                                        <div className="flex flex-col space-y-4">
                                            <Input
                                                placeholder="Collection name"
                                                value={newCollectionName}
                                                onChange={(e) => setNewCollectionName(e.target.value)}
                                            />
                                            <Button onClick={handleCreateCollection}>
                                                Create Collection
                                            </Button>
                                        </div>
                                    </DialogContent>
                                </Dialog>
                            </div>

                            <Button
                                onClick={handleBookmark}
                                disabled={!selectedCollection}
                                className="bg-primary hover:bg-primary/90 text-white transition-colors"
                            >
                                Add Bookmark
                            </Button>
                        </>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    )
}
