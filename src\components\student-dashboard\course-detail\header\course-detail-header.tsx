import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Play } from 'lucide-react'; 
import { Progress } from '@/components/ui/progress';

interface CourseDetailHeaderProps {
    courseTitle: string;
    instructorName: string;
    progressPercentage: number;
    lessonsCompleted: number;
    totalLessons: number;
    timeLeft: string;
    onBackClick: () => void;
    onResumeClick: () => void;
}

const CourseDetailHeader: React.FC<CourseDetailHeaderProps> = ({
    courseTitle,
    instructorName,
    progressPercentage,
    lessonsCompleted,
    totalLessons,
    timeLeft,
    onBackClick,
    onResumeClick,
}) => {
    return (
        <div className="bg-white rounded-lg shadow-sm p-6 w-full">
            <div className="flex flex-col space-y-4">
                {/* Back button and title row */}
                <div className="flex items-center gap-4">
                    <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={onBackClick}
                        className="h-8 w-8 rounded-full hover:bg-slate-100"
                    >
                        <ArrowLeft className="h-4 w-4" />
                    </Button>
                    <div>
                        <h1 className="text-xl md:text-2xl font-semibold text-slate-800">{courseTitle}</h1>
                        <p className="text-slate-500 text-sm">Instructor: {instructorName}</p>
                    </div>
                </div>

                {/* Progress section */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div className="w-full sm:w-3/4">
                        <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-medium text-slate-700">
                                {lessonsCompleted} of {totalLessons} lessons completed ({Math.round(progressPercentage)}%)
                            </span>
                            <span className="text-sm font-medium text-slate-500">{timeLeft}</span>
                        </div>
                        <Progress value={progressPercentage} className="h-2.5 bg-slate-100" />
                    </div>
                    <Button 
                        onClick={onResumeClick} 
                        className="bg-emerald-600 hover:bg-emerald-700 text-white w-full sm:w-auto"
                    >
                        <Play className="h-4 w-4 mr-2" />
                        Resume Course
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default CourseDetailHeader;
