import { API_BASE_URL } from '../constants/apiConstants';
import { makeAuthenticatedRequest } from '../common/basicAPI';

export const NotesService = {
  // Get all notes for the current user
  getUserNotes: async () => {
    return await makeAuthenticatedRequest(`${API_BASE_URL}/notes/user`, 'GET');
  },

  // Get notes for a specific topic
  getTopicNotes: async (topicId) => {
    return await makeAuthenticatedRequest(`${API_BASE_URL}/notes/topic/${topicId}`, 'GET');
  },

  // Create a new note
  createNote: async (noteData) => {
    return await makeAuthenticatedRequest(`${API_BASE_URL}/notes`, 'POST', noteData);
  },

  // Update an existing note
  updateNote: async (noteId, noteData) => {
    return await makeAuthenticatedRequest(`${API_BASE_URL}/notes/${noteId}`, 'PUT', noteData);
  },

  // Delete a note
  deleteNote: async (noteId) => {
    return await makeAuthenticatedRequest(`${API_BASE_URL}/notes/${noteId}`, 'DELETE');
  },

  // Create a new folder
  createFolder: async (folderData) => {
    return await makeAuthenticatedRequest(`${API_BASE_URL}/notes/folders`, 'POST', folderData);
  },

  // Get all folders for the user
  getUserFolders: async () => {
    return await makeAuthenticatedRequest(`${API_BASE_URL}/notes/folders`, 'GET');
  },

  // Update a folder
  updateFolder: async (folderId, folderData) => {
    return await makeAuthenticatedRequest(`${API_BASE_URL}/notes/folders/${folderId}`, 'PUT', folderData);
  },

  // Delete a folder
  deleteFolder: async (folderId) => {
    return await makeAuthenticatedRequest(`${API_BASE_URL}/notes/folders/${folderId}`, 'DELETE');
  }
};

export default NotesService;