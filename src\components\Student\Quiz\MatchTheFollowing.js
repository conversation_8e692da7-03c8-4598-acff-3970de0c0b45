import React from "react";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";

const ItemType = "ANSWER";

const DraggableAnswer = ({ answer, index }) => {
  const [{ isDragging }, drag] = useDrag({
    type: ItemType,
    item: { index },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  });

  return (
    <div
      ref={drag}
      className="p-3 m-1 bg-white border rounded-lg shadow-sm cursor-move transition-colors hover:bg-accent"
      style={{
        opacity: isDragging ? 0.5 : 1,
      }}
    >
      {answer}
    </div>
  );
};

const AnswerSlot = ({ answer, index, moveAnswer }) => {
  const [{ isOver }, drop] = useDrop({
    accept: ItemType,
    hover: (draggedItem) => {
      if (draggedItem.index !== index) {
        moveAnswer(draggedItem.index, index);
        draggedItem.index = index;
      }
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  });

  return (
    <div
      ref={drop}
      className={`p-1 m-1 border-2 border-dashed rounded-lg min-h-[50px] transition-colors ${
        isOver ? "bg-accent/20" : "bg-muted"
      }`}
    >
      {answer ? (
        <DraggableAnswer answer={answer} index={index} />
      ) : (
        <div className="flex items-center justify-center h-full text-sm text-muted-foreground">
          Drop here
        </div>
      )}
    </div>
  );
};

const MatchTheFollowing = ({
  matchingAnswers,
  matchingQuestions,
  setMatchingAnswersFromChild,
}) => {
  const moveAnswer = (fromIndex, toIndex) => {
    const updatedAnswers = [...matchingAnswers];
    const [movedAnswer] = updatedAnswers.splice(fromIndex, 1);
    updatedAnswers.splice(toIndex, 0, movedAnswer);
    setMatchingAnswersFromChild(updatedAnswers);
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="flex justify-between gap-8">
        <div className="flex-1 space-y-2">
          {matchingQuestions.map((question, index) => (
            <div key={index} className="p-3 bg-muted rounded-lg shadow-sm">
              {question}
            </div>
          ))}
        </div>

        <div className="flex-1 space-y-2">
          {matchingAnswers.map((answer, index) => (
            <AnswerSlot
              key={index}
              answer={answer}
              index={index}
              moveAnswer={moveAnswer}
            />
          ))}
        </div>
      </div>
    </DndProvider>
  );
};

export default MatchTheFollowing;
