import React, { useEffect } from 'react';
import ScormLesson from '@/components/scorm-content';
import { useLazyGetContentRelatedTopicDataQuery } from '@/APIConnect';
import { Loader2Icon, AlertCircle, FileQuestion } from 'lucide-react';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent } from "@/components/ui/card";
import { ContentType } from './types';

interface ContentRendererProps {
    topicId: string;
    previewData?: {
        resultObject: {
            type: ContentType;
            content?: string;
            contentUrl?: string;
        };
    };
}

const ContentRenderer = ({ topicId, previewData }: ContentRendererProps) => {
    const [getContentRelatedTopicData, getContentRelatedTopicDataResult] =
        useLazyGetContentRelatedTopicDataQuery();

    const [content, setContent] = React.useState<{
        contentType: string;
        data: any;
    } | null>(null);

    const [error, setError] = React.useState<string | null>(null);

    React.useEffect(() => {
        if (!previewData) {
            getContentRelatedTopicData(topicId);
        }
    }, [getContentRelatedTopicData, topicId, previewData]);

    const contentTypeMap = [
        'SCORM',
        'HTML',
        'AUDIO_URL',
        'VIDEO_URL',
        'SCORM',
    ];

    const getContentDataForType = (type: number, data: any) => {
        try {
            const contentType = contentTypeMap[type];
            console.log(contentType, data);

            if (!data) {
                throw new Error('No content data available');
            }

            switch (contentType) {
                case 'SCORM':
                    if (!data.contentUrl) throw new Error('No SCORM content URL available');
                    return <ScormLesson url={data.contentUrl} />;
                case 'HTML':
                    if (!data.content) throw new Error('No HTML content available');
                    return <div dangerouslySetInnerHTML={{ __html: data.content }} />;
                case 'IMAGE_URL':
                    if (!data) throw new Error('No image URL available');
                    return <img src={data} alt="Topic illustration" className="max-w-full h-auto rounded-lg shadow-md" />;
                case 'VIDEO_URL':
                    if (!data.contentUrl) throw new Error('No video URL available');
                    if (data.contentUrl.includes('youtube')) {
                        const videoId = data.contentUrl.split('v=')[1];
                        if (!videoId) throw new Error('Invalid YouTube URL');
                        return (
                            <iframe
                                src={`https://www.youtube.com/embed/${videoId}`}
                                className="w-full h-[600px] rounded-lg shadow-md"
                                title="learning content"
                                allowFullScreen
                            />
                        );
                    }
                    return (
                        <video
                            src={data.contentUrl}
                            controls
                            className="w-full h-[600px] rounded-lg shadow-md"
                            title="learning content"
                        />
                    );
                case 'AUDIO_URL':
                    if (!data.contentUrl) throw new Error('No audio URL available');
                    return (
                        <audio
                            src={data.contentUrl}
                            controls
                            className="w-full"
                        />
                    );
                default:
                    throw new Error('Unsupported content type');
            }
        } catch (error) {
            console.error('Error rendering content:', error);
            throw error;
        }
    };

    useEffect(() => {
        try {
            // If preview data is provided, use that instead of API data
            const resultObject = previewData?.resultObject || getContentRelatedTopicDataResult.data?.resultObject;

            if (!resultObject) {
                setError('No content available for this topic');
                return;
            }

            setContent({
                contentType: resultObject.type,
                data: getContentDataForType(resultObject.type, resultObject)
            });
            setError(null);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to load content');
            setContent(null);
        }
    }, [getContentRelatedTopicDataResult, previewData]);

    if (!previewData && getContentRelatedTopicDataResult.isLoading) {
        return (
            <div className='w-full h-[600px] flex items-center justify-center'>
                <Loader2Icon className='w-10 h-10 animate-spin text-primary' />
            </div>
        );
    }

    if (error) {
        return (
            <Card className="w-full">
                <CardContent className="p-6">
                    <div className="flex flex-col items-center justify-center space-y-4 text-center">
                        <FileQuestion className="w-12 h-12 text-muted-foreground" />
                        <div className="space-y-2">
                            <h3 className="text-lg font-medium">No Content Available</h3>
                            <p className="text-sm text-muted-foreground">
                                {error}
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (!content?.data) {
        return (
            <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                    Unable to load content. Please try again later.
                </AlertDescription>
            </Alert>
        );
    }

    return (
        <div className='w-full h-full'>
            <ErrorBoundary>
                {content.data}
            </ErrorBoundary>
        </div>
    );
};

class ErrorBoundary extends React.Component<
    { children: React.ReactNode },
    { hasError: boolean }
> {
    constructor(props: { children: React.ReactNode }) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: any) {
        return { hasError: true };
    }

    componentDidCatch(error: any, errorInfo: any) {
        console.error('Content rendering error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                        Something went wrong while displaying the content.
                    </AlertDescription>
                </Alert>
            );
        }

        return this.props.children;
    }
}

export default ContentRenderer;
