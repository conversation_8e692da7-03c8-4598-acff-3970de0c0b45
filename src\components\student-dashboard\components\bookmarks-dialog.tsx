import React, { useState, useMemo, useEffect } from 'react';
import { Star, Trash, Search, ExternalLink, Calendar, Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import SearchInput from './common/search-input';
import { cn } from '@/lib/utils';
import {
    useLazyGetBookmarksQuery,
    useDeleteBookmarkMutation,
    useLazyGetBookmarkCollectionQuery,
    useCreateBookmarkCollectionMutation,
    useDeleteBookmarkCollectionMutation
} from '@/APIConnect';

interface Bookmark {
    id: string;
    studentId: string;
    bookmarkCollectionId: string;
    courseId: string;
    chapterId: string;
    topicId: string;
    courseName: string;
    chapterName: string;
    topicName: string;
    createdOn: string;
    updatedOn?: string;
}

interface BookmarkCollection {
    id: string;
    bookmarkCollectionName: string;
}

const BookmarksDialog = ({ iconOnly = false,
    onCourseSelect, // Add this prop to handle course selection
    onTopicSelect,   // Add this prop to handle topic selection
    triggerText,
    buttonVariant = "ghost",
    className
}: {
    iconOnly?: boolean,
    onCourseSelect?: (courseId: string) => Promise<void>,
    onTopicSelect?: (topicId: string) => void,
    triggerText?: string;
    buttonVariant?: "ghost" | "link" | "default" | "destructive" | "outline" | "secondary" | null | undefined;
    className?: string;
}) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCollection, setSelectedCollection] = useState<string>('ALL');
    const [newCollectionName, setNewCollectionName] = useState('');
    const [isCreateCollectionOpen, setIsCreateCollectionOpen] = useState(false);
    const [open, setOpen] = useState(false);

    const isMobile = window.innerWidth < 768;
    const history = useNavigate();

    // API Hooks
    const [getBookmarks, { data: bookmarksData }] = useLazyGetBookmarksQuery();
    const [getCollections, { data: collectionsData }] = useLazyGetBookmarkCollectionQuery();
    const [deleteBookmark] = useDeleteBookmarkMutation();
    const [createCollection] = useCreateBookmarkCollectionMutation();
    const [deleteCollection] = useDeleteBookmarkCollectionMutation();

    useEffect(() => {
        getBookmarks({});
        getCollections({});

        // Set up interval for refetching
        const interval = setInterval(() => {
            getBookmarks({});
            getCollections({});
        }, 10000); // 10 seconds

        // Cleanup interval on component unmount
        return () => clearInterval(interval);
    }, [getBookmarks, getCollections]);

    // Get bookmarks and collections from API response
    const bookmarks: Bookmark[] = bookmarksData?.resultObject || [];
    const collections: BookmarkCollection[] = collectionsData?.resultObject || [];

    // Debug logging
    useEffect(() => {
        if (bookmarks.length > 0) {
            console.log('All bookmarks:', bookmarks);
            const individualBookmarks = bookmarks.filter(b => !b.bookmarkCollectionId || b.bookmarkCollectionId === '');
            console.log('Individual bookmarks (no collection):', individualBookmarks);
            const collectionBookmarks = bookmarks.filter(b => b.bookmarkCollectionId && b.bookmarkCollectionId !== '');
            console.log('Collection bookmarks:', collectionBookmarks);
        }
    }, [bookmarks]);

    const handleDeleteBookmark = async (id: string) => {
        try {
            await deleteBookmark({ id });
            getBookmarks({}); // Refresh bookmarks after deletion
        } catch (error) {
            console.error('Error deleting bookmark:', error);
        }
    };

    const handleCreateCollection = async () => {
        if (newCollectionName.trim()) {
            try {
                await createCollection({ name: newCollectionName.trim() });
                setNewCollectionName('');
                setIsCreateCollectionOpen(false);
                getCollections({});
            } catch (error) {
                console.error('Error creating collection:', error);
            }
        }
    };

    const navigateToBookmark = async (bookmark: Bookmark) => {
        console.log('Navigating to bookmark:', bookmark);

        // Close the dialog first
        setOpen(false);

        // Navigate directly to the course detail page with proper URL parameters
        const searchParams = new URLSearchParams();
        searchParams.set('courseId', bookmark.courseId);

        // Try to get classroomId from localStorage or use default
        // This is a temporary solution - ideally classroomId should be stored with the bookmark
        const storedClassroomId = localStorage.getItem(`classroomId_${bookmark.courseId}`);
        if (storedClassroomId) {
            searchParams.set('classroomId', storedClassroomId);
        } else {
            // Use default classroomId as fallback
            searchParams.set('classroomId', 'default-classroom');
        }

        // Add topicId to automatically select the topic
        if (bookmark.topicId) {
            searchParams.set('topicId', bookmark.topicId);
        }

        // Add chapterId if available
        if (bookmark.chapterId) {
            searchParams.set('chapterId', bookmark.chapterId);
        }

        // Navigate to the course detail page
        const url = `/courses?${searchParams.toString()}`;
        console.log('Navigating to URL:', url);
        console.log('Bookmark data used:', {
            courseId: bookmark.courseId,
            topicId: bookmark.topicId,
            chapterId: bookmark.chapterId,
            courseName: bookmark.courseName,
            topicName: bookmark.topicName
        });

        // Use replace: false to ensure the navigation triggers properly
        history(url, { replace: false });
    };

    const filteredBookmarks = useMemo(() => {
        let filtered = bookmarks;
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            filtered = filtered.filter(
                bookmark => bookmark?.topicName?.toLowerCase().includes(query)
                    || bookmark?.chapterName?.toLowerCase().includes(query)
                    || bookmark?.courseName?.toLowerCase().includes(query)
            );
        }
        if (selectedCollection !== 'ALL' && selectedCollection !== '') {
            if (selectedCollection === 'INDIVIDUAL') {
                // Show only individual bookmarks (no collection)
                filtered = filtered.filter(
                    bookmark => !bookmark.bookmarkCollectionId || bookmark.bookmarkCollectionId === ''
                );
            } else {
                // Show bookmarks from specific collection
                filtered = filtered.filter(
                    bookmark => bookmark.bookmarkCollectionId === selectedCollection
                );
            }
        }
        return filtered;
    }, [bookmarks, searchQuery, selectedCollection]);

    return (
        <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
                {triggerText ? (
                    <Button variant={buttonVariant} className={className}>
                        {triggerText}
                    </Button>
                ) : (
                    <Button variant={buttonVariant} className={cn("w-full justify-start", className)}>
                        ⭐ {iconOnly ? "" : "My Bookmarks"}
                    </Button>
                )}
            </SheetTrigger>
            <SheetContent
                side="right"
                hideCloseButton={true}
                className={cn(
                    "p-0",
                    isMobile ? "w-full h-[85vh]" : "w-[400px] sm:w-[540px]"
                )}
            >
                <SheetHeader className="flex flex-row items-center justify-between p-4 border-b  border-b-2 border-[#347468] bg-[#ebf1f0]">
                    <SheetTitle className="flex items-center text-xl font-semibold text-black">
                        ⭐ My Bookmarks
                    </SheetTitle>
                    <Button
                        variant="ghost"
                        size="icon"
                        className="rounded-full"
                        onClick={() => setOpen(false)}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="lucide">
                            <circle cx="12" cy="12" r="10" />
                            <path d="m15 9-6 6" />
                            <path d="m9 9 6 6" />
                        </svg>
                    </Button>
                </SheetHeader>
                <div className="p-6 space-y-6">
                    <div className="flex flex-col space-y-4">
                        <div className="flex space-x-2 ">
                            <Select
                                value={selectedCollection}
                                onValueChange={setSelectedCollection}
                                
                            >
                                <SelectTrigger className="w-full border-2 border-[#8cb04f] rounded-[8px]">
                                    <SelectValue placeholder="All Collections" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="ALL">All Bookmarks</SelectItem>
                                    <SelectItem value="INDIVIDUAL">Individual Bookmarks</SelectItem>
                                    {collections?.map((collection: BookmarkCollection) => (
                                        <SelectItem key={collection.id} value={collection.id}>
                                            {collection.bookmarkCollectionName}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Dialog open={isCreateCollectionOpen} onOpenChange={setIsCreateCollectionOpen}>
                                <DialogTrigger asChild>
                                    <Button variant="outline" size="icon" className="p-0 border-2 border-[#8cb04f] !rounded-[8px] w-[50px]">
                                        <Plus className="h-4 w-4 text-[#8cb04f]" />
                                    </Button>
                                </DialogTrigger>
                                <DialogContent>
                                    <DialogHeader>
                                        <DialogTitle>Create New Collection</DialogTitle>
                                    </DialogHeader>
                                    <div className="flex flex-col space-y-4">
                                        <Input
                                            placeholder="Collection name"
                                            value={newCollectionName}
                                            onChange={(e) => setNewCollectionName(e.target.value)}
                                        />
                                        <Button onClick={handleCreateCollection}>
                                            Create Collection
                                        </Button>
                                    </div>
                                </DialogContent>
                            </Dialog>
                        </div>

                        <SearchInput
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e)}
                            placeholder="Search bookmarks..."
                        />
                    </div>

                    <ScrollArea className={cn("w-full", isMobile ? "w-full h-[70vh]" : "w-full h-[600px]")}>
                        {filteredBookmarks.length === 0 ? (
                            <div className="flex flex-col items-center justify-center h-[200px] text-muted-foreground">
                                <Search className="w-8 h-8 mb-2" />
                                <p>No bookmarks found</p>
                            </div>
                        ) : (
                            filteredBookmarks?.map((bookmark: Bookmark) => (
                                <Card key={bookmark.id} className="mb-4 rounded-[8px] border-2 border-[#8cb04f] bg-white hover:bg-[#f9fbf7] transition-colors w-full">
                                    <div className="p-3 ">
                                        <div className="flex flex-row items-center justify-between">
                                            <div>
                                                <h3 className="text-[#347468] text-lg font-medium">
                                                    {bookmark.chapterName || 'Chapter 1'}
                                                </h3>
                                                <p className="text-xs text-gray-500 mt-0.5">
                                                    {bookmark.courseName} - {bookmark.topicName || 'Introduction To Pronouns'}
                                                </p>
                                            </div>
                                            <div className="flex gap-1">
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => navigateToBookmark(bookmark)}
                                                    className="h-8 w-8 p-0 hover:bg-blue-100 hover:text-blue-600 rounded-md"
                                                >
                                                    <ExternalLink className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleDeleteBookmark(bookmark.id)}
                                                    className="h-8 w-8 p-0 hover:bg-red-100 text-red-600 rounded-md"
                                                >
                                                    <Trash className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="mt-4">
                                            <span className="text-xs text-gray-400 flex items-center">
                                                <Calendar className="w-3 h-3 mr-1" />
                                                Last Edited On {new Date(bookmark?.createdOn ?? 0).toLocaleDateString('en-GB', {day: '2-digit', month: 'short'})}
                                            </span>
                                        </div>
                                    </div>
                                </Card>
                            ))
                        )}
                    </ScrollArea>
                </div>
            </SheetContent>
        </Sheet>
    );
};

export default BookmarksDialog;
