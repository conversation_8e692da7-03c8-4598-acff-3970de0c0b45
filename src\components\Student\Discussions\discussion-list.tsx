import { motion, AnimatePresence } from 'framer-motion'
import { MessageCircle, ThumbsUp, Clock, ArrowUpCircle, AlertCircle } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { Card, CardContent } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { cn } from '@/lib/utils'
import { Discussion } from './types'

interface DiscussionListProps {
  discussions: Discussion[]
  onDiscussionClick: (discussion: Discussion) => void
  isLoading?: boolean
  error?: any
  onRetry?: () => void
}

export const DiscussionList = ({ 
  discussions, 
  onDiscussionClick,
  isLoading,
  error,
  onRetry
}: DiscussionListProps) => {
  if (error) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load discussions. {error.message}
          {onRetry && (
            <Button
              variant="link"
              className="pl-2 h-auto p-0 text-destructive-foreground underline"
              onClick={onRetry}
            >
              Try again
            </Button>
          )}
        </AlertDescription>
      </Alert>
    )
  }

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        <p className="mt-4 text-muted-foreground">Loading discussions...</p>
      </div>
    )
  }

  if (!discussions?.length) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <MessageCircle className="h-12 w-12 text-muted-foreground/30" />
        <h3 className="mt-4 text-lg font-medium">No discussions yet</h3>
        <p className="text-muted-foreground mt-2">
          Be the first to start a discussion
        </p>
      </div>
    )
  }

  return (
    <AnimatePresence mode="sync">
      <div className="space-y-4">
        {discussions.map((discussion, index) => (
          <motion.div
            key={discussion.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ delay: index * 0.05 }}
          >
            <Card
              className="p-6 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => onDiscussionClick(discussion)}
            >
              <div className="flex gap-4">
                <div className="flex flex-col items-center gap-1">
                  <ArrowUpCircle className="w-5 h-5 text-muted-foreground" />
                  <span className="text-sm font-medium">{discussion.voteCount}</span>
                </div>
                <div className="flex-1 space-y-2">
                  <div className="flex items-start justify-between">
                    <h3 className="text-lg font-semibold leading-tight">
                      {discussion.title}
                    </h3>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <MessageCircle className="w-4 h-4" />
                      <span>{discussion.commentCount}</span>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {discussion.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src="/placeholder-avatar.jpg" />
                        <AvatarFallback>
                          {discussion.createdBy.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm text-muted-foreground">
                        {new Date(discussion.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      {discussion.courseName && (
                        <Badge variant="outline">{discussion.courseName}</Badge>
                      )}
                      {discussion.topicName && (
                        <Badge variant="outline">{discussion.topicName}</Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>
    </AnimatePresence>
  )
}