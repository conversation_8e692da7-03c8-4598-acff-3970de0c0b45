import React from 'react';
import { motion } from 'framer-motion';

// Animation variants
const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.5 } },
};

// Mock data for radar chart
const skills = [
  { name: 'Critical Thinking & Problem Solving', value: 80 },
  { name: 'Presentation & Communication', value: 70 },
  { name: 'Collaboration & Team work', value: 90 },
  { name: 'Subject Knowledge', value: 85 },
  { name: 'Strategic Thinking', value: 75 },
  { name: 'Innovation', value: 60 },
  { name: 'Ethical Decision making', value: 65 },
  { name: 'Sustainability Quotient', value: 55 },
  { name: 'Experiential, Social & Cultural Awareness', value: 50 }
];

interface SkillsMasteryMapProps {
  className?: string;
}

const SkillsMasteryMap: React.FC<SkillsMasteryMapProps> = ({ className = '' }) => {
  const centerX = 200;
  const centerY = 200;
  const radius = 140;
  const levels = 5; // Number of concentric circles
  
  // Calculate positions for each skill point on the radar
  const getCoordinatesForSkill = (index: number, value: number) => {
    const angle = (Math.PI * 2 * index) / skills.length - Math.PI / 2;
    const adjustedValue = value / 100;
    const x = centerX + radius * adjustedValue * Math.cos(angle);
    const y = centerY + radius * adjustedValue * Math.sin(angle);
    return { x, y };
  };
  
  // Create the polygon points string for the radar chart
  const polygonPoints = skills.map((skill, index) => {
    const { x, y } = getCoordinatesForSkill(index, skill.value);
    return `${x},${y}`;
  }).join(' ');
  
  // Create level circles
  const levelCircles = Array.from({ length: levels }).map((_, index) => {
    const levelRadius = (radius / levels) * (index + 1);
    return (
      <circle 
        key={index}
        cx={centerX}
        cy={centerY}
        r={levelRadius}
        fill="none"
        stroke="#e5e7eb"
        strokeWidth="1"
        strokeDasharray="4"
      />
    );
  });
  
  // Create skill axis lines
  const skillAxes = skills.map((_, index) => {
    const angle = (Math.PI * 2 * index) / skills.length - Math.PI / 2;
    const x2 = centerX + radius * Math.cos(angle);
    const y2 = centerY + radius * Math.sin(angle);
    return (
      <line 
        key={index}
        x1={centerX}
        y1={centerY}
        x2={x2}
        y2={y2}
        stroke="#e5e7eb"
        strokeWidth="1"
      />
    );
  });
  
  // Create skill labels
  const skillLabels = skills.map((skill, index) => {
    const angle = (Math.PI * 2 * index) / skills.length - Math.PI / 2;
    // Position labels slightly outside the radar
    const labelRadius = radius * 1.1;
    const x = centerX + labelRadius * Math.cos(angle);
    const y = centerY + labelRadius * Math.sin(angle);
    
    // Adjust text alignment based on position
    let textAnchor = "middle";
    if (angle > -Math.PI * 0.75 && angle < Math.PI * 0.25) {
      textAnchor = "start";
    } else if (angle > Math.PI * 0.25 && angle < Math.PI * 0.75) {
      textAnchor = "middle";
    } else {
      textAnchor = "end";
    }
    
    return (
      <text 
        key={index}
        x={x}
        y={y}
        textAnchor={textAnchor}
        dominantBaseline="middle"
        fontSize="10"
        fill="#347468"
        className="text-xs font-medium"
      >
        {skill.name}
      </text>
    );
  });
  
  // Create data points
  const dataPoints = skills.map((skill, index) => {
    const { x, y } = getCoordinatesForSkill(index, skill.value);
    return (
      <circle 
        key={index}
        cx={x}
        cy={y}
        r="4"
        fill="#347468"
      />
    );
  });
  
  return (
    <motion.div 
      className={`bg-white relative flex flex-col rounded-[10px] border border-[#97C48A] shadow-sm p-5 ${className}`}
      variants={fadeIn}
      initial="initial"
      animate="animate"
    >
      <h3 className="text-lg font-semibold text-[#2D2D2D] mb-6">Skills Mastery Map</h3>
      
      <div className="flex items-center justify-center overflow-hidden w-full h-full sm:h-[300px] mx-auto">
        <svg width="100%" height="100%" viewBox="0 0 450 450" preserveAspectRatio="xMidYMid meet" style={{ maxHeight: '100%', maxWidth: '100%' }}>
          {/* Concentric circles for levels */}
          {levelCircles}
          
          {/* Skill axes */}
          {skillAxes}
          
          {/* Filled polygon representing the data */}
          <polygon 
            points={polygonPoints} 
            fill="rgba(52, 116, 104, 0.2)" 
            stroke="#347468" 
            strokeWidth="2"
          />
          
          {/* Data points */}
          {dataPoints}
          
          {/* Skill labels */}
          {skillLabels}
        </svg>
      </div>
    </motion.div>
  );
};

export default SkillsMasteryMap;
