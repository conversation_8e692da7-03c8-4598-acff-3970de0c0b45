<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <title>Sign In | VyUDI</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
  <!-- Auth0 SDK -->
  <script src="https://cdn.auth0.com/js/auth0/9.28/auth0.min.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter';
      background: #FFFFFF;
      margin: 0;
      padding: 0;
      height: 100vh;
      display: flex;
      overflow: hidden;
    }

    .login-container {
      display: flex;
      width: 100%;
      height: 100vh;
    }

    /* Left Side - Login Form */
    .login-form-section {
      width: 591px;
      flex-shrink: 0; /* Prevent form from shrinking */
      height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: flex-start; /* Align content to the left */
      background: #FFFFFF;
      position: relative;
      padding-top: 40px; /* Adjusted top padding */
      overflow-y: auto; /* Add scroll on small heights */
    }

    .logo-container {
      width: auto; /* Fit to content */
      margin-bottom: 40px; /* Reduced space below logo */
      display: flex;
      flex-direction: row; /* Align items horizontally */
      align-items: center; /* Vertically align items */
      gap: 5px; /* Reduced gap */
      position: relative;
      left: 30px; /* Moved further right */
      top: -10px; /* Move up */
    }

    .logo-img {
      width: 185px; /* Set to new width */
      height: 47px;  /* Set to new height */
      object-fit: contain;
    }

    .signin-container, .signup-container {
      width: 527px;
      padding: 0 64px 0 84px; /* Increased left padding */
      display: flex;
      flex-direction: column;
      gap: 32px;
      opacity: 1;
    }

    .signup-container {
      display: none; /* Hidden by default */
      gap: 10px; /* Further reduced gap */
    }

    .signup-container .signin-title {
      font-size: 32px; /* Reduced title size */
      height: auto;
    }

    .signup-container .signin-subtitle {
      font-size: 16px; /* Reduced subtitle size */
      height: auto;
    }

    .signup-container .form-container {
      gap: 12px; /* Increased gap for signup form */
    }

    .signup-container .signin-button {
      height: 44px; /* Reduced button height */
    }

    .signup-container .form-input {
      height: 42px; /* Increased height slightly */
      padding: 8px 16px;
      border: 1px solid #DCDCDC; /* Added default border */
    }

    .signup-container .form-label {
      top: 11px; /* Adjust label position */
      font-size: 14px;
    }

    .signup-container .form-input:focus + .form-label,
    .signup-container .form-input:not(:placeholder-shown) + .form-label {
      top: -10px;
      font-size: 12px;
    }

    .signin-header {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .signin-title {
      width: 117px;
      height: 44px;
      font-family: 'Inter';
      font-weight: 700;
      font-size: 38px;
      line-height: 110%;
      letter-spacing: -0.04em;
      text-align: left;
      color: #2D2D2D;
    }

    .signin-subtitle {
      width: 399px;
      height: 27px;
      font-family: 'Inter';
      font-weight: 400;
      font-size: 18px;
      line-height: 150%;
      color: #969696;
    }

    .form-container {
      width: 399px;
      height: auto; /* Allow height to adjust */
      display: flex;
      flex-direction: column;
      gap: 15px; /* Reduced gap */
    }

    .form-group {
      position: relative; /* Needed for floating label */
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .form-input {
      width: 399px;
      height: 54px; /* Reduced height */
      border-radius: 10px;
      padding: 14px 16px; /* Adjusted padding */
      font-family: 'Inter';
      font-size: 16px;
      outline: none;
      border: 1px solid #DCDCDC; /* Added default border */
    }

    .email-input {
      border: 1.5px solid #347468;
    }

    .password-input {
      border: 1px solid #D9D9D9;
    }

    .form-input:focus {
      border: 1px solid #347468; /* Thinner border */
    }

    .form-input::placeholder {
      color: transparent; /* Hide original placeholder */
    }

    .form-label {
      position: absolute;
      top: 15px;
      left: 16px;
      font-family: 'Inter';
      font-size: 16px;
      color: #969696;
      pointer-events: none;
      transition: all 0.2s ease-in-out;
    }

    .form-input:focus + .form-label,
    .form-input:not(:placeholder-shown) + .form-label {
      top: -10px;
      left: 12px;
      font-size: 12px;
      color: #347468; /* Green color */
      background-color: #FFFFFF;
      padding: 0 4px;
    }

    .checkbox-container {
      width: 172px;
      height: 24px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .checkbox {
      width: 24px;
      height: 24px;
      cursor: pointer;
    }

    .checkbox-label {
      width: 138px;
      height: 24px;
      font-family: 'Inter';
      font-weight: 500;
      font-size: 14px;
      line-height: 150%;
      color: #2D2D2D;
      cursor: pointer;
      align-items: center;
      justify-content: center;
    }

    .signin-button {
      width: 399px;
      height: 54px;
      border-radius: 10px;
      border: 1px solid #347468;
      background: #347468;
      padding: 16px 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .signin-button:hover {
      background: #2a5d54;
    }

    .signin-button-text {
      font-family: 'Inter';
      font-weight: 700;
      font-size: 18px;
      line-height: 120%;
      letter-spacing: -0.01em;
      color: #FFFFFF;
    }

    .signup-link {
      width: 399px;
      height: 27px;
      font-family: 'Inter';
      font-weight: 400;
      font-size: 18px;
      line-height: 150%;
      text-align: center;
      color: #2D2D2D;
    }

    .signup-link a {
      color: #347468;
      font-weight: 700;
      text-decoration: underline;
    }

    .social-section {
      position: absolute;
      bottom: 32px;
      left: 32px;
      width: auto;
      height: auto;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .social-text {
      font-family: 'Inter';
      font-weight: 400;
      font-size: 14px; /* Reduced font size */
      line-height: 150%;
      text-align: left;
      color: #6C6C6C;
    }

    .social-icons {
      display: flex;
      gap: 16px;
    }

    .social-icon {
      width: 20px; /* Reduced icon size */
      height: 20px; /* Reduced icon size */
    }

    /* Right Side - Image */
    .image-section {
      flex-grow: 1; /* Allow image section to fill remaining space */
      height: 100vh;
      background: #FFFFFF;
      padding: 9px 11px; /* top/bottom 10px, left/right 12px */
      box-sizing: border-box; /* IMPORTANT: Ensures padding is inside the flex item */
      overflow: hidden;
    }

    .side-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 24px;
    }

    /* Error message styling */
    .error-message {
      color: #dc3545;
      font-size: 14px;
      margin-top: 8px;
      font-family: 'Inter';
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
      .login-container {
        flex-direction: column;
      }
      
      .login-form-section {
        width: 100%;
        height: auto;
        min-height: 100vh;
        padding-top: 40px;
        padding-bottom: 40px;
      }
      
      .image-section {
        display: none;
      }
    }

    @media (max-width: 600px) {
      .logo-container {
        width: 100%;
        padding: 0 24px;
      }
      .signin-container {
        width: 100%;
        padding: 0 24px;
      }
      
      .form-container,
      .form-input,
      .signin-button,
      .signup-link {
        width: 100%;
      }
      
      .signin-title {
        width: auto;
        text-align: left;
        font-family: 'Inter';
      }
      
      .signin-subtitle {
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="login-container">
    <!-- Left Side - Login Form -->
    <div class="login-form-section">
      <!-- Logo -->
      <div class="logo-container">
        <img src="VyUDI-logo.png" alt="VyUDI Logo" class="logo-img">
      </div>

      <!-- Sign In Container -->
      <div class="signin-container">
        <!-- Header -->
        <div class="signin-header">
          <h1 class="signin-title">Sign in</h1>
          <p class="signin-subtitle">Please login to continue to your account.</p>
        </div>

        <!-- Login Form -->
        <form method="post" name="usernamePasswordForm" class="form-container">
          <!-- Email Input -->
          <div class="form-group">
            <input 
              type="email" 
              id="email"
              class="form-input email-input" 
              name="username" 
              placeholder="Email"
              required
            />
            <label for="email" class="form-label">Email</label>
          </div>

          <!-- Password Input -->
          <div class="form-group">
            <input 
              type="password" 
              id="password"
              class="form-input password-input" 
              name="password" 
              placeholder="Password"
              required
            />
            <label for="password" class="form-label">Password</label>
          </div>

          <!-- Keep Logged In Checkbox -->
          <div class="checkbox-container">
            <input type="checkbox" id="keep-logged-in" class="checkbox">
            <label for="keep-logged-in" class="checkbox-label">Keep me logged in</label>
          </div>

          <!-- Sign In Button -->
          <button type="submit" class="signin-button" id="btn-login">
            <span class="signin-button-text">Sign in</span>
          </button>

          <!-- Error Message Display -->
          <div id="error-message" class="error-message" style="display: none;"></div>

        </form>

        <!-- Sign Up Link -->
        <p class="signup-link">
          Need an account? <a href="#" id="show-signup-link">Create one</a>
        </p>
      </div>

      <!-- Sign Up Container -->
      <div class="signup-container">
        <!-- Header -->
        <div class="signin-header">
          <h1 class="signin-title">Sign up</h1>
          <p class="signin-subtitle">Sign up to upskill yourself!</p>
        </div>

        <!-- Signup Form -->
        <form method="post" name="signupForm" class="form-container">
          <div class="form-group">
            <input type="text" id="first-name" class="form-input" name="firstName" placeholder="First name" required>
            <label for="first-name" class="form-label">First name</label>
          </div>
          <div class="form-group">
            <input type="text" id="last-name" class="form-input" name="lastName" placeholder="Last name" required>
            <label for="last-name" class="form-label">Last name</label>
          </div>
          <div class="form-group">
            <select id="school-name" class="form-input" name="schoolName" required>
              <option value="" disabled selected>School name</option>
              <option value="other">Other School</option>
            </select>
          </div>
          <div class="form-group">
            <input type="text" id="class" class="form-input" name="class" placeholder="Class" required>
            <label for="class" class="form-label">Class</label>
          </div>
          <div class="form-group">
            <input type="email" id="signup-email" class="form-input email-input" name="email" placeholder="Email" required>
            <label for="signup-email" class="form-label">Email</label>
          </div>
          <div class="form-group">
            <input type="tel" id="phone-number" class="form-input" name="phoneNumber" placeholder="Phone Number" required>
            <label for="phone-number" class="form-label">Phone Number</label>
          </div>
          <div class="form-group">
            <input type="password" id="signup-password" class="form-input password-input" name="password" placeholder="Password" required>
            <label for="signup-password" class="form-label">Password</label>
          </div>

          <button type="submit" class="signin-button" id="btn-signup">
            <span class="signin-button-text">Sign up</span>
          </button>

          <!-- Error Message Display for Signup -->
          <div id="signup-error-message" class="error-message" style="display: none;"></div>
        </form>

        <p class="signup-link">
          Already have an account? <a href="#" id="show-signin-link">Sign in</a>
        </p>
      </div>

      <!-- Social Media Section -->
      <div class="social-section">
        <p class="social-text">Follow us on:</p>
        <div class="social-icons">
          <!-- Globe Icon -->
          <svg class="social-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" fill="#6C6C6C"/>
          </svg>
          
          <!-- LinkedIn Icon -->
          <svg class="social-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" fill="#6C6C6C"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Right Side - Image -->
    <div class="image-section">
      <img src="login-side-image.png" alt="Login Side Image" class="side-image">
    </div>
  </div>

  <script>
    // Auth0 Configuration from your project
    const AUTH0_CONFIG = {
      domain: 'dev-learnido.au.auth0.com',
      clientId: '8JJS7xJDYzD1moxQSVFmdurRCbHxVgip',
      audience: 'learnido.dev.auth.api.v1',
      scope: 'openid profile email',
      responseType: 'code',
      connection: 'Dev-Lernido-UP-Authentication'
    };

    // Initialize Auth0 WebAuth with your actual config
    let webAuth;
    let databaseConnection = AUTH0_CONFIG.connection;

    // Try to get config from Auth0 template if available, otherwise use project config
    try {
      const templateConfig = JSON.parse(
        decodeURIComponent(escape(window.atob('@@config@@')))
      );

      webAuth = new auth0.WebAuth({
        domain: templateConfig.auth0Domain || AUTH0_CONFIG.domain,
        clientID: templateConfig.clientID || AUTH0_CONFIG.clientId,
        redirectUri: templateConfig.callbackURL || window.location.origin,
        responseType: 'code',
        scope: templateConfig.internalOptions?.scope || AUTH0_CONFIG.scope,
        audience: AUTH0_CONFIG.audience
      });

      // Use the connection from template or fallback to project config
      databaseConnection = templateConfig.internalOptions?.connection || AUTH0_CONFIG.connection;
    } catch (e) {
      // Fallback to project config
      webAuth = new auth0.WebAuth({
        domain: AUTH0_CONFIG.domain,
        clientID: AUTH0_CONFIG.clientId,
        redirectUri: window.location.origin,
        responseType: AUTH0_CONFIG.responseType,
        scope: AUTH0_CONFIG.scope,
        audience: AUTH0_CONFIG.audience
      });
      console.log('Using project Auth0 config');
    }

    document.addEventListener('DOMContentLoaded', function() {
      const signinContainer = document.querySelector('.signin-container');
      const signupContainer = document.querySelector('.signup-container');
      const showSignupLink = document.getElementById('show-signup-link');
      const showSigninLink = document.getElementById('show-signin-link');
      const socialSection = document.querySelector('.social-section');
      const logoContainer = document.querySelector('.logo-container');

      // Toggle between signin and signup forms
      showSignupLink.addEventListener('click', function(e) {
        e.preventDefault();
        signinContainer.style.display = 'none';
        signupContainer.style.display = 'flex';
        logoContainer.style.marginBottom = '10px';
        socialSection.style.bottom = '20px';
      });

      showSigninLink.addEventListener('click', function(e) {
        e.preventDefault();
        signupContainer.style.display = 'none';
        signinContainer.style.display = 'flex';
        logoContainer.style.marginBottom = '40px';
        socialSection.style.bottom = '32px';
      });

      // Form styling and focus effects
      const emailInput = document.querySelector('input[name="username"]');
      const passwordInput = document.querySelector('input[name="password"]');

      emailInput.addEventListener('focus', function() {
        this.style.borderColor = '#347468';
        this.style.borderWidth = '1.5px';
      });

      emailInput.addEventListener('blur', function() {
        if (!this.value) {
          this.style.borderColor = '#347468';
        }
      });

      passwordInput.addEventListener('focus', function() {
        this.style.borderColor = '#347468';
        this.style.borderWidth = '1.5px';
      });

      passwordInput.addEventListener('blur', function() {
        if (!this.value) {
          this.style.borderColor = '#D9D9D9';
          this.style.borderWidth = '1px';
        }
      });

      // Auth0 Login Function - Using Cross-Origin Authentication
      function login(e) {
        e.preventDefault();
        const button = document.getElementById('btn-login');
        const username = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        if (!username || !password) {
          displayError({ description: 'Please fill in all fields' });
          return;
        }

        button.disabled = true;
        button.querySelector('.signin-button-text').textContent = 'Signing in...';

        // Use cross-origin authentication which is supported
        webAuth.crossOriginAuthentication.login({
          realm: databaseConnection,
          username: username,
          password: password
        }, function(err, authResult) {
          button.disabled = false;
          button.querySelector('.signin-button-text').textContent = 'Sign in';

          if (err) {
            console.error('Login error:', err);
            displayError(err);
          } else if (authResult) {
            console.log('Login successful:', authResult);
            // Store tokens and redirect
            handleAuthResult(authResult);
          }
        });
      }

      // Handle authentication result
      function handleAuthResult(authResult) {
        if (authResult && authResult.accessToken && authResult.idToken) {
          console.log('Authentication successful, storing tokens...');

          const tokenData = {
            accessToken: authResult.accessToken,
            idToken: authResult.idToken,
            user: authResult.idTokenPayload
          };

          localStorage.setItem('JWT', JSON.stringify(tokenData));
          localStorage.setItem('IdToken', authResult.idToken);
          localStorage.setItem('access_token', authResult.accessToken);
          localStorage.setItem('id_token', authResult.idToken);
          localStorage.setItem('expires_at', JSON.stringify((authResult.expiresIn * 1000) + new Date().getTime()));

          console.log('Redirecting to main application...');
          window.location.href = window.location.origin;
        }
      }

      // Auth0 Signup Function
      function signup(e) {
        e.preventDefault();
        const button = document.getElementById('btn-signup');
        const email = document.getElementById('signup-email').value;
        const password = document.getElementById('signup-password').value;
        const firstName = document.getElementById('first-name').value;
        const lastName = document.getElementById('last-name').value;

        if (!email || !password || !firstName || !lastName) {
          displaySignupError({ description: 'Please fill in all required fields' });
          return;
        }

        button.disabled = true;
        button.querySelector('.signin-button-text').textContent = 'Signing up...';

        // Use webAuth.signup for database connection signup
        webAuth.signup({
          connection: databaseConnection,
          email: email,
          password: password,
          userMetadata: {
            first_name: firstName,
            last_name: lastName,
            school_name: document.getElementById('school-name').value,
            class: document.getElementById('class').value,
            phone_number: document.getElementById('phone-number').value
          }
        }, function(err, result) {
          button.disabled = false;
          button.querySelector('.signin-button-text').textContent = 'Sign up';

          if (err) {
            console.error('Signup error:', err);
            displaySignupError(err);
          } else {
            console.log('Signup successful:', result);
            // Show success message and redirect to login
            displaySignupError({ description: 'Account created successfully! Please check your email to verify your account, then sign in.' });
            setTimeout(() => {
              // Switch back to login form
              document.querySelector('.signup-container').style.display = 'none';
              document.querySelector('.signin-container').style.display = 'flex';
            }, 3000);
          }
        });
      }

      // Error display functions
      function displayError(err) {
        const errorMessage = document.getElementById('error-message');
        errorMessage.innerText = err.policy || err.description || 'An error occurred during login';
        errorMessage.style.display = 'block';

        setTimeout(() => {
          errorMessage.style.display = 'none';
        }, 5000);
      }

      function displaySignupError(err) {
        const errorMessage = document.getElementById('signup-error-message');
        errorMessage.innerText = err.policy || err.description || 'An error occurred during signup';
        errorMessage.style.display = 'block';

        setTimeout(() => {
          errorMessage.style.display = 'none';
        }, 5000);
      }

      // Event listeners for forms
      document.querySelector('form[name="usernamePasswordForm"]').addEventListener('submit', login);
      document.querySelector('form[name="signupForm"]').addEventListener('submit', signup);

      // Check for authentication result on page load
      webAuth.parseHash(function(err, authResult) {
        if (err) {
          console.error('Parse hash error:', err);
          displayError(err);
        } else if (authResult && authResult.accessToken && authResult.idToken) {
          handleAuthResult(authResult);
        }
      });
    });
  </script>
  
</body>
</html>