import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, PlayCircle, BookOpen, FileText, HelpCircle } from 'lucide-react';

// Animation variants
const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.5 } },
};

// Index type
type IndexType = 'Employability Index' | 'Academic Index' | 'Social Index';

// Contribution item interface
interface ContributionItem {
  id: number;
  title: string;
  timeAgo: string;
  iconType: 'video' | 'book' | 'document' | 'question';
}

interface IndexContributionBreakdownProps {
  className?: string;
}

const IndexContributionBreakdown: React.FC<IndexContributionBreakdownProps> = ({ className = '' }) => {
  const [selectedIndex, setSelectedIndex] = useState<IndexType>('Employability Index');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Mock data
  const score = 60;
  const maxScore = 100;
  
  // Mock contributions
  const contributions: ContributionItem[] = [
    { 
      id: 1, 
      title: '01: Introduction to Pronouns', 
      timeAgo: '59 minutes ago',
      iconType: 'video' // Video lecture
    },
    { 
      id: 2, 
      title: '01: Introduction to Pronouns', 
      timeAgo: '59 minutes ago',
      iconType: 'book' // Course reading
    },
    { 
      id: 3, 
      title: '01: Introduction to Pronouns', 
      timeAgo: 'Half hour',
      iconType: 'video' // Document/article
    },
    { 
      id: 4, 
      title: '01: Introduction to Pronouns', 
      timeAgo: '22 minutes ago',
      iconType: 'book' // Quiz or question
    }
  ];

  // Available indices
  const indices: IndexType[] = [
    'Employability Index',
    'Academic Index',
    'Social Index'
  ];

  // Toggle dropdown
  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Select index
  const selectIndex = (index: IndexType) => {
    setSelectedIndex(index);
    setIsDropdownOpen(false);
  };

  return (
    <motion.div
      className={`bg-white rounded-2xl border border-[#97C48A] shadow-sm p-5 sm:p-6 ${className}`}
      variants={fadeIn}
      initial="initial"
      animate="animate"
    >
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold text-[#2D2D2D]">Index Contribution Breakdown</h3>
        
        {/* Index selector dropdown */}
        <div className="relative">
          <button 
            onClick={toggleDropdown}
            className="flex items-center gap-2 px-3 py-1 text-sm font-medium text-[#347468] border-2 border-[#347468] rounded-full"
          >
            {selectedIndex}
            <ChevronDown className={`w-4 h-4 transform transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
          </button>
          
          {isDropdownOpen && (
            <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-[#E2E8F0]">
              {indices.map((index) => (
                <button
                  key={index}
                  onClick={() => selectIndex(index)}
                  className={`block w-full text-left px-4 py-2 text-sm ${
                    selectedIndex === index ? 'bg-[#F7FAFC] text-[#4A5568]' : 'text-[#4A5568] hover:bg-[#F7FAFC]'
                  }`}
                >
                  {index}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
      
      {/* Score display */}
      <div className="mb-6 bg-[#F7FAFC] border border-[#347468] rounded-[14px] w-full">
        <div 
          className="text-center italic font-bold text-lg text-white rounded-[14px] py-2 px-4" 
          style={{
            width: `${(score / maxScore) * 100}%`,
            background: 'linear-gradient(90deg, #86C3B8 0%, #347468 100%)'
          }}
        >
          {score}/{maxScore}
        </div>
      </div>
      
      {/* Contributions list */}
      <div className="space-y-3">
        {contributions.map((item) => (
          <div key={item.id} className="flex items-center gap-3 p-2 hover:bg-[#F7FAFC] rounded-md transition-colors">
            <div className="flex-shrink-0 w-8 h-8 bg-[#E6F2EF] rounded-full flex items-center justify-center text-[#347468]">
              {item.iconType === 'video' && <PlayCircle size={18} />}
              {item.iconType === 'book' && <BookOpen size={18} />}
              {item.iconType === 'document' && <FileText size={18} />}
              {item.iconType === 'question' && <HelpCircle size={18} />}
            </div>
            <div className="flex-grow">
              <p className="text-sm font-medium text-[#2D2D2D]">{item.title}</p>
              <p className="text-xs text-gray-500">{item.timeAgo}</p>
            </div>
          </div>
        ))}
      </div>
    </motion.div>
  );
};

export default IndexContributionBreakdown;
