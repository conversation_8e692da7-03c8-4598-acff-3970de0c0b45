import React from 'react';
import { Card } from "@/components/ui/card";
import { Bell } from 'lucide-react';
import { NotificationAlert } from '@/types/projectCenter';

interface NotificationsAlertsProps {
  notifications: NotificationAlert[];
}

const NotificationsAlerts: React.FC<NotificationsAlertsProps> = ({ notifications }) => {
  return (
    <div>
      <div className="flex items-center gap-2 mb-3">
        <Bell size={20} className="text-slate-600" />
        <h2 className="text-zinc-800 text-lg font-semibold">Notifications & Alerts</h2>
      </div>
      <div className="space-y-3">
        {notifications.map(notification => (
          <Card key={notification.id} className="px-4 py-2 bg-white rounded-xl border border-slate-600">
            <div className="flex justify-between items-start">
              <div>
                <div className="flex items-center gap-1">
                  <p className="text-neutral-500 text-xs font-normal">{notification.type}</p>
                  <p className="text-zinc-800 text-xs font-medium">{notification.date}</p>
                </div>
                <p className="text-slate-600 text-sm font-medium mt-1">
                  {notification.title}
                </p>
              </div>
              {notification.isNew && (
                <span className="text-xs text-white bg-blue-500 px-1.5 py-0.5 rounded-full">
                  New
                </span>
              )}
            </div>
          </Card>
        ))}
        {notifications.length === 0 && (
          <p className="text-sm text-gray-400 text-center py-4">No new notifications.</p>
        )}
      </div>
    </div>
  );
};

export default NotificationsAlerts; 