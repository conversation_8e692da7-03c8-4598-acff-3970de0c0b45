import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
    BarChart,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
    Pie<PERSON>hart,
    Pie,
    Cell,
    LineChart,
    Line
} from 'recharts';
import { ChapterAnalytics as ChapterAnalyticsType } from '@/types/analytics';
import {
    Trophy,
    Clock,
    Send,
    Repeat2,
    <PERSON><PERSON><PERSON> as PieChartIcon,
    <PERSON><PERSON>hart as BarChartIcon,
    <PERSON><PERSON>hart as LineChartIcon,
    AlertCircle
} from 'lucide-react';

const COLORS = ['#6366f1', '#22c55e', '#eab308', '#ec4899', '#8b5cf6'];
const CHART_ANIMATION_DURATION = 1000;

interface ChapterAnalyticsProps {
    data?: ChapterAnalyticsType;
    isLoading?: boolean;
    error?: string;
}

export const ChapterAnalytics: React.FC<ChapterAnalyticsProps> = ({
    data,
    isLoading = false,
    error
}) => {
    if (error) {
        return (
            <Alert variant="destructive" className="m-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

    if (isLoading) {
        return (
            <div className="p-6 space-y-6">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {[...Array(4)].map((_, i) => (
                        <Card key={i}>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <Skeleton className="h-4 w-[150px]" />
                            </CardHeader>
                            <CardContent>
                                <Skeleton className="h-8 w-[100px] mb-2" />
                                <Skeleton className="h-4 w-[120px]" />
                            </CardContent>
                        </Card>
                    ))}
                </div>
                <Card>
                    <CardContent className="h-[400px] flex items-center justify-center">
                        <Skeleton className="h-full w-full" />
                    </CardContent>
                </Card>
            </div>
        );
    }

    if (!data) return null;

    const questionTypeData = [
        { name: 'Multiple Choice', value: data.averageMultipleChoiceCount.averagePassCount },
        { name: 'Single Choice', value: data.averageSingleChoiceCount.averagePassCount },
        { name: 'Text Input', value: data.averageTextInputCount.averagePassCount },
        { name: 'Match', value: data.averageMatchCount.averagePassCount },
        { name: 'Binary', value: data.averageBinaryCount.averagePassCount }
    ].filter(item => item.value > 0);

    const levelData = [
        { name: 'Level 1', ...data.averageLvl1Count },
        { name: 'Level 2', ...data.averageLvl2Count },
        { name: 'Level 3', ...data.averageLvl3Count },
        { name: 'Level 4', ...data.averageLvl4Count },
        { name: 'Level 5', ...data.averageLvl5Count }
    ].filter(level => level.averageTotalCount > 0);

    const timeData = [
        { name: 'Multiple Choice', time: data.averageMultipleChoiceCount.averageTotalTimeTaken },
        { name: 'Single Choice', time: data.averageSingleChoiceCount.averageTotalTimeTaken },
        { name: 'Text Input', time: data.averageTextInputCount.averageTotalTimeTaken },
        { name: 'Match', time: data.averageMatchCount.averageTotalTimeTaken },
        { name: 'Binary', time: data.averageBinaryCount.averageTotalTimeTaken }
    ].filter(item => item.time > 0);

    return (
        <div className="p-6 space-y-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card className="transition-all duration-200 hover:shadow-lg">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Average Score</CardTitle>
                        <Trophy className="h-4 w-4 text-yellow-500" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{data.averageScore.toFixed(1)}</div>
                        <p className="text-xs text-muted-foreground">
                            {data.averagePercentage.toFixed(1)}% success rate
                        </p>
                    </CardContent>
                </Card>
                <Card className="transition-all duration-200 hover:shadow-lg">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Average Time</CardTitle>
                        <Clock className="h-4 w-4 text-blue-500" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{(data.averageTimeTaken / 1000).toFixed(1)}s</div>
                        <p className="text-xs text-muted-foreground">Per question attempt</p>
                    </CardContent>
                </Card>
                <Card className="transition-all duration-200 hover:shadow-lg">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Submissions</CardTitle>
                        <Send className="h-4 w-4 text-green-500" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{data.averageSuccessfulSubmissions}</div>
                        <p className="text-xs text-muted-foreground">Average successful attempts</p>
                    </CardContent>
                </Card>
                <Card className="transition-all duration-200 hover:shadow-lg">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Repetitions</CardTitle>
                        <Repeat2 className="h-4 w-4 text-purple-500" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{data.averageRepitionCount}</div>
                        <p className="text-xs text-muted-foreground">Average practice attempts</p>
                    </CardContent>
                </Card>
            </div>

            <Tabs defaultValue="question-types" className="space-y-4">
                <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="question-types" className="space-x-2">
                        <PieChartIcon className="h-4 w-4" />
                        <span>Question Types</span>
                    </TabsTrigger>
                    <TabsTrigger value="difficulty-levels" className="space-x-2">
                        <BarChartIcon className="h-4 w-4" />
                        <span>Difficulty Levels</span>
                    </TabsTrigger>
                    <TabsTrigger value="time-analysis" className="space-x-2">
                        <LineChartIcon className="h-4 w-4" />
                        <span>Time Analysis</span>
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="question-types" className="space-y-4">
                    <Card className="transition-all duration-200 hover:shadow-lg">
                        <CardHeader>
                            <CardTitle>Question Type Performance</CardTitle>
                        </CardHeader>
                        <CardContent className="h-[400px]">
                            <ResponsiveContainer width="100%" height="100%">
                                <PieChart>
                                    <Pie
                                        data={questionTypeData}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                                        outerRadius={150}
                                        fill="#8884d8"
                                        dataKey="value"
                                        animationDuration={CHART_ANIMATION_DURATION}
                                        animationBegin={0}
                                    >
                                        {questionTypeData.map((entry, index) => (
                                            <Cell
                                                key={`cell-${index}`}
                                                fill={COLORS[index % COLORS.length]}
                                                className="transition-all duration-300 hover:opacity-80"
                                            />
                                        ))}
                                    </Pie>
                                    <Tooltip
                                        contentStyle={{
                                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                            borderRadius: '8px',
                                            border: 'none',
                                            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                                        }}
                                    />
                                    <Legend />
                                </PieChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="difficulty-levels" className="space-y-4">
                    <Card className="transition-all duration-200 hover:shadow-lg">
                        <CardHeader>
                            <CardTitle>Performance by Difficulty Level</CardTitle>
                        </CardHeader>
                        <CardContent className="h-[400px]">
                            <ResponsiveContainer width="100%" height="100%">
                                <BarChart data={levelData}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                                    <XAxis dataKey="name" />
                                    <YAxis />
                                    <Tooltip
                                        contentStyle={{
                                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                            borderRadius: '8px',
                                            border: 'none',
                                            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                                        }}
                                    />
                                    <Legend />
                                    <Bar
                                        name="Pass Count"
                                        dataKey="averagePassCount"
                                        fill={COLORS[0]}
                                        animationDuration={CHART_ANIMATION_DURATION}
                                        className="transition-all duration-300 hover:opacity-80"
                                    />
                                    <Bar
                                        name="Total Attempts"
                                        dataKey="averageTotalCount"
                                        fill={COLORS[1]}
                                        animationDuration={CHART_ANIMATION_DURATION}
                                        className="transition-all duration-300 hover:opacity-80"
                                    />
                                </BarChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="time-analysis" className="space-y-4">
                    <Card className="transition-all duration-200 hover:shadow-lg">
                        <CardHeader>
                            <CardTitle>Time Spent by Question Type</CardTitle>
                        </CardHeader>
                        <CardContent className="h-[400px]">
                            <ResponsiveContainer width="100%" height="100%">
                                <LineChart data={timeData}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                                    <XAxis dataKey="name" />
                                    <YAxis label={{ value: 'Time (seconds)', angle: -90, position: 'insideLeft' }} />
                                    <Tooltip
                                        formatter={(value) => `${(Number(value) / 1000).toFixed(1)}s`}
                                        contentStyle={{
                                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                            borderRadius: '8px',
                                            border: 'none',
                                            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                                        }}
                                    />
                                    <Legend />
                                    <Line
                                        type="monotone"
                                        dataKey="time"
                                        stroke={COLORS[4]}
                                        name="Average Time"
                                        strokeWidth={2}
                                        dot={{ fill: COLORS[4] }}
                                        activeDot={{ r: 8 }}
                                        animationDuration={CHART_ANIMATION_DURATION}
                                        className="transition-all duration-300 hover:opacity-80"
                                    />
                                </LineChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default ChapterAnalytics;
