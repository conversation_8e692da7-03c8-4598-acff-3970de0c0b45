import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

interface DataPoint {
    date: string;
    value: number;
}

interface LineChartProps {
    data: DataPoint[];
    width?: number;
    height?: number;
    xLabel: string;
    yLabel: string;
    title: string;
    studentMode?: boolean;
}

export const LineChart: React.FC<LineChartProps> = ({
    data,
    width = 600,
    height = 400,
    xLabel,
    yLabel,
    title,
    studentMode = false
}) => {
    const svgRef = useRef<SVGSVGElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const tooltipRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        if (!data || !svgRef.current || !containerRef.current) return;

        // Clear previous chart
        d3.select(svgRef.current).selectAll('*').remove();

        // Get container dimensions
        const containerWidth = containerRef.current.clientWidth;
        const containerHeight = containerRef.current.clientHeight;
        const actualWidth = containerWidth;
        const actualHeight = containerHeight;

        const margin = { top: 30, right: 30, bottom: 50, left: 60 };
        const innerWidth = actualWidth - margin.left - margin.right;
        const innerHeight = height - margin.top - margin.bottom;

        // Create responsive SVG
        const svg = d3.select(svgRef.current)
            .attr('width', '100%')
            .attr('height', '100%')
            .attr('viewBox', `0 0 ${actualWidth} ${actualHeight}`)
            .attr('preserveAspectRatio', 'xMidYMid meet')
            .style('overflow', 'visible');

        // Add gradients and filters
        const defs = svg.append('defs');

        // Line gradient
        const lineGradient = defs.append('linearGradient')
            .attr('id', 'line-gradient')
            .attr('gradientUnits', 'userSpaceOnUse')
            .attr('x1', 0)
            .attr('y1', 0)
            .attr('x2', innerWidth)
            .attr('y2', 0);

        if (studentMode) {
            lineGradient.append('stop')
                .attr('offset', '0%')
                .attr('stop-color', 'hsl(217, 85%, 75%)'); // Light Blue

            lineGradient.append('stop')
                .attr('offset', '100%')
                .attr('stop-color', 'hsl(217, 85%, 65%)'); // Slightly Darker Blue
        } else {
            lineGradient.append('stop')
                .attr('offset', '0%')
                .attr('stop-color', 'hsl(217, 91%, 60%)'); // Blue-500

            lineGradient.append('stop')
                .attr('offset', '100%')
                .attr('stop-color', 'hsl(221, 83%, 53%)'); // Darker Blue
        }

        // Area gradient
        const areaGradient = defs.append('linearGradient')
            .attr('id', 'area-gradient')
            .attr('x1', '0%')
            .attr('y1', '0%')
            .attr('x2', '0%')
            .attr('y2', '100%');

        areaGradient.append('stop')
            .attr('offset', '0%')
            .attr('stop-color', 'hsl(217, 91%, 60%)')
            .attr('stop-opacity', 0.2);

        areaGradient.append('stop')
            .attr('offset', '100%')
            .attr('stop-color', 'hsl(217, 91%, 60%)')
            .attr('stop-opacity', 0);

        // Glow effect
        const glow = defs.append('filter')
            .attr('id', 'glow')
            .attr('height', '300%')
            .attr('width', '300%')
            .attr('x', '-100%')
            .attr('y', '-100%');

        glow.append('feGaussianBlur')
            .attr('stdDeviation', '3')
            .attr('result', 'coloredBlur');

        const feMerge = glow.append('feMerge');
        feMerge.append('feMergeNode')
            .attr('in', 'coloredBlur');
        feMerge.append('feMergeNode')
            .attr('in', 'SourceGraphic');

        const g = svg.append('g')
            .attr('transform', `translate(${margin.left},${margin.top})`);

        // Scales
        const xScale = d3.scaleTime()
            .domain(d3.extent(data, d => new Date(d.date)) as [Date, Date])
            .range([0, innerWidth]);

        const yScale = d3.scaleLinear()
            .domain([0, d3.max(data, d => d.value) as number])
            .nice()
            .range([innerHeight, 0]);

        // Add grid lines with animation
        const yAxis = d3.axisLeft(yScale)
            .tickSize(-innerWidth)
            .tickFormat(() => '')
            .ticks(5);

        const xAxis = d3.axisBottom(xScale)
            .tickSize(-innerHeight)
            .tickFormat(() => '')
            .ticks(5);

        // Add Y grid lines
        const yGrid = g.append('g')
            .attr('class', 'grid y-grid')
            .attr('opacity', 0)
            .call(yAxis as any);

        yGrid.transition()
            .duration(1000)
            .attr('opacity', 0.1);

        // Add X grid lines
        const xGrid = g.append('g')
            .attr('class', 'grid x-grid')
            .attr('transform', `translate(0,${innerHeight})`)
            .attr('opacity', 0)
            .call(xAxis as any);

        xGrid.transition()
            .duration(1000)
            .attr('opacity', 0.1);

        // Style grid lines
        g.selectAll('.grid line')
            .style('stroke', 'hsl(var(--border))')
            .style('stroke-dasharray', '4,4');

        // Line generator
        const line = d3.line<DataPoint>()
            .x(d => xScale(new Date(d.date)))
            .y(d => yScale(d.value))
            .curve(d3.curveMonotoneX);

        // Add axes with improved styling
        const xAxisDisplay = d3.axisBottom(xScale)
            .ticks(5)
            .tickSize(6);

        const xAxisGroup = g.append('g')
            .attr('transform', `translate(0,${innerHeight})`)
            .attr('class', 'x-axis')
            .style('color', 'hsl(var(--muted-foreground))')
            .style('font-size', '12px')
            .call(xAxisDisplay as any);

        const yAxisDisplay = d3.axisLeft(yScale)
            .ticks(5)
            .tickSize(6);

        const yAxisGroup = g.append('g')
            .attr('class', 'y-axis')
            .style('color', 'hsl(var(--muted-foreground))')
            .style('font-size', '12px')
            .call(yAxisDisplay as any);

        // Style axis lines
        g.selectAll('.axis line')
            .style('stroke', 'hsl(var(--border))');

        g.selectAll('.axis path')
            .style('stroke', 'hsl(var(--border))');

        // Add line path with animation and glow effect
        const path = g.append('path')
            .datum(data)
            .attr('fill', 'none')
            .attr('stroke', 'url(#line-gradient)')
            .attr('stroke-width', 3)
            .attr('filter', 'url(#glow)')
            .attr('d', line(data) || '');

        // Animate the line
        const pathLength = path.node()?.getTotalLength() || 0;
        path.attr('stroke-dasharray', pathLength)
            .attr('stroke-dashoffset', pathLength)
            .transition()
            .duration(2000)
            .ease(d3.easeQuadOut)
            .attr('stroke-dashoffset', 0);

        // Add gradient area with animation
        const areaGenerator = d3.area<DataPoint>()
            .x(d => xScale(new Date(d.date)))
            .y0(innerHeight)
            .y1(d => yScale(d.value))
            .curve(d3.curveMonotoneX);

        const area = g.append('path')
            .datum(data)
            .attr('fill', 'url(#area-gradient)')
            .attr('opacity', 0)
            .attr('d', areaGenerator);

        area.transition()
            .duration(1500)
            .attr('opacity', 1);

        // Add animated markers with hover effects
        const markers = g.selectAll('.marker')
            .data(data)
            .enter()
            .append('g')
            .attr('class', 'marker')
            .attr('transform', d => `translate(${xScale(new Date(d.date))},${yScale(d.value)})`);

        if (studentMode) {
            markers.append('circle')
                .attr('r', 0)
                .attr('fill', 'hsl(217, 85%, 75%)')
                .attr('stroke', 'white')
                .attr('stroke-width', 2)
                .attr('filter', 'url(#glow)')
                .transition()
                .delay((_, i) => i * 150)
                .duration(400)
                .attr('r', 8)
                .ease(d3.easeBounceOut);

            // Add bouncing animation
            markers.each(function () {
                const marker = d3.select(this);
                function bounce() {
                    marker.transition()
                        .duration(1000)
                        .attr('transform', function (d: any) {
                            return `translate(${xScale(new Date(d.date))},${yScale(d.value) - 5})`;
                        })
                        .transition()
                        .duration(1000)
                        .attr('transform', function (d: any) {
                            return `translate(${xScale(new Date(d.date))},${yScale(d.value)})`;
                        })
                        .on('end', bounce);
                }
                bounce();
            });
        } else {
            markers.append('circle')
                .attr('r', 0)
                .attr('fill', 'white')
                .attr('stroke', 'hsl(217, 91%, 60%)')
                .attr('stroke-width', 2)
                .style('filter', 'url(#glow)')
                .transition()
                .delay((_, i) => i * 150)
                .duration(400)
                .attr('r', 4)
                .ease(d3.easeBounceOut);
        }

        // Enhanced tooltip
        const tooltip = d3.select('body')
            .append('div')
            .attr('class', 'tooltip')
            .style('position', 'absolute')
            .style('background-color', 'hsl(var(--background))')
            .style('padding', '12px')
            .style('border', '1px solid hsl(var(--border))')
            .style('border-radius', '8px')
            .style('pointer-events', 'none')
            .style('opacity', 0)
            .style('font-size', '14px')
            .style('color', 'hsl(var(--foreground))')
            .style('box-shadow', '0 4px 12px rgba(0, 0, 0, 0.1)')
            .style('transform', 'translate(-50%, -100%)');

        markers.on('mouseover', function (this: SVGElement, event: MouseEvent, d: DataPoint) {
            const marker = d3.select(this);
            const circle = marker.select('circle');

            circle.transition()
                .duration(200)
                .attr('r', studentMode ? 10 : 6)
                .attr('stroke-width', 3);

            tooltip.transition()
                .duration(200)
                .style('opacity', 1);

            tooltip.html(`
                <div class="font-medium">${d.date}</div>
                <div class="text-sm text-muted-foreground">Score: ${d.value}</div>
            `)
                .style('left', (event.pageX) + 'px')
                .style('top', (event.pageY - 10) + 'px');
        })
            .on('mouseout', function () {
                d3.select(this).select('circle')
                    .transition()
                    .duration(200)
                    .attr('r', studentMode ? 8 : 4)
                    .attr('stroke-width', 2);

                tooltip.transition()
                    .duration(200)
                    .style('opacity', 0);
            });

        // Handle resize
        const handleResize = () => {
            if (!containerRef.current) return;
            const newWidth = containerRef.current.clientWidth;
            svg.attr('width', newWidth)
                .attr('viewBox', `0 0 ${newWidth} ${height}`);

            // Update scales and redraw
            xScale.range([0, newWidth - margin.left - margin.right]);

            // Update line gradient
            lineGradient.attr('x2', newWidth - margin.left - margin.right);

            // Update line
            path.attr('d', line(data) || '');

            // Update area
            area.attr('d', areaGenerator);

            // Update markers
            markers.attr('transform', d => `translate(${xScale(new Date(d.date))},${yScale(d.value)})`);

            // Update x-axis
            const newXAxis = d3.axisBottom(xScale)
                .ticks(5)
                .tickSize(0);

            g.select('.x-axis')
                .call(newXAxis as any);
        };

        window.addEventListener('resize', handleResize);

        // Cleanup
        return () => {
            d3.select('body').selectAll('.tooltip').remove();
            window.removeEventListener('resize', handleResize);
        };
    }, [data, width, height, xLabel, yLabel, title, studentMode]);

    return (
        <div ref={containerRef} className="w-full h-full">
            <svg ref={svgRef} className="w-full h-full" />
        </div>
    );
};
