import React, { useState, useEffect, Suspense } from "react";
import { useAuth0 } from "@auth0/auth0-react";
import { useNavigate, useParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "../../ui/table";
import { ScrollArea } from "../../ui/scroll-area";
import { Button } from "../../ui/button";
import { Textarea } from "../../ui/textarea";
import { Input } from "../../ui/input";
import { Progress } from "../../ui/progress";
import { useGetSchoolClassroomsQuery, useLazyGetCourseDetailsDataQuery, useCreateAnnouncementMutation, useGetAnnouncementsQuery, useGetClassRoomTokenQuery } from "../../../APIConnect";
import { Loader2, ChevronLeft, Plus, Users, Calendar, Book, Award, Clock, AlertCircle, Video } from "lucide-react";
import { TeacherClassroomLoadingSkeleton } from "./TeacherClassroomLoadingSkeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../ui/tabs";
import { Card, CardHeader, CardTitle, CardContent } from "../../ui/card";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "../../ui/dialog";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "../../ui/tooltip";
import {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip as ChartTooltip,
    Legend,
    BarChart,
    Bar,
    ResponsiveContainer,
} from "recharts";
import { Module, Course, Topic } from "../../Admin/Courses/types";
import { StudentProgress, Announcement } from "./types";
import TeacherAnalytics from "../../Analytics/TeacherAnalytics";
import StudentAnalytics from "../../Analytics/StudentAnalytics";
import ModulesAndContent from "@/components/Admin/Courses/ModulesAndContent";

// Animation variants
const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.3,
            when: "beforeChildren",
            staggerChildren: 0.1
        }
    },
    exit: { opacity: 0, y: -20 }
};

const itemVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: { opacity: 1, x: 0 }
};

const cardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
        opacity: 1,
        scale: 1,
        transition: {
            type: "spring",
            stiffness: 100,
            damping: 15
        }
    }
};

// Mock data for analytics
const participationData = [
    { week: "Week 1", participation: 85 },
    { week: "Week 2", participation: 92 },
    { week: "Week 3", participation: 78 },
    { week: "Week 4", participation: 88 },
];

const performanceData = [
    { chapter: "Chapter 1", score: 75 },
    { chapter: "Chapter 2", score: 82 },
    { chapter: "Chapter 3", score: 68 },
    { chapter: "Chapter 4", score: 90 },
];

const questionTypeData = [
    { type: "Multiple Choice", attempts: 150 },
    { type: "True/False", attempts: 120 },
    { type: "Short Answer", attempts: 80 },
    { type: "Essay", attempts: 45 },
];

const mockStudentProgress: StudentProgress = {
    overallScore: 85,
    chaptersCompleted: 4,
    topicsCompleted: 12,
    quizzesTaken: 8,
    averageQuizScore: 78,
    lastActive: "2024-01-22",
};

const TeacherClassroomDetailsPage = () => {
    const navigate = useNavigate();
    const { classroomId } = useParams();
    const { user } = useAuth0();
    const schoolId = user?.["http://learnido-app/schoolId"] as string | undefined;
    const [newAnnouncement, setNewAnnouncement] = useState({ title: "", content: "" });
    const [createAnnouncement, { isLoading: isCreatingAnnouncement }] = useCreateAnnouncementMutation();
    const { data: announcementsData, isLoading: isLoadingAnnouncements } = useGetAnnouncementsQuery({ type: "classroomNotification", id: classroomId });
    const [courseDetails, setCourseDetails] = useState<Course | null>(null);
    const [isLoadingCourse, setIsLoadingCourse] = useState(false);
    const [classroom, setClassroom] = useState<any>(null);
    const [selectedStudent, setSelectedStudent] = useState<any>(null);
    const [showStudentDialog, setShowStudentDialog] = useState(false);
    const [isStartingLiveSession, setIsStartingLiveSession] = useState(false);

    const { data: classroomsData, isLoading } = useGetSchoolClassroomsQuery(schoolId);
    const [getCourseDetails] = useLazyGetCourseDetailsDataQuery();
    const { data: tokenData } = useGetClassRoomTokenQuery(
        { classroomId: classroomId || "", displayName: user?.name || "Teacher" },
        { skip: !isStartingLiveSession || !classroomId }
    );

    useEffect(() => {
        const loadCourseDetails = async (courseId: string) => {
            setIsLoadingCourse(true);
            try {
                const { data } = await getCourseDetails({ courseId });
                if (data?.resultObject) {
                    const courseData = data.resultObject;
                    let chapters = courseData.chapters;

                    // Sort chapters using anteriorId
                    let sortedChapters = [];
                    let rootChapter = chapters.find((chapter: any) =>
                        chapter.anteriorId === chapter.id
                    );
                    if (rootChapter) {
                        while (rootChapter && sortedChapters.length < chapters.length) {
                            sortedChapters.push(rootChapter);
                            rootChapter = chapters.find((chapter: any) =>
                                chapter.anteriorId === rootChapter!.id && chapter.id !== rootChapter!.id
                            );
                        }
                    } else {
                        sortedChapters = [...chapters];
                    }

                    // Sort topics within each chapter
                    const chaptersWithSortedTopics = sortedChapters.map(chapter => {
                        let sortedTopics = [];
                        let rootTopic = chapter.topics.find((topic: any) =>
                            topic.anteriorId === topic.id
                        );

                        if (rootTopic) {
                            while (rootTopic && sortedTopics.length < chapter.topics.length) {
                                sortedTopics.push(rootTopic);
                                rootTopic = chapter.topics.find((topic: any) =>
                                    topic.anteriorId === rootTopic!.id && topic.id !== rootTopic!.id
                                );
                            }
                        } else {
                            sortedTopics = [...chapter.topics];
                        }

                        return {
                            ...chapter,
                            topics: sortedTopics
                        };
                    });
                    setCourseDetails({
                        ...courseData,
                        chapters: chaptersWithSortedTopics
                    });
                }
            } catch (error) {
                console.error('Error loading course details:', error);
            } finally {
                setIsLoadingCourse(false);
            }
        };
        if (classroomsData?.resultObject && classroomId) {
            const foundClassroom = classroomsData.resultObject.find(
                (c: any) => c.id === classroomId
            );
            if (foundClassroom) {
                setClassroom(foundClassroom);
                if (foundClassroom.courseId) {
                    loadCourseDetails(foundClassroom.courseId);
                }
            }
        }
    }, [classroomsData, classroomId, getCourseDetails]);

    useEffect(() => {
        if (tokenData?.token && classroomId && isStartingLiveSession) {
            navigate(`/live-session/${classroomId}`);
        }
    }, [tokenData, classroomId, navigate, isStartingLiveSession]);

    const handleCreateAnnouncement = async () => {
        if (!newAnnouncement.title.trim() || !newAnnouncement.content.trim() || !classroomId) return;

        try {
            await createAnnouncement({
                type: 'classroomNotification',
                classroomId,
                notifyString: newAnnouncement.title + "\n\n" + newAnnouncement.content,
                senderId: user?.sub?.replace("auth0|", "")
            }).unwrap();
            setNewAnnouncement({ title: "", content: "" });
        } catch (error) {
            console.error('Failed to create announcement:', error);
        }
    };

    const handleStartLiveSession = () => {
        setIsStartingLiveSession(true);
    };

    const StudentProgressCard = ({ student, progress }: { student: any; progress: StudentProgress }) => (
        <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
        >
            <div className="grid grid-cols-2 gap-6">
                <TooltipProvider>
                    <motion.div variants={cardVariants}>
                        <Card className="p-6 hover:shadow-lg transition-all duration-300">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-lg font-semibold flex items-center">
                                    <Award className="w-5 h-5 mr-2 text-primary" />
                                    Overall Progress
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    <div className="text-3xl font-bold text-primary">{progress.overallScore}%</div>
                                    <Progress value={progress.overallScore} className="h-2" />
                                </div>
                            </CardContent>
                        </Card>
                    </motion.div>

                    <motion.div variants={cardVariants}>
                        <Card className="p-6 hover:shadow-lg transition-all duration-300">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-lg font-semibold flex items-center">
                                    <Book className="w-5 h-5 mr-2 text-primary" />
                                    Course Progress
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <div className="space-y-1">
                                                <div className="flex justify-between text-sm">
                                                    <span>Chapters</span>
                                                    <span className="font-medium">{progress.chaptersCompleted}/10</span>
                                                </div>
                                                <Progress value={progress.chaptersCompleted * 10} className="h-2" />
                                            </div>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>{progress.chaptersCompleted} chapters completed</p>
                                        </TooltipContent>
                                    </Tooltip>

                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <div className="space-y-1">
                                                <div className="flex justify-between text-sm">
                                                    <span>Topics</span>
                                                    <span className="font-medium">{progress.topicsCompleted}/30</span>
                                                </div>
                                                <Progress value={progress.topicsCompleted * 3.33} className="h-2" />
                                            </div>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>{progress.topicsCompleted} topics completed</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </div>
                            </CardContent>
                        </Card>
                    </motion.div>
                </TooltipProvider>
            </div>

            <div className="grid grid-cols-2 gap-6">
                <motion.div variants={cardVariants}>
                    <Card className="p-6 hover:shadow-lg transition-all duration-300">
                        <CardHeader className="pb-2">
                            <CardTitle className="text-lg font-semibold flex items-center">
                                <Award className="w-5 h-5 mr-2 text-primary" />
                                Quiz Performance
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div>
                                    <div className="flex justify-between text-sm mb-1">
                                        <span>Average Score</span>
                                        <span className="font-medium">{progress.averageQuizScore}%</span>
                                    </div>
                                    <Progress value={progress.averageQuizScore} className="h-2" />
                                </div>
                                <div className="text-sm text-muted-foreground">
                                    Total Quizzes Taken: {progress.quizzesTaken}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>

                <motion.div variants={cardVariants}>
                    <Card className="p-6 hover:shadow-lg transition-all duration-300">
                        <CardHeader className="pb-2">
                            <CardTitle className="text-lg font-semibold flex items-center">
                                <Clock className="w-5 h-5 mr-2 text-primary" />
                                Recent Activity
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                <div className="flex items-center text-sm text-muted-foreground">
                                    <Calendar className="w-4 h-4 mr-2" />
                                    Last active: {new Date(progress.lastActive).toLocaleDateString()}
                                </div>
                                <div className="flex items-center text-sm text-muted-foreground">
                                    <Book className="w-4 h-4 mr-2" />
                                    Recent completion: Chapter {progress.chaptersCompleted}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>
            </div>
        </motion.div>
    );

    const ClassroomAnalytics = ({ crId }: { crId: string }) =>
        <TeacherAnalytics preselectedClassroom={crId} />

    if (isLoading || !classroom) {
        return <TeacherClassroomLoadingSkeleton />;
    }

    return (
        <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="container mx-auto p-4"
        >
            <div className="flex flex-col space-y-6">
                <motion.div
                    variants={itemVariants}
                    className="flex items-center space-x-2 text-sm text-muted-foreground"
                >
                    <Button
                        variant="ghost"
                        className="h-8 px-2 hover:bg-accent transition-colors"
                        onClick={() => navigate('/teacher/classroom')}
                    >
                        <ChevronLeft className="h-4 w-4 mr-1" />
                        Back to Classrooms
                    </Button>
                    <span>/</span>
                    <span>Classrooms</span>
                    <span>/</span>
                    <span className="font-medium text-foreground">{classroom.name}</span>
                </motion.div>

                <motion.div
                    variants={itemVariants}
                    className="flex items-center justify-between"
                >
                    <div>
                        <h1 className="text-2xl font-bold flex items-center text-foreground">
                            <Users className="h-6 w-6 mr-2" />
                            {classroom.name}
                        </h1>
                        <AnimatePresence>
                            {classroom.description && (
                                <motion.p
                                    initial={{ opacity: 0, y: 5 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-muted-foreground mt-1"
                                >
                                    {classroom.description}
                                </motion.p>
                            )}
                        </AnimatePresence>
                    </div>
                    <Button
                        onClick={handleStartLiveSession}
                        className="flex items-center space-x-2"
                        disabled={isStartingLiveSession}
                    >
                        {isStartingLiveSession ? (
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                            <Video className="h-4 w-4 mr-2" />
                        )}
                        {isStartingLiveSession ? "Starting..." : "Start Live Session"}
                    </Button>
                </motion.div>

                <Tabs defaultValue="course" className="w-full mt-6">
                    <TabsList className="w-full bg-card border-b rounded-none p-0 h-12 space-x-8">
                        <TabsTrigger
                            value="course"
                            className="data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-primary rounded-none h-12 transition-all duration-200"
                        >
                            <Book className="h-4 w-4 mr-2" />
                            Course Content
                        </TabsTrigger>
                        <TabsTrigger
                            value="students"
                            className="data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-primary rounded-none h-12 transition-all duration-200"
                        >
                            <Users className="h-4 w-4 mr-2" />
                            Students
                        </TabsTrigger>
                        <TabsTrigger
                            value="announcements"
                            className="data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-primary rounded-none h-12 transition-all duration-200"
                        >
                            <Plus className="h-4 w-4 mr-2" />
                            Announcements
                        </TabsTrigger>
                        <TabsTrigger
                            value="analytics"
                            className="data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-primary rounded-none h-12 transition-all duration-200"
                        >
                            <Award className="h-4 w-4 mr-2" />
                            Analytics
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent value="course" className="mt-6">
                        <AnimatePresence mode="wait">
                            {isLoadingCourse ? (
                                <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                    className="flex justify-center items-center h-32"
                                >
                                    <Loader2 className="w-8 h-8 animate-spin text-primary" />
                                </motion.div>
                            ) : courseDetails ? (
                                <motion.div
                                    variants={containerVariants}
                                    initial="hidden"
                                    animate="visible"
                                    exit="exit"
                                >
                                    <ModulesAndContent
                                        courseId={courseDetails.id}
                                        modules={courseDetails.chapters}
                                        classroomId={classroom.id}
                                        onUpdate={() => { }}
                                        disabled={true}
                                    />
                                </motion.div>
                            ) : (
                                <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    className="flex items-center justify-center py-8 text-muted-foreground"
                                >
                                    <AlertCircle className="h-5 w-5 mr-2" />
                                    No course content available
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </TabsContent>

                    <TabsContent value="students" className="mt-4">
                        <motion.div variants={containerVariants} initial="hidden" animate="visible">
                            <Card className="border shadow-sm">
                                <ScrollArea className="h-[calc(100vh-300px)]">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Name</TableHead>
                                                <TableHead>Email</TableHead>
                                                <TableHead>Overall Progress</TableHead>
                                                <TableHead>Last Active</TableHead>
                                                <TableHead>Actions</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {classroom.students?.map((student: any) => (
                                                <TableRow key={student.id} className="hover:bg-accent/50 transition-colors">
                                                    <TableCell className="font-medium">
                                                        {student.user.firstName} {student.user.lastName}
                                                    </TableCell>
                                                    <TableCell>{student.user.email}</TableCell>
                                                    <TableCell>
                                                        <div className="flex items-center space-x-2">
                                                            <Progress value={mockStudentProgress.overallScore} className="w-[60px]" />
                                                            <span className="text-sm font-medium">
                                                                {mockStudentProgress.overallScore}%
                                                            </span>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell className="text-sm text-muted-foreground">
                                                        {new Date(mockStudentProgress.lastActive).toLocaleDateString()}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => {
                                                                setSelectedStudent(student);
                                                                setShowStudentDialog(true);
                                                            }}
                                                            className="hover:bg-primary hover:text-primary-foreground transition-colors"
                                                        >
                                                            View analytics
                                                        </Button>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </ScrollArea>
                            </Card>
                        </motion.div>

                        <Dialog open={showStudentDialog} onOpenChange={setShowStudentDialog}>
                            <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
                                <DialogHeader>
                                    <DialogTitle>
                                        Student Progress - {selectedStudent?.user.firstName} {selectedStudent?.user.lastName}
                                    </DialogTitle>
                                </DialogHeader>
                                <ScrollArea className="h-[calc(90vh-120px)] mt-4">
                                    <div>
                                        <StudentAnalytics studentId={selectedStudent?.user?.id} classroomId={classroom.id} />
                                    </div>
                                </ScrollArea>
                            </DialogContent>
                        </Dialog>
                    </TabsContent>

                    <TabsContent value="announcements" className="mt-4">
                        <motion.div variants={containerVariants} initial="hidden" animate="visible" className="space-y-4">
                            <Card className="border shadow-sm">
                                <CardHeader>
                                    <CardTitle>Create Announcement</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <Input
                                            placeholder="Announcement Title"
                                            value={newAnnouncement.title}
                                            onChange={(e) => setNewAnnouncement(prev => ({ ...prev, title: e.target.value }))}
                                            className="transition-all duration-200 focus:ring-2 focus:ring-primary"
                                        />
                                        <Textarea
                                            placeholder="Announcement Content"
                                            value={newAnnouncement.content}
                                            onChange={(e) => setNewAnnouncement(prev => ({ ...prev, content: e.target.value }))}
                                            className="min-h-[100px] transition-all duration-200 focus:ring-2 focus:ring-primary"
                                        />
                                        <Button
                                            onClick={handleCreateAnnouncement}
                                            className="w-full sm:w-auto transition-all duration-200 hover:bg-primary/90"
                                            disabled={!newAnnouncement.title.trim() || !newAnnouncement.content.trim() || isCreatingAnnouncement}
                                        >
                                            <Plus className="w-4 h-4 mr-2" />
                                            Create Announcement
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>

                            <ScrollArea className="h-[calc(100vh-500px)]">
                                <AnimatePresence>
                                    {announcementsData?.resultObject?.map((announcement: Announcement) => (
                                        <motion.div
                                            key={announcement.id}
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, y: -20 }}
                                            className="mb-4"
                                        >
                                            <Card className="border shadow-sm hover:shadow-md transition-all duration-300">
                                                <CardHeader>
                                                    <div className="flex justify-between items-start">
                                                        <CardTitle>{announcement['notifyString'].split("\n\n")[0]}</CardTitle>
                                                        {/* <span className="text-sm text-muted-foreground">
                                                            {new Date(announcement.createdAt).toLocaleDateString()}
                                                        </span> */}
                                                    </div>
                                                </CardHeader>
                                                <CardContent>
                                                    <p className="text-foreground/80">{announcement['notifyString'].split("\n\n")[1]}</p>
                                                </CardContent>
                                            </Card>
                                        </motion.div>
                                    ))}
                                    {!isLoadingAnnouncements && (!announcementsData?.resultObject || announcementsData.resultObject.length === 0) && (
                                        <motion.div
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1 }}
                                            className="flex items-center justify-center py-8 text-muted-foreground"
                                        >
                                            <AlertCircle className="h-5 w-5 mr-2" />
                                            No announcements yet
                                        </motion.div>
                                    )}
                                </AnimatePresence>
                            </ScrollArea>
                        </motion.div>
                    </TabsContent>

                    <TabsContent value="analytics" className="mt-4">
                        <motion.div variants={containerVariants} initial="hidden" animate="visible">
                            {classroomId && <ClassroomAnalytics crId={classroomId} />}
                        </motion.div>
                    </TabsContent>
                </Tabs>
            </div>
        </motion.div>
    );
};

export default TeacherClassroomDetailsPage;
