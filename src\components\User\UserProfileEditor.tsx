import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, Edit, User, BookOpen, Lock, Eye, EyeOff, Loader2, UserPen, AwardIcon, School2, School2Icon, Library, LockKeyhole, GraduationCap } from 'lucide-react';
import { useGetUserByIdQuery, useUpdateUserMutation } from 'src/APIConnect';
import { toast } from "@/hooks/use-toast";
import { useAuth0 } from '@auth0/auth0-react';

// Updated form state to include new fields from the design
interface FormState {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    aboutMe: string;
    schoolName: string;
    class: string;
    password?: string;
    confirmPassword?: string;
    imageUrl?: string;
}

// Updated form errors
interface FormErrors {
    firstName?: string;
    lastName?: string;
    email?: string;
    phoneNumber?: string;
    aboutMe?: string;
    schoolName?: string;
    class?: string;
    password?: string;
    confirmPassword?: string;
}

const UserProfileEditor = () => {
    const navigate = useNavigate();
    const { user } = useAuth0();
    const userId = user?.sub?.replace('auth0|', '') || '';

    const { data: userData, isLoading: isLoadingUser, isError } = useGetUserByIdQuery(userId, { skip: !userId });
    const [updateUser, { isLoading: isUpdating }] = useUpdateUserMutation();

    const [isEditMode, setIsEditMode] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const [formData, setFormData] = useState<FormState>({
        firstName: '',
        lastName: '',
        email: '',
        phoneNumber: '',
        aboutMe: '',
        schoolName: '',
        class: '',
        password: '',
        confirmPassword: ''
    });

    const [errors, setErrors] = useState<FormErrors>({});
    const [imageFile, setImageFile] = useState<File | null>(null);

    useEffect(() => {
        if (userData) {
            setFormData(prev => ({
                ...prev,
                firstName: userData.firstName || '',
                lastName: userData.lastName || '',
                email: userData.email || '',
                phoneNumber: userData.phoneNumber || '',
                aboutMe: userData.aboutMe || '',
                schoolName: userData.schoolName || '',
                class: userData.class || '',
                imageUrl: userData.picture || '',
            }));
        }
    }, [userData]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
        if (errors[name as keyof FormErrors]) {
            setErrors(prev => ({ ...prev, [name]: undefined }));
        }
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setImageFile(file);
            setFormData(prev => ({ ...prev, imageUrl: URL.createObjectURL(file) }));
        }
    };

    const validateForm = (): boolean => {
        const newErrors: FormErrors = {};
        let isValid = true;

        if (!formData.firstName.trim()) {
            newErrors.firstName = "First name is required";
            isValid = false;
        }
        if (!formData.lastName.trim()) {
            newErrors.lastName = "Last name is required";
            isValid = false;
        }
        if (!formData.email.trim()) {
            newErrors.email = "Email is required";
            isValid = false;
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = "Please enter a valid email address";
            isValid = false;
        }
        if (formData.password && formData.password.trim()) {
            if (formData.password.length < 8) {
                newErrors.password = "Password must be at least 8 characters long";
                isValid = false;
            }
            if (formData.password !== formData.confirmPassword) {
                newErrors.confirmPassword = "Passwords do not match";
                isValid = false;
            }
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!validateForm()) {
            toast({
                title: "Please fix the errors",
                description: "Some fields are missing or invalid",
                variant: "destructive",
            });
            return;
        }

        try {
            // NOTE: Only sending fields that are supported by the current API.
            // You will need to add the new fields (phoneNumber, aboutMe, etc.) here
            // once your backend supports them.
            // TODO: Implement actual file upload logic here. 
            // This might involve uploading `imageFile` to a service and getting a URL back.
            const updateData = {
                id: userId,
                firstName: formData.firstName,
                lastName: formData.lastName,
                email: formData.email,
                name: `${formData.firstName} ${formData.lastName}`,
                ...(formData.password && { password: formData.password }),
                // Pass the new image URL if it has been changed
                ...(imageFile && { picture: formData.imageUrl }),
            };

            await updateUser(updateData).unwrap();

            toast({
                title: "Profile updated successfully",
                variant: "default",
            });
            setIsEditMode(false);
        } catch (error) {
            console.error('Error saving profile:', error);
            toast({
                title: "Failed to save profile",
                description: "An unexpected error occurred. Please try again later.",
                variant: "destructive",
            });
        }
    };

    if (isLoadingUser) {
        return (
            <div className="flex justify-center items-center h-screen bg-gray-50">
                <Loader2 className="w-8 h-8 animate-spin text-teal-600" />
            </div>
        );
    }
    
    if (isError) {
        return (
            <div className="flex flex-col justify-center items-center h-screen bg-gray-50">
                <p className="text-red-500 mb-4">Failed to load user profile.</p>
                <Button onClick={() => navigate(-1)}>Go Back</Button>
            </div>
        );
    }

    const isSubmitting = isUpdating;

    return (
        <div className="h-screen  bg-white p-4 sm:p-6 lg:p-8">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="flex items-center">
                    <Button style={{ backgroundColor: '#347468', borderRadius: '12px' }} onClick={() => navigate(-1)} className="mr-2 sm:mr-5 text-white flex items-center p-2 sm:px-4 sm:py-2">
                        <ArrowLeft className="h-5 w-5" />
                        <span className="hidden sm:inline ml-2">Back</span>
                    </Button>
                    <div className="flex items-center">
                        {isEditMode ? <UserPen className="h-5 w-5 sm:h-6 sm:w-6 mr-2" /> : <User className="h-5 w-5 sm:h-6 sm:w-6 mr-2" />}
                        <h1 className="text-lg  font-semibold text-[#2D2D2D]">
                            {isEditMode ? 'Edit Profile' : 'Profile'}
                        </h1>
                        
                    </div>
                </div>
            </div>
            <hr className="my-6" style={{ borderColor: '#E6E6E6' }} />
            <div className="max-w-6xl mx-auto ">
                <form onSubmit={handleSubmit} className="">
                    <Card className="overflow-hidden border-none shadow-none ">
                        <CardContent className="">
                            {/* Personal Details Section */}
                            <div className="flex items-center justify-between mb-6">
                                <div className="flex items-center">
                                    <User className="h-6 w-6 mr-3 text-teal-600" />
                                    <h2 className="text-lg font-semibold text-[#2D2D2D]">Personal Details</h2>
                                </div>
                                {!isEditMode && (
                                    <Button style={{  borderRadius: '12px' }} className='bg-white border-2 border-[#347468] text-[#347468] font-semibold hover:bg-[#347468] hover:text-white px-4 py-1 flex items-center' onClick={() => setIsEditMode(true)}>
                                         Edit
                                     </Button>
                                )}
                            </div>
                            <div className="flex flex-col md:flex-row items-start gap-8 mb-8">
                                <div className="flex-shrink-0 mx-auto md:mx-0 relative">
                                    <label htmlFor="profile-picture-upload" className={isEditMode ? 'cursor-pointer' : ''}>
                                        {formData.imageUrl ? (
                                            <img 
                                                src={formData.imageUrl} 
                                                alt="Profile" 
                                                className="w-24 h-24 md:w-32 md:h-32 rounded-full object-cover border-2 transition-opacity duration-300 hover:opacity-80"
                                                style={{ borderColor: '#347468' }}
                                            />
                                        ) : (
                                            <div 
                                                className="w-24 h-24 md:w-32 md:h-32 rounded-full border-3 bg-slate-600 flex items-center justify-center transition-opacity duration-300 hover:opacity-80 "
                                                style={{ borderColor: '#347468' }}
                                            >
                                                <span className="text-4xl md:text-5xl font-semibold text-white">
                                                    {((formData.firstName?.[0] || '') + (formData.lastName?.[0] || '')).toUpperCase()}
                                                </span>
                                            </div>
                                        )}
                                        {isEditMode && (
                                            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full opacity-0 hover:opacity-100 transition-opacity duration-300">
                                                <Edit className="w-8 h-8 text-white" />
                                            </div>
                                        )}
                                    </label>
                                    {isEditMode && <Input id="profile-picture-upload" type="file" accept="image/*" className="hidden" onChange={handleImageChange} />}
                                </div>
                                <div className="flex-grow grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-8 w-full">
                                    <div className="relative">
                                        <Input id="firstName" name="firstName" value={formData.firstName} onChange={handleInputChange} disabled={!isEditMode || isSubmitting} className={`w-full p-2 rounded border-2 text-black disabled:bg-white disabled:opacity-100 ${errors.firstName ? 'border-red-500' : 'border-[#347468]'}`} />
                                        <Label htmlFor="firstName" className="absolute left-3 -top-2.5 text-xs bg-white px-1 text-[#347468] font-semibold">First name</Label>
                                        {errors.firstName && <p className="text-xs text-red-500 mt-1">{errors.firstName}</p>}
                                    </div>
                                    <div className="relative">
                                        <Input id="lastName" name="lastName" value={formData.lastName} onChange={handleInputChange} disabled={!isEditMode || isSubmitting} className={`w-full p-2 rounded border-2 text-black disabled:bg-white disabled:opacity-100 ${errors.lastName ? 'border-red-500' : 'border-[#347468]'}`} />
                                        <Label htmlFor="lastName" className="absolute left-3 -top-2.5 text-xs bg-white px-1 text-[#347468] font-semibold">Last name</Label>
                                        {errors.lastName && <p className="text-xs text-red-500 mt-1">{errors.lastName}</p>}
                                    </div>
                                    <div className="relative">
                                        <Input id="email" name="email" type="email" value={formData.email} onChange={handleInputChange} disabled={!isEditMode || isSubmitting} className={`w-full p-2 rounded border-2 text-black disabled:bg-white disabled:opacity-100 ${errors.email ? 'border-red-500' : 'border-[#347468]'}`} />
                                        <Label htmlFor="email" className="absolute left-3 -top-2.5 text-xs bg-white px-1 text-[#347468] font-semibold">Email</Label>
                                        {errors.email && <p className="text-xs text-red-500 mt-1">{errors.email}</p>}
                                    </div>
                                    <div className="relative">
                                        <Input id="phoneNumber" name="phoneNumber" value={formData.phoneNumber} onChange={handleInputChange} disabled={!isEditMode || isSubmitting} className="w-full p-2 rounded border-2 border-[#347468] text-black disabled:bg-white disabled:opacity-100" />
                                        <Label htmlFor="phoneNumber" className="absolute left-3 -top-2.5 text-xs bg-white px-1 text-[#347468] font-semibold">Phone Number</Label>
                                    </div>
                                </div>
                            </div>
                            <div className="relative sm:col-span-2 mb-8">
                                <Textarea id="aboutMe" name="aboutMe" value={formData.aboutMe} onChange={handleInputChange} disabled={!isEditMode || isSubmitting} rows={3} className="w-full p-2 rounded border-2 border-[#347468] text-black disabled:bg-white disabled:opacity-100" />
                                <Label htmlFor="aboutMe" className="absolute left-3 -top-2.5 text-xs bg-white px-1 text-[#347468] font-semibold">About Me</Label>
                            </div>

                            {/* Academic Details Section */}
                            <div className="flex items-center mb-6">
                                <GraduationCap className="h-6 w-6 mr-3 text-teal-600" />
                                <h2 className="text-lg font-semibold text-[#2D2D2D]">Academic Details</h2>
                            </div>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-8 mb-8">
                                <div className="relative">
                                    <Input id="schoolName" name="schoolName" value={formData.schoolName} onChange={handleInputChange} disabled={!isEditMode || isSubmitting} className="w-full p-2 rounded border-2 border-[#347468] text-black disabled:bg-white disabled:opacity-100" />
                                    <Label htmlFor="schoolName" className="absolute left-3 -top-2.5 text-xs bg-white px-1 text-[#347468] font-semibold">School name</Label>
                                </div>
                                <div className="relative">
                                    <Input id="class" name="class" value={formData.class} onChange={handleInputChange} disabled={!isEditMode || isSubmitting} className="w-full p-2 rounded border-2 border-[#347468] text-black disabled:bg-white disabled:opacity-100" />
                                    <Label htmlFor="class" className="absolute left-3 -top-2.5 text-xs bg-white px-1 text-[#347468] font-semibold">Class</Label>
                                </div>
                            </div>

                            {/* Change Password Section */}
                            <>
                                <div className="flex items-center mb-6">
                                    <LockKeyhole className="h-6 w-6 mr-3 text-teal-600" />
                                    <h2 className="text-lg font-semibold text-[#2D2D2D]">Change Password</h2>
                                </div>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-8">
                                    <div className="relative">
                                        <Input id="password" name="password" type={showPassword ? "text" : "password"} value={formData.password} onChange={handleInputChange} disabled={!isEditMode || isSubmitting} className={`w-full p-2 rounded border-2 text-black disabled:bg-white disabled:opacity-100 pr-10 ${errors.password ? 'border-red-500' : 'border-[#347468]'}`} />
                                        <Label htmlFor="password" className="absolute left-3 -top-2.5 text-xs bg-white px-1 text-[#347468] font-semibold">Enter New Password</Label>
                                        <button type="button" onClick={() => setShowPassword(p => !p)} className="absolute right-2 top-2.5 text-muted-foreground">
                                            {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                        </button>
                                        {errors.password && <p className="text-xs text-red-500 mt-1">{errors.password}</p>}
                                    </div>
                                    <div className="relative">
                                        <Input id="confirmPassword" name="confirmPassword" type={showConfirmPassword ? "text" : "password"} value={formData.confirmPassword} onChange={handleInputChange} disabled={!isEditMode || isSubmitting} className={`w-full p-2 rounded border-2 text-black disabled:bg-white disabled:opacity-100 pr-10 ${errors.confirmPassword ? 'border-red-500' : 'border-[#347468]'}`} />
                                        <Label htmlFor="confirmPassword" className="absolute left-3 -top-2.5 text-xs bg-white px-1 text-[#347468] font-semibold">Confirm New Password</Label>
                                        <button type="button" onClick={() => setShowConfirmPassword(p => !p)} className="absolute right-2 top-2.5 text-muted-foreground">
                                            {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                        </button>
                                        {errors.confirmPassword && <p className="text-xs text-red-500 mt-1">{errors.confirmPassword}</p>}
                                    </div>
                                </div>
                            </>
                        </CardContent>
                    </Card>

                    {/* Save Button */}
                    {isEditMode && (
                        <div className="mt-2 pb-12 flex justify-center">
                            <Button type="submit" size="lg" style={{ backgroundColor: '#347468' }} className="hover:bg-teal-700 w-full sm:w-80 px-10 text-white" disabled={isSubmitting}>
                                {isSubmitting ? (
                                    <>
                                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                        Saving...
                                    </>
                                ) : (
                                    'Save Changes'
                                )}
                            </Button>
                        </div>
                    )}
                </form>
            </div>
        </div>
    );
};

export default UserProfileEditor;