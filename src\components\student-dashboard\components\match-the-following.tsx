// match-the-following.tsx
"use client"

import React, { useState, useRef, useEffect, useMemo } from "react"
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { CheckCircle2, XCircle } from "lucide-react"

type MatchItem = {
    id: string
    text: string
}

type Connection = {
    left: MatchItem
    right: MatchItem
    color: string
}

type MatchTheFollowingProps = {
    question: string
    description?: string
    leftItems?: MatchItem[]
    rightItems?: MatchItem[]
    onMatch: (connections: { left: string; right: string }[]) => void
    preview?: boolean // New prop to handle preview mode
}

const colorPalette = [
    "#60A5FA", // blue-400
    "#34D399", // green-400
    "#FBBF24", // yellow-400
    "#FB7185", // red-400
    "#A78BFA", // purple-400
    "#22D3EE", // cyan-400
    "#F472B6", // pink-400
    "#818CF8", // indigo-400
]

export default function InteractiveMatchTheFollowing({
    question = "Match the following",
    description,
    leftItems = [],
    rightItems = [],
    onMatch,
    preview = false, // Default to false for backward compatibility
}: MatchTheFollowingProps) {
    // Initialize jumbled arrays only once when component mounts or when inputs change
    const [jr, jl] = useMemo(() => {
        let jl = [];
        let jr = [];
        // if (preview) {
        //     // Use the original arrays in preview mode
        //     jl = leftItems;
        //     jr = rightItems;
        // } else {
        // Create a new array and sort it to avoid mutating props
        jl = [...leftItems].sort(() => Math.random() - 0.5);
        jr = [...rightItems].sort(() => Math.random() - 0.5);
        // }
        return [jl, jr];
    }, []);
    const [connections, setConnections] = useState<Connection[]>([])
    const [selectedLeft, setSelectedLeft] = useState<MatchItem | null>(null)
    const [leftJumbled, setLeftJumbled] = useState<MatchItem[]>([])
    const [rightJumbled, setRightJumbled] = useState<MatchItem[]>([])
    const [isSubmitted, setIsSubmitted] = useState(false)
    const canvasRef = useRef<HTMLCanvasElement>(null)



    const handleLeftClick = (item: MatchItem) => {
        if (preview) return; // Disable interaction in preview mode
        if (selectedLeft && selectedLeft.id === item.id) {
            setSelectedLeft(null)
        } else {
            setSelectedLeft(item)
        }
    }

    const handleRightClick = (item: MatchItem) => {
        if (preview) return; // Disable interaction in preview mode
        if (selectedLeft) {
            const existingConnectionIndex = connections.findIndex(
                (conn) => conn.left.id === selectedLeft.id || conn.right.id === item.id
            )
            if (existingConnectionIndex !== -1) {
                // Prevent multiple connections
                return
            }
            const newColor =
                colorPalette[connections.length % colorPalette.length]
            const updatedConnections = [
                ...connections,
                { left: selectedLeft, right: item, color: newColor },
            ]
            setConnections(updatedConnections)
            setSelectedLeft(null)
        } else {
            // Disconnect if already connected
            const existingConnectionIndex = connections.findIndex(
                (conn) => conn.right.id === item.id
            )
            if (existingConnectionIndex !== -1) {
                const newConnections = [...connections]
                newConnections.splice(existingConnectionIndex, 1)
                setConnections(newConnections)
            }
        }
    }

    // Use useRef to store the previous connections to prevent unnecessary calls
    const prevConnectionsRef = useRef<string>('')

    useEffect(() => {
        if (!preview) { // Only call onMatch if not in preview mode
            const simplifiedConnections = connections.map((conn) => ({
                left: conn.left.id,
                right: conn.right.id,
            }))

            // Create a string representation to compare with previous
            const connectionsString = JSON.stringify(simplifiedConnections)

            // Only call onMatch if connections actually changed
            if (connectionsString !== prevConnectionsRef.current) {
                prevConnectionsRef.current = connectionsString
                onMatch(simplifiedConnections)
            }
        }
    }, [connections, preview]) // Remove onMatch from dependencies

    const isCorrectMatch = (leftId: string, rightId: string) => {
        return (
            leftItems.findIndex((item) => item.id === leftId) ===
            rightItems.findIndex((item) => item.id === rightId)
        )
    }

    const getBackgroundColor = (id: string, isLeft: boolean) => {
        const connection = connections.find((conn) =>
            isLeft ? conn.left.id === id : conn.right.id === id
        )
        if (connection) {
            return connection.color
        }
        return selectedLeft && isLeft && selectedLeft.id === id
            ? "rgba(229, 231, 235, 0.7)" // Tailwind Gray-200
            : "#FFFFFF"
    }

    const getTextColor = (bgColor: string) => {
        // Calculate brightness to set text color
        const [r, g, b] = bgColor
            .match(/\w\w/g)!
            .map((c) => parseInt(c, 16) / 255)
        const brightness = (r * 299 + g * 587 + b * 114) / 1000
        return brightness > 0.6 ? "#1F2937" : "#FFFFFF" // Tailwind Gray-800 or White
    }

    useEffect(() => {
        const drawConnections = () => {
            const canvas = canvasRef.current
            if (!canvas) return

            const ctx = canvas.getContext("2d")
            if (!ctx) return

            // Resize canvas to match container
            const container = canvas.parentElement
            if (container) {
                canvas.width = container.clientWidth
                canvas.height = container.clientHeight
            }

            ctx.clearRect(0, 0, canvas.width, canvas.height)

            connections.forEach((conn) => {
                const leftElement = document.getElementById(`left-${conn.left.id}`)
                const rightElement = document.getElementById(`right-${conn.right.id}`)

                if (leftElement && rightElement) {
                    const leftRect = leftElement.getBoundingClientRect()
                    const rightRect = rightElement.getBoundingClientRect()
                    const canvasRect = canvas.getBoundingClientRect()

                    const startX = leftRect.right - canvasRect.left
                    const startY = leftRect.top + leftRect.height / 2 - canvasRect.top
                    const endX = rightRect.left - canvasRect.left
                    const endY = rightRect.top + rightRect.height / 2 - canvasRect.top

                    ctx.beginPath()
                    ctx.moveTo(startX, startY)
                    ctx.lineTo(endX, endY)
                    ctx.strokeStyle = isSubmitted
                        ? isCorrectMatch(conn.left.id, conn.right.id)
                            ? "#10B981" // Green-500
                            : "#EF4444" // Red-500
                        : conn.color
                    ctx.lineWidth = 3
                    ctx.stroke()
                }
            })
        }

        drawConnections()
        window.addEventListener("resize", drawConnections)
        return () => window.removeEventListener("resize", drawConnections)
    }, [connections, isSubmitted])

    const handleSubmit = () => {
        if (preview) return; // Disable submission in preview mode
        setIsSubmitted(true)
    }

    const handleReset = () => {
        if (preview) return; // Disable reset in preview mode
        setConnections([])
        setIsSubmitted(false)
        setSelectedLeft(null)
    }

    return (
        <Card className="w-full max-w-5xl mx-auto">
            <CardHeader>
                <CardTitle>{question}</CardTitle>
                {description && <CardDescription>{description}</CardDescription>}
            </CardHeader>
            <CardContent>
                <div className="relative grid grid-cols-2 gap-8 md:gap-16">
                    <canvas
                        ref={canvasRef}
                        className="absolute inset-0 pointer-events-none"
                    />
                    <div className="space-y-2">
                        {jl.map((item) => (
                            <div key={item.id} id={`left-${item.id}`}>
                                <button
                                    onClick={() => handleLeftClick(item)}
                                    className={`w-full p-3 text-left rounded-md font-medium transition-colors ${selectedLeft?.id === item.id ? "ring-2 ring-gray-400" : ""
                                        } ${preview ? "cursor-default" : "cursor-pointer"}`}
                                    style={{
                                        backgroundColor: getBackgroundColor(item.id, true),
                                        color: getTextColor(getBackgroundColor(item.id, true)),
                                    }}
                                >
                                    {item.text}
                                </button>
                            </div>
                        ))}
                    </div>
                    <div className="space-y-2">
                        {jr.map((item) => (
                            <div key={item.id} id={`right-${item.id}`}>
                                <button
                                    onClick={() => handleRightClick(item)}
                                    className={`w-full p-3 text-left rounded-md font-medium transition-colors ${preview ? "cursor-default" : "cursor-pointer"
                                        }`}
                                    style={{
                                        backgroundColor: getBackgroundColor(item.id, false),
                                        color: getTextColor(getBackgroundColor(item.id, false)),
                                    }}
                                >
                                    {item.text}
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
                {!preview && ( // Only show buttons if not in preview mode
                    <div className="mt-6 space-x-2">
                        {/* <Button
                            onClick={handleSubmit}
                            disabled={
                                isSubmitted || connections.length !== leftItems.length
                            }
                        >
                            Submit
                        </Button> */}
                        <Button onClick={handleReset} variant="outline">
                            Reset
                        </Button>
                    </div>
                )}
                {isSubmitted && !preview && ( // Only show results if not in preview mode
                    <div className="mt-4 text-center">
                        {connections.every((conn) =>
                            isCorrectMatch(conn.left.id, conn.right.id)
                        ) ? (
                            <div className="text-green-500 flex items-center justify-center">
                                <CheckCircle2 className="mr-2" />
                                All matches are correct!
                            </div>
                        ) : (
                            <div className="text-red-500 flex items-center justify-center">
                                <XCircle className="mr-2" />
                                Some matches are incorrect. Try again!
                            </div>
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
    )
}
