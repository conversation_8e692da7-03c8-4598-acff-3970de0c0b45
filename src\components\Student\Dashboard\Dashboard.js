import React, { useEffect, useState } from "react";
import Chapters from "components/Student/Courses/Chapters";
import Courses from "components/Student/Courses/Courses";
import "./Dashboard.css";
import { useSelector } from "react-redux";

const Dashboard = () => {
  const courseDetail = useSelector((state) => state.courses.courseDetail);
  const [tandcModal, settandcModal] = useState(false);

  useEffect(() => {
    document.body.style.overflow = tandcModal ? "hidden" : "scroll";
  }, [tandcModal]);

  useEffect(() => {
    const tc = localStorage.getItem("TC");
    const tcData = JSON.parse(tc);
    if (tcData !== null) {
      settandcModal(tcData);
    } else {
      settandcModal(true);
    }
  }, []);

  const handleAgree = () => {
    localStorage.setItem("TC", JSON.stringify(false));
    settandcModal(false);
  };

  return (
    <div className="dashboard-container">
      <Courses />
      {/* {Object.keys(courseDetail).length > 0 ? (
                <Chapters />
            ) : (
                <div className="dashboard-nochapter-block" />
            )} */}

      {tandcModal && (
        <div className="dashboard-modal-overlay">
          <div className="dashboard-modal-content">
            <p className="dashboard-tc-message">Disclaimer</p>
            <p className="dashboard-tc-message">
              This app and all its content belong to Viswin Global Solutions.
              You are not permitted to share, distribute, or provide access to
              the app or its content without prior authorization or written
              permission from Viswin Global Solutions. Unauthorized use,
              copying, or distribution of the content, including but not limited
              to text, images, and functionality, is strictly prohibited and may
              result in legal action. By using this app, you agree to comply
              with these terms and respect the intellectual property rights of
              Viswin Global Solutions.
            </p>
            <button
              className="dashboard-agree-btn"
              onClick={() => handleAgree()}
            >
              I Agree
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
