import React from 'react';
import { motion } from 'framer-motion';
import { Icon } from 'lucide-react';
import { elephantFace } from '@lucide/lab';

const RightSidebar = () => {
  const flashNews = {
    title: 'Flash News',
    content: 'VyUDI partners with University of Sydney Credits from 3 core courses now count towards their Master of Global Health.',
  };

  const tipsAndTricks = {
    title: 'Tips and Tricks',
    instructor: 'English Lecturer',
    suggestion: 'Score better by learning this course',
  };

  return (
    <aside className="w-64 bg-white p-4 h-full">
      <div className="border-2 border-[#347468] py-2 rounded-[10px] mb-4">
        <h2 className="text-lg font-semibold mb-2 flex items-center px-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-trophy-icon lucide-trophy mr-2"><path d="M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978" /><path d="M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978" /><path d="M18 9h1.5a1 1 0 0 0 0-5H18" /><path d="M4 22h16" /><path d="M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z" /><path d="M6 9H4.5a1 1 0 0 1 0-5H6" /></svg>
          {flashNews.title}
        </h2>
        <div className="border border-b border-[#347468]"></div>
        <p className="p-2 text-sm text-black">{flashNews.content}</p>
      </div>
      <h2 className="text-lg font-semibold mb-4">{tipsAndTricks.title}</h2>
      <div className="border-2 border-[#347468] p-2 rounded-[10px]">
        <p className="font-sm text-base text-black">{tipsAndTricks.instructor}</p>
        <p className="text-xs text-gray-500">{tipsAndTricks.suggestion}</p>
      </div>
      {/* AI Mentor Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="fixed bottom-6 right-6 z-50"
      >
        <button className="w-auto bg-gradient-to-r from-[#386C8D] via-[#036664] to-[#060B0B] text-white px-4 py-2.5 rounded-xl shadow-lg flex items-center justify-center gap-3 hover:shadow-xl transition-all hover:scale-105">
          <Icon iconNode={elephantFace} size={28} />
          <span className="text-base font-semibold capitalize">AI Mentor</span>
        </button>
      </motion.div>
    </aside>
  );
};

export default RightSidebar;
