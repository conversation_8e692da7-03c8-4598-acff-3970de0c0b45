import { Routes, Route, useNavigate, useLocation } from "react-router-dom";
import React, { lazy, useEffect, Suspense } from "react";
import GuestRoute from "./routes/GuestRoute";

import LiveLecture from "./components/livekit/LiveLecture";
import {
  auth0Audience,
  auth0ClientID,
  auth0Domain,
} from "./constant/AppConstant";
import {
  Auth0Provider,
  useAuth0,
  withAuthenticationRequired,
} from "@auth0/auth0-react";
import ProtectedRoute from "./routes/ProtectedRoute";
import { useSelector } from "react-redux";
import {
  useGetSchoolClassroomsQuery,
  useLazyGetCourseDetailsDataQuery,
} from "./APIConnect";
// import { DiscussionRoutes } from './routes/DiscussionRoutes';

// Lazy load components
const LoginPage = lazy(() => import("components/Login/Login"));
const TestPage = lazy(() => import("components/Test/Test"));
const DashboardPage = lazy(() =>
  import("components/Student/Dashboard/Dashboard")
);
const AllCoursesPage = lazy(() =>
  import("components/Admin/AllCourses/AllCourses")
);
const ContentCreationPage = lazy(() =>
  import("components/Admin/Content/ContentCreation")
);
const ContentViewPage = lazy(() =>
  import("components/Student/ContentView/ContentView")
);
const CreationFormPage = lazy(() =>
  import("components/Admin/Forms/CreationForm")
);
const ContentMediaPage = lazy(() =>
  import("components/Student/ContentMedia/ContentMedia")
);
const EnhancedLearningPage = lazy(() =>
  import("components/student-dashboard/enhanced-learning-page")
);

const NewCourseDetailsPage = lazy(() =>
  import("components/student-dashboard/course-detail/course-detail-page")
);

const ProjectCenterPage = lazy(() =>
  import("components/student-dashboard/project-center/ProjectCenterPage")
);

const ProjectDetailsPage = lazy(() =>
  import("components/student-dashboard/project-center/ProjectDetailsPage")
);

const QuestionCreationPage = lazy(() =>
  import("components/Admin/QuestionCreation/QuestionCreation")
);
const QuestionsPage = lazy(() =>
  import("components/Admin/Questions/QuestionsPage")
);
const QuestionsEditor = lazy(() =>
  import("components/Admin/Questions/QuestionEditor")
);
const QuizViewPage = lazy(() => import("components/Student/Quiz/QuizView"));
const ReportCardPage = lazy(() =>
  import("components/Student/ReportCard/ReportCardPage")
);
const QAPage = lazy(() => import("components/Student/QA/QAPage"));
const InitalScreenPage = lazy(() =>
  import("components/Student/InitalScreen/InitalScreenPage")
);
const AdminCoursesPage = lazy(() =>
  import("components/Admin/Courses/CoursesPage")
);
const AdminCoursesEditor = lazy(() =>
  import("components/Admin/Courses/CourseEditor")
);
const AdminSchoolsPage = lazy(() =>
  import("components/Admin/Schools/SchoolsPage")
);
const AdminSchoolEditor = lazy(() =>
  import("components/Admin/Schools/SchoolEditor")
);
const AdminClassroomEditor = lazy(() =>
  import("components/Admin/Classrooms/ClassroomEditor")
);
const AdminClassroomsPage = lazy(() =>
  import("components/Admin/Classrooms/ClassroomsPage")
);
const AdminUsersPage = lazy(() => import("components/Admin/Users/<USER>"));
const AdminUsersEditor = lazy(() =>
  import("components/Admin/Users/<USER>")
);
const UserProfileEditor = lazy(() =>
  import("components/User/UserProfileEditor")
);
const AdminSubjectsPage = lazy(() =>
  import("components/Admin/Subjects/SubjectsPage")
);
const AdminSubjectEditor = lazy(() =>
  import("components/Admin/Subjects/SubjectEditor")
);
// Teacher Components
const TeacherClassroomPage = lazy(() =>
  import("components/Teacher/Classroom/TeacherClassroomPage")
);
const TeacherClassroomDetailsPage = lazy(() =>
  import("components/Teacher/Classroom/TeacherClassroomDetailsPage")
);
const TeacherQueriesPage = lazy(() =>
  import("components/Teacher/Queries/TeacherQueriesPage")
);

const TeacherMySpaceDialog = lazy(() =>
  import("components/teacher-dashboard/components/my-space-page")
);

const StudentAnalytics = lazy(() =>
  import("components/Analytics/StudentAnalytics")
);
const TeacherAnalytics = lazy(() =>
  import("components/Analytics/TeacherAnalytics")
);
const DiscussionsPage = lazy(() =>
  import("components/Student/Discussions/DiscussionsPage")
);
const DiscussionManagement = lazy(() =>
  import("components/Discussions/DiscussionManagement")
);

const DiscussionRoutes = lazy(() => import("./routes/DiscussionRoutes"));

const NewDashboard = lazy(() =>
  import("components/student-dashboard//NewDashboardPage")
);

const VyudiLearnPage = lazy(() =>
  import("components/student-dashboard/vyudilearn-course/VyudiLearnPage")
);

const RoutesComponent = () => {
  const { userIsRendered } = useSelector((state) => state.user);
  const { user } = useAuth0();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (user) {
      const roleId = user["http://learnido-app/roleId"];
      if (location.pathname === "/") {
        if (roleId === "1") {
          navigate("/admin/courses");
        } else if (roleId === "2") {
          navigate("/school-admin/courses");
        } else if (roleId === "3") {
          navigate("/teacher/classroom");
        } else if (roleId === "4") {
          navigate("/");
        }
      }
    }
  }, [user, location.pathname, navigate]);

  return (
    <Routes>
      <Route path="/login" element={<LoginPage />} />
      <Route path="/profile" element={<UserProfileEditor />} />
      {userIsRendered && user ? (
        user["http://learnido-app/roleId"] === "4" ? (
          <Route>
            <Route path="/" element={<NewDashboard />} />
            <Route
              path="/enhanced-learning"
              element={<EnhancedLearningPage />}
            />
            <Route path="/courses" element={<NewCourseDetailsPage />} />
            <Route path="/vyudi-learn" element={<VyudiLearnPage />} />
            <Route path="/project-center" element={<ProjectCenterPage />} />
            <Route
              path="/project-center/:projectId"
              element={<ProjectDetailsPage />}
            />
            <Route path="/discussions/*" element={
              <Suspense fallback={<div>Loading...</div>}>
                <DiscussionRoutes />
              </Suspense>
            } />
            <Route
              path="/reportCard"
              element={
                <StudentAnalytics
                  classroomId={null}
                  studentId={user.sub?.replace("auth0|", "")}
                  schoolId={user["http://learnido-app/schoolId"]}
                />
              }
            />
            <Route
              path="/qa"
              element={
                <QAPage
                  studentId={user.sub?.replace("auth0|", "")}
                  schoolId={user["http://learnido-app/schoolId"]}
                />
              }
            />
          </Route>
        ) : user["http://learnido-app/roleId"] === "1" ? (
          <Route>
            {/** App Admin routes */}
            <Route path="/admin/questions" element={<QuestionsPage />} />
            <Route path="/admin/questions/:id" element={<QuestionsEditor />} />
            <Route
              path="/admin/questions/create"
              element={<QuestionsEditor />}
            />
            <Route path="/admin/courses" element={<AdminCoursesPage />} />
            <Route path="/admin/courses/:id" element={<AdminCoursesEditor />} />
            <Route
              path="/admin/courses/create"
              element={<AdminCoursesEditor />}
            />
            <Route path="/admin/schools" element={<AdminSchoolsPage />} />
            <Route path="/admin/schools/:id" element={<AdminSchoolEditor />} />
            <Route
              path="/admin/classrooms/:id"
              element={<AdminClassroomEditor />}
            />
            <Route
              path="/admin/schools/create"
              element={<AdminSchoolEditor />}
            />
            <Route path="/admin/users" element={<AdminUsersPage />} />
            <Route path="/admin/users/:id" element={<AdminUsersEditor />} />
            <Route path="/admin/users/create" element={<AdminUsersEditor />} />

            <Route path="/admin/subjects" element={<AdminSubjectsPage />} />
            <Route
              path="/admin/subjects/create"
              element={<AdminSubjectEditor />}
            />
            <Route
              path="/admin/subjects/:id"
              element={<AdminSubjectEditor />}
            />
            <Route
              path="/school-admin/discussions/*"
              element={
                <Suspense fallback={<div>Loading...</div>}>
                  <DiscussionRoutes />
                </Suspense>
              }
            />
          </Route>
        ) : user["http://learnido-app/roleId"] === "2" ? (
          <Route>
            {/** School Admin routes */}
            <Route
              path="/school-admin/courses"
              element={<AdminCoursesPage />}
            />
            <Route
              path="/school-admin/courses/:id"
              element={<AdminCoursesEditor />}
            />
            <Route path="/school-admin/users" element={<AdminUsersPage />} />
            <Route
              path="/school-admin/users/:id"
              element={<AdminUsersEditor />}
            />
            <Route
              path="/school-admin/users/create"
              element={<AdminUsersEditor />}
            />
            <Route
              path="/school-admin/classrooms"
              element={<AdminClassroomsPage />}
            />
            <Route
              path="/school-admin/classrooms/:id"
              element={<AdminClassroomEditor />}
            />
            <Route
              path="/school-admin/classrooms/create"
              element={<AdminClassroomEditor />}
            />
            <Route
              path="/school-admin/discussions/*"
              element={
                <Suspense fallback={<div>Loading...</div>}>
                  <DiscussionRoutes />
                </Suspense>
              }
            />
          </Route>
        ) : user["http://learnido-app/roleId"] === "3" ? (
          <Route>
            {/** Teacher routes */}
            <Route path="/" element={<TeacherClassroomPage />} />
            <Route
              path="/teacher/classroom"
              element={<TeacherClassroomPage />}
            />
            <Route
              path="/teacher/classroom/:classroomId"
              element={<TeacherClassroomDetailsPage />}
            />
            <Route path="/teacher/queries" element={<TeacherQueriesPage />} />
            <Route path="/teacher/analytics" element={<TeacherAnalytics />} />
            <Route
              path="/teacher/my-space"
              element={
                <Suspense fallback={<div>Loading...</div>}>
                  <TeacherMySpaceDialog />
                </Suspense>
              }
            />
            <Route
              path="/teacher/discussions"
              element={
                <Suspense fallback={<div>Loading...</div>}>
                  <DiscussionRoutes />
                </Suspense>
              }
            />
          </Route>
        ) : (
          <Route path="/" element={<LoginPage />} />
        )
      ) : (
        <Route path="/" element={<LoginPage />} />
      )}
      {/* Discussion Forum Routes */}
      <Route
        path="/discussions/*"
        element={
          <Suspense fallback={<div>Loading...</div>}>
            <DiscussionRoutes />
          </Suspense>
        }
      />
      <Route path="/live-session/:classroomId" element={<LiveLecture />} />
    </Routes>
  );
};

export default RoutesComponent;
