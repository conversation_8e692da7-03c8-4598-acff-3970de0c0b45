import React, { useState, useEffect } from 'react';
import { 
  ControlBar,
  GridLayout,
  ParticipantTile,
  RoomAudioRenderer,
  useTracks,
  RoomContext,
} from '@livekit/components-react';
import { Room, Track } from 'livekit-client';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth0 } from "@auth0/auth0-react";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from 'lucide-react';
import { useGetClassRoomTokenQuery } from "@/APIConnect";
import '@livekit/components-styles';

const LiveLecture = () => {
  const { classroomId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth0();
  const [activePanel, setActivePanel] = useState('chat'); // 'chat', 'info', 'participants'
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [transcriptions, setTranscriptions] = useState<Array<{text: string, speaker: string, timestamp: string}>>([]);
  // Create a Room instance with optimization options
  const [room] = useState(() => new Room({
    // Optimize video quality for each participant's screen
    adaptiveStream: true,
    // Enable automatic audio/video quality optimization
    dynacast: true,
  }));

  const { data: tokenData, isLoading, isError } = useGetClassRoomTokenQuery({
    classroomId: classroomId || '',
    displayName: user?.name || user?.email || 'Anonymous'
  }, {
    skip: !classroomId || !user
  });

  // Connect to room
  useEffect(() => {
    let mounted = true;
    let connectionEstablished = false;
    
    const connect = async () => {
      if (mounted && tokenData) {
        try {
          console.log('Connecting to LiveKit room:', classroomId);
          await room.connect("wss://learnido-5lag7e24.livekit.cloud", tokenData);
          connectionEstablished = true;
          console.log('Successfully connected to room');
        } catch (error) {
          console.error('Failed to connect to LiveKit room:', error);
        }
      }
    };
    connect();

    return () => {
      mounted = false;
      if (connectionEstablished) {
        console.log('Disconnecting from LiveKit room');
        room.disconnect();
      }
    };
  }, [room, tokenData?.token, classroomId]);

  const handleBackClick = () => {
    if (window.confirm('Are you sure you want to leave the live session?')) {
      const role = user?.["http://learnido-app/roleId"];
      if (role === "3") { // Teacher
        navigate('/teacher/classroom');
      } else { // Student
        navigate('/');
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (isError || !tokenData) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <h2 className="text-2xl font-bold mb-4">Failed to join live session</h2>
        <p className="text-muted-foreground mb-6">Unable to connect to the session. Please try again later.</p>
        <Button onClick={handleBackClick}>Go Back</Button>
      </div>
    );
  }

  const toggleTranscription = () => {
    setIsTranscribing(!isTranscribing);
    // In a real app, this would trigger the LiveKit transcription service
    if (!isTranscribing) {
      // Mock adding a transcription after enabling
      const newTranscription = {
        text: "Welcome to today's session. We'll be discussing the key concepts from our last class.",
        speaker: "Teacher",
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };
      setTranscriptions([...transcriptions, newTranscription]);
    }
  };

  return (
    <div className="w-full h-screen flex flex-col bg-gray-100">
      {/* Top header bar */}
      <div className="bg-gradient-to-r from-primary to-primary/80 px-6 py-3 flex items-center justify-between shadow-md z-10">
        <div className="flex items-center text-white">
          <Button 
            variant="ghost" 
            className="mr-2 p-2 hover:bg-primary-foreground/10 text-white"
            onClick={handleBackClick}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-lg font-semibold">Live Session</h1>
            <h2 className="text-xs opacity-90">Classroom: {classroomId}</h2>
          </div>
        </div>
        <div>
          <span className="bg-red-600 px-3 py-1 text-white text-sm font-medium rounded-full animate-pulse">LIVE</span>
        </div>
      </div>
      
      {/* Main content area - Split 60/40 */}
      <div className="flex-1 flex flex-col lg:flex-row">
        {/* Left content - LiveKit integration */}
        <div className="w-full lg:w-3/5 p-6 flex flex-col">
          <div className="flex-1 bg-black rounded-lg overflow-hidden shadow-lg border border-gray-700 relative">
            {/* Using RoomContext.Provider instead of LiveKitRoom */}
            <RoomContext.Provider value={room}>
              <div data-lk-theme="default" className="h-full">
                {/* Custom VideoConference implementation */}
                <MyVideoConference />
                {/* The RoomAudioRenderer takes care of room-wide audio */}
                <RoomAudioRenderer />
                {/* Controls for the user to start/stop audio, video, and screen share tracks */}
                <ControlBar />
              </div>
            </RoomContext.Provider>
          </div>
          
          {/* Transcription section */}
          <div className="mt-6 flex flex-col border border-gray-200 rounded-lg shadow-md overflow-hidden">
            <div className="bg-primary text-white px-4 py-2 flex items-center justify-between">
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 11C19 14.866 15.866 18 12 18M12 18C8.13401 18 5 14.866 5 11M12 18V22M12 22H8M12 22H16M12 14C10.3431 14 9 12.6569 9 11V5C9 3.34315 10.3431 2 12 2C13.6569 2 15 3.34315 15 5V11C15 12.6569 13.6569 14 12 14Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="font-medium text-sm">AI Transcribe</span>
              </div>
              <Button 
                variant={isTranscribing ? "destructive" : "secondary"} 
                size="sm"
                onClick={toggleTranscription}
              >
                {isTranscribing ? "Stop Transcription" : "Start Transcription"}
              </Button>
            </div>
            
            {/* Transcription content */}
            <div className="overflow-y-auto p-4 h-32 bg-white">
              {transcriptions.length === 0 ? (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  {isTranscribing ? 
                    "Transcription started. Speech will appear here..." : 
                    "Click 'Start Transcription' to enable AI transcription"
                  }
                </div>
              ) : (
                transcriptions.map((item, index) => (
                  <div className="mb-4" key={index}>
                    <div className="flex items-center mb-1">
                      <div className="w-6 h-6 rounded-full bg-primary flex items-center justify-center text-white text-xs mr-2">
                        {item.speaker[0]}
                      </div>
                      <span className="font-medium mr-2 text-sm">{item.speaker}</span>
                      <span className="text-xs text-gray-500">{item.timestamp}</span>
                    </div>
                    <p className="text-sm text-gray-700 ml-8">"{item.text}"</p>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
        
        {/* Right side section - Chat/Participants panel */}
        <div className="w-full lg:w-2/5 p-6 bg-gray-50 flex flex-col">
          <div className="h-full bg-white border border-gray-200 rounded-lg shadow-md flex flex-col overflow-hidden">
            {/* Panel content */}
            <div className="flex-1 overflow-auto bg-white">
              {activePanel === 'chat' && (
                <ChatPanel />
              )}
              
              {activePanel === 'info' && (
                <InfoPanel classroomId={classroomId} />
              )}
              
              {activePanel === 'participants' && (
                <ParticipantsPanel />
              )}
            </div>
            
            {/* Bottom panel buttons */}
            <div className="h-16 border-t border-gray-200 flex items-center justify-around px-4 bg-gray-50">
              <button 
                className={`flex flex-col items-center justify-center px-6 py-2 rounded-lg transition-colors ${activePanel === 'chat' ? 'bg-primary/10 text-primary border border-primary/20' : 'text-gray-600 hover:bg-gray-100'}`}
                onClick={() => setActivePanel('chat')}
              >
                <svg className="w-5 h-5 mb-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 12H8.01M12 12H12.01M16 12H16.01M21 12C21 16.418 16.97 20 12 20C10.5 20 9.18 19.612 8 18.98L3 20L4.5 15.5C3.544 14.312 3 13.03 3 12C3 7.582 7.03 4 12 4C16.97 4 21 7.582 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs font-medium">Chat</span>
              </button>
              <button 
                className={`flex flex-col items-center justify-center px-6 py-2 rounded-lg transition-colors ${activePanel === 'info' ? 'bg-primary/10 text-primary border border-primary/20' : 'text-gray-600 hover:bg-gray-100'}`}
                onClick={() => setActivePanel('info')}
              >
                <svg className="w-5 h-5 mb-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <path d="M12 8V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                  <circle cx="12" cy="16" r="1" fill="currentColor"/>
                </svg>
                <span className="text-xs font-medium">Info</span>
              </button>
              <button 
                className={`flex flex-col items-center justify-center px-6 py-2 rounded-lg transition-colors ${activePanel === 'participants' ? 'bg-primary/10 text-primary border border-primary/20' : 'text-gray-600 hover:bg-gray-100'}`}
                onClick={() => setActivePanel('participants')}
              >
                <svg className="w-5 h-5 mb-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M17 21V19C17 16.7909 15.2091 15 13 15H5C2.79086 15 1 16.7909 1 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <circle cx="9" cy="7" r="4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M23 21V19C22.9986 17.1771 21.765 15.5857 20 15.13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M16 3.13C17.7699 3.58425 19.0078 5.17973 19.0078 7.005C19.0078 8.83026 17.7699 10.4257 16 10.88" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs font-medium">Participants</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Custom VideoConference implementation using GridLayout and ParticipantTile
function MyVideoConference() {
  // `useTracks` returns all camera and screen share tracks. If a user
  // joins without a published camera track, a placeholder track is returned.
  const tracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: true },
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { onlySubscribed: false },
  );
  
  return (
    <GridLayout tracks={tracks} style={{ height: 'calc(100% - var(--lk-control-bar-height))' }}>
        <ParticipantTile
        />
    </GridLayout>
  );
}

// Chat Panel Component
const ChatPanel = () => {
  const [messages, setMessages] = useState([
    { sender: "Sarah Johnson", time: "10:30", content: "Can someone explain the main concept again?" },
    { sender: "Michael Chen", time: "10:32", content: "The examples were really helpful!" },
    { sender: "Emily Rodriguez", time: "10:35", content: "Will this be covered in the upcoming exam?" },
  ]);
  const [newMessage, setNewMessage] = useState("");

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      setMessages([
        ...messages, 
        { 
          sender: "You", 
          time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }), 
          content: newMessage 
        }
      ]);
      setNewMessage("");
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-3 border-b border-gray-100 bg-gray-50">
        <h3 className="font-medium text-gray-700">Class Chat</h3>
      </div>
      <div className="flex-1 p-3 space-y-3 overflow-auto">
        {messages.map((message, index) => (
          <div key={index} className="bg-gray-50 p-3 rounded-lg">
            <div className="flex justify-between text-sm mb-1">
              <span className="font-medium text-primary">{message.sender}</span>
              <span className="text-gray-500">{message.time}</span>
            </div>
            <p className="text-sm">{message.content}</p>
          </div>
        ))}
      </div>
      <div className="p-3 border-t border-gray-200 flex">
        <input 
          type="text" 
          placeholder="Type a message..." 
          className="flex-1 border border-gray-300 rounded-l-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              handleSendMessage();
            }
          }}
        />
        <button 
          className="bg-primary text-white px-4 py-2 rounded-r-lg text-sm hover:bg-primary/80 transition-colors"
          onClick={handleSendMessage}
        >
          Send
        </button>
      </div>
    </div>
  );
};

// Info Panel Component
const InfoPanel = ({ classroomId }: { classroomId?: string }) => {
  return (
    <div className="h-full overflow-auto">
      <div className="p-3 border-b border-gray-100 bg-gray-50">
        <h3 className="font-medium text-gray-700">Session Information</h3>
      </div>
      <div className="p-4">
        <h4 className="font-medium text-lg mb-3 text-primary">Live Session</h4>
        <p className="text-sm mb-4 text-gray-600">
          This is a live classroom session for Classroom ID: {classroomId || "Unknown"}
        </p>
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <h5 className="font-medium mb-1 text-gray-700">Duration</h5>
          <p className="text-sm">Started 10 minutes ago</p>
        </div>
        <div className="p-3 bg-gray-50 rounded-lg">
          <h5 className="font-medium mb-1 text-gray-700">Session Controls</h5>
          <ul className="text-sm list-disc pl-5">
            <li>Use the video controls to toggle your camera</li>
            <li>Use the mic controls to toggle your audio</li>
            <li>Share your screen with the "Share" button</li>
            <li>View participants in the Participants tab</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

// Participants Panel Component
const ParticipantsPanel = () => {
  // This would normally use LiveKit's useParticipants() hook
  const [participants, setParticipants] = useState([
    { name: "John Doe (Teacher)", isTeacher: true, isActive: true },
    { name: "Sarah Johnson", isActive: true },
    { name: "Michael Chen", isActive: true },
    { name: "Emily Rodriguez", isActive: true },
    { name: "James Wilson", isActive: false },
    { name: "Sofia Garcia", isActive: true },
    { name: "Aiden Taylor", isActive: true },
    { name: "Olivia Brown", isActive: false },
    { name: "Ethan Martinez", isActive: true },
    { name: "Isabella Lopez", isActive: true },
  ]);

  return (
    <div className="h-full overflow-auto">
      <div className="p-3 border-b border-gray-100 bg-gray-50 flex justify-between items-center">
        <h3 className="font-medium text-gray-700">Participants</h3>
        <span className="text-xs px-2 py-1 bg-primary/10 text-primary rounded-full">
          {participants.filter(p => p.isActive).length} online
        </span>
      </div>
      
      {participants.map((participant, index) => (
        <div 
          key={index} 
          className="flex items-center justify-between p-3 border-b border-gray-100 hover:bg-gray-50"
        >
          <div className="flex items-center">
            <div className={`w-2 h-2 ${participant.isActive ? 'bg-green-500' : 'bg-gray-400'} rounded-full mr-2`}></div>
            <span className={participant.isTeacher ? 'font-medium text-primary' : ''}>
              {participant.name} {participant.isTeacher && '(Teacher)'}
            </span>
          </div>
          <div className="flex">
            <button className="text-gray-400 hover:text-gray-700 mr-2 p-1">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
              </svg>
            </button>
            <button className="text-gray-400 hover:text-gray-700 p-1">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default LiveLecture;