import React from 'react';
import { motion } from 'framer-motion';

const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.5 } },
};

interface PeerPerformanceSnapshotProps {
  className?: string;
}

const PeerPerformanceSnapshot: React.FC<PeerPerformanceSnapshotProps> = ({ className = '' }) => {
  const percentage = 85
  const indexType = 'Employaility index';
  const percentile = 25;

  const tickCount = 40;
  const activeTicks = Math.round((percentage / 100) * tickCount);

  return (
    <motion.div
      className={`bg-white rounded-2xl border border-[#97C48A] shadow-sm p-5 sm:p-6 ${className}`}
      variants={fadeIn}
      initial="initial"
      animate="animate"
    >
      <h3 className="text-lg font-bold text-[#2D2D2D] mb-12 ">Peer Performance Snapshot</h3>

      <div className="relative w-36 h-36 mb-4 mx-auto">
        {/* Outer circle border */}
        <div className="absolute inset-0 rounded-full border-4 border-[#347468] bg-[#E0F3F0]"></div>

        {/* Ticks in top half */}
        <svg viewBox="0 0 100 50" className="absolute top-0 left-0 w-full h-1/2">
          <defs>
            <linearGradient id="tickGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#86C3B8" />
              <stop offset="100%" stopColor="#347468" />
            </linearGradient>
          </defs>

          {Array.from({ length: tickCount }).map((_, i) => {
            const angle = (i / (tickCount - 1)) * Math.PI; // 0 to π
            const rOuter = 40; // Moved inwards slightly
            const rInner = 34; // Moved inwards slightly

            const x1 = 50 + rInner * Math.cos(angle);
            const y1 = 50 - rInner * Math.sin(angle);
            const x2 = 50 + rOuter * Math.cos(angle);
            const y2 = 50 - rOuter * Math.sin(angle);

            return (
              <line
                key={i}
                x1={x1}
                y1={y1}
                x2={x2}
                y2={y2}
                stroke={i < activeTicks ? 'url(#tickGradient)' : '#C7E3D8'}
                strokeWidth="1.5"
              />
            );
          })}
        </svg>

        {/* Inner percentage circle */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-[#E0F3F0] rounded-full w-24 h-24 flex items-center justify-center">
            <span className="text-2xl font-bold text-[#2D2D2D]">{percentage}%</span>
          </div>
        </div>
      </div>

      <p className="text-base font-medium text-[#2D2D2D] text-center pt-6">
        You're in top {percentile}% for {indexType}
      </p>
    </motion.div>
  );
};

export default PeerPerformanceSnapshot;
