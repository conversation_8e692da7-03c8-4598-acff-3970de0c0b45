import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Topic as <PERSON><PERSON><PERSON>op<PERSON>,
} from "../../Admin/Courses/types";

export interface APIChapter extends Omit<AdminChapter, "topics"> {
  anteriorId: string;
  topics: APITopic[];
}

export interface APITopic extends AdminTopic {
  anteriorId: string;
}

export interface APIResponse {
  resultObject: {
    id: string;
    name: string;
    description: string;
    chapters: APIChapter[];
  };
}

export interface StudentProgress {
  overallScore: number;
  chaptersCompleted: number;
  topicsCompleted: number;
  quizzesTaken: number;
  averageQuizScore: number;
  lastActive: string;
}

export interface Announcement {
  id: string;
  senderId: string;
  classroomId: string;
  notifyString: string;
  createdAt: string;
}
