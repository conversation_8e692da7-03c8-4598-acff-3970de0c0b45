import { format } from "date-fns";
import { StudentAnalytics } from "../../../types/analytics";

export interface StudentAnalyticsData {
  weeklyScores: Array<{ date: string; value: number }>;
  quizDurations: Array<{ label: string; value: number }>;
  topicRepetitions: Array<{ label: string; value: number }>;
  difficultyPassCounts: Array<{ label: string; value: number; color: string }>;
  questionTypePassCounts: Array<{
    label: string;
    value: number;
    color: string;
  }>;
  attemptCounts: {
    average: number;
    min: number;
    max: number;
  };
}

const difficultyLevelColors = {
  "1": "#22c55e",
  "2": "#86efac",
  "3": "#f59e0b",
  "4": "#fb923c",
  "5": "#ef4444",
};

const questionTypeColors = {
  "Multiple Choice": "#3b82f6",
  "Single Choice": "#8b5cf6",
  "Text Input": "#ec4899",
  Match: "#14b8a6",
  Binary: "#eab308",
  Soundbased: "#6366f1",
  "Picture Match": "#f43f5e",
};

// Define the keys with specific types
const levelKeys: Array<
  "level1Count" | "level2Count" | "level3Count" | "level4Count" | "level5Count"
> = ["level1Count", "level2Count", "level3Count", "level4Count", "level5Count"];

const typeKeys: Array<
  | "multipleChoiceCount"
  | "singleChoiceCount"
  | "textInputCount"
  | "matchCount"
  | "binaryCount"
  | "soundbasedCount"
  | "pictureMatchCount"
> = [
  "multipleChoiceCount",
  "singleChoiceCount",
  "textInputCount",
  "matchCount",
  "binaryCount",
  "soundbasedCount",
  "pictureMatchCount",
];

export const mapStudentAnalyticsData = (
  apiData: StudentAnalytics[],
  topicIdNameMap: { [topicId: string]: string } = {}
): StudentAnalyticsData => {
  const weeklyScoresMap: { [date: string]: number } = {};
  const quizDurationsMap: { [topicId: string]: number } = {};
  const topicRepetitionsMap: { [topicId: string]: number } = {};
  const difficultyPassCountsMap: { [difficulty: string]: number } = {};
  const questionTypePassCountsMap: { [questionType: string]: number } = {};
  const attemptCountsArray: number[] = [];

  apiData.forEach((item) => {
    const date = format(new Date(item.updatedon), "yyyy-MM-dd");
    weeklyScoresMap[date] = (weeklyScoresMap[date] || 0) + item.score;

    quizDurationsMap[item.topicId] =
      (quizDurationsMap[item.topicId] || 0) + item.timeTakenForQuiz;

    topicRepetitionsMap[item.topicId] =
      (topicRepetitionsMap[item.topicId] || 0) + item.quizRepetitionCount;

    levelKeys.forEach((levelKey, index) => {
      const count = item[levelKey]?.passCount || 0;
      const difficulty = (index + 1).toString();
      difficultyPassCountsMap[difficulty] =
        (difficultyPassCountsMap[difficulty] || 0) + count;
    });

    typeKeys.forEach((typeKey) => {
      const count = item[typeKey]?.passCount || 0;
      const typeName = typeKey
        .replace("Count", "")
        .replace(/([A-Z])/g, " $1")
        .trim();
      questionTypePassCountsMap[typeName] =
        (questionTypePassCountsMap[typeName] || 0) + count;
    });

    attemptCountsArray.push(item.quizRepetitionCount);
  });

  const weeklyScores = Object.keys(weeklyScoresMap).map((date) => ({
    date,
    value: weeklyScoresMap[date],
  }));

  const quizDurations = Object.keys(quizDurationsMap).map((topicId) => ({
    label: topicIdNameMap[topicId], // Map to topic name if available
    value: Math.round(quizDurationsMap[topicId] / 60), // Convert seconds to minutes
  }));

  const topicRepetitions = Object.keys(topicRepetitionsMap).map((topicId) => ({
    label: topicIdNameMap[topicId], // Map to topic name if available
    value: topicRepetitionsMap[topicId],
  }));

  const difficultyPassCounts = Object.keys(difficultyPassCountsMap).map(
    (difficulty: string) => ({
      label: difficulty,
      value: difficultyPassCountsMap[difficulty],
      color:
        difficultyLevelColors[
          difficulty.replace("level", "").replace("Count", "") as
            | "1"
            | "2"
            | "3"
            | "4"
            | "5"
        ] || "#000",
    })
  );

  const questionTypePassCounts = Object.keys(questionTypePassCountsMap).map(
    (typeName: string) => ({
      label: typeName,
      value: questionTypePassCountsMap[typeName],
      color:
        questionTypeColors[
          typeName as
            | "Multiple Choice"
            | "Single Choice"
            | "Text Input"
            | "Match"
            | "Binary"
            | "Soundbased"
            | "Picture Match"
        ] ?? "#000",
    })
  );

  const attemptCounts = {
    average:
      attemptCountsArray.reduce((a, b) => a + b, 0) / attemptCountsArray.length,
    min: Math.min(...attemptCountsArray),
    max: Math.max(...attemptCountsArray),
  };

  return {
    weeklyScores,
    quizDurations,
    topicRepetitions,
    difficultyPassCounts,
    questionTypePassCounts,
    attemptCounts,
  };
};
