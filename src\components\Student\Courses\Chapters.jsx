import React from "react";
import { useSelector } from "react-redux";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";

import videoCamera from "./assets/videoCamera.svg";
import lockImg from "./assets/lock.svg";

const Chapters = () => {
  const courseDetail = useSelector((state) => state.courses.courseDetail);

  return (
    <div className="space-y-4 max-h-[60vh] overflow-y-auto pr-2">
      {courseDetail?.chapters?.length ? (
        courseDetail.chapters.map((item, index) => {
          const isLocked = index > 0 && item.progress < 100;
          const isClickable = !isLocked;

          return (
            <Card
              key={index}
              className={`p-4 ${isLocked ? "bg-gray-100" : "bg-white"} ${
                isClickable
                  ? "cursor-pointer hover:shadow-md transition-shadow duration-300"
                  : "opacity-70"
              }`}
            >
              <div className="flex items-center space-x-4">
                <div
                  className="w-12 h-12 bg-center bg-no-repeat bg-contain rounded-full flex items-center justify-center"
                  style={{
                    backgroundColor: isLocked ? "#f3f4f6" : "#e0f2fe",
                  }}
                >
                  <img
                    src={isLocked ? lockImg : videoCamera}
                    alt={isLocked ? "Locked" : "Video"}
                    className="w-6 h-6"
                  />
                </div>
                <div className="flex-grow">
                  <h4 className="text-lg font-semibold text-[#2E645B]">
                    {item.name}
                  </h4>
                  <p className="text-sm text-[#644B49] mt-1">
                    {item.description}
                  </p>
                  {!isLocked && (
                    <Progress value={item.progress} className="mt-2 h-2" />
                  )}
                </div>
                {isClickable && (
                  <Button>
                    <span className="relative z-10">Start</span>
                    {/* <span className="absolute inset-0 bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span> */}
                  </Button>
                )}
              </div>
            </Card>
          );
        })
      ) : (
        <Card className="p-4 bg-white">
          <h3 className="text-lg font-semibold text-[#644B49]">
            No chapters available for this course.
          </h3>
        </Card>
      )}
    </div>
  );
};

export default Chapters;
