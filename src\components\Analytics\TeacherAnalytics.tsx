import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from 'components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from 'components/ui/select';
import { LineChart } from './charts/LineChart';
import { Bar<PERSON><PERSON> } from './charts/BarChart';
import { StreamChart } from './charts/StreamChart';
import { generateTeacherAnalyticsData, generateTeacherAnalyticsDataFromApi, mockClassrooms } from './data/teacherData';
import { Loader2, Users, BookOpen, GraduationCap, BarChart2, Target, TrendingUp, Clock } from 'lucide-react';
import { StatCard } from './components/StatCard';
import { useGetClassroomAnalyticsQuery } from '../../APIConnect';
import { motion } from 'framer-motion';

const TeacherAnalytics = ({ preselectedClassroom }: { preselectedClassroom: string }) => {
    const [loading, setLoading] = React.useState(true);
    const [data, setData] = React.useState<any>(null);
    const [selectedClassroom, setSelectedClassroom] = React.useState<string>(preselectedClassroom);
    const { data: classroomData, isLoading } = useGetClassroomAnalyticsQuery({
        classroomId: selectedClassroom,
    }, {
        skip: !selectedClassroom
    });

    React.useEffect(() => {
        if (classroomData) {
            if (classroomData.resultObject) {
                setData(generateTeacherAnalyticsDataFromApi(classroomData));
                setLoading(false);
            } else {
                setLoading(false);
                setData(null);
            }
        }
    }, [classroomData]);

    if (!selectedClassroom) {
        return (
            <div className="container mx-auto p-4 md:p-6 max-w-7xl">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8"
                >
                    <div>
                        <h1 className="text-3xl font-bold text-primary">Teacher Analytics</h1>
                        <p className="text-muted-foreground mt-1">Monitor classroom performance and engagement</p>
                    </div>
                </motion.div>

                <Card className="mt-8">
                    <CardContent className="pt-6">
                        <div className="max-w-md mx-auto text-center">
                            <motion.div
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ delay: 0.2 }}
                            >
                                <GraduationCap className="w-12 h-12 mx-auto mb-4 text-primary" />
                            </motion.div>
                            <h2 className="text-xl font-semibold mb-4 text-primary">Select a Classroom</h2>
                            <p className="text-muted-foreground mb-6">Choose a classroom to view detailed analytics and performance metrics</p>
                            <Select
                                value={selectedClassroom}
                                onValueChange={setSelectedClassroom}
                            >
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Choose a classroom" />
                                </SelectTrigger>
                                <SelectContent>
                                    {mockClassrooms.map((classroom) => (
                                        <SelectItem key={classroom.id} value={classroom.id}>
                                            {classroom.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="flex justify-center items-center min-h-[calc(100vh-4rem)]">
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center"
                >
                    <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
                    <p className="text-muted-foreground">Loading classroom analytics...</p>
                </motion.div>
            </div>
        );
    }

    console.log(data);

    if (!data) {
        return (
            <div className="flex justify-center items-center min-h-[calc(100vh-4rem)]">
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center"
                >
                    <p className="text-destructive">Error loading analytics data</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:opacity-90 transition-opacity"
                    >
                        Retry
                    </button>
                </motion.div>
            </div>
        );
    }

    if (data.noData) {
        return (
            <div className="container mx-auto p-4 md:p-6 max-w-7xl">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8"
                >
                    <div>
                        <h1 className="text-3xl font-bold text-primary">Teacher Analytics</h1>
                        <p className="text-muted-foreground mt-1">Monitor classroom performance and engagement</p>
                    </div>
                </motion.div>

                <Card className="mt-8">
                    <CardContent className="pt-6">
                        <div className="max-w-md mx-auto text-center">
                            <motion.div
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ delay: 0.2 }}
                            >
                                <GraduationCap className="w-12 h-12 mx-auto mb-4 text-primary" />
                            </motion.div>
                            <h2 className="text-xl font-semibold mb-4 text-primary">No Data Available</h2>
                            <p className="text-muted-foreground mb-6">No analytics data available for this classroom</p>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    const selectedClassroomName = mockClassrooms.find(c => c.id === selectedClassroom)?.name;
    const averageParticipation = Math.round(data.participation.reduce((acc: number, curr: any) => acc + curr.value, 0) / data.participation.length);
    const totalTopics = data.classPerformance.length;
    const averageScore = Math.round(data.chapterScores.reduce((acc: number, curr: any) => acc + curr.value, 0) / data.chapterScores.length);

    return (
        <div className="container mx-auto p-4 md:p-6 space-y-8 max-w-7xl">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex flex-col gap-6"
            >
                <div>
                    <h1 className="text-3xl font-bold text-primary">Teacher Analytics</h1>
                    <p className="text-muted-foreground mt-1">
                        Viewing analytics for {selectedClassroomName}
                    </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
                        <StatCard
                            title="Average Participation"
                            value={averageParticipation}
                            icon={Users}
                        />
                    </motion.div>
                    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
                        <StatCard
                            title="Total Topics"
                            value={totalTopics}
                            icon={BookOpen}
                        />
                    </motion.div>
                    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>
                        <StatCard
                            title="Average Score"
                            value={averageScore}
                            icon={Target}
                        />
                    </motion.div>
                    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>
                        <StatCard
                            title="Active Students"
                            value={data.participation[data.participation.length - 1].value}
                            icon={TrendingUp}
                        />
                    </motion.div>
                </div>
            </motion.div>

            <div className="space-y-8">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                >
                    <Card className="overflow-hidden">
                        <CardHeader className="border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60">
                            <div className="flex items-center gap-2">
                                <Users className="w-5 h-5 text-primary" />
                                <CardTitle className="text-primary">Student Participation Trend</CardTitle>
                            </div>
                            <CardDescription>Track student engagement over time</CardDescription>
                        </CardHeader>
                        <CardContent className="pt-6 overflow-hidden">
                            <div className="h-[300px] sm:h-[400px] w-full">
                                <LineChart
                                    data={data.participation}
                                    xLabel="Date"
                                    yLabel="Student Count"
                                    title=""
                                />
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.6 }}
                    >
                        <Card className="h-full overflow-hidden">
                            <CardHeader className="border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60">
                                <div className="flex items-center gap-2">
                                    <BarChart2 className="w-5 h-5 text-primary" />
                                    <CardTitle className="text-primary">Topic Performance</CardTitle>
                                </div>
                                <CardDescription>Performance across different topics</CardDescription>
                            </CardHeader>
                            <CardContent className="pt-6 overflow-hidden">
                                <div className="h-[300px] sm:h-[400px]">
                                    <BarChart
                                        data={data.classPerformance}
                                        xLabel="Topic"
                                        yLabel="Total Score"
                                        title=""
                                        horizontal={true}
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    </motion.div>

                    <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.7 }}
                    >
                        <Card className="h-full overflow-hidden">
                            <CardHeader className="border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60">
                                <div className="flex items-center gap-2">
                                    <BookOpen className="w-5 h-5 text-primary" />
                                    <CardTitle className="text-primary">Chapter Performance</CardTitle>
                                </div>
                                <CardDescription>Average scores by chapter</CardDescription>
                            </CardHeader>
                            <CardContent className="pt-6 overflow-hidden">
                                <div className="h-[300px] sm:h-[400px]">
                                    <BarChart
                                        data={data.chapterScores}
                                        xLabel="Chapter"
                                        yLabel="Average Score"
                                        title=""
                                        horizontal={true}
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    </motion.div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.8 }}
                    >
                        <Card className="h-full overflow-hidden">
                            <CardHeader className="border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60">
                                <div className="flex items-center gap-2">
                                    <Target className="w-5 h-5 text-primary" />
                                    <CardTitle className="text-primary">Question Type Distribution</CardTitle>
                                </div>
                                <CardDescription>Distribution of question types over time</CardDescription>
                            </CardHeader>
                            <CardContent className="pt-6 overflow-hidden">
                                <div className="h-[300px] sm:h-[400px]">
                                    <StreamChart
                                        data={data.questionTypeAttempts}
                                        keys={['Multiple Choice', 'Fill in the Blank', 'True/False', 'Match the Following']}
                                        title=""
                                        colors={['hsl(var(--chart-1))', 'hsl(var(--chart-2))', 'hsl(var(--chart-3))', 'hsl(var(--chart-4))']}
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    </motion.div>

                    <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.9 }}
                    >
                        <Card className="h-full overflow-hidden">
                            <CardHeader className="border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60">
                                <div className="flex items-center gap-2">
                                    <Clock className="w-5 h-5 text-primary" />
                                    <CardTitle className="text-primary">Difficulty Level Distribution</CardTitle>
                                </div>
                                <CardDescription>Distribution of difficulty levels over time</CardDescription>
                            </CardHeader>
                            <CardContent className="pt-6 overflow-hidden">
                                <div className="h-[300px] sm:h-[400px]">
                                    <StreamChart
                                        data={data.difficultyAttempts}
                                        keys={['1', '2', '3', '4', '5']}
                                        title=""
                                        colors={['hsl(var(--chart-1))', 'hsl(var(--chart-2))', 'hsl(var(--chart-3))', 'hsl(var(--chart-4))', 'hsl(var(--chart-5))']}
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    </motion.div>
                </div>
            </div>
        </div>
    );
};

export default TeacherAnalytics;
