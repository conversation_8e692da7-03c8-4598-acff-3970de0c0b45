import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { PlusCircle, Folder, File, X, Edit, Save, Trash2 } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';

const Notes = ({ currentTopic }) => {
  const [folders, setFolders] = useState([]);
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [notes, setNotes] = useState({});
  const [selectedNote, setSelectedNote] = useState(null);
  const [noteContent, setNoteContent] = useState('');
  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
  const [isCreateNoteOpen, setIsCreateNoteOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [newNoteName, setNewNoteName] = useState('');
  const [editMode, setEditMode] = useState(false);
  
  // Load data from localStorage on component mount
  useEffect(() => {
    const storedFolders = localStorage.getItem('studentNoteFolders');
    const storedNotes = localStorage.getItem('studentNotes');
    
    if (storedFolders) {
      setFolders(JSON.parse(storedFolders));
    }
    
    if (storedNotes) {
      setNotes(JSON.parse(storedNotes));
    }
  }, []);
  
  // Save data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('studentNoteFolders', JSON.stringify(folders));
  }, [folders]);
  
  useEffect(() => {
    localStorage.setItem('studentNotes', JSON.stringify(notes));
  }, [notes]);
  
  // Create new folder
  const handleCreateFolder = () => {
    if (newFolderName.trim() === '') return;
    
    const newFolder = {
      id: Date.now(),
      name: newFolderName
    };
    
    setFolders([...folders, newFolder]);
    setNotes({
      ...notes,
      [newFolder.id]: []
    });
    
    setNewFolderName('');
    setIsCreateFolderOpen(false);
  };
  
  // Create new note
  const handleCreateNote = () => {
    if (newNoteName.trim() === '' || !selectedFolder) return;
    
    const newNote = {
      id: Date.now(),
      name: newNoteName,
      content: '',
      topicId: currentTopic?.id || null,
      topicName: currentTopic?.name || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    setNotes({
      ...notes,
      [selectedFolder]: [...(notes[selectedFolder] || []), newNote]
    });
    
    setNewNoteName('');
    setIsCreateNoteOpen(false);
  };
  
  // Delete folder
  const handleDeleteFolder = (folderId) => {
    const updatedFolders = folders.filter(folder => folder.id !== folderId);
    
    const updatedNotes = { ...notes };
    delete updatedNotes[folderId];
    
    setFolders(updatedFolders);
    setNotes(updatedNotes);
    
    if (selectedFolder === folderId) {
      setSelectedFolder(null);
      setSelectedNote(null);
      setNoteContent('');
    }
  };
  
  // Delete note
  const handleDeleteNote = (noteId) => {
    if (!selectedFolder) return;
    
    const updatedNotes = {
      ...notes,
      [selectedFolder]: notes[selectedFolder].filter(note => note.id !== noteId)
    };
    
    setNotes(updatedNotes);
    
    if (selectedNote && selectedNote.id === noteId) {
      setSelectedNote(null);
      setNoteContent('');
    }
  };
  
  // Select note to view/edit
  const handleSelectNote = (note) => {
    setSelectedNote(note);
    setNoteContent(note.content);
    setEditMode(false);
  };
  
  // Save note content
  const handleSaveNote = () => {
    if (!selectedFolder || !selectedNote) return;
    
    const updatedNotes = {
      ...notes,
      [selectedFolder]: notes[selectedFolder].map(note => 
        note.id === selectedNote.id 
          ? { ...note, content: noteContent, updatedAt: new Date().toISOString() }
          : note
      )
    };
    
    setNotes(updatedNotes);
    setEditMode(false);
  };

  return (
    <div className="flex flex-col h-full bg-white rounded-lg shadow-md">
      <div className="p-4 border-b">
        <h2 className="text-2xl font-semibold text-gray-800">My Notes</h2>
        <p className="text-sm text-gray-500">
          {currentTopic 
            ? `Currently viewing: ${currentTopic.name}` 
            : 'Create and organize your learning notes'}
        </p>
      </div>
      
      <div className="flex flex-1 overflow-hidden">
        {/* Folders panel */}
        <div className="w-64 border-r p-4 overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-medium">Folders</h3>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => setIsCreateFolderOpen(true)}
            >
              <PlusCircle className="h-5 w-5" />
            </Button>
          </div>
          
          <div className="space-y-2">
            {folders.map(folder => (
              <motion.div
                key={folder.id}
                className={`flex justify-between items-center p-2 rounded-md cursor-pointer ${selectedFolder === folder.id ? 'bg-blue-100' : 'hover:bg-gray-100'}`}
                onClick={() => setSelectedFolder(folder.id)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center">
                  <Folder className="h-4 w-4 mr-2 text-blue-500" />
                  <span className="text-sm truncate">{folder.name}</span>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteFolder(folder.id);
                  }}
                >
                  <Trash2 className="h-4 w-4 text-gray-500" />
                </Button>
              </motion.div>
            ))}
          </div>
        </div>
        
        {/* Notes panel */}
        <div className="w-64 border-r p-4 overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-medium">Notes</h3>
            {selectedFolder && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsCreateNoteOpen(true)}
              >
                <PlusCircle className="h-5 w-5" />
              </Button>
            )}
          </div>
          
          {selectedFolder ? (
            <div className="space-y-2">
              {(notes[selectedFolder] || []).map(note => (
                <motion.div
                  key={note.id}
                  className={`flex justify-between items-center p-2 rounded-md cursor-pointer ${selectedNote && selectedNote.id === note.id ? 'bg-blue-100' : 'hover:bg-gray-100'}`}
                  onClick={() => handleSelectNote(note)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center">
                    <File className="h-4 w-4 mr-2 text-gray-500" />
                    <span className="text-sm truncate">{note.name}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteNote(note.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4 text-gray-500" />
                  </Button>
                </motion.div>
              ))}
              
              {(notes[selectedFolder] || []).length === 0 && (
                <div className="text-center text-sm text-gray-500 p-4">
                  No notes yet. Create one!
                </div>
              )}
            </div>
          ) : (
            <div className="text-center text-sm text-gray-500 p-4">
              Select a folder to view notes
            </div>
          )}
        </div>
        
        {/* Note content panel */}
        <div className="flex-1 p-4 overflow-y-auto flex flex-col">
          {selectedNote ? (
            <>
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h3 className="font-semibold">{selectedNote.name}</h3>
                  {selectedNote.topicName && (
                    <span className="text-xs text-gray-500">
                      From: {selectedNote.topicName}
                    </span>
                  )}
                </div>
                <div className="flex gap-2">
                  {editMode ? (
                    <Button
                      onClick={handleSaveNote}
                      size="sm"
                      className="flex items-center"
                    >
                      <Save className="h-4 w-4 mr-1" />
                      Save
                    </Button>
                  ) : (
                    <Button
                      onClick={() => setEditMode(true)}
                      variant="outline"
                      size="sm"
                      className="flex items-center"
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  )}
                </div>
              </div>
              
              {editMode ? (
                <Textarea
                  value={noteContent}
                  onChange={(e) => setNoteContent(e.target.value)}
                  className="flex-1 resize-none"
                  placeholder="Write your notes here..."
                />
              ) : (
                <div className="flex-1 border rounded-md p-4 overflow-y-auto whitespace-pre-wrap">
                  {noteContent || <span className="text-gray-400">No content yet</span>}
                </div>
              )}
              
              <div className="text-xs text-gray-500 mt-4">
                Last updated: {new Date(selectedNote.updatedAt).toLocaleString()}
              </div>
            </>
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500">
              Select a note to view its content
            </div>
          )}
        </div>
      </div>
      
      {/* Create folder dialog */}
      <Dialog open={isCreateFolderOpen} onOpenChange={setIsCreateFolderOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
          </DialogHeader>
          <Input
            placeholder="Folder name"
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
          />
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateFolderOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateFolder}>Create</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Create note dialog */}
      <Dialog open={isCreateNoteOpen} onOpenChange={setIsCreateNoteOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Note</DialogTitle>
          </DialogHeader>
          <Input
            placeholder="Note title"
            value={newNoteName}
            onChange={(e) => setNewNoteName(e.target.value)}
          />
          {currentTopic && (
            <div className="text-sm text-gray-500">
              This note will be associated with: {currentTopic.name}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateNoteOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateNote}>Create</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Notes;