@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');

.content-creation * {
  margin: 0px;
  padding: 0;
  box-sizing: border-box;
}

.content-creation .body {
  font-family: 'D<PERSON> Sans', sans-serif;
  background-color: #f3f4f6;
  color: #333;
  line-height: 1.6;
}

.content-creation .container {
  max-width: 800px;
  margin: 40px auto;
  padding: 20px;
  background: rgba(255, 245, 239, 1);
  border: 1.5px solid #C2A878;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  font-family: 'DM Sans', sans-serif;
}

.content-creation .button-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.content-creation button {
  display: inline-block;
  background: rgba(19, 109, 98, 1);
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.3s ease-in-out, transform 0.2s ease-in-out;
  font-family: 'DM Sans', sans-serif;
}

.content-creation button:hover {
  background: rgba(19, 109, 98, 0.8);
  transform: translateY(-2px);
}

.content-creation .delete-button {
  background: #f56565; /* Light red color */
}

.content-creation .delete-button:hover {
  background: #e53e3e; /* Darker red on hover */
}

.content-creation span {
  font-weight: bold;
  color: #4a5568;
  display: block;
  margin-bottom: 8px;
  margin-top: 20px;
}

.content-creation input {
  width: 100%;
  padding: 12px 15px;
  border: 1.5px solid #C2A878;
  border-radius: 8px;
  font-size: 16px;
  margin-bottom: 20px;
  transition: border 0.3s ease-in-out;
  font-family: 'DM Sans', sans-serif;
}

.content-creation input:focus {
  border-color: rgba(19, 109, 98, 1);
  outline: none;
  box-shadow: 0 0 0 3px rgba(19, 109, 98, 0.3);
}

/* Add specific CSS for sun-editor to avoid conflicts */
.content-creation .sun-editor button {
  background: initial; /* Reset background to initial */
  color: initial; /* Reset color to initial */
  border: initial; /* Reset border to initial */
  box-shadow: initial; /* Reset box-shadow to initial */
}

.content-creation .sun-editor {
  margin-bottom: 20px;
  border: 1.5px solid #C2A878;
  border-radius: 8px;
  font-family: 'DM Sans', sans-serif;
}

.content-creation .no-content-message {
  text-align: center;
  color: #4a5568;
  font-weight: bold;
  margin-bottom: 20px;
}

.content-creation .align-right {
  justify-content: flex-end;
}

.content-creation .topic-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.content-creation .topic-header span {
  font-weight: bold;
  color: #4a5568;
}

.content-creation .topic-header .delete-button {
  margin-left: auto; 
}


@media (max-width: 768px) {
  .content-creation .container {
    padding: 15px;
    margin:20px;
  }

  .content-creation button {
    margin-bottom: 15px;
  }

  .content-creation input {
    font-size: 14px;
  }
}
