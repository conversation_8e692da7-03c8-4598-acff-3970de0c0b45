import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Plus, Loader2, Search, Pencil, Trash2, Building2 } from 'lucide-react';
import { useGetSchoolsQuery, useDeleteSchoolMutation } from '@/APIConnect';
import { School } from './types';
import { toast } from '@/hooks/use-toast';

const SchoolsPage = () => {
    const navigate = useNavigate();
    const [searchQuery, setSearchQuery] = useState('');

    const { data: schoolsData, isLoading: isLoadingSchools, refetch } = useGetSchoolsQuery('');
    const [deleteSchool] = useDeleteSchoolMutation();

    const handleEdit = (school: School) => {
        navigate(`/admin/schools/${school.id}`);
    };

    const handleDelete = async (id: string) => {
        try {
            let a = ('Are you sure you want to delete this school?');
            if (a) {
                await deleteSchool({ Id: id }).unwrap();
                toast({
                    title: "School deleted successfully",
                    variant: "default",
                });
                refetch();
            }

        } catch (error) {
            console.error('Error deleting school:', error);
            toast({
                title: "Failed to delete school",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const filteredSchools = (schoolsData?.resultObject || []).filter((school: School) => {
        const matchesSearch = searchQuery
            ? school.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            school.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
            school.location.toLowerCase().includes(searchQuery.toLowerCase())
            : true;
        return matchesSearch;
    });

    return (
        <div className="container mx-auto p-6">
            <div className="flex flex-col space-y-8">
                <Card className="p-6">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
                        <div>
                            <h1 className="text-3xl font-semibold tracking-tight text-foreground">Schools</h1>
                            <p className="text-sm text-muted-foreground mt-1">Manage and organize educational institutions</p>
                        </div>
                        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full md:w-auto">
                            <div className="relative flex-1 sm:flex-none">
                                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search schools..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="pl-9 w-full sm:w-[260px] bg-background"
                                />
                            </div>
                            <Button
                                onClick={() => navigate('/admin/schools/create')}
                                className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-primary-foreground transition-colors"
                            >
                                <Plus className="w-4 h-4 mr-2" />
                                Create School
                            </Button>
                        </div>
                    </div>
                </Card>

                <Card>
                    <ScrollArea className="h-[calc(100vh-280px)] rounded-md">
                        {isLoadingSchools ? (
                            <div className="flex justify-center items-center h-32">
                                <div className="flex flex-col items-center space-y-3">
                                    <Loader2 className="w-8 h-8 animate-spin text-muted-foreground" />
                                    <p className="text-sm text-muted-foreground">Loading schools...</p>
                                </div>
                            </div>
                        ) : (
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-muted/50">
                                        <TableHead className="w-[300px]">School Details</TableHead>
                                        <TableHead>Description</TableHead>
                                        <TableHead className="w-[150px]">Location</TableHead>
                                        <TableHead className="w-[120px]">Created On</TableHead>
                                        <TableHead className="w-[100px] text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredSchools.map((school: School) => (
                                        <TableRow key={school.id} className="group hover:bg-muted/50 transition-colors">
                                            <TableCell>
                                                <div className="flex items-center space-x-3">
                                                    <div className="h-10 w-10 rounded-lg bg-muted flex items-center justify-center">
                                                        <Building2 className="h-5 w-5 text-muted-foreground" />
                                                    </div>
                                                    <div className="font-medium text-foreground">
                                                        {school.name}
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-muted-foreground">
                                                {school.description}
                                            </TableCell>
                                            <TableCell className="text-muted-foreground">
                                                {school.location}
                                            </TableCell>
                                            <TableCell className="text-muted-foreground">
                                                {new Date(school.createdOn).toLocaleDateString()}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex justify-end space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        onClick={() => handleEdit(school)}
                                                        className="hover:bg-muted hover:text-foreground"
                                                    >
                                                        <Pencil className="h-4 w-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        onClick={() => handleDelete(school.id)}
                                                        className="hover:bg-muted hover:text-destructive"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                    {filteredSchools.length === 0 && (
                                        <TableRow>
                                            <TableCell colSpan={5}>
                                                <div className="flex flex-col items-center justify-center py-8 space-y-2">
                                                    <Building2 className="h-8 w-8 text-muted-foreground" />
                                                    <p className="text-muted-foreground text-sm">No schools found</p>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        )}
                    </ScrollArea>
                </Card>
            </div>
        </div>
    );
};

export default SchoolsPage;
