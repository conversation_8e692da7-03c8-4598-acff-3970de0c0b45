import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger,
} from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
    FileText,
    Upload,
    Video,
    Music,
    Package,
    Pencil,
    Eye,
    FileEdit,
    FilePlus2,
    BookOpen,
    Loader2,
    Image,
    Video as VideoIcon,
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { TopicContent as ITopicContent, ContentType } from './types';
import { useFileUpload } from '@/lib/utils';
import ContentRenderer from './ContentRenderer';
import JSZip from 'jszip';
import {
    useCreateContentMutation,
    useUpdateContentMutation,
    useDeleteContentMutation,
    useGetContentByTopicIdQuery
} from '@/APIConnect';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip";

// Add a helper function to extract YouTube video ID
const extractYouTubeVideoId = (url: string): string | null => {
    const regExp = /^.*(?:youtu\.be\/|v\/|embed\/|watch\?v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return match && match[1].length === 11 ? match[1] : null;
};

interface TopicContentProps {
    topicId: string;
    courseId: string;
    topicName: string;
    disabled?: boolean;
}

interface ExtractedFile {
    path: string;
    file: File;
}

const TopicContent: React.FC<TopicContentProps> = ({ topicId, courseId, topicName, disabled = false }) => {
    const { data: contentData, refetch: refetchContent } = useGetContentByTopicIdQuery(topicId);
    const [createContent] = useCreateContentMutation();
    const [updateContent] = useUpdateContentMutation();
    const [deleteContent] = useDeleteContentMutation();
    const { uploadFile, progress } = useFileUpload();

    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [activeTab, setActiveTab] = useState("edit");
    const [contentFormData, setContentFormData] = useState<Partial<ITopicContent>>({
        topicId: '',
        courseId: '',
        topicName: '',
        type: ContentType.HTML,
        content: '',
    });
    const [file, setFile] = useState<File | null>(null);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [isProcessing, setIsProcessing] = useState(false);
    const [extractedFiles, setExtractedFiles] = useState<ExtractedFile[]>([]);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (!files || !files[0]) return;

        const selectedFile = files[0];
        setFile(selectedFile);

        // Generate preview URL for applicable file types
        if (contentFormData.type === ContentType.AUDIO_URL || contentFormData.type === ContentType.VIDEO_URL) {
            const url = URL.createObjectURL(selectedFile);
            setPreviewUrl(url);
        } else {
            setPreviewUrl(null);
        }

        // Handle SCORM zip file
        if (contentFormData.type === ContentType.SCORM && selectedFile.type === 'application/zip') {
            setIsProcessing(true);
            try {
                const zip = new JSZip();
                const zipContent = await zip.loadAsync(selectedFile);

                // Reset extracted files
                setExtractedFiles([]);

                let launchFile = '';
                const totalFiles = Object.keys(zipContent.files).length;
                let processedFiles = 0;

                // Process each file in the zip
                const uploadPromises: Promise<any>[] = [];

                for (const [path, zipFile] of Object.entries(zipContent.files)) {
                    if (zipFile.dir) {
                        processedFiles++;
                        setUploadProgress((processedFiles / totalFiles) * 100);
                        continue;
                    }

                    // Check if this is potentially the launch file
                    if (path.toLowerCase().includes('index.html') ||
                        path.toLowerCase().includes('launch.html') ||
                        path.toLowerCase().includes('start.html')) {
                        launchFile = path;
                    }

                    // Extract file content
                    const content = await zipFile.async('blob');
                    const extractedFile = new File([content], path.split('/').pop() || path, {
                        type: content.type || 'application/octet-stream'
                    });

                    setExtractedFiles(prev => [...prev, { path, file: extractedFile }]);

                    // Upload the file
                    const uploadPromise = uploadFile(extractedFile, path).then(() => {
                        processedFiles++;
                        setUploadProgress((processedFiles / totalFiles) * 100);
                    });

                    uploadPromises.push(uploadPromise);
                }

                // Wait for all uploads to complete
                await Promise.all(uploadPromises);

                if (!launchFile) {
                    throw new Error('No launch file found in SCORM package');
                }

                // Set the launch file URL as the content
                setContentFormData(prev => ({
                    ...prev,
                    content: launchFile
                }));

                toast({
                    title: "SCORM package processed successfully",
                    variant: "default",
                });
            } catch (error) {
                console.error('Error processing SCORM package:', error);
                toast({
                    title: "Failed to process SCORM package",
                    description: error instanceof Error ? error.message : "Please try again",
                    variant: "destructive",
                });
            } finally {
                setIsProcessing(false);
            }
        }
    };

    const handleUpload = async () => {
        if (!file) return;

        try {
            const fileName = `${Date.now()}_${file.name}`;
            const response = await uploadFile(file, fileName);

            if (typeof response === 'string' && response.startsWith("http")) {
                setContentFormData(prev => ({
                    ...prev,
                    content: response,
                }));
                toast({
                    title: "File uploaded successfully",
                    variant: "default",
                });
            } else {
                throw new Error('Upload failed');
            }
        } catch (error) {
            console.error('Error uploading file:', error);
            toast({
                title: "Failed to upload file",
                description: "Please try again later",
                variant: "destructive",
            });
        } finally {
            // Clean up preview URL
            if (previewUrl) {
                URL.revokeObjectURL(previewUrl);
                setPreviewUrl(null);
            }
        }
    };

    useEffect(() => {
        if (contentData?.resultObject) {
            const content = contentData.resultObject.type === ContentType.HTML
                ? contentData.resultObject.content
                : contentData.resultObject.contentUrl;

            setContentFormData({
                ...contentData.resultObject,
                content: content || '',
            });
            setPreviewUrl(contentData.resultObject.content);
        }
    }, [contentData]);

    useEffect(() => {
        // Clean up preview URL on unmount
        return () => {
            if (previewUrl) {
                URL.revokeObjectURL(previewUrl);
            }
        };
    }, [previewUrl]);

    const handleSave = async () => {
        try {
            if (!contentFormData.content) {
                toast({
                    title: "Content is required",
                    variant: "destructive",
                });
                return;
            }

            const payload = {
                ...contentFormData,
                topicId,
                courseId,
                topicName,
            };

            // Set content or contentUrl based on type
            if (contentFormData.type === ContentType.HTML) {
                payload.content = contentFormData.content;
                payload.contentUrl = '';
            } else {
                payload.content = contentFormData.content;
                // payload.content = '';
            }

            if (contentData?.resultObject?.id) {
                await updateContent({ id: contentData.resultObject.id, ...payload }).unwrap();
                toast({
                    title: "Content updated successfully",
                    variant: "default",
                });
            } else {
                await createContent(payload).unwrap();
                toast({
                    title: "Content created successfully",
                    variant: "default",
                });
            }

            setIsDialogOpen(false);
            setContentFormData({
                topicId: '',
                courseId: '',
                topicName: '',
                type: ContentType.HTML,
                content: '',
            });
            setFile(null);
            refetchContent();
        } catch (error) {
            console.error('Error saving content:', error);
            toast({
                title: "Failed to save content",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const handleDelete = async () => {
        if (!contentData?.resultObject?.id) return;

        try {
            await deleteContent(contentData.resultObject.id).unwrap();
            toast({
                title: "Content deleted successfully",
                variant: "default",
            });
            setContentFormData({
                topicId: '',
                courseId: '',
                topicName: '',
                type: ContentType.HTML,
                content: '',
            });
            refetchContent();
        } catch (error) {
            console.error('Error deleting content:', error);
            toast({
                title: "Failed to delete content",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const openDialog = () => {
        if (contentData?.resultObject) {
            const content = contentData.resultObject.type === ContentType.HTML
                ? contentData.resultObject.content
                : contentData.resultObject.contentUrl;

            setContentFormData({
                ...contentData.resultObject,
                content: content || '',
            });
        }
        setIsDialogOpen(true);
        setActiveTab("edit");
    };

    const modules = {
        toolbar: [
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'list': 'ordered' }, { 'list': 'bullet' }],
            [{ 'color': [] }, { 'background': [] }],
            ['link', 'image'],
            ['clean']
        ],
    };

    // Create a preview object that matches the ContentRenderer interface
    const previewData = {
        resultObject: {
            type: contentFormData.type || ContentType.HTML,
            content: contentFormData.type === ContentType.HTML ? contentFormData.content : undefined,
            contentUrl: contentFormData.type !== ContentType.HTML ? contentFormData.content : undefined,
        }
    };

    const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;

        if (contentFormData.type === ContentType.VIDEO_URL) {
            const videoId = extractYouTubeVideoId(value);
            if (videoId) {
                const embedUrl = `https://www.youtube.com/embed/${videoId}`;
                setContentFormData(prev => ({ ...prev, content: embedUrl }));
                setPreviewUrl(embedUrl);
            } else {
                setContentFormData(prev => ({ ...prev, content: value }));
                setPreviewUrl(value);
            }
        } else {
            setContentFormData(prev => ({ ...prev, content: value }));
            setPreviewUrl(value);
        }
    };

    return (
        <>
            {/* <div className="flex items-center space-x-2">
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={openDialog}
                                disabled={disabled}
                                className="flex items-center space-x-2"
                            >
                                {contentData?.resultObject ? (
                                    <>
                                        <FileEdit className="w-4 h-4" />
                                        <span>Edit Content</span>
                                    </>
                                ) : (
                                    <>
                                        <FilePlus2 className="w-4 h-4" />
                                        <span>Add Content</span>
                                    </>
                                )}
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                            {contentData?.resultObject ? 'Edit topic content' : 'Create new topic content'}
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>

                {contentData?.resultObject && (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                        openDialog();
                                        setActiveTab("preview");
                                    }}
                                    className="flex items-center space-x-2"
                                >
                                    <BookOpen className="w-4 h-4" />
                                    <span>Preview</span>
                                </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                                Preview topic content
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                )}
            </div> */}

            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogContent className="max-w-4xl">
                    <DialogHeader>
                        <DialogTitle>Topic Content</DialogTitle>
                    </DialogHeader>

                    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                        <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="edit">Edit</TabsTrigger>
                            <TabsTrigger value="preview">Preview</TabsTrigger>
                        </TabsList>

                        <TabsContent value="edit" className="space-y-4 py-4">
                            <div className="space-y-2">
                                <Label>Content Type</Label>
                                <Select
                                    value={contentFormData.type?.toString()}
                                    onValueChange={(value) => setContentFormData(prev => ({
                                        ...prev,
                                        type: parseInt(value) as ContentType,
                                        content: '', // Reset content when type changes
                                    }))}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select content type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value={ContentType.HTML.toString()}>HTML</SelectItem>
                                        <SelectItem value={ContentType.AUDIO_URL.toString()}>Audio</SelectItem>
                                        <SelectItem value={ContentType.VIDEO_URL.toString()}>Video</SelectItem>
                                        <SelectItem value={ContentType.SCORM.toString()}>SCORM</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            {contentFormData.type === ContentType.HTML ? (
                                <div className="space-y-2">
                                    <Label>HTML Content</Label>
                                    <div className="min-h-[200px]">
                                        <ReactQuill
                                            theme="snow"
                                            value={contentFormData.content}
                                            onChange={(content) => setContentFormData(prev => ({
                                                ...prev,
                                                content
                                            }))}
                                            modules={modules}
                                            className="h-[150px]"
                                        />
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label>URL</Label>
                                        <Input
                                            value={contentFormData.content}
                                            onChange={handleUrlChange}
                                            placeholder="Enter URL or upload file"
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label>Or Upload File</Label>
                                        <div className="flex items-center space-x-2">
                                            <Input
                                                type="file"
                                                onChange={handleFileChange}
                                                accept={
                                                    contentFormData.type === ContentType.AUDIO_URL ? "audio/*" :
                                                        contentFormData.type === ContentType.VIDEO_URL ? "video/*" :
                                                            contentFormData.type === ContentType.SCORM ? ".zip" :
                                                                undefined
                                                }
                                            />
                                            <Button
                                                type="button"
                                                onClick={handleUpload}
                                                disabled={!file || isProcessing}
                                            >
                                                {isProcessing ? (
                                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                                ) : (
                                                    <Upload className="w-4 h-4 mr-2" />
                                                )}
                                                Upload
                                            </Button>
                                        </div>
                                        {(uploadProgress > 0 || progress > 0) && (
                                            <Progress
                                                value={Math.max(uploadProgress, progress)}
                                                className="w-full"
                                            />
                                        )}
                                        {isProcessing && (
                                            <p className="text-sm text-muted-foreground">
                                                Processing SCORM package... {extractedFiles.length} files extracted
                                            </p>
                                        )}
                                    </div>
                                    {previewUrl && (
                                        <div className="space-y-2">
                                            <Label>Preview</Label>
                                            {contentFormData.type === ContentType.VIDEO_URL && (
                                                previewUrl.includes('youtube.com/embed') ? (
                                                    <iframe
                                                        src={previewUrl}
                                                        className="w-full h-64"
                                                        frameBorder="0"
                                                        allowFullScreen
                                                    />
                                                ) : (
                                                    <video controls src={previewUrl} className="w-full h-auto mt-2" />
                                                )
                                            )}
                                            {contentFormData.type === ContentType.AUDIO_URL && (
                                                <audio controls src={previewUrl} className="w-full mt-2" />
                                            )}
                                        </div>
                                    )}
                                </div>
                            )}
                        </TabsContent>

                        <TabsContent value="preview">
                            <ContentRenderer
                                topicId={topicId}
                                previewData={previewData}
                            />
                        </TabsContent>
                    </Tabs>

                    <DialogFooter className="flex justify-between">
                        <div>
                            {contentData?.resultObject && (
                                <Button variant="destructive" onClick={handleDelete}>
                                    Delete Content
                                </Button>
                            )}
                        </div>
                        <div className="flex space-x-2">
                            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                                Cancel
                            </Button>
                            <Button onClick={handleSave}>
                                {contentData?.resultObject ? 'Update' : 'Add'}
                            </Button>
                        </div>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default TopicContent;
