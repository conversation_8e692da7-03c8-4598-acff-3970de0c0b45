import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

interface InstructorInfo {
  name: string;
  bio?: string;
  imageUrl?: string;
  expertise?: string[];
  // Add other instructor details as needed
}

interface PerformanceData {
  // Define structure for performance metrics
  completionRate?: number;
  averageScore?: number;
  timeSpent?: number;
  // Add other performance metrics as needed
}

interface CourseInfoTabsProps {
  courseDescription: string;
  courseObjectives: string[];
  instructorInfo: InstructorInfo | null;
  performanceData: PerformanceData | null;
}

const CourseInfoTabs: React.FC<CourseInfoTabsProps> = ({
  courseDescription,
  courseObjectives,
  instructorInfo,
  performanceData,
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid grid-cols-3 mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="instructor">Instructor</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>
        
        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold text-slate-800 mb-2">Course Description</h3>
            <p className="text-slate-600">{courseDescription || 'No description available.'}</p>
          </div>
          
          {courseObjectives && courseObjectives.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-slate-800 mb-2">Learning Objectives</h3>
              <ul className="list-disc pl-5 space-y-1">
                {courseObjectives.map((objective, index) => (
                  <li key={index} className="text-slate-600">{objective}</li>
                ))}
              </ul>
            </div>
          )}
        </TabsContent>
        
        {/* Instructor Tab */}
        <TabsContent value="instructor">
          {instructorInfo ? (
            <div className="flex flex-col sm:flex-row gap-4">
              {instructorInfo.imageUrl && (
                <div className="flex-shrink-0">
                  <img
                    src={instructorInfo.imageUrl}
                    alt={instructorInfo.name}
                    className="h-24 w-24 rounded-full object-cover"
                  />
                </div>
              )}
              <div>
                <h3 className="text-lg font-semibold text-slate-800 mb-1">{instructorInfo.name}</h3>
                {instructorInfo.expertise && instructorInfo.expertise.length > 0 && (
                  <p className="text-sm text-emerald-600 mb-2">{instructorInfo.expertise.join(', ')}</p>
                )}
                {instructorInfo.bio && (
                  <p className="text-slate-600">{instructorInfo.bio}</p>
                )}
              </div>
            </div>
          ) : (
            <p className="text-slate-500">Instructor information not available.</p>
          )}
        </TabsContent>
        
        {/* Performance Tab */}
        <TabsContent value="performance">
          {performanceData ? (
            <div className="space-y-4">
              {performanceData.completionRate !== undefined && (
                <div>
                  <h3 className="text-sm font-medium text-slate-500">Completion Rate</h3>
                  <p className="text-lg font-semibold text-slate-800">{performanceData.completionRate}%</p>
                </div>
              )}
              
              {performanceData.averageScore !== undefined && (
                <div>
                  <h3 className="text-sm font-medium text-slate-500">Average Assessment Score</h3>
                  <p className="text-lg font-semibold text-slate-800">{performanceData.averageScore}%</p>
                </div>
              )}
              
              {performanceData.timeSpent !== undefined && (
                <div>
                  <h3 className="text-sm font-medium text-slate-500">Time Spent</h3>
                  <p className="text-lg font-semibold text-slate-800">{performanceData.timeSpent} hours</p>
                </div>
              )}
            </div>
          ) : (
            <p className="text-slate-500">Performance data not available. Complete some lessons to see your performance metrics.</p>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CourseInfoTabs;
