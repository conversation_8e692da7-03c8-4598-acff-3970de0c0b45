import React from 'react';
import { Button } from "@/components/ui/button";
import { Filter } from 'lucide-react';
import { ProjectActivity } from '@/types/projectCenter';

interface ProjectActivityTableProps {
  activities: ProjectActivity[];
  onViewProject: (project: ProjectActivity) => void;
  onFilter: () => void;
}

const MemberAvatars = ({ count }: { count: number }) => (
  <div className="flex -space-x-2 overflow-hidden justify-center">
    {Array.from({ length: Math.min(count, 4) }).map((_, index) => (
      <img
        key={index}
        className="inline-block h-7 w-7 rounded-full ring-1 ring-white"
        src={`https://i.pravatar.cc/28?u=member${index + Math.random()}`}
        alt={`Team member ${index + 1}`}
      />
    ))}
  </div>
);

const ProjectActivityTable: React.FC<ProjectActivityTableProps> = ({
  activities,
  onViewProject,
  onFilter
}) => {
  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <h2 className="text-zinc-800 text-xl font-semibold">Project Activity Tracker</h2>
        <Button 
          variant="outline" 
          className="border-slate-600 text-slate-600 font-medium capitalize rounded-3xl px-3 py-2"
          onClick={onFilter}
        >
          <Filter size={16} className="mr-1" />
          Filter
        </Button>
      </div>
      <div className="bg-stone-50 rounded-xl border border-lime-500 overflow-x-auto">
        <table className="w-full min-w-[1000px]">
          <thead className="bg-white">
            <tr>
              {['Project Name', 'Type', 'Class Access', 'Members', 'Submission', 'Peer Review', 'Instructor Review', 'Action'].map(header => (
                <th 
                  key={header} 
                  className="p-3 border-b border-stone-300 text-slate-600 text-base font-semibold text-center first:text-left last:text-right first:pl-4 last:pr-4"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {activities.map(activity => (
              <tr key={activity.id} className="border-b border-stone-300 last:border-b-0 hover:bg-gray-50">
                <td className="p-3 text-zinc-800 text-sm font-semibold text-left pl-4">
                  {activity.projectName}
                </td>
                <td className="p-3 text-zinc-800 text-sm font-medium text-center">
                  {activity.type}
                </td>
                <td className="p-3 text-zinc-800 text-sm font-medium text-center">
                  {activity.classAccess}
                </td>
                <td className="p-3 text-center">
                  <MemberAvatars count={typeof activity.members === 'number' ? activity.members : activity.members.length} />
                </td>
                <td className="p-3 text-center">
                  <div className="flex items-center justify-center gap-1">
                    <span className={`w-1.5 h-1.5 ${activity.submissionStatus.statusColor.replace('text-', 'bg-')} rounded-full`}></span>
                    <span className={activity.submissionStatus.statusColor}>
                      {activity.submissionStatus.count}/{activity.submissionStatus.total}
                    </span>
                  </div>
                </td>
                <td className="p-3 text-center">
                  <div className="flex items-center justify-center gap-1">
                    <span className={`w-1.5 h-1.5 ${activity.peerReviewStatus.statusColor.replace('text-', 'bg-')} rounded-full`}></span>
                    <span className={activity.peerReviewStatus.statusColor}>
                      {activity.peerReviewStatus.count}/{activity.peerReviewStatus.total}
                    </span>
                  </div>
                </td>
                <td className="p-3 text-center">
                  <div className="flex items-center justify-center gap-1">
                    <span className={`w-1.5 h-1.5 ${activity.instructorReviewStatus.statusColor.replace('text-', 'bg-')} rounded-full`}></span>
                    <span className={activity.instructorReviewStatus.statusColor}>
                      {activity.instructorReviewStatus.count}/{activity.instructorReviewStatus.total}
                    </span>
                  </div>
                </td>
                <td className="p-3 text-right pr-4">
                  <Button 
                    variant="default" 
                    size="sm" 
                    className="bg-slate-600 hover:bg-slate-700 text-white text-[10px] font-medium capitalize h-7 px-2 rounded-xl"
                    onClick={() => onViewProject(activity)}
                  >
                    View
                  </Button>
                </td>
              </tr>
            ))}
            {activities.length === 0 && (
              <tr>
                <td colSpan={8} className="text-center text-gray-500 py-10">No project activities found.</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ProjectActivityTable; 