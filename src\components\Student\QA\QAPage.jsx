import React, { useState, useEffect } from "react";
import {
  useGetQueriesQuery,
  useCreateQueryMutation,
  useGetSchoolClassroomsQuery,
} from "../../../APIConnect";
import { Button } from "components/ui/button";
import { ScrollArea } from "components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "components/ui/dialog";
import { Textarea } from "components/ui/textarea";
import { format } from "date-fns";
import {
  Loader2,
  MessageCircle,
  Send,
  Clock,
  CheckCircle2,
  HelpCircle,
  Calendar,
  Search,
  Filter,
  ChevronDown,
} from "lucide-react";
import { Input } from "components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";
import { motion } from "framer-motion";

const QAPage = ({ schoolId, studentId }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [question, setQuestion] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [filter, setFilter] = useState("all");
  const [currentClassroomId, setCurrentClassroomId] = useState(null);

  const { data: classroomsData, isLoading: isClassroomsLoading } =
    useGetSchoolClassroomsQuery(schoolId);
  const {
    data: queriesData,
    isLoading: isQueriesLoading,
    refetch,
  } = useGetQueriesQuery(
    { classroomId: currentClassroomId },
    { skip: !currentClassroomId }
  );
  const [createQuery] = useCreateQueryMutation();

  useEffect(() => {
    if (!currentClassroomId && classroomsData?.resultObject?.length > 0) {
      setCurrentClassroomId(classroomsData.resultObject[0].id);
    }
  }, [currentClassroomId, classroomsData]);

  const handleSubmitQuestion = async () => {
    if (!question.trim() || !currentClassroomId) return;

    try {
      await createQuery({
        studentId,
        classroomId: currentClassroomId,
        queryText: question,
        isAnswered: false,
      });
      setQuestion("");
      setIsDialogOpen(false);
      refetch();
    } catch (error) {
      console.error("Error submitting question:", error);
    }
  };

  const filteredQueries = queriesData?.resultObject?.filter((qa) => {
    const matchesSearch =
      qa.queryText.toLowerCase().includes(searchQuery.toLowerCase()) ||
      qa.reply?.answerText?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter =
      filter === "all" ||
      (filter === "answered" && qa.isAnswered) ||
      (filter === "unanswered" && !qa.isAnswered);
    return matchesSearch && matchesFilter;
  });

  const QACard = ({ qa }) => (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-4 hover:shadow-md transition-shadow border border-gray-100">
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-start space-x-3 flex-1">
          <div className="mt-1">
            <div className="bg-primary/10 p-2 rounded-full">
              <HelpCircle className="h-5 w-5 text-primary" />
            </div>
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-lg flex items-center">
              Question
            </h3>
            <p className="text-gray-700 mt-1 text-base leading-relaxed">
              {qa.queryText}
            </p>
            <div className="flex items-center text-sm text-gray-500 mt-2">
              <Calendar className="h-4 w-4 mr-1" />
              {format(new Date(qa.createdOn), "MMM d, yyyy")}
            </div>
          </div>
        </div>
      </div>

      {qa.isAnswered && qa.reply && (
        <div className="mt-4 pt-4 border-t border-dashed">
          <div className="flex items-start space-x-3">
            <div className="mt-1">
              <div className="bg-green-50 p-2 rounded-full">
                <MessageCircle className="h-5 w-5 text-green-500" />
              </div>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-md mb-2 flex items-center">
                Answer
                <CheckCircle2 className="h-4 w-4 ml-2 text-green-500" />
              </h4>
              <p className="text-gray-700 text-base leading-relaxed">
                {qa.reply.answerText}
              </p>
              <div className="mt-2 text-sm text-gray-500 flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Answered on {format(new Date(qa.updatedon), "MMM d, yyyy")}
              </div>
            </div>
          </div>
        </div>
      )}

      {!qa.isAnswered && (
        <div className="mt-4 pt-4 border-t border-dashed">
          <div className="bg-yellow-50 rounded-lg p-4">
            <p className="text-yellow-600 text-sm flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              Waiting for answer... We'll notify you when your question is
              answered! 🔔
            </p>
          </div>
        </div>
      )}
    </div>
  );

  if (isClassroomsLoading || isQueriesLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-100">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold flex items-center">
            <MessageCircle className="h-6 w-6 mr-2 text-primary" />
            Questions & Answers
          </h1>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center space-x-2">
                <HelpCircle className="h-5 w-5" />
                <span>Ask a Question</span>
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="flex items-center">
                  <HelpCircle className="h-5 w-5 mr-2 text-primary" />
                  Ask Your Question
                </DialogTitle>
              </DialogHeader>
              <div className="mt-4 space-y-4">
                <Textarea
                  placeholder="Type your question here... 💭"
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  className="min-h-[100px]"
                />
                <div className="flex justify-end">
                  <Button
                    onClick={handleSubmitQuestion}
                    className="flex items-center space-x-2"
                  >
                    <Send className="h-4 w-4" />
                    <span>Submit Question</span>
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {classroomsData?.resultObject && (
          <motion.div
            className="mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <div className="flex items-center gap-4">
              <label
                htmlFor="classroom-select"
                className="text-muted-foreground font-medium"
              >
                Select Classroom:
              </label>
              <div className="relative">
                <select
                  id="classroom-select"
                  className="appearance-none px-4 py-2 pr-10 bg-card border rounded-lg shadow-sm focus:ring-2 focus:ring-primary/20 outline-none transition-all duration-200"
                  value={currentClassroomId || ""}
                  onChange={(e) => setCurrentClassroomId(e.target.value)}
                >
                  {classroomsData.resultObject.map((classroom) => (
                    <option key={classroom.id} value={classroom.id}>
                      {classroom.name}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
              </div>
            </div>
          </motion.div>
        )}

        <div className="flex gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search questions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="w-48">
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger>
                <div className="flex items-center">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Questions</SelectItem>
                <SelectItem value="answered">Answered</SelectItem>
                <SelectItem value="unanswered">Waiting for Answer</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <ScrollArea className="h-[calc(100vh-280px)]">
        <div className="space-y-4">
          {filteredQueries?.length === 0 ? (
            <div className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-100">
              <MessageCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500 mb-2">
                No questions found{" "}
                {searchQuery ? "matching your search" : "yet"}! 🔍
              </p>
              {searchQuery ? (
                <p className="text-sm text-gray-400">
                  Try adjusting your search terms
                </p>
              ) : (
                <p className="text-sm text-gray-400">
                  Be the first to ask a question! 🎯
                </p>
              )}
            </div>
          ) : (
            filteredQueries?.map((qa) => <QACard key={qa.id} qa={qa} />)
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

export default QAPage;
