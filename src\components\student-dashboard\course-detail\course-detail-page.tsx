import React, { useState, useEffect } from 'react';
// Import icons from lucide-react
import {
  ChevronDown, ChevronUp, Video, BookOpen, CheckSquare, CalendarDays,
  MessageSquare, ArrowLeft, Settings, Bell, User, Clock, Layers3,
  FileText, BarChartHorizontalBig, Search, Home, Library, Users, LifeBuoy, LogOut, ChevronRight,
  BookmarkIcon, NotebookText, PanelRightClose, PanelRightOpen, Lightbulb, PlayCircle, FileAudio,
  Code, HelpCircle, Icon, File
} from 'lucide-react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { courseDetailService, setAuthToken } from './course-detail-page-data';
import type { CourseDetails as CourseDetailsType, Module, Lesson, SubItem, PerformanceData, InstructorDetails } from '../../../types/courseTypes';
import { useAuth0 } from '@auth0/auth0-react';
import ContentRenderer from '../components/content/content-renderer';
import EnhancedContentRenderer from '../components/content/enhanced-content-renderer';
import  BookmarksDialog  from '../components/bookmarks-dialog';
import BookmarkCollectionDialog from '../components/bookmark-collection-dialog';
import NotesDialog from '../components/notes-dialog';
import MySpaceDialog from '../components/my-space-dialog';
import QuizRenderer from '../components/quiz/quiz-renderer';
import { CaseStudyRenderer, SelfReflectiveJournalRenderer, DiscussionForumRenderer } from '@/components/student-dashboard/components/assignments';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import ChatWindow from '../../chat/ChatWindow';
import { useGetClassRoomTokenQuery, useLazyGetBookmarksQuery } from '../../../APIConnect';
import { elephantFace } from '@lucide/lab';
import { cn } from '@/lib/utils';
// --- Reusable Icon Component ---
// Simplifies using icons with consistent styling
const IconWrapper = ({ icon: Icon, size = 20, className = "" }: {
  icon: React.ElementType;
  size?: number;
  className?: string;
}) => (
  <Icon size={size} className={`flex-shrink-0 ${className}`} />
);



// --- Header Component ---
const Header = () => (
  <header className="bg-gradient-to-r from-[#386C8D] via-[#036664] via-12% to-[#0E3D35] to-83% text-white border-b-2 border-gray-200 rounded-b-lg">
    <div className="container mx-auto px-6 sm:px-12 py-4 flex justify-between items-center">
      {/* Left Section: Logo & Navigation */}
      <div className="flex items-center gap-8 lg:gap-16 xl:gap-64">
        {/* Placeholder for Logo */}
        <div className="bg-gray-300 h-10 w-36 rounded flex items-center justify-center text-gray-600 text-sm">
          Logo Placeholder
        </div>
        <nav className="hidden md:flex items-center gap-6 lg:gap-8">
          <button className="p-2 rounded-full hover:bg-white/10 transition-colors">
            <IconWrapper icon={Home} size={24} />
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-white/10 border border-white/40 rounded-lg hover:bg-white/20 transition-colors">
            <IconWrapper icon={Search} size={24} />
            <span className="text-lg font-medium">Q&A</span>
          </button>
          <button className="p-2 rounded-full hover:bg-white/10 transition-colors">
            <IconWrapper icon={Library} size={24} />
          </button>
          <button className="p-2 rounded-full hover:bg-white/10 transition-colors">
            <IconWrapper icon={Users} size={24} />
          </button>
        </nav>
      </div>

      {/* Right Section: Icons & Profile */}
      <div className="flex items-center gap-4 md:gap-6 lg:gap-8">
        <button className="p-2 rounded-full hover:bg-white/10 transition-colors">
          <IconWrapper icon={Settings} size={24} />
        </button>
        <button className="p-2 rounded-full hover:bg-white/10 transition-colors">
          <IconWrapper icon={Bell} size={24} />
        </button>
        {/* Placeholder for Profile Image */}
        <div className="w-10 h-10 bg-gray-300 rounded-full border border-white flex-shrink-0"></div>
      </div>
    </div>
  </header>
);

// --- Top Bar Below Header ---
// Modified to include conditional bookmark button that only shows when subtopic is selected
const TopBar = ({ 
  onBackClick, 
  onBookmarkClick, 
  showBookmarkButton = false,
  isBookmarked = false,
  onNotesClick,
  showNotesButton = false
}: { 
  onBackClick: () => void, 
  onBookmarkClick: () => void, 
  showBookmarkButton?: boolean,
  isBookmarked?: boolean,
  onNotesClick?: () => void; 
  showNotesButton?: boolean; 
}) => (
  <div className="container mx-auto px-6 sm:px-12 py-6 flex justify-between items-center border-b-2 border-gray-200">
     {/* Back Button - Adjusted position for better flow */}
     <button
        onClick={onBackClick}
        className="bg-[#347468] text-white px-3 py-1.5 rounded-xl flex items-center gap-2 hover:bg-[#2a5d54] transition-colors text-sm">
        <IconWrapper icon={ArrowLeft} size={18} />
        <span>Back</span>
      </button>
    <div className="flex items-center gap-2 md:gap-6 mr-2 ml-2 md:mr-[460px]">
      {/* Bookmark Button - Toggle between bookmarked and not bookmarked states */}
      {showBookmarkButton && (
        <button 
          onClick={onBookmarkClick}
          className="bg-[#347468] text-white px-3 py-1.5 rounded-xl flex items-center gap-2 hover:bg-[#2a5d54] transition-colors text-sm"
          title={isBookmarked ? "Remove bookmark" : "Bookmark this content"}
        >
          {isBookmarked ? (
            <>
              <BookmarkIcon size={18} fill="white" />
              <span className="text-sm">Bookmarked</span>
            </>
          ) : (
            <>
              <IconWrapper icon={BookmarkIcon} size={18} />
              <span>Bookmark</span>
            </>
          )}
        </button>
      )}
      {/* Notes Button */}
      {showNotesButton && onNotesClick && (
        <button 
          onClick={onNotesClick} 
          className="bg-[#347468] text-white px-3 py-1.5 rounded-xl flex items-center gap-2 hover:bg-[#2a5d54] transition-colors text-sm"
          title="Add notes"
        >
          <IconWrapper icon={NotebookText} size={18} />
          <span>Notes</span>
        </button>
      )}
    </div>
  </div>
);


// --- Course Details Component ---
const CourseDetails = () => (
  <div className="flex flex-col gap-8 md:gap-10">
    {/* Title and Instructor */}
    <div className="flex flex-col gap-4">
      <h1 className="text-3xl md:text-4xl font-semibold text-gray-800 capitalize">
        Types of Pronoun
      </h1>
      <p className="text-xl md:text-2xl">
        <span className="text-gray-700 font-medium capitalize">Instructor - </span>
        <span className="text-[#347468] font-semibold capitalize">John Doe</span>
      </p>
    </div>

    {/* Progress Bar */}
    <div className="flex flex-col gap-3">
      <div className="flex justify-between items-center text-sm">
        <span className="text-gray-700">
          <span className="font-semibold text-gray-800">5</span>/10 lessons
          <span className="text-gray-400 mx-2">|</span>
          <span className="font-semibold text-gray-800">50%</span>
        </span>
        <span className="text-[#347468] font-bold">4 hrs left</span>
      </div>
      <div className="w-full h-4 bg-[#347468]/20 rounded-full overflow-hidden">
        <div className="h-full bg-[#347468] rounded-full" style={{ width: '50%' }}></div>
      </div>
    </div>

    {/* Resume Button */}
    <button className="bg-[#347468] text-white px-5 py-3 rounded-xl self-start flex items-center gap-2 hover:bg-[#2a5d54] transition-colors text-md font-semibold capitalize">
      Resume Course
    </button>
  </div>
);

// --- Course Stats Component ---
const CourseStats = () => (
  <div className="flex flex-col gap-6 text-gray-800 p-4 md:p-6 rounded-lg border border-gray-100 self-start md:self-center">
    <div className="flex items-center gap-2">
      <IconWrapper icon={Clock}  className="text-[#347468] text-sm" />
      <span className="text-md font-medium">8 Hours</span>
    </div>
    <div className="flex items-center gap-2">
      <IconWrapper icon={Layers3}  className="text-[#347468] text-sm" />
      <span className="text-md font-medium">20 Topics</span>
    </div>
    <div className="flex items-center gap-2">
      <IconWrapper icon={FileText}  className="text-[#347468] text-sm" />
      <span className="text-md font-medium">3 Assessments</span>
    </div>
  </div>
);

// --- Course Content Sidebar Component ---
const CourseContentSidebar = () => {
  const [openModule, setOpenModule] = useState<string | null>('module-1'); // Track open module

  const toggleModule = (moduleId: string | null) => {
    setOpenModule(prev => (prev === moduleId ? null : moduleId));
  };

  const modules = [
    {
      id: 'module-1',
      title: 'Module 1',
      duration: '22 min',
      lessons: [
        {
          id: 'lesson-1-1',
          title: '01: Introduction to Pronouns',
          subItems: [
            { id: 'sub-1-1-1', title: 'Definition and purpose of pronouns', type: 'video', duration: '5 min' },
            { id: 'sub-1-1-2', title: 'Assessment', type: 'assessment', duration: '1 min' },
            { id: 'sub-1-1-3', title: 'Pronouns and antecedents', type: 'video', duration: '6 min' },
            { id: 'sub-1-1-4', title: 'Assessment', type: 'assessment', duration: '1 min' },
            { id: 'sub-1-1-5', title: 'Overview of pronoun categories', type: 'document', duration: '10 min' },
            { id: 'sub-1-1-6', title: 'Assessment', type: 'assessment', duration: '1 min' },
          ],
        },
        { id: 'lesson-1-2', title: '02: Personal Pronouns', subItems: [] },
        { id: 'lesson-1-3', title: '03: Demonstrative Pronouns', subItems: [] },
      ],
    },
    { id: 'module-2', title: 'Module 2', duration: '12 min', lessons: [] },
    { id: 'module-3', title: 'Module 3', duration: '32 min', lessons: [] },
  ];

  const getIcon = (type:string) => {
    switch (type) {
      case 'video': return Video;
      case 'scorm': return PlayCircle;
      case 'audio': return FileAudio;
      case 'assessment':
      case 'quiz': return Lightbulb;
      case 'html': return Code;
      case 'document': return FileText;
      case 'text': return BookOpen;
      case 'unknown':
      default: return HelpCircle; // Default for unknown content types
    }
  };

  return (
    <div className="border-2 border-gray-200 overflow-hidden w-full max-w-sm xl:max-w-md">
      {modules.map((module) => (
        <div key={module.id} className="border-b-2 border-gray-200 last:border-b-0">
          {/* Module Header */}
          <button
            onClick={() => toggleModule(module.id)}
            className="w-full flex justify-between items-center p-4 md:p-5 hover:bg-gray-50 transition-colors"
          >
            <span className="text-base font-semibold text-gray-800 capitalize">{module.title}</span>
            <div className="flex items-center gap-3">
              <span className="text-xs text-gray-600 capitalize">{module.duration ?? "4 Hours"}</span>
              <div className={`p-1.5 border border-gray-700 rounded-md transition-transform duration-300 ${openModule === module.id ? 'rotate-0' : 'rotate-180'}`}>
                <IconWrapper icon={ChevronUp} size={16} className="text-[#347468]" />
              </div>
            </div>
          </button>

          {/* Module Content (Lessons & Sub-items) */}
          {openModule === module.id && (
            <div className="bg-white">
              {module.lessons.map((lesson) => (
                <div key={lesson.id} className="border-t border-gray-200">
                  {/* Lesson Header */}
                  <div className={`flex justify-between items-center p-4 pl-6 ${lesson.id === 'lesson-1-1' ? 'bg-[#347468]/10' : ''} hover:bg-[#347468]/5 transition-colors`}>
                    <span className="text-sm font-semibold text-gray-800 capitalize">{lesson.title}</span>
                    {/* Optional: Add expand/collapse for lessons if they have sub-items */}
                  </div>

                  {/* Sub Items (if any) - Shown for the active lesson example */}
                  {lesson.id === 'lesson-1-1' && lesson.subItems && (
                    <div className="bg-[#347468]/10 pb-2">
                      {lesson.subItems.map((item) => (
                        <div key={item.id} className="flex justify-between items-center px-6 py-2.5 hover:bg-[#347468]/20 transition-colors">
                          <div className="flex items-center gap-2">
                            <IconWrapper icon={getIcon(item.type)} size={16} className="text-gray-700" />
                            <span className="text-xs font-medium text-gray-800 capitalize">{item.title}</span>
                          </div>
                          {item.duration && (
                             <span className="text-xs text-gray-600">{item.duration}</span>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};


// --- Performance Chart Component ---
const PerformanceChart = () => {
  const performanceData = [
    { label: "Critical Thinking & Problem Solving", value: 50, color: "bg-[#9F9FF8]" },
    { label: "Presentation and Communication", value: 88, color: "bg-[#96E2D6]" },
    { label: "Collaboration and Team work", value: 62, color: "bg-black" }, // Using black as per example
    { label: "Subject Knowledge", value: 100, color: "bg-[#92BFFF]" },
    { label: "Strategic Thinking", value: 38, color: "bg-[#AEC7ED]" },
    { label: "Innovation", value: 75, color: "bg-[#94E9B8]" },
    { label: "Ethical Decision making", value: 75, color: "bg-[#9F9FF8]" },
    { label: "Sustainability Quotient", value: 75, color: "bg-[#96E2D6]" },
    { label: "Experiential, Social, Cultural, Awareness", value: 75, color: "bg-black" },
    { label: "Emotional Intel. & Adaptability", value: 75, color: "bg-[#92BFFF]" },
  ];

  const maxValue = 100; // Assuming percentage

  return (
    <div className="bg-[#F9F9FA] p-4 sm:p-6 rounded-xl w-full overflow-x-auto">
      <h3 className="text-sm font-semibold text-gray-800 mb-4">Key Performances</h3>
      <div className="flex gap-2 sm:gap-4 min-w-[700px]"> {/* Ensure min-width for smaller screens */}
        {/* Y-Axis Labels */}
        <div className="flex flex-col justify-between text-right text-xs text-gray-500 pr-2 flex-shrink-0 w-10">
          <span>100%</span>
          <span>75%</span>
          <span>50%</span>
          <span>25%</span>
          <span>0%</span>
        </div>

        {/* Chart Bars and X-Axis Labels */}
        <div className="flex-grow border-l border-gray-300 pl-2 sm:pl-4">
          <div className="h-48 sm:h-56 relative flex justify-around items-end gap-1 sm:gap-2"> {/* Chart Area */}
            {performanceData.map((item, index) => (
              <div key={index} className="flex flex-col items-center h-full w-full group pt-4"> {/* Added pt-4 for tooltip space */}
                 {/* Tooltip (Hidden by default, shown on hover) */}
                 <div className="absolute -top-2 mb-1 hidden group-hover:block px-2 py-1 bg-black/80 text-white text-xs rounded whitespace-nowrap z-10">
                    {`${item.label}: ${item.value}%`}
                 </div>
                 {/* Bar */}
                <div
                  className={`w-5 sm:w-7 rounded-t-md ${item.color} transition-all duration-300 ease-out hover:opacity-80`}
                  style={{ height: `${item.value}%` }}
                  title={`${item.label}: ${item.value}%`} // Basic tooltip for accessibility
                ></div>
              </div>
            ))}
          </div>
          {/* X-Axis Labels */}
          <div className="flex justify-around items-start gap-1 sm:gap-2 mt-2 border-t border-gray-300 pt-2">
            {performanceData.map((item, index) => (
              <div key={index} className="text-[8px] sm:text-[10px] text-center text-gray-700 font-medium w-full leading-tight px-0.5">
                {/* Use dangerouslySetInnerHTML for <br/> tag or split string */}
                {item.label.split('<br/>').map((line, i) => <React.Fragment key={i}>{line}{i < item.label.split('<br/>').length - 1 && <br/>}</React.Fragment>)}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};


// --- Performance Section Tabs ---
const PerformanceTabs = () => {
  const [activeTab, setActiveTab] = useState('Performance');

  return (
    <div className="bg-gradient-to-r from-[#386C8D] via-[#036664] to-[#0E3D35] p-3 sm:p-4 rounded-xl text-white">
      <div className="flex items-center gap-2 sm:gap-4">
        {['Instructor', 'Overview', 'Performance'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            style={{
              borderRadius : '12px',
            }}
            className={`px-3 py-1.5 rounded-[16px]  text-sm sm:text-base font-medium transition-colors ${
              activeTab === tab
                ? 'bg-white/10 border border-white/40 font-semibold'
                : 'hover:bg-white/5'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>
    </div>
  );
};

// --- Main App Component ---
const App = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth0();
  
  // State for course details and data
  const [courseDetails, setCourseDetails] = useState<CourseDetailsType | null>(null);
  const [performanceData, setPerformanceData] = useState<PerformanceData[] | null>(null);
  const [instructorDetails, setInstructorDetails] = useState<InstructorDetails | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('Overview');
  const [expandedModuleId, setExpandedModuleId] = useState<string | null>(null);
  const [expandedLessonIds, setExpandedLessonIds] = useState<string[]>([]);
  const [selectedSubItem, setSelectedSubItem] = useState<{moduleId: string, lessonId: string, subItemId: string} | null>(null);
  const [isCurrentItemBookmarked, setIsCurrentItemBookmarked] = useState(false);
  const [isTopicBookmarked, setIsTopicBookmarked] = useState(false);
  const [subTopicsMap, setSubTopicsMap] = useState<Record<string, SubItem[]>>({});
  const [loadingSubTopics, setLoadingSubTopics] = useState<Record<string, boolean>>({});
  const { getAccessTokenSilently } = useAuth0();
  const [token, setToken] = useState<string | null>(null);
  const [isRightPanelVisible, setIsRightPanelVisible] = useState(true);
  
  // Get parameters from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const courseId = queryParams.get('courseId');
  const topicIdFromUrl = queryParams.get('topicId');
  const chapterIdFromUrl = queryParams.get('chapterId');

  // Log URL parameters for debugging
  console.log('URL Parameters:', {
    courseId,
    topicId: topicIdFromUrl,
    chapterId: chapterIdFromUrl,
    fullUrl: location.search
  });

  // If userId is needed for performance data, get it from Auth0 user object
  const userId = user?.sub;

  // For classroom ID, get from URL or use default
  const classroomId = queryParams.get('classroomId') || 'default-classroom';

  // State for bookmarks
  const [isBookmarkCollectionDialogOpen, setIsBookmarkCollectionDialogOpen] = useState(false);
  
  // State for notes
  const [isNotesDialogOpen, setIsNotesDialogOpen] = useState(false);
  const [chatWindowOpen, setChatWindowOpen] = useState(false);
  const [startLiveSession, setStartLiveSession] = useState(false);

  // State for quiz handling
  const [selectedQuiz, setSelectedQuiz] = useState<any | null>(null);
  const [quizData, setQuizData] = useState<any | null>(null);
  const [isQuizLoading, setIsQuizLoading] = useState(false);

  const { data: liveSessionToken, isLoading: isLoadingLiveSession } = useGetClassRoomTokenQuery(
    { classroomId: classroomId || '', displayName: user?.name || user?.email || 'Student' },
    { skip: !startLiveSession || !classroomId }
  );

  // API hook for checking bookmarks
  const [getBookmarks] = useLazyGetBookmarksQuery();

  useEffect(() => {
  // Set the token when the component mounts
  const setTokenFromAut0 = async () => {
    try {
      const token = await getAccessTokenSilently();
      console.log('Token:', token);
      setAuthToken(token);
      setToken(token);
    } catch (error) {
      console.error('Error getting authentication token:', error);
    }
  };
  
  setTokenFromAut0();
}, [getAccessTokenSilently]);
  
  // Fetch course details on component mount
  useEffect(() => {
    const fetchData = async () => {
      if (!courseId || !token) {
        // setError('Course ID is missing in URL parameters');
        // setLoading(false);
        return;
      }

      try {
        setLoading(true);
        
        // Fetch course details
        const details = await courseDetailService.getCourseDetails(courseId);
        setCourseDetails(details);

        // Store classroomId for this course in localStorage for bookmark navigation
        if (classroomId && classroomId !== 'default-classroom') {
          localStorage.setItem(`classroomId_${courseId}`, classroomId);
        }
        
        // Set first module as expanded initially
        if (details.modules.length > 0) {
          setExpandedModuleId(details.modules[0].id);
        }
        
        // Fetch instructor details
        const instructor = await courseDetailService.getInstructorDetails(classroomId);
        setInstructorDetails(instructor);
        
        // Fetch performance data if userId is available
        if (userId) {
          const performance = await courseDetailService.getPerformanceData(userId, "");
          setPerformanceData(performance);
        }
        
        // Fetch all subtopics for all lessons in the course
        await fetchAllSubTopics(details.modules);

        setLoading(false);

      } catch (err) {
        console.error('Error loading course data:', err);
        setError('Failed to load course data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, [courseId, userId, classroomId, token]);

  useEffect(() => {
    console.log('liveSessionToken:', liveSessionToken);
    if (liveSessionToken && classroomId) {
      console.log('liveSessionToken:', liveSessionToken);
      navigate(`/live-session/${classroomId}`);
    }
  }, [liveSessionToken, classroomId, startLiveSession, navigate]);

  // Separate useEffect to handle URL parameter changes for topic selection
  useEffect(() => {
    const handleUrlParameterNavigation = async () => {
      // Only proceed if course details are loaded
      if (!courseDetails || loading) {
        return;
      }

      const currentParams = new URLSearchParams(location.search);
      const topicId = currentParams.get('topicId');
      const chapterId = currentParams.get('chapterId');

      console.log('URL parameter navigation check:', { topicId, chapterId, courseDetails: !!courseDetails });

      if (topicId) {
        console.log('Auto-selecting topic from URL:', topicId);

        // Find the module and lesson that contains this topic
        let topicFound = false;
        for (const module of courseDetails.modules) {
          for (const lesson of module.lessons) {
            if (lesson.id === topicId) {
              console.log('Found topic in module:', module.title, 'lesson:', lesson.title);

              // Expand the module
              setExpandedModuleId(module.id);

              // Expand the lesson
              setExpandedLessonIds(prev => {
                if (!prev.includes(lesson.id)) {
                  return [...prev, lesson.id];
                }
                return prev;
              });

              // Fetch subtopics for this lesson if not already loaded
              if (!subTopicsMap[lesson.id]) {
                await fetchSubTopics(lesson.id);
              }

              // Wait a bit for subtopics to load, then select the first one
              setTimeout(async () => {
                const subTopics = subTopicsMap[lesson.id] || await courseDetailService.getSubtopics(lesson.id);
                if (subTopics && subTopics.length > 0) {
                  console.log('Auto-selecting first subtopic:', subTopics[0]);
                  await handleSubItemSelect(module.id, lesson.id, subTopics[0].id);
                  // Check bookmark status for this topic
                  await checkIfTopicIsBookmarked(lesson.id);
                }
              }, 500);

              topicFound = true;
              break;
            }
          }
          if (topicFound) break;
        }

        if (!topicFound) {
          console.log('Topic not found:', topicId);
        }
      } else if (chapterId) {
        console.log('Auto-expanding chapter from URL:', chapterId);
        // Just expand the specified module/chapter
        setExpandedModuleId(chapterId);
      }
    };

    handleUrlParameterNavigation();
  }, [location.search, courseDetails, loading, subTopicsMap]); // Watch for URL changes and course details

  // Function to toggle module expansion
  const toggleModule = (moduleId: string) => {
    setExpandedModuleId(prevId => (prevId === moduleId ? null : moduleId));
  };

  // Function to toggle lesson expansion
  const toggleLesson = (lessonId: string) => {
    setExpandedLessonIds(prev => {
      if (prev.includes(lessonId)) {
        return prev.filter(id => id !== lessonId);
      } else {
        return [...prev, lessonId];
      }
    });
  };

  // Function to fetch subtopics for a lesson (topic)
  const fetchSubTopics = async (topicId: string) => {
    if (loadingSubTopics[topicId] || subTopicsMap[topicId]) {
      return; // Already loading or loaded
    }

    try {
      setLoadingSubTopics(prev => ({ ...prev, [topicId]: true }));
      const subTopics = await courseDetailService.getSubtopics(topicId);

      // Deduplicate subtopics by ID to prevent duplicates in the sidebar
      const uniqueSubtopics = subTopics.filter((item, index, self) =>
        index === self.findIndex(t => t.id === item.id)
      );

      console.log(`Individual fetch for topic ${topicId}: ${subTopics.length} subtopics, deduplicated to ${uniqueSubtopics.length}`);
      setSubTopicsMap(prev => ({ ...prev, [topicId]: uniqueSubtopics }));
    } catch (err) {
      console.error(`Error fetching subtopics for topic ${topicId}:`, err);
    } finally {
      setLoadingSubTopics(prev => ({ ...prev, [topicId]: false }));
    }
  };

  // Function to fetch all subtopics for all lessons
  const fetchAllSubTopics = async (modules: Module[]) => {
    if (!modules || modules.length === 0) return;
    
    try {
      // Collect all lesson IDs from all modules
      const allLessonIds: string[] = [];
      modules.forEach(module => {
        module.lessons.forEach(lesson => {
          allLessonIds.push(lesson.id);
        });
      });

      // Mark all lessons as loading
      const loadingMap: Record<string, boolean> = {};
      allLessonIds.forEach(id => {
        loadingMap[id] = true;
      });
      setLoadingSubTopics(prev => ({ ...prev, ...loadingMap }));

      // Fetch subtopics for all lessons in parallel
      const subtopicPromises = allLessonIds.map(async (lessonId) => {
        try {
          return { 
            lessonId, 
            subtopics: await courseDetailService.getSubtopics(lessonId) 
          };
        } catch (error) {
          console.error(`Error fetching subtopics for lesson ${lessonId}:`, error);
          return { lessonId, subtopics: [] };
        }
      });

      const results = await Promise.all(subtopicPromises);
      
      // Update the subtopics map with all fetched data
      const newSubTopicsMap: Record<string, SubItem[]> = {};
      results.forEach(result => {
        // Deduplicate subtopics by ID to prevent duplicates in the sidebar
        const uniqueSubtopics = result.subtopics.filter((item, index, self) =>
          index === self.findIndex(t => t.id === item.id)
        );

        console.log(`Lesson ${result.lessonId}: ${result.subtopics.length} subtopics, deduplicated to ${uniqueSubtopics.length}`);
        newSubTopicsMap[result.lessonId] = uniqueSubtopics;
      });

      console.log(`new subtopics map:`, newSubTopicsMap);
      setSubTopicsMap(prev => ({ ...prev, ...newSubTopicsMap }));
      // Mark all lessons as loading
      const loadingMapFalse: Record<string, boolean> = {};
      allLessonIds.forEach(id => {
        loadingMap[id] = false;
      });
      setLoadingSubTopics(prev => ({ ...prev, ...loadingMapFalse })); // Set all loading states to false
    } catch (err) {
      console.error('Error fetching all subtopics:', err);
    } finally {
      // Mark all as not loading
      const notLoadingMap: Record<string, boolean> = {};
      Object.keys(loadingSubTopics).forEach(id => {
        notLoadingMap[id] = false;
      });
      setLoadingSubTopics(prev => ({ ...prev, ...notLoadingMap }));
    }
  };

  // Handle lesson click - toggle expansion (subtopics are prefetched)
  const handleLessonClick = (lessonId: string) => {
    toggleLesson(lessonId);
    // Subtopics are fetched initially, no need to fetch here
  };

  // Handle subitem selection
  const handleSubItemSelect = async (moduleId: string, lessonId: string, subItemId: string) => {
    console.log('SubItem selected:', { moduleId, lessonId, subItemId });
    setSelectedSubItem({ moduleId, lessonId, subItemId });

    // Check if this topic is bookmarked
    await checkIfTopicIsBookmarked(lessonId);

    // Find the selected item to check if it's a quiz
    const selectedItem = subTopicsMap[lessonId]?.find(item => item.id === subItemId);
    console.log('Selected item:', selectedItem);
    console.log('Content type detection:', {
      type: selectedItem?.type,
      quizId: selectedItem?.quizId,
      contentUrl: selectedItem?.contentUrl,
      isQuiz: selectedItem && (selectedItem.type === 'assessment' || selectedItem.type === 'quiz' || selectedItem.quizId),
      isDocument: selectedItem?.type === 'document'
    });

    // Check if this item has an assignment or is a legacy quiz
    const hasAssignment = selectedItem && selectedItem.assignment;
    const hasLegacyQuiz = selectedItem && (selectedItem.type === 'assessment' || selectedItem.type === 'quiz' || selectedItem.quizId);

    if (hasAssignment || hasLegacyQuiz) {
      console.log('Handling as assignment/assessment:', {
        hasAssignment,
        assignmentType: selectedItem?.assignment?.type,
        hasLegacyQuiz,
        quizId: selectedItem?.quizId || selectedItem?.assignment?.quizId
      });
      // Handle as assignment/assessment
      await handleAssessmentSelect(selectedItem);
    } else {
      console.log('Handling as regular content');
      // Reset quiz state for non-assignment items
      setSelectedQuiz(null);
      setQuizData(null);
      setIsQuizLoading(false);
    }
  };
  
  // Handler for bookmark button click - opens collection selection dialog
  const handleBookmarkClick = async () => {
    if (!user?.sub) {
      console.error('User ID not available');
      return;
    }

    if (!selectedSubItem) {
      console.error('No subtopic selected');
      return;
    }

    // Debug: Log the selected item data
    console.log('Selected item for bookmark:', selectedSubItem);
    console.log('Course details:', courseDetails);
    console.log('SubTopics map:', subTopicsMap);

    // Open the bookmark collection selection dialog
    setIsBookmarkCollectionDialogOpen(true);
  };

  // Function to check if current topic is bookmarked
  const checkIfTopicIsBookmarked = async (topicId: string) => {
    try {
      const { data: bookmarksData } = await getBookmarks({});
      if (bookmarksData?.resultObject) {
        const isBookmarked = bookmarksData.resultObject.some((bookmark: any) =>
          bookmark.topicId === topicId
        );
        console.log(`Topic ${topicId} bookmark status:`, isBookmarked);
        setIsCurrentItemBookmarked(isBookmarked);
        setIsTopicBookmarked(isBookmarked);
        return isBookmarked;
      }
    } catch (error) {
      console.error('Error checking bookmark status:', error);
    }
    return false;
  };

  // Handler for when bookmark is successfully created
  const handleBookmarkCreated = () => {
    setIsCurrentItemBookmarked(true);
    setIsTopicBookmarked(true);
    console.log('Bookmark created successfully');
  };



  // Handler for notes button click
  const handleNotesClick = () => {
    setIsNotesDialogOpen(true);
  };

  // Handler for assignment/assessment selection
  const handleAssessmentSelect = async (assessment: any) => {
    console.log('Assignment/Assessment selected:', assessment);
    setSelectedQuiz(assessment);

    // Check assignment type
    const assignmentType = assessment.assignment?.type;
    const isQuizAssignment = assignmentType === 1 || assessment.quizId;
    const isSelfReflectiveJournal = assignmentType === 4;

    // Handle quiz assignments (type 1) or self-reflective journals (type 4) that have quizId
    if (isQuizAssignment || (isSelfReflectiveJournal && assessment.assignment?.quizId)) {
      setIsQuizLoading(true);
      setQuizData(null);

      try {
        console.log('Fetching quiz data for subtopic:', assessment.id);

        // Get quiz ID from assignment or legacy field
        let quizId = assessment.assignment?.quizId || assessment.quizId;

        // If no direct quiz ID, try fetching from subtopic
        if (!quizId) {
          const { quizId: fetchedQuizId, assignment } = await courseDetailService.fetchQuizDataFromSubtopic(assessment.id);
          quizId = fetchedQuizId;
          console.log('Quiz data fetched from subtopic:', { quizId, assignment });
        }

        if (quizId) {
          console.log('Fetching complete quiz data for quiz ID:', quizId);
          // Fetch the complete quiz with questions
          const fullQuizData = await courseDetailService.fetchQuizWithQuestions(quizId);
          console.log('Full quiz data:', fullQuizData);

          if (fullQuizData) {
            // Create quiz object in the format expected by QuizRenderer with pre-fetched data
            const quizDataToSet = {
              id: quizId,
              title: assessment.assignment?.title || assessment.subTopicName || assessment.title || 'Quiz',
              questionSet: fullQuizData.questionSet // Include the pre-fetched questions
            };
            console.log('Setting quiz data:', quizDataToSet);
            setQuizData(quizDataToSet);
          } else {
            console.error('Failed to fetch quiz data');
          }
        } else {
          console.error('No quiz ID found for this assessment');
        }
      } catch (error) {
        console.error('Error fetching quiz data:', error);
      } finally {
        setIsQuizLoading(false);
      }
    } else {
      // For non-quiz assignments, we don't need to fetch quiz data
      console.log('Non-quiz assignment selected:', {
        type: assessment.assignment?.type,
        title: assessment.assignment?.title
      });
      setIsQuizLoading(false);
      setQuizData(null);
    }
  };

  // Handler for quiz completion
  const handleQuizCompleted = () => {
    console.log('Quiz completed successfully');
    // You can add any post-completion logic here
    // For example, refresh the course progress, show a success message, etc.
  };

  const handleAiMentorClick = () => {
    // Logic for AI Mentor click
    console.log('AI Mentor clicked');
    setChatWindowOpen(true);
  }

  // Handle resuming course
  const handleResumeCourse = () => {
    if (!courseDetails) return;
    
    // Find the first incomplete item
    const nextItem = courseDetailService.findNextItemToResume(courseDetails.modules);
    if (nextItem) {
      // Either navigate to that item or set the UI to show it
      setExpandedModuleId(nextItem.moduleId);
      // Expand the lesson if it's not already expanded
      if (!expandedLessonIds.includes(nextItem.lessonId)) {
        setExpandedLessonIds(prev => [...prev, nextItem.lessonId]);
        // No need to fetch subtopics here, they are prefetched
      }
      // Select the specific subitem to resume
      setSelectedSubItem({
        moduleId: nextItem.moduleId,
        lessonId: nextItem.lessonId,
        subItemId: nextItem.itemId
      });
    }
  };

  // Handle back navigation
  const handleBackClick = () => {
    if (selectedSubItem) {
      // If a subtopic is selected, go back to course overview
      setSelectedSubItem(null);
      setSelectedQuiz(null);
      setQuizData(null);
      setIsQuizLoading(false);

      // Update URL to remove topic parameters
      const currentParams = new URLSearchParams(location.search);
      currentParams.delete('topicId');
      currentParams.delete('chapterId');

      // Navigate to course overview with updated URL
      const newSearch = currentParams.toString();
      navigate(`${location.pathname}${newSearch ? `?${newSearch}` : ''}`, { replace: true });
    } else {
      // If no subtopic is selected, navigate back to previous page (dashboard)
      navigate(-1);
    }
  };

  const toggleRightPanel = () => setIsRightPanelVisible(!isRightPanelVisible);

  if (loading) {
    return <div className="flex justify-center items-center min-h-screen">
      <div className="text-xl">Loading course details...</div>
    </div>;
  }

  if (error) {
    return <div className="flex justify-center items-center min-h-screen">
      <div className="text-xl text-red-500">{error}</div>
    </div>;
  }

  if (!courseDetails) {
    return <div className="flex justify-center items-center min-h-screen">
      <div className="text-xl">Course not found</div>
    </div>;
  }

  const handleOpenNotesSidebar = () => {
    setIsNotesDialogOpen(true);
  };

  return (
    <div className="min-h-screen bg-white font-sans text-gray-900">
      <TopBar 
        onBackClick={handleBackClick} 
        onBookmarkClick={handleBookmarkClick} 
        showBookmarkButton={selectedSubItem !== null}
        isBookmarked={isCurrentItemBookmarked}
        onNotesClick={handleOpenNotesSidebar} 
        showNotesButton={selectedSubItem !== null} // Only show Notes button when a subtopic is selected (same as bookmark)
      />

      {/* Main Content Area */}
      <main className="container w-full">
        <div className="flex flex-col lg:flex-row p-0 relative">

          {/* Left Column: Takes full width if right panel is hidden or if an item is selected and right panel is hidden */}
          <div className={`flex flex-col gap-10 transition-all duration-300 ease-in-out \
                         ${isRightPanelVisible ? 'w-full lg:w-2/3 xl:w-6/8' : 'w-full'}`}>
            {!selectedSubItem && (
            <>
             {/* Course Details & Stats Section */}
             <section className="p-6 md:p-8 border-b-2 border-gray-200 shadow-sm bg-white">
                <div className="flex flex-col md:flex-row justify-between gap-8 md:gap-12">
                    <div className="flex flex-col gap-6 md:gap-8 flex-1">
                      <div className="flex flex-col gap-2">
                        <h1 className="text-2xl md:text-2xl font-semibold text-gray-800 capitalize">
                          {courseDetails.title}
                        </h1>
                       
                      </div>

                      {/* Tiles Row - Equal width and height for all tiles */}
                      <div className="flex justify-start gap-3 mt-1">
                        <div className="flex flex-col items-center justify-between bg-[#F9F9FA] border border-gray-200 rounded-lg px-2 py-1 w-[90px] h-[70px]">
                          <IconWrapper icon={Clock} className="text-[#347468]" size={16} />
                          <span className="text-[12px] text-gray-500">Duration</span>
                          <span className="text-xs font-semibold text-gray-800">{courseDetails.duration == '' ? "4h" : courseDetails.duration}</span>
                        </div>
                        <div className="flex flex-col items-center justify-between bg-[#F9F9FA] border border-gray-200 rounded-lg px-2 py-1 w-[90px] h-[70px]">
                          <IconWrapper icon={Layers3} className="text-[#347468]" size={16} />
                          <span className="text-[12px] text-gray-500">Topics</span>
                          <span className="text-xs font-semibold text-gray-800">{courseDetails.topicsCount}</span>
                        </div>
                        <div className="flex flex-col items-center justify-between bg-[#F9F9FA] border border-gray-200 rounded-lg px-2 py-1 w-[90px] h-[70px]">
                          <IconWrapper icon={FileText} className="text-[#347468]" size={16} />
                          <span className="text-[12px] text-gray-500">Assessments</span>
                          <span className="text-xs font-semibold text-gray-800">{courseDetails.assessmentsCount}</span>
                        </div>
                      </div>

                      {/* Progress Bar - Reverted to original logic using courseDetails.modules */}
                      {(() => {
                        const totalModules = courseDetails.modules.length;
                        const completedModules = courseDetails.modules.filter((m: any) => m.completed).length; // Assuming Module type has a 'completed' property
                        const percent = totalModules > 0 ? Math.round((completedModules / totalModules) * 100) : 0;
                        return (
                          <div className="flex flex-col gap-3 mb-2">
                            <div className="flex justify-between items-center text-sm">
                              <span className="text-gray-700">
                                <span className="font-semibold text-gray-800">{completedModules}</span>/{totalModules} modules
                                <span className="text-gray-400 mx-2">|</span>
                                <span className="font-semibold text-gray-800">{percent}%</span>
                              </span>
                            </div>
                            <div className="w-full h-4 bg-[#347468]/20 rounded-full overflow-hidden">
                              <div className="h-full bg-[#347468] rounded-full" style={{ width: `${percent}%` }}></div>
                            </div>
                          </div>
                        );
                      })()}

                      <button 
                        className="bg-[#347468] text-white px-3 py-2 rounded-xl self-start flex items-center gap-2 hover:bg-[#2a5d54] transition-colors text-base font-semibold capitalize"
                        onClick={handleResumeCourse}
                      >
                        Resume Course
                      </button>
                    </div>
                    <div className="flex flex-col justify-center items-center space-y-4 md:min-w-[120px] md:mt-0 md:pl-4">
                          <MySpaceDialog />
                          <NotesDialog />
                          <BookmarksDialog />
                        </div>
                </div>
             </section>

            {/* Performance Section - This should be inside the !selectedSubItem block */}
            <section className="flex flex-col gap-4 p-2 ">
              {/* Tabs */}
              <div style={{
                background: 'linear-gradient(90.2deg, #386C8D 0%, #036664 12.17%, #0E3D35 86.96%, #060B0B 105.19%)',
                
              }} className=" p-2 rounded-xl text-white">
                <div className="flex items-center gap-2 sm:gap-4">
                  {['Instructor', 'Overview', 'News Feed'].map((tab) => (
                    <button
                      key={tab}
                      onClick={() => setActiveTab(tab)}
                      style={{
                        borderRadius : '12px',
                      }}
                      className={`px-3 py-1.5 rounded-md text-sm sm:text-base font-medium transition-colors ${                        activeTab === tab
                          ? 'bg-white/10 border rounded-[12px] border-white/40 font-semibold'
                          : 'hover:bg-white/5 rounded-[12px]'
                      }`}
                    >
                      {tab}
                    </button>
                  ))}
                </div>
              </div>

              {/* Tab Content */}
              {activeTab === 'News Feed' && (
                <div className="bg-[#F9F9FA] p-4 sm:p-6 rounded-xl w-full overflow-x-auto text-gray-500 text-center">News Feed coming soon...</div>
              )}

                {activeTab === 'Instructor' && instructorDetails && (
                <div className="bg-white p-6 rounded-xl border border-primary shadow-sm">
                  <div className="flex flex-col md:flex-row gap-8">
                  <div className="w-40 h-64 flex-shrink-0">
                    {instructorDetails.avatar ? (
                    <img 
                      src={instructorDetails.avatar} 
                      alt={instructorDetails.name} 
                      className="w-full h-full object-cover rounded-lg"
                    />
                    ) : (
                    <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                      <User size={48} className="text-gray-400" />
                    </div>
                    )}
                  </div>
                  
                  <div className="flex flex-col gap-4">
                    <div>
                    <h3 className="text-2xl font-semibold text-gray-800">{instructorDetails.name}</h3>
                    <p className="text-lg text-[#347468]">{instructorDetails.role || 'Instructor'}</p>
                    </div>
                    
                    {instructorDetails.bio ? (
                    <p className="text-gray-700 leading-relaxed">
                      {instructorDetails.bio}
                    </p>
                    ) : (
                    <p className="text-gray-700 leading-relaxed">
                      {instructorDetails.name} is an experienced instructor passionate about teaching and dedicated to helping students master the subject. With years of teaching experience, they bring a dynamic and interactive approach to their courses, emphasizing clear explanations, practical applications, and a supportive learning environment.
                    </p>
                    )}
                    
                    {instructorDetails.email && (
                    <p className="text-sm text-gray-600 mt-2">
                      <span className="font-medium">Contact: </span>{instructorDetails.email}
                    </p>
                    )}
                  </div>
                  </div>
                </div>
                )}

              {activeTab === 'Overview' && (
                <div className="bg-[#F9F9FA] p-4 sm:p-6 rounded-xl">
                  <h3 className="text-lg font-semibold mb-4">Course Overview</h3>
                  <p className="text-gray-700">
                    {courseDetails.title} provides comprehensive instruction on key concepts with 
                    {courseDetails.modules.length} modules and {courseDetails.totalLessons} lessons.
                    The course includes {courseDetails.assessmentsCount} assessments to test your knowledge.
                  </p>
                </div>
              )}
            </section>
            </>
            )}

            {/* Enhanced Content Renderer: Takes full width if right panel is hidden */}
            { selectedSubItem?.subItemId &&
              <div className='p-6 md:p-8 border-1 border-gray-200 shadow-sm bg-white'>
                {(() => {
                  if (selectedQuiz) {
                    // Check assignment type to determine which renderer to use
                    const assignmentType = selectedQuiz.assignment?.type;
                    const quizType = quizData?.type || 'REGULAR';

                    // Handle different assignment types
                    if (assignmentType === 2) {
                      // Case Study Assignment
                      return (
                        <div className="assignment-content">
                          <CaseStudyRenderer
                            title={selectedQuiz.assignment?.title || 'Case Study'}
                            description={selectedQuiz.assignment?.assignmentText || 'Complete this case study assignment'}
                            assignmentId={selectedQuiz.id}
                            classroomId={classroomId}
                            courseId={courseId || undefined}
                            onCompleted={handleQuizCompleted}
                          />
                        </div>
                      );
                    } else if (assignmentType === 5) {
                      // Discussion Forum Assignment
                      return (
                        <div className="assignment-content">
                          <DiscussionForumRenderer
                            title={selectedQuiz.assignment?.title || 'Discussion Forum'}
                            description={selectedQuiz.assignment?.assignmentText || 'Participate in the discussion forum'}
                            assignmentId={selectedQuiz.id}
                            classroomId={classroomId}
                            courseId={courseId || undefined}
                            discussionId={selectedQuiz.assignment?.discussionId}
                            projectId={selectedQuiz.assignment?.projectId}
                            onCompleted={handleQuizCompleted}
                          />
                        </div>
                      );
                    } else if (assignmentType === 4) {
                      // Self Reflective Journal Assignment
                      console.log('Debug courseDetails for classroomId:', {
                        courseDetails,
                        classroomIdFromQuery: classroomId,
                        courseId,
                        selectedQuiz: selectedQuiz.id
                      });

                      return (
                        <div className="assignment-content">
                          <SelfReflectiveJournalRenderer
                            title={selectedQuiz.assignment?.title || 'Self Reflective Journal'}
                            description={selectedQuiz.assignment?.assignmentText || 'Complete your self-reflective journal'}
                            quizData={quizData}
                            courseId={courseId || undefined}
                            assignmentId={selectedQuiz.id}
                            classroomId={classroomId}
                            onCompleted={handleQuizCompleted}
                          />
                        </div>
                      );
                    } else if ((assignmentType === 1 || !assignmentType) && quizData) {
                      // Quiz Assignment or legacy quiz
                      if (quizType === 'SELF_REFLECTIVE_JOURNAL') {
                        console.log('Debug courseDetails for classroomId (quiz type):', {
                          courseDetails,
                          classroomIdFromQuery: classroomId,
                          courseId,
                          selectedQuiz: selectedQuiz.id
                        });

                        return (
                          <div className="assignment-content">
                            <SelfReflectiveJournalRenderer
                              title={selectedQuiz.assignment?.title || 'Self Reflective Journal'}
                              description={selectedQuiz.assignment?.assignmentText || 'Complete your self-reflective journal'}
                              quizData={quizData}
                              courseId={courseId || undefined}
                              assignmentId={selectedQuiz.id}
                              classroomId={classroomId}
                              onCompleted={handleQuizCompleted}
                            />
                          </div>
                        );
                      } else {
                        return (
                          <div className="quiz-content">
                            <QuizRenderer
                              quiz={quizData}
                              courseId={courseId || undefined}
                              onQuizSuccessfullyCompleted={handleQuizCompleted}
                              onQuizCancelled={() => {
                                // Navigate back to course overview by clearing all selection state
                                console.log('Quiz cancelled - navigating to course overview');
                                console.log('Current selectedSubItem:', selectedSubItem);
                                console.log('Current selectedQuiz:', selectedQuiz);

                                // Clear all selection state to go back to course overview
                                setSelectedSubItem(null);
                                setSelectedQuiz(null);
                                setQuizData(null);
                                setIsQuizLoading(false);

                                // Update URL to remove topic parameters
                                const currentParams = new URLSearchParams(location.search);
                                currentParams.delete('topicId');
                                currentParams.delete('chapterId');
                                const newUrl = `${location.pathname}?${currentParams.toString()}`;
                                navigate(newUrl, { replace: true });

                                console.log('Navigated back to course overview');
                              }}
                            />
                          </div>
                        );
                      }
                    }
                  } else if (isQuizLoading) {
                    console.log('Rendering quiz loading state');
                    return (
                      // Show loading state for quiz
                      <div className="flex items-center justify-center min-h-[400px]">
                        <div className="h-8 w-8 rounded-full border-4 border-emerald-500 border-t-transparent animate-spin mb-4"></div>
                        <p className="text-slate-500 ml-4">Loading quiz...</p>
                      </div>
                    );
                  } else {
                    // Check if this is a document type
                    const selectedItem = subTopicsMap[selectedSubItem.lessonId]?.find(item => item.id === selectedSubItem.subItemId);

                    if (selectedItem?.type === 'document' && selectedItem.contentUrl) {
                      console.log('Rendering document content:', selectedItem.contentUrl);

                      return (
                        <div className="w-full h-full bg-white relative overflow-hidden">
                          <object
                            data={`${selectedItem.contentUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`}
                            type="application/pdf"
                            className="w-full h-full"
                            style={{
                              height: 'calc(100vh - 120px)',
                              minHeight: '800px',
                              border: 'none',
                              outline: 'none'
                            }}
                            onContextMenu={(e) => e.preventDefault()}
                          >
                            <iframe
                              src={`${selectedItem.contentUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`}
                              className="w-full h-full border-0"
                              title="Document Content"
                              style={{
                                height: 'calc(100vh - 120px)',
                                minHeight: '800px',
                                border: 'none',
                                outline: 'none'
                              }}
                              onContextMenu={(e) => e.preventDefault()}
                              sandbox="allow-same-origin allow-scripts"
                              scrolling="auto"
                              frameBorder="0"
                            />
                          </object>
                          {/* CSS to hide PDF controls */}
                          <style dangerouslySetInnerHTML={{
                            __html: `
                              object[type="application/pdf"] {
                                -webkit-appearance: none;
                                -moz-appearance: none;
                                appearance: none;
                              }
                              iframe {
                                -webkit-appearance: none;
                                -moz-appearance: none;
                                appearance: none;
                              }
                            `
                          }} />
                        </div>
                      );
                    } else {
                      console.log('Rendering regular content');
                      return (
                        // Render regular content
                        <EnhancedContentRenderer
                          defaultTopicId={selectedSubItem.subItemId}
                          key={selectedSubItem.subItemId}
                        />
                      );
                    }
                  }
                })()}
              </div>
            }
          </div>
          
          {/* Sidebar Toggle Button for ALL screens - positioned fixed */}
          <button
            onClick={toggleRightPanel}
            className={`fixed top-20 right-4 z-50 p-2 bg-gray-100 hover:bg-gray-200 rounded-md shadow-md`}
            title={isRightPanelVisible ? "Hide Panel" : "Show Panel"}
          >
            <IconWrapper icon={isRightPanelVisible ? PanelRightClose : PanelRightOpen} size={20} />
          </button>

          {/* Right Column (Sidebar) */}
          {isRightPanelVisible && (
          <aside className="w-full lg:w-1/3 xl:w-2/8 flex flex-col bg-white p-4 gap-4 border-l border-gray-200 transition-all duration-300 ease-in-out">
            <div className='border-b flex-col border-gray-200 pb-4 flex items-start'> 
              <div className="w-full flex justify-between items-center">
                <h2 className="text-xl font-semibold text-gray-800 capitalize">Course Content</h2>
                {/* REMOVED the toggle button from here as it's now universal and fixed */}
              </div>
              <div className="flex items-start flex-row mt-2">
                 {/* Join Live Lecture Button moved here */}
                 <button
                    className="bg-gradient-to-r from-[#386C8D] via-[#036664] to-[#0E3D35] text-white px-3 py-2 rounded-xl shadow-md flex items-center gap-2 hover:shadow-lg transition-all text-sm disabled:opacity-60"
                    onClick={() => setStartLiveSession(true)}
                    disabled={isLoadingLiveSession}
                  >
                    <Video size={18} />
                    {isLoadingLiveSession ? 'Joining...' : 'Join Live Lecture'}
                  </button>
              </div>
            </div>
            {/* Dynamic Course Content Sidebar */} 
            <div className="border-2 border-gray-200 overflow-hidden w-full max-w-sm xl:max-w-md">
              {courseDetails.modules.map((module) => (
                <div key={module.id} className="border-b-2 border-gray-200 last:border-b-0">
                  {/* Module Header */}
                  <button
                    onClick={() => toggleModule(module.id)}
                    className="w-full flex justify-between items-center p-4 md:p-5 hover:bg-gray-50 transition-colors"
                  >
                    <span className="text-base font-semibold text-gray-800 capitalize">{module.title}</span>
                    <div className="flex items-center gap-3">
                      <span className="text-xs text-gray-600 capitalize">{module.duration}</span>
                      <div className={`p-1.5 border border-gray-700 rounded-[8px] transition-transform duration-300 ${expandedModuleId === module.id ? 'rotate-0' : 'rotate-180'}`}>
                        <IconWrapper icon={ChevronUp} size={16} className="text-[#347468]" />
                      </div>
                    </div>
                  </button>

                  {/* Module Content (Lessons & Sub-items) */}
                  {expandedModuleId === module.id && (
                    <div className="bg-white">
                      {module.lessons.map((lesson) => (
                        <div key={lesson.id} className="border-t border-gray-200">
                          {/* Lesson Header */}
                          <div 
                            className={`flex justify-between items-center p-4 pl-6 cursor-pointer
                              ${lesson.isActive ? 'bg-[#347468]/10' : ''} 
                              hover:bg-[#347468]/5 transition-colors`}
                            onClick={() => handleLessonClick(lesson.id)}
                          >
                            <span className="text-sm font-semibold text-gray-800 capitalize">{lesson.title}</span>
                            <div className={`p-1 transition-transform duration-300 ${expandedLessonIds.includes(lesson.id) ? 'rotate-0' : 'rotate-180'}`}>
                              <IconWrapper icon={ChevronUp} size={14} className="text-[#347468]" />
                            </div>
                          </div>

                          {/* Sub Items */}
                          {expandedLessonIds.includes(lesson.id) && (
                            <div className="bg-[#347468]/10 pb-2">
                              {
                              (
                                <>
                                  {subTopicsMap[lesson.id] && subTopicsMap[lesson.id].length > 0 ? (
                                    (() => {
                                      // Debug logging to see what's in the subtopics map
                                      console.log(`Rendering subtopics for lesson ${lesson.id}:`, {
                                        originalCount: subTopicsMap[lesson.id].length,
                                        items: subTopicsMap[lesson.id].map(item => ({ id: item.id, title: item.title, type: item.type }))
                                      });

                                      // First deduplicate by ID
                                      const uniqueById = subTopicsMap[lesson.id].filter((item, index, self) =>
                                        index === self.findIndex(t => t.id === item.id)
                                      );

                                      // Smart deduplication: prioritize content over quiz when both have the same title
                                      const smartDeduplication = uniqueById.filter((item, index, self) => {
                                        // If this is a quiz item, check if there's already a content item with the same title
                                        if (item.type === 'quiz') {
                                          const hasContentWithSameTitle = self.some(otherItem =>
                                            otherItem.title === item.title &&
                                            otherItem.type === 'text' &&
                                            otherItem.id !== item.id
                                          );

                                          // If there's already content with the same title, skip this quiz entry
                                          if (hasContentWithSameTitle) {
                                            console.log(`Skipping quiz entry "${item.title}" because content entry exists`);
                                            return false;
                                          }
                                        }

                                        // For all other cases, check if it's the first occurrence of this title+type combination
                                        return index === self.findIndex(t =>
                                          t.title === item.title &&
                                          t.type === item.type
                                        );
                                      });

                                      console.log(`After deduplication for lesson ${lesson.id}:`, {
                                        originalCount: subTopicsMap[lesson.id].length,
                                        uniqueById: uniqueById.length,
                                        smartDeduplication: smartDeduplication.length,
                                        finalItems: smartDeduplication.map((item: any) => ({ id: item.id, title: item.title, type: item.type }))
                                      });

                                      return smartDeduplication;
                                    })().map((item) => {
                                      // Check for assignment
                                      const hasAssignment = !!item.assignment;
                                      // Legacy quiz check for backward compatibility
                                      const hasLegacyQuiz = !hasAssignment && (item.type === 'assessment' || item.type === 'quiz' || item.quizId);

                                      // Debug logging for specific item
                                      if (item.id === '685b6fbba41609a1d53c72a2') {
                                        console.log('Debug item data:', {
                                          id: item.id,
                                          title: item.title,
                                          hasAssignment,
                                          assignment: item.assignment,
                                          hasLegacyQuiz,
                                          quizId: item.quizId
                                        });
                                      }

                                      return (
                                        <div
                                          key={item.id}
                                          className={`flex justify-between items-center px-6 py-2.5 cursor-pointer
                                            ${selectedSubItem && selectedSubItem.subItemId === item.id ? 'bg-[#347468]/30' : ''}
                                            hover:bg-[#347468]/20 transition-colors`}
                                          onClick={() => handleSubItemSelect(module.id, lesson.id, item.id)}
                                        >
                                          <div className="flex items-center gap-2 flex-grow">
                                            <IconWrapper
                                              icon={
                                                hasAssignment || hasLegacyQuiz ? Lightbulb :
                                                item.type === 'video' ? Video :
                                                item.type === 'scorm' ? PlayCircle :
                                                item.type === 'audio' ? FileAudio :
                                                item.type === 'html' ? Code :
                                                item.type === 'quiz' || item.type === 'assessment' ? Lightbulb :
                                                item.type === 'document' ? FileText :
                                                item.type === 'text' ? BookOpen :
                                                item.type === 'unknown' ? HelpCircle :
                                                BookOpen
                                              }
                                              size={16}
                                              className="text-gray-700"
                                            />
                                            <span className="text-xs font-medium text-gray-800 capitalize truncate">{item.title}</span>
                                          </div>
                                          {item.duration && (
                                            <span className="text-xs text-gray-600">{item.duration && item.duration !='N/A'}</span>
                                          )}
                                        </div>
                                      );
                                    })
                                  ) : (
                                    <div className="py-4 flex justify-center items-center">
                                      <span className="text-sm text-gray-600">No content available for this topic.</span>
                                    </div>
                                  )}
                                </>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </aside>
          )}

          {/* AI Mentor Button - Now positioned outside the sidebar conditional rendering block */}
          <button 
            className="fixed right-6 bottom-6 lg:right-16 lg:bottom-16 mt-4 bg-gradient-to-r from-[#386C8D] via-[#036664] to-[#060B0B] text-white px-4 py-2.5 rounded-xl shadow-lg flex items-center gap-3 hover:shadow-xl transition-all hover:scale-105 z-50"
            onClick={handleAiMentorClick}
          >
            <Icon iconNode={elephantFace} size={28} /> 
            <span className="text-base font-semibold capitalize">AI Mentor</span>
          </button>
        </div>
      </main>
      
      {/* Bookmark Collection Selection Dialog */}
      {selectedSubItem && (
        <BookmarkCollectionDialog
          open={isBookmarkCollectionDialogOpen}
          onOpenChange={setIsBookmarkCollectionDialogOpen}
          topicId={selectedSubItem.lessonId} // Use lessonId as topicId since lesson represents the topic
          topicName={courseDetails?.modules?.find(m => m.id === selectedSubItem.moduleId)?.lessons?.find(l => l.id === selectedSubItem.lessonId)?.title || 'Current Topic'}
          courseName={courseDetails?.title || 'Current Course'}
          chapterName={courseDetails?.modules?.find(m => m.id === selectedSubItem.moduleId)?.title || 'Current Chapter'}
          onBookmarkCreated={handleBookmarkCreated}
        />
      )}

      {/* NotesDialog now controlled externally */}
      <NotesDialog
        externalOpen={isNotesDialogOpen} 
        onExternalOpenChange={setIsNotesDialogOpen} 
        hideTriggerButton={true} 
      />

      <ChatWindow
        isVisible={chatWindowOpen}
        onClose={() => setChatWindowOpen(false)}
        context={JSON.stringify({
        text:`  Hi! I'm a student studying the following course: ${courseDetails?.title}.
          This is my progress so far: ${(() => { // Using original module calculation for chat context too
            const totalModules = courseDetails?.modules?.length || 0;
            const completedModules = courseDetails?.modules?.filter((m: any) => m.completed).length || 0;
            return `${completedModules}/${totalModules} modules completed`;
          })() }.
          i'm currently studying the topic: ${
          selectedSubItem && selectedSubItem.subItemId // Added null check
            ? subTopicsMap[selectedSubItem.lessonId]?.find(item => item.id === selectedSubItem.subItemId)?.title
            : 'No topic selected'
          }.`,
        })}
        />
        
      {/* REMOVED FLOATING ICONS */}
    </div>
  );
};

export default App;
