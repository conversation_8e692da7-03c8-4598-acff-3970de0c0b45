import React, { useState } from 'react';
import { useGetPendingModerationQuery, useApproveCommentMutation, useRejectCommentMutation, useGetModerationStatsQuery } from '../../services/discussionsAPIjs';
import { Card, CardContent, CardHeader, CardFooter } from '../ui/card';
import { Button } from '../ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Skeleton } from '../ui/skeleton';
import { CheckCircle, XCircle, AlertCircle, Info, MessageCircle, Users, Shield, Activity } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { 
  Dialog, 
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription 
} from '../ui/dialog';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "../ui/tabs";
import { Comment } from '../../services/discussionsAPIjs';
import { CustomPagination } from './CustomPagination';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import { Progress } from '../ui/progress';
import { Input } from '../ui/input';
import { useDiscussionForum } from '../../hooks/useDiscussionForum';
import { useLeaderboard } from '../../hooks/useLeaderboard';
import { toast } from '../../hooks/use-toast';

// Extended Comment interface that includes additional fields needed for moderation
interface ModerationComment extends Comment {}

interface UserScores {
  id: string;
  name: string;
  profilePicture?: string;
  commentCount: number;
  averageScore: number;
  lastActive: Date;
  totalEngagement: number;
  scoreBreakdown: {
    engagement: number;
    relevance: number;
    depthOfThought: number;
    evidence: number;
    peerInteraction: number;
  };
}

export function ModerationQueue() {
  const [activeTab, setActiveTab] = useState('moderation');
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  
  // Use the API hooks
  const { 
    data: moderationData, 
    isLoading: loadingModeration, 
    error: moderationError, 
    refetch: refetchModeration 
  } = useGetPendingModerationQuery({ page, limit: 10 });
  
  const { 
    data: statsData,
    isLoading: loadingStats
  } = useGetModerationStatsQuery({});
  
  const [approveComment, { isLoading: isApproving }] = useApproveCommentMutation();
  const [rejectComment, { isLoading: isRejecting }] = useRejectCommentMutation();
  
  // Get the discussion forum data
  const { 
    discussions, 
    userScores, 
    pagination: discussionPagination,
    isLoading: loadingDiscussions
  } = useDiscussionForum(page, 10);
  
  const [selectedComment, setSelectedComment] = useState<ModerationComment | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);

  const moderationItems = moderationData?.moderationItems || [];
  const pagination = moderationData?.pagination;

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleApprove = async (id: string) => {
    try {
      await approveComment(id).unwrap();
      toast({
        title: "Comment approved",
        description: "The comment has been approved and published.",
        variant: "default",
      });
    } catch (err) {
      toast({
        title: "Error approving comment",
        description: "An error occurred while approving the comment.",
        variant: "destructive",
      });
    }
  };

  const handleReject = async (id: string) => {
    try {
      await rejectComment(id).unwrap();
      toast({
        title: "Comment rejected",
        description: "The comment has been rejected and won't be published.",
        variant: "default",
      });
    } catch (err) {
      toast({
        title: "Error rejecting comment",
        description: "An error occurred while rejecting the comment.",
        variant: "destructive",
      });
    }
  };

  const handleShowDetails = (comment: ModerationComment) => {
    setSelectedComment(comment);
    setDetailsOpen(true);
  };

  const getInitials = (name?: string) => {
    if (!name) return '?';
    return name.charAt(0).toUpperCase();
  };

  const filterUsers = () => {
    if (!searchQuery.trim()) return userScores;
    return userScores.filter((user:any) => 
      user.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  // Calculate a color based on score
  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600';
    if (score >= 6) return 'text-amber-600';
    return 'text-red-600';
  };

  // Loading state
  if (loadingModeration && activeTab === 'moderation' && (!moderationItems || moderationItems.length === 0)) {
    return (
      <div className="space-y-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="moderation" className="flex items-center gap-2">
              <Shield className="h-4 w-4" /> Moderation
            </TabsTrigger>
            <TabsTrigger value="discussions" className="flex items-center gap-2">
              <MessageCircle className="h-4 w-4" /> Discussions
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" /> Users
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <h3 className="text-xl font-semibold">Pending Moderation</h3>
        {[1, 2, 3].map((i) => (
          <Card key={i} className="mb-4">
            <CardHeader className="flex flex-row space-x-4 pb-2 pt-4">
              <Skeleton className="w-10 h-10 rounded-full" />
              <div>
                <Skeleton className="h-4 w-32 mb-2" />
                <Skeleton className="h-3 w-20" />
              </div>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-8 w-24 mr-2" />
              <Skeleton className="h-8 w-24" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  if (moderationError && activeTab === 'moderation') {
    return (
      <Alert variant="destructive">
        <AlertDescription>{(moderationError as any)?.data?.message || 'An error occurred loading moderation data'}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6 p-4 md:p-6 lg:p-8 overflow-y-auto max-h-screen">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 mb-6">
          <TabsTrigger value="moderation" className="flex items-center gap-2">
            <Shield className="h-4 w-4" /> Moderation
          </TabsTrigger>
          <TabsTrigger value="discussions" className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4" /> Discussions
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" /> Users
          </TabsTrigger>
        </TabsList>
        
        {!loadingStats && statsData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Pending</p>
                    <h3 className="text-2xl font-bold text-amber-500">{statsData.counts.pending}</h3>
                  </div>
                  <div className="bg-amber-100 p-3 rounded-full">
                    <Activity className="h-5 w-5 text-amber-500" />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Approved</p>
                    <h3 className="text-2xl font-bold text-green-500">{statsData.counts.approved}</h3>
                  </div>
                  <div className="bg-green-100 p-3 rounded-full">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Rejected</p>
                    <h3 className="text-2xl font-bold text-red-500">{statsData.counts.rejected}</h3>
                  </div>
                  <div className="bg-red-100 p-3 rounded-full">
                    <XCircle className="h-5 w-5 text-red-500" />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Avg. Score</p>
                    <h3 className="text-2xl font-bold text-blue-500">
                      {(statsData.averages.aiScore * 10).toFixed(1)}
                    </h3>
                  </div>
                  <div className="bg-blue-100 p-3 rounded-full">
                    <Activity className="h-5 w-5 text-blue-500" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <AnimatePresence mode="wait">
          <TabsContent value="moderation" asChild>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold">
                  Pending Moderation {pagination && pagination.total ? `(${pagination.total})` : ''}
                </h3>
              </div>

              {moderationItems.length === 0 ? (
                <Card className="text-center py-12 border border-dashed">
                  <CardContent>
                    <div className="flex flex-col items-center justify-center text-gray-500">
                      <CheckCircle className="w-16 h-16 mb-4 text-green-500" />
                      <h3 className="text-xl font-medium">All caught up!</h3>
                      <p className="mt-2">No comments waiting for moderation.</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <>
                  {moderationItems.map((comment: any, index: number) => (
                    <motion.div
                      key={comment.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <Card className="mb-4 border-l-4 border-l-amber-400 hover:shadow-md transition-shadow">
                        <CardHeader className="flex flex-row items-start space-x-4 pb-2 pt-4">
                          <Avatar className="w-10 h-10 border">
                            <AvatarImage src={comment.authorPictureUrl} alt={comment.authorName} />
                            <AvatarFallback>{getInitials(comment.authorName)}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex justify-between items-center">
                              <div>
                                <h4 className="font-semibold text-sm">{comment.authorName}</h4>
                                <p className="text-xs text-gray-500">
                                  {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                                </p>
                              </div>
                              
                              <Badge variant="outline" className="ml-2 bg-amber-50 text-amber-700 border-amber-200">
                                PENDING REVIEW
                              </Badge>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <p className="whitespace-pre-wrap mb-4 text-gray-700">{comment.content}</p>
                          
                          <div className="flex flex-wrap gap-2">
                            {comment.sentimentScore !== undefined && (
                              <Badge variant={comment.sentimentScore > 0.5 ? "default" : "destructive"} className="bg-opacity-85">
                                Sentiment: {Math.round(comment.sentimentScore * 100)}%
                              </Badge>
                            )}
                            
                            {comment.accuracyScore !== undefined && (
                              <Badge variant={comment.accuracyScore > 0.5 ? "default" : "destructive"} className="bg-opacity-85">
                                Accuracy: {Math.round(comment.accuracyScore * 100)}%
                              </Badge>
                            )}
                            
                            {comment.aiScore !== undefined && (
                              <Badge variant={comment.aiScore > 0.5 ? "default" : "destructive"} className="bg-opacity-85">
                                AI Score: {Math.round(comment.aiScore * 100)}%
                              </Badge>
                            )}
                            
                            {comment.flaggedTerms && comment.flaggedTerms.length > 0 && (
                              <Badge variant="destructive" className="animate-pulse">
                                Flagged Terms: {comment.flaggedTerms.length}
                              </Badge>
                            )}
                          </div>
                        </CardContent>
                        <CardFooter className="flex justify-between pt-1 pb-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleShowDetails(comment)}
                            className="text-gray-600 hover:bg-gray-50"
                          >
                            <Info className="h-4 w-4 mr-1" />
                            Details
                          </Button>
                          
                          <div className="flex space-x-2">
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => handleReject(comment.id)}
                              disabled={isApproving || isRejecting}
                              className="border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 transition-colors"
                            >
                              <XCircle className="h-4 w-4 mr-1" />
                              Reject
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => handleApprove(comment.id)}
                              disabled={isApproving || isRejecting}
                              className="border-green-200 text-green-600 hover:bg-green-50 hover:border-green-300 transition-colors"
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Approve
                            </Button>
                          </div>
                        </CardFooter>
                      </Card>
                    </motion.div>
                  ))}

                  {pagination && pagination.total > pagination.limit && (
                    <div className="flex justify-center mt-6">
                      <CustomPagination
                        currentPage={pagination.page ?? 1}
                        totalPages={pagination.pages}
                        onPageChange={handlePageChange}
                      />
                    </div>
                  )}
                </>
              )}
            </motion.div>
          </TabsContent>

          <TabsContent value="discussions" asChild>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold">
                  Discussion Forum
                </h3>
              </div>

              {loadingDiscussions ? (
                <div className="space-y-6">
                  {[1, 2].map((i) => (
                    <Card key={i} className="mb-6">
                      <CardHeader>
                        <Skeleton className="h-6 w-48 mb-2" />
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {[1, 2].map((j) => (
                          <div key={j} className="p-4 bg-gray-50 rounded-lg">
                            <div className="flex items-start space-x-3">
                              <Skeleton className="w-8 h-8 rounded-full" />
                              <div className="flex-1">
                                <Skeleton className="h-4 w-32 mb-2" />
                                <Skeleton className="h-3 w-24 mb-3" />
                                <Skeleton className="h-4 w-full mb-2" />
                                <Skeleton className="h-4 w-3/4" />
                              </div>
                            </div>
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : discussions && discussions.length > 0 ? (
                discussions.map((discussion:any, discussionIndex: number) => (
                  <motion.div 
                    key={discussion.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3, delay: discussionIndex * 0.1 }}
                  >
                    <Card className="mb-6 border-t-4 border-t-blue-500">
                      <CardHeader>
                        <h3 className="font-bold text-lg">{discussion.title}</h3>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {discussion.comments.map((comment: any, commentIndex: number) => (
                          <motion.div
                            key={comment.id}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: 0.1 + (commentIndex * 0.1) }}
                          >
                            <div className="p-4 bg-gray-50 rounded-lg">
                              <div className="flex items-start space-x-3">
                                <Avatar className="w-8 h-8">
                                  <AvatarImage src={comment.authorPictureUrl} alt={comment.authorName} />
                                  <AvatarFallback>{getInitials(comment.authorName)}</AvatarFallback>
                                </Avatar>
                                <div className="flex-1">
                                  <div className="flex justify-between items-center">
                                    <div>
                                      <h4 className="font-semibold text-sm">{comment.authorName}</h4>
                                      <p className="text-xs text-gray-500">
                                        {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                                      </p>
                                    </div>
                                    
                                    <div className="flex flex-row items-center">
                                      <span className="text-xs mr-2 text-gray-500">Score:</span>
                                      <Badge variant="outline" className={`${comment.evaluation?.overallScore >= 7.5 ? 'bg-green-50 text-green-700 border-green-200' : 'bg-amber-50 text-amber-700 border-amber-200'}`}>
                                        {comment.evaluation?.overallScore?.toFixed(1) || '?'}/10
                                      </Badge>
                                    </div>
                                  </div>
                                  
                                  <p className="whitespace-pre-wrap mt-2 text-gray-700">{comment.content}</p>
                                  
                                  <div className="mt-3 pt-2 border-t border-gray-200">
                                    <div className="grid grid-cols-2 gap-3">
                                      <div className="space-y-1">
                                        <div className="flex justify-between items-center text-xs">
                                          <span className="text-gray-600">Engagement</span>
                                          <span className={getScoreColor(comment.evaluation?.engagement || 0)}>
                                            {comment.evaluation?.engagement || '?'}/10
                                          </span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-1">
                                          <div 
                                            className="h-1 rounded-full bg-blue-500"
                                            style={{ width: `${((comment.evaluation?.engagement || 0) / 10) * 100}%` }}
                                          ></div>
                                        </div>
                                      </div>
                                      <div className="space-y-1">
                                        <div className="flex justify-between items-center text-xs">
                                          <span className="text-gray-600">Depth</span>
                                          <span className={getScoreColor(comment.evaluation?.depthOfThought || 0)}>
                                            {comment.evaluation?.depthOfThought || '?'}/10
                                          </span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-1">
                                          <div 
                                            className="h-1 rounded-full bg-purple-500"
                                            style={{ width: `${((comment.evaluation?.depthOfThought || 0) / 10) * 100}%` }}
                                          ></div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </CardContent>
                      <CardFooter className="flex justify-end border-t pt-3">
                        <Button variant="outline" size="sm" className="text-gray-600">
                          <MessageCircle className="h-4 w-4 mr-1" />
                          View All Comments
                        </Button>
                      </CardFooter>
                    </Card>
                  </motion.div>
                ))
              ) : (
                <Card className="text-center py-12 border border-dashed">
                  <CardContent>
                    <div className="flex flex-col items-center justify-center text-gray-500">
                      <MessageCircle className="w-16 h-16 mb-4 text-blue-500" />
                      <h3 className="text-xl font-medium">No discussions yet</h3>
                      <p className="mt-2">Start the conversation by creating a new discussion.</p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {discussionPagination && discussionPagination.total > discussionPagination.limit && (
                <div className="flex justify-center mt-6">
                  <CustomPagination
                    currentPage={discussionPagination.page ?? 1}
                    totalPages={discussionPagination.pages}
                    onPageChange={handlePageChange}
                  />
                </div>
              )}
            </motion.div>
          </TabsContent>

          <TabsContent value="users" asChild>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold">
                  User Engagement Scores
                </h3>
                <div className="w-1/3">
                  <Input
                    placeholder="Search users..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
              </div>

              <Card className="mb-6">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Comments</TableHead>
                      <TableHead className="text-center">Avg. Score</TableHead>
                      <TableHead className="text-right">Last Active</TableHead>
                      <TableHead className="text-center">Score Breakdown</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filterUsers().map((user: any, index: number) => (
                      <TableRow key={user.id} className={index % 2 === 0 ? "bg-gray-50" : ""}>
                        <TableCell className="font-medium">
                          <div className="flex items-center space-x-2">
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={user.profilePicture} alt={user.name} />
                              <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                            </Avatar>
                            <span>{user.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{user.commentCount}</TableCell>
                        <TableCell className="text-center">
                          <Badge variant="outline" className={`
                            ${user.averageScore >= 8 ? 'bg-green-50 text-green-700 border-green-200' : 
                              user.averageScore >= 6 ? 'bg-amber-50 text-amber-700 border-amber-200' : 
                              'bg-red-50 text-red-700 border-red-200'}
                          `}>
                            {user.averageScore.toFixed(1)}/10
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right text-sm text-gray-500">
                          {formatDistanceToNow(user.lastActive, { addSuffix: true })}
                        </TableCell>
                        <TableCell>
                          <div className="space-y-2">
                            <div className="flex justify-between text-xs">
                              <span>Engagement</span>
                              <span className={getScoreColor(user.scoreBreakdown.engagement)}>{user.scoreBreakdown.engagement.toFixed(1)}</span>
                            </div>
                            <Progress value={user.scoreBreakdown.engagement * 10} className="h-1" />
                            
                            <div className="flex justify-between text-xs">
                              <span>Depth</span>
                              <span className={getScoreColor(user.scoreBreakdown.depthOfThought)}>{user.scoreBreakdown.depthOfThought.toFixed(1)}</span>
                            </div>
                            <Progress value={user.scoreBreakdown.depthOfThought * 10} className="h-1" />
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Card>
            </motion.div>
          </TabsContent>
        </AnimatePresence>
      </Tabs>
      
      {/* Comment Details Dialog */}
      {selectedComment && (
        <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Comment Details</DialogTitle>
              <DialogDescription>
                AI-powered evaluation of this comment
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4 py-4">
              <div className="flex items-center space-x-4">
                <Avatar className="w-12 h-12">
                  <AvatarImage src={selectedComment.authorPictureUrl} alt={selectedComment.authorName} />
                  <AvatarFallback>{getInitials(selectedComment.authorName)}</AvatarFallback>
                </Avatar>
                <div>
                  <h4 className="font-semibold">{selectedComment.authorName}</h4>
                  <p className="text-sm text-gray-500">
                    {formatDistanceToNow(new Date(selectedComment.createdAt), { addSuffix: true })}
                  </p>
                </div>
              </div>
              
              <div className="rounded-lg bg-gray-50 p-4">
                <p className="whitespace-pre-wrap">{selectedComment.content}</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Sentiment Analysis</h4>
                  <div className="flex items-center space-x-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          (selectedComment.sentimentScore || 0) > 0.5 ? 'bg-green-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${(selectedComment.sentimentScore || 0) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">
                      {Math.round((selectedComment.sentimentScore || 0) * 100)}%
                    </span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Accuracy Score</h4>
                  <div className="flex items-center space-x-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          (selectedComment.accuracyScore || 0) > 0.5 ? 'bg-green-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${(selectedComment.accuracyScore || 0) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">
                      {Math.round((selectedComment.accuracyScore || 0) * 100)}%
                    </span>
                  </div>
                </div>
              </div>
              
              {selectedComment.toneAnalysis && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Tone Analysis</h4>
                  <div className="grid grid-cols-3 gap-2">
                    <div className="space-y-1">
                      <div className="text-xs">Curious</div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className="h-1.5 rounded-full bg-blue-500"
                          style={{ width: `${selectedComment.toneAnalysis.curious * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-xs">Critical</div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className="h-1.5 rounded-full bg-orange-500"
                          style={{ width: `${selectedComment.toneAnalysis.critical * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-xs">Analytical</div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className="h-1.5 rounded-full bg-purple-500"
                          style={{ width: `${selectedComment.toneAnalysis.analytical * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {selectedComment.flaggedTerms && selectedComment.flaggedTerms.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-red-500">Flagged Content</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedComment.flaggedTerms.map((term, index) => (
                      <Badge key={index} variant="destructive">{term}</Badge>
                    ))}
                  </div>
                </div>
              )}
              
              {selectedComment.evaluation && (
                <div className="space-y-3 pt-2 border-t border-gray-200">
                  <h4 className="font-medium">Student Engagement Evaluation</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Engagement</span>
                        <span className="text-sm font-bold">{selectedComment.evaluation.engagement}/10</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className="h-1.5 rounded-full bg-blue-500"
                          style={{ width: `${(selectedComment.evaluation.engagement / 10) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Depth of Thought</span>
                        <span className="text-sm font-bold">{selectedComment.evaluation.depthOfThought}/10</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className="h-1.5 rounded-full bg-blue-500"
                          style={{ width: `${(selectedComment.evaluation.depthOfThought / 10) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Relevance</span>
                        <span className="text-sm font-bold">{selectedComment.evaluation.relevance}/10</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className="h-1.5 rounded-full bg-blue-500"
                          style={{ width: `${(selectedComment.evaluation.relevance / 10) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Peer Interaction</span>
                        <span className="text-sm font-bold">{selectedComment.evaluation.peerInteraction}/10</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className="h-1.5 rounded-full bg-blue-500"
                          style={{ width: `${(selectedComment.evaluation.peerInteraction / 10) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div className="space-y-2 md:col-span-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Evidence</span>
                        <span className="text-sm font-bold">{selectedComment.evaluation.evidence}/10</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className="h-1.5 rounded-full bg-blue-500"
                          style={{ width: `${(selectedComment.evaluation.evidence / 10) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  
                  {selectedComment.evaluation.feedback && selectedComment.evaluation.feedback.overall && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-md text-sm">
                      <p className="font-medium mb-1">AI Feedback:</p>
                      <p>{selectedComment.evaluation.feedback.overall}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
            
            <DialogFooter className="flex justify-between">
              <Button 
                variant="outline"
                onClick={() => setDetailsOpen(false)}
              >
                Close
              </Button>
              
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => {
                    handleReject(selectedComment.id);
                    setDetailsOpen(false);
                  }}
                  disabled={isApproving || isRejecting}
                  className="border-red-200 text-red-600 hover:bg-red-50"
                >
                  <XCircle className="h-4 w-4 mr-1" />
                  Reject
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => {
                    handleApprove(selectedComment.id);
                    setDetailsOpen(false);
                  }}
                  disabled={isApproving || isRejecting}
                  className="border-green-200 text-green-600 hover:bg-green-50"
                >
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Approve
                </Button>
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}