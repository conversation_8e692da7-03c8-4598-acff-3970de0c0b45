import { useState, useRef } from 'react'
import { Loader2, <PERSON>clip, Image, XCircle } from 'lucide-react'
import { useCreateCommentMutation } from '@/services/discussionsAPIjs'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { toast } from '@/hooks/use-toast'
import { useAuth0 } from '@auth0/auth0-react'

interface CommentFormProps {
  discussionId: string
  className?: string
  onSuccess?: () => void
}

export function CommentForm({ discussionId, className, onSuccess }: CommentFormProps) {
  const [content, setContent] = useState('')
  const [createComment, { isLoading }] = useCreateCommentMutation()
  const [mediaUrls, setMediaUrls] = useState<string[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const { user } = useAuth0()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!content.trim() && mediaUrls.length === 0) return

    try {
      let commentContent = content.trim()
      
      if (mediaUrls.length > 0) {
        commentContent += '\n\n' + mediaUrls.map(url => {
          if (url.match(/\.(jpeg|jpg|gif|png)$/i)) {
            return `![Image](${url})`;
          } else if (url.match(/\.(mp4|webm|ogg)$/i)) {
            return `[VIDEO](${url})`;
          }
          return url;
        }).join('\n');
      }
      
      const userName = user?.name || user?.nickname || 'Anonymous';
      const userPictureUrl = user?.picture;
      
      await createComment({
        discussionId,
        content: commentContent,
        userName,
        userPictureUrl
      }).unwrap()

      setContent('')
      setMediaUrls([])
      toast({
        title: 'Success',
        description: 'Your comment has been submitted and is pending review',
      })
      onSuccess?.()
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add comment. Please try again.',
        variant: 'destructive',
      })
    }
  }
  
  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;
    
    setIsUploading(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newUrls = Array.from(e.target.files || []).map(
        file => URL.createObjectURL(file)
      );
      
      setMediaUrls(prev => [...prev, ...newUrls]);
      
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to upload media. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };
  
  const removeMedia = (index: number) => {
    setMediaUrls(prev => prev.filter((_, i) => i !== index));
  };

  const getInitials = (name?: string) => {
    if (!name) return '?';
    return name.split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <form onSubmit={handleSubmit} className={className}>
      <div className="flex gap-4">
        <Avatar className="h-8 w-8">
          <AvatarImage src={user?.picture} alt={user?.name || 'User'} />
          <AvatarFallback>{getInitials(user?.name)}</AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Share your thoughts..."
            className="min-h-[100px] mb-2 resize-none"
            disabled={isLoading || isUploading}
          />
          
          {mediaUrls.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-3">
              {mediaUrls.map((url, index) => (
                <div key={index} className="relative group">
                  {url.match(/\.(jpeg|jpg|gif|png)$/i) ? (
                    <div className="relative w-20 h-20">
                      <img 
                        src={url} 
                        alt="Preview" 
                        className="w-full h-full object-cover rounded border border-gray-200" 
                      />
                      <button
                        type="button"
                        onClick={() => removeMedia(index)}
                        className="absolute -top-2 -right-2 bg-background text-destructive rounded-full hover:bg-destructive hover:text-destructive-foreground transition-colors"
                      >
                        <XCircle className="h-5 w-5" />
                        <span className="sr-only">Remove</span>
                      </button>
                    </div>
                  ) : (
                    <div className="relative w-20 h-20">
                      <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded border border-gray-200">
                        <Image className="w-8 h-8 text-gray-400" />
                      </div>
                      <button
                        type="button"
                        onClick={() => removeMedia(index)}
                        className="absolute -top-2 -right-2 bg-background text-destructive rounded-full hover:bg-destructive hover:text-destructive-foreground transition-colors"
                      >
                        <XCircle className="h-5 w-5" />
                        <span className="sr-only">Remove</span>
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
          
          <div className="flex justify-between">
            <div>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                multiple
                accept="image/*"
                onChange={handleFileSelect}
                disabled={isLoading || isUploading}
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                disabled={isLoading || isUploading}
                className="text-muted-foreground"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Paperclip className="mr-2 h-4 w-4" />
                    Add Image
                  </>
                )}
              </Button>
            </div>
            <Button 
              type="submit" 
              disabled={isLoading || isUploading || (!content.trim() && mediaUrls.length === 0)}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Posting...
                </>
              ) : 'Post Comment'}
            </Button>
          </div>
        </div>
      </div>
    </form>
  )
}