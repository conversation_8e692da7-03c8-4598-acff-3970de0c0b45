import React from 'react';
import { <PERSON>, ListFilter, UserRound<PERSON><PERSON><PERSON>, C<PERSON>boardList, Check<PERSON>he<PERSON> } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';

const LeftSidebar = () => {
  const quickFilters = [
    { id: 'not-started', label: 'Not Started' },
    { id: 'in-progress', label: 'In Progress' },
    { id: 'completed', label: 'Completed' },
  ];

  const learningSummary = {
    hoursSpent: 15,
    expertSessionsCompleted: 1,
    coursesEnrolled: 4,
    coursesCompleted: 1,
  };

  const instructorSuggestion = {
    instructor: '<PERSON> (English Lecturer)',
    suggestion: 'Improve your research skills with this course',
  };

  return (
    <aside className="w-64 bg-white shadow-md border-r border-[#E6E6E6] h-full">
      <ScrollArea className="h-full">
      <div>
        <h2 className="text-lg font-semibold p-4 flex items-center"><ListFilter className="mr-2" color="#347468" />Quick Filters</h2>
        <div className="space-y-2 border-t border-[#E6E6E6] p-4">
          {quickFilters.map((filter) => (
            <div key={filter.id}>
              <input className='accent-[#347468]' type="checkbox" id={filter.id} />
              <label htmlFor={filter.id} className="ml-3 text-black text-md">{filter.label}</label>
            </div>
          ))}
        </div>
      </div>
      <div className="border-t border-[#E6E6E6] p-4">
        <h2 className="text-lg font-semibold">My Learning Summary</h2>
        <div className="space-y-2 mt-4">
          <p className="flex items-center text-black text-base"><Clock className="mr-2 text-[#347468]" />{learningSummary.hoursSpent} Hours Spent</p>
          <p className="flex items-center text-black text-base"><UserRoundCheck className="mr-2 text-[#347468]" />{learningSummary.expertSessionsCompleted} Expert Ses. Completed</p>
          <p className="flex items-center text-black text-base"><ClipboardList className="mr-2 text-[#347468]" />{learningSummary.coursesEnrolled} Courses Enrolled</p>
          <p className="flex items-center text-black text-base"><CheckCheck className="mr-2 text-[#347468]" />{learningSummary.coursesCompleted} Course Completed</p>
        </div>
      </div>
      <div className="border-t border-[#E6E6E6] p-4 mt-4">
        <h2 className="text-lg font-semibold mb-4">Instructor's Suggestion</h2>
        <div className="border-2 p-2 rounded-[10px] border-[#347468]">
          <p className="font-semibold text-black text-sm">{instructorSuggestion.instructor}</p>
          <p className="text-xs text-gray-500">{instructorSuggestion.suggestion}</p>
        </div>
      </div>
      </ScrollArea>
    </aside>
  );
};

export default LeftSidebar;
