import React from 'react';
import { Link } from 'react-router-dom';

interface HubItemProps {
  iconSrc: string;
  title: string;
  description?: string;
  iconBgColorClass?: string; // Optional background color for the icon container e.g., 'bg-red-100'
  small?: boolean;
  href?: string;
  displayStyle?: 'imageOnly' | 'standard'; // New prop for style
}

const HubItem: React.FC<HubItemProps> = ({
  iconSrc,
  title,
  description,
  iconBgColorClass = 'bg-gray-100', // Default background
  small = false,
  href = '#',
  displayStyle = 'imageOnly' // Default to new image-only style
}) => {
  if (displayStyle === 'standard') {
    // OLD UI: Render icon, title, and description
    return (
      <Link to={href} className="mb-2 block">
        <div className={small ? "border border-primary rounded-lg p-2 flex items-center gap-2 hover:bg-gray-50 transition-colors cursor-pointer" : "border mb-2 border-primary rounded-xl p-4 flex items-center gap-3 hover:bg-gray-50 transition-colors cursor-pointer"}>
          <div className={small ? `w-7 h-7 ${iconBgColorClass} rounded-lg flex items-center justify-center flex-shrink-0` : `w-10 h-10 ${iconBgColorClass} rounded-lg flex items-center justify-center flex-shrink-0`}>
            <img src={iconSrc} alt={title} className={small ? "object-contain w-4 h-4" : "object-contain w-6 h-6"} /> {/* Changed to object-contain for old UI icons */}
          </div>
          <div>
            <h3 className={small ? "font-medium text-xs text-black" : "font-medium text-md text-black"}>{title}</h3>
            {description && <p className={small ? "text-[10px] text-gray-600" : "text-xs text-gray-600"}>{description}</p>}
          </div>
        </div>
      </Link>
    );
  }

  // NEW UI: Image covers background (default)
  const containerClasses = small
    ? "border border-primary rounded-lg p-0 flex items-center justify-center hover:bg-gray-50 transition-colors cursor-pointer relative overflow-hidden h-20"
    : "border mb-2 border-primary rounded-xl p-0 flex items-center justify-center hover:bg-gray-50 transition-colors cursor-pointer relative overflow-hidden h-28";

  const imageClasses = small
    ? "absolute inset-0 w-full h-full object-cover"
    : "absolute inset-0 w-full h-full object-cover";

  return (
    <Link to={href} className="mb-2 block">
      <div className={containerClasses} style={{ backgroundColor: iconBgColorClass }}>
        <img src={iconSrc} alt={title} className={imageClasses} />
      </div>
    </Link>
  );
};

export default HubItem;
