// API Response Types (Based on GET /Courses/{courseId} and related endpoints)

export interface ApiSubTopic {
  id: string;
  subTopicName?: string | null;
  contentType?: 'text' | 'video' | 'audio' | 'scorm' | 'html' | 'quiz' | 'assessment' | 'document' | string | null; // Allow other strings
  contentUrl?: string | null;
  content?: string | null;
  completed?: boolean | null;
  weight?: number | null; // For sorting
  // Add other relevant fields from API
  quizId?: string | null; // Quiz ID if this subtopic has a quiz
}

// Also add to SubTopic
export interface SubTopic {
  id: string;
  subTopicName?: string | null;
  type?: number | string;
  contentUrl?: string | null;
  content?: string | null;
  completed?: boolean | null;
  weight?: number | null;
  quizId?: string | null; // Quiz ID if this subtopic has a quiz
}

export interface ApiTopic {
  id: string;
  name?: string | null;
  anteriorId: string;
  completed?: boolean | null;
  subTopics?: ApiSubTopic[] | null;
  recommendedTime?: number; // Recommended time to spend on this topic
  // Add other relevant fields from API
}

export interface ApiChapter {
  id: string;
  name?: string | null;
  anteriorId: string;
  completed?: boolean | null;
  topics?: ApiTopic[] | null;
  recommendedTime?: number | null; // Recommended time to spend on this chapter
  // Add other relevant fields from API
}

export interface ApiResultObject { // Represents the data within 'resultObject' for GET /Courses/{courseId}
  id: string;
  name?: string | null;
  instructorName?: string | null;
  instructorId?: string | null; // Optional: ID for fetching detailed instructor info
  chapters?: ApiChapter[] | null;
  // Add other relevant top-level course fields from API
}

// Frontend Display Types (Used by the Course Detail Page UI)

export interface SubItem {
  id: string;
  title: string;
  type: 'text' | 'video' | 'audio' | 'scorm' | 'html' | 'quiz' | 'assessment' | 'document' | 'unknown';
  duration: string; // Formatted string (e.g., "5 min")
  isCompleted: boolean;
  contentUrl?: string | null; // URL for video, scorm, etc.
  content?: string | null; // Direct content for text/html
  quizId?: string | null; // Quiz ID if this subtopic has a quiz
  assignment?: {
    type: number; // 1=Quiz, 2=CaseStudy, 3=TeamProject, 4=SelfReflectiveJournal, etc.
    tags: Array<{
      tagId: number; // 1-10 assignment tags
      tagName?: string; // Optional tag name
    }>;
    title: string;
    assignmentText: string;
    fileUrl?: string;
    quizId?: string; // For quiz type assignments
  } | null;
}

export interface Lesson {
  id: string;
  title: string; // e.g., "01: Introduction"
  isActive: boolean; // Is this the currently viewed/active lesson?
  isCompleted: boolean;
  subItems: SubItem[];
  recommendedTime?: number; // Recommended time to spend on this lesson (e.g., "30 min")
}

export interface Module {
  id: string;
  title: string;
  duration: string; // Total duration for the module (e.g., "1h 30m")
  lessons: Lesson[];
  isCompleted: boolean;
}

export interface CourseDetails {
  id: string;
  title: string;
  instructor: string; // Instructor name
  modules: Module[];
  completedLessons: number;
  totalLessons: number;
  completionPercentage: number;
  timeRemaining: string; // Formatted string (e.g., "2h 15m")
  duration: string; // Total course duration (e.g., "5h 45m")
  topicsCount: number; // Same as totalLessons?
  assessmentsCount: number;
  classroomId?: string; // Added this property
}

export interface PerformanceData {
  label: string; // Skill name
  value: number; // Score or percentage
  color: string; // Tailwind background color class (e.g., "bg-[#9F9FF8]")
}

export interface InstructorDetails {
  id: string;
  name: string;
  avatar?: string; // URL to avatar image
  role?: string;
  bio?: string;
  email?: string;
  phoneNumber?: string;
}
