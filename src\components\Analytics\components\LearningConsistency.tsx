import React from 'react';
import { motion } from 'framer-motion';

// Animation variants
const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.5 } },
};

interface LearningConsistencyProps {
  className?: string;
}

// Define activity level type
type ActivityLevel = 'none' | 'low' | 'medium' | 'high' | 'veryHigh' | 'poor';

// Define the activity levels and their corresponding colors
const activityLevels: Record<ActivityLevel, string> = {
  none: 'bg-green-300',
  low: 'bg-green-400',
  medium: 'bg-[#FFDE78]',
  high: 'bg-green-500',
  veryHigh: 'bg-green-600',
  poor: 'bg-[#FE5B50]',
};

// Define day type
type Day = 'Mon' | 'Tue' | 'Wed' | 'Thu' | 'Fri' | 'Sat' | 'Sun';

// Mock data for the heatmap
const mockHeatmapData: Record<Day, ActivityLevel[]> = {
  // Format: [day][week] = activity level
  // Week days: <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>hu, <PERSON><PERSON>, Sat, Sun
  Mon: ['medium', 'low', 'medium', 'high', 'none', 'medium', 'low'],
  Tue: ['poor', 'medium', 'poor', 'poor', 'medium', 'low', 'medium'],
  Wed: ['poor', 'high', 'poor', 'medium', 'low', 'high', 'low'],
  Thu: ['high', 'medium', 'high', 'poor', 'medium', 'low', 'medium'],
  Fri: ['low', 'medium', 'poor', 'medium', 'high', 'poor', 'low'],
  Sat: ['high', 'low', 'high', 'medium', 'none', 'veryHigh', 'none'],
  Sun: ['medium', 'medium', 'medium', 'low', 'medium', 'low', 'low'],
};

const LearningConsistency: React.FC<LearningConsistencyProps> = ({ className = '' }) => {
  // Days of the week for display
  const daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  
  return (
    <motion.div
      className={`bg-white rounded-2xl border border-[#97C48A] shadow-sm p-5 ${className}`}
      variants={fadeIn}
      initial="initial"
      animate="animate"
    >
      <div className="flex justify-between items-start mb-4">
        <h3 className="text-lg font-bold text-[#2D2D2D]">Learning Consistency & Engagement</h3>
        <div className="text-right">
          <p className="text-sm text-gray-500">Time Spent:</p>
          <p className="text-xl font-medium text-[#347468] italic">40 Hrs</p>
        </div>
      </div>
      
      <div className="flex mt-4">
        {/* Day labels */}
        <div className="flex flex-col pr-3 mr-1">
          {daysOfWeek.map(day => (
            <div key={day} className="h-8 mb-1 flex items-center justify-end text-xs text-gray-500 font-medium">
              {day}
            </div>
          ))}
        </div>
        
        {/* Heatmap grid */}
        <div className="flex-1 grid grid-cols-7 gap-1">
          {daysOfWeek.map((day) => (
            <React.Fragment key={day}>
              {mockHeatmapData[day as Day].map((level: ActivityLevel, index: number) => (
                <div 
                  key={`${day}-${index}`} 
                  className={`h-8 w-full rounded ${activityLevels[level]} cursor-pointer hover:opacity-80 transition-opacity`}
                  title={`${day}, Week ${index + 1}: ${level} activity`}
                />
              ))}
            </React.Fragment>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default LearningConsistency;
