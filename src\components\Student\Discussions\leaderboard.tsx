import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Medal, Trophy, Award } from 'lucide-react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useGetLeaderboardQuery } from '@/services/discussionsAPIjs'
import { TimeRange } from './types'

const RANK_ICONS = {
  1: <Trophy className="h-6 w-6 text-yellow-500" />,
  2: <Medal className="h-6 w-6 text-gray-400" />,
  3: <Medal className="h-6 w-6 text-amber-600" />,
}

type TimeRangeOption = {
  label: string
  value: TimeRange
}

const TIME_RANGES: TimeRangeOption[] = [
  { label: 'Today', value: 'daily' },
  { label: 'This Week', value: 'weekly' },
  { label: 'This Month', value: 'monthly' },
  { label: 'All Time', value: 'alltime' },
]

export function DiscussionLeaderboard() {
  const [timeRange, setTimeRange] = useState<TimeRange>('weekly')
  const { data: leaderboard, isLoading } = useGetLeaderboardQuery(timeRange)

  return (
    <Card className="p-4">
      <div className="flex flex-col gap-3 mb-4">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <Award className="h-5 w-5" />
          Top Contributors
        </h2>
        
        <div className="flex flex-wrap gap-1">
          {TIME_RANGES.map((range) => (
            <Button
              key={range.value}
              size="sm"
              variant={timeRange === range.value ? 'default' : 'ghost'}
              className="px-2 py-1 text-xs h-auto"
              onClick={() => setTimeRange(range.value)}
            >
              {range.label}
            </Button>
          ))}
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      ) : (
        <ScrollArea className="h-[300px] pr-4">
          <div className="space-y-3">
            {leaderboard?.items.map((entry: any, index: number) => (
              <motion.div
                key={entry.userId}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="flex items-center gap-4 p-3 rounded-lg bg-card hover:bg-accent/5 transition-colors">
                  <div className="flex items-center justify-center w-8">
                    {RANK_ICONS[entry.rank as keyof typeof RANK_ICONS] || (
                      <span className="text-lg font-semibold text-muted-foreground">
                        {entry.rank}
                      </span>
                    )}
                  </div>
                  <Avatar>
                    <AvatarImage src={`/placeholder-avatar.jpg`} />
                    <AvatarFallback>
                      {entry.userId.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium">User {entry.userId}</div>
                    <div className="text-sm text-muted-foreground">
                      {Math.round(entry.score)} points
                    </div>
                  </div>
                  {entry.rank <= 3 && (
                    <div className="hidden sm:block text-sm text-muted-foreground">
                      Top {entry.rank}% of contributors
                    </div>
                  )}
                </div>
              </motion.div>
            ))}

            {(!leaderboard?.items || leaderboard.items.length === 0) && (
              <div className="text-center py-8 text-muted-foreground">
                No contributors for this time period
              </div>
            )}
          </div>
        </ScrollArea>
      )}
    </Card>
  )
}