import { useState, useEffect } from "react";
import {
    useGetQueriesQuery,
    useCreateQueryMutation,
    useGetSchoolClassroomsQuery,
} from "../../../APIConnect";
import { QueryResponse, ClassroomResponse, QA } from "./types";
import { Button } from "components/ui/button";
import { ScrollArea } from "components/ui/scroll-area";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "components/ui/dialog";
import { Textarea } from "components/ui/textarea";
import {
    MessageCircle,
    Send,
    HelpCircle,
    Search,
    Filter,
    School,
} from "lucide-react";
import { Input } from "components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "components/ui/select";
import { motion, AnimatePresence } from "framer-motion";
import { QACard } from "./QACard";
import { QALoadingSkeleton } from "./QALoadingSkeleton";
import { useToast } from "@/hooks/use-toast";

interface QAPageProps {
    schoolId: string;
    studentId: string;
}

export const QAPage = ({ schoolId, studentId }: QAPageProps) => {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [question, setQuestion] = useState("");
    const [searchQuery, setSearchQuery] = useState("");
    const [filter, setFilter] = useState("all");
    const [currentClassroomId, setCurrentClassroomId] = useState<string | null>(null);

    const { toast } = useToast();

    const { data: classroomsData, isLoading: isClassroomsLoading } =
        useGetSchoolClassroomsQuery(schoolId) as {
            data?: ClassroomResponse;
            isLoading: boolean;
        };
    const {
        data: queriesData,
        isLoading: isQueriesLoading,
        refetch,
    } = useGetQueriesQuery(
        { classroomId: currentClassroomId },
        { skip: !currentClassroomId }
    ) as {
        data?: QueryResponse;
        isLoading: boolean;
        refetch: () => void;
    };
    const [createQuery] = useCreateQueryMutation();

    useEffect(() => {
        if (!currentClassroomId && classroomsData?.resultObject && classroomsData.resultObject.length > 0) {
            setCurrentClassroomId(classroomsData.resultObject[0].id);
        }
    }, [currentClassroomId, classroomsData]);

    const handleSubmitQuestion = async () => {
        if (!question.trim() || !currentClassroomId) return;

        try {
            await createQuery({
                studentId,
                classroomId: currentClassroomId,
                queryText: question,
                isAnswered: false,
            });
            setQuestion("");
            setIsDialogOpen(false);
            refetch();
            toast({
                title: "Question submitted successfully! 🎉",
                description: "We'll notify you when your teacher responds.",
            });
        } catch (error) {
            console.error("Error submitting question:", error);
            toast({
                variant: "destructive",
                title: "Uh oh! Something went wrong.",
                description: "There was a problem submitting your question. Please try again.",
            });
        }
    };

    const filteredQueries = queriesData?.resultObject?.filter((qa: QA) => {
        const matchesSearch =
            qa.queryText.toLowerCase().includes(searchQuery.toLowerCase()) ||
            qa.reply?.answerText?.toLowerCase().includes(searchQuery.toLowerCase());
        const matchesFilter =
            filter === "all" ||
            (filter === "answered" && qa.isAnswered) ||
            (filter === "unanswered" && !qa.isAnswered);
        return matchesSearch && matchesFilter;
    });

    if (isClassroomsLoading || isQueriesLoading) {
        return (
            <div className="container mx-auto px-4 py-6 max-w-4xl">
                <QALoadingSkeleton />
            </div>
        );
    }

    const classrooms = classroomsData?.resultObject || [];

    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="container mx-auto px-4 py-6 max-w-4xl"
        >
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-100">
                <motion.div
                    initial={{ y: -20 }}
                    animate={{ y: 0 }}
                    className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-6"
                >
                    <h1 className="text-xl md:text-2xl font-bold flex items-center">
                        <MessageCircle className="h-6 w-6 mr-2 text-primary" />
                        Questions & Answers
                    </h1>
                    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                                                        <Button className="flex items-center space-x-2 w-full md:w-auto">
                                <HelpCircle className="h-5 w-5" />
                                <span>Ask a Question</span>
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle className="flex items-center">
                                    <HelpCircle className="h-5 w-5 mr-2 text-primary" />
                                    Ask Your Question
                                </DialogTitle>
                            </DialogHeader>
                            <div className="mt-4 space-y-4">
                                <Textarea
                                    placeholder="Type your question here... 💭"
                                    value={question}
                                    onChange={(e) => setQuestion(e.target.value)}
                                    className="min-h-[100px]"
                                />
                                <div className="flex justify-end">
                                    <Button
                                        onClick={handleSubmitQuestion}
                                        className="flex items-center space-x-2"
                                        disabled={!question.trim()}
                                    >
                                        <Send className="h-4 w-4" />
                                        <span>Submit Question</span>
                                    </Button>
                                </div>
                            </div>
                        </DialogContent>
                    </Dialog>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="mb-6"
                >
                    <Select
                        value={currentClassroomId || ""}
                        onValueChange={setCurrentClassroomId}
                    >
                        <SelectTrigger className="w-full">
                            <div className="flex items-center">
                                <School className="mr-2 h-4 w-4" />
                                <SelectValue placeholder="Select classroom..." />
                            </div>
                        </SelectTrigger>
                        <SelectContent>
                            {classrooms.map((classroom) => (
                                <SelectItem
                                    key={classroom.id}
                                    value={classroom.id}
                                >
                                    <div className="flex items-center">
                                        {/* <School className="mr-2 h-4 w-4" /> */}
                                        {classroom.name}
                                    </div>
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="flex gap-4 mb-6"
                >
                    <div className="flex-1 relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                            placeholder="Search questions..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-10"
                        />
                    </div>
                    <div className="w-48">
                        <Select value={filter} onValueChange={setFilter}>
                            <SelectTrigger>
                                <div className="flex items-center">
                                    <Filter className="h-4 w-4 mr-2" />
                                    <SelectValue placeholder="Filter" />
                                </div>
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Questions</SelectItem>
                                <SelectItem value="answered">Answered</SelectItem>
                                <SelectItem value="unanswered">Waiting for Answer</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </motion.div>
            </div>

            <ScrollArea className="h-[calc(100vh-280px)]">
                <AnimatePresence mode="wait">
                    {filteredQueries?.length === 0 ? (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.95 }}
                            className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-100"
                        >
                            <MessageCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                            <p className="text-gray-500 mb-2">
                                No questions found{" "}
                                {searchQuery ? "matching your search" : "yet"}! 🔍
                            </p>
                            {searchQuery ? (
                                <p className="text-sm text-gray-400">
                                    Try adjusting your search terms
                                </p>
                            ) : (
                                <p className="text-sm text-gray-400">
                                    Be the first to ask a question! 🎯
                                </p>
                            )}
                        </motion.div>
                    ) : (
                        <div className="space-y-4">
                            {filteredQueries?.map((qa: QA) => (
                                <QACard key={qa.id} qa={qa} />
                            ))}
                        </div>
                    )}
                </AnimatePresence>
            </ScrollArea>
        </motion.div>
    );
};

export default QAPage;
