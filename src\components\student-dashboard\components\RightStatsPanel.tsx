import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import RadialProgressChart from './RadialProgressChart';
import SkillsBarChart from './SkillsBarChart';
import { Bot, Icon } from 'lucide-react'; // Import Icon
import { elephantFace } from '@lucide/lab'; // Import elephantFace from @lucide/lab

interface RightStatsPanelProps {
  onOpenChat: () => void; // Add the prop for opening chat
}

const RightStatsPanel: React.FC<RightStatsPanelProps> = ({ onOpenChat }) => {
  // Placeholder data for indices - replace with actual data when available
  const indices = [
    { label: 'Employability Index', percentage: 80, color: 'text-primary' },
    { label: "Master's Ready Index", percentage: 80, color: 'text-primary' },
    { label: 'Sustainability Index', percentage: 80, color: 'text-primary' },
    { label: 'Leadership Index', percentage: 80, color: 'text-primary' },
  ];

  // Skills data is handled by SkillsBarChart defaultProps for now

  return (
        <div className="h-full flex flex-col bg-white">
      <ScrollArea className="h-full flex-1">
        <div className="p-4 space-y-6">
          {/* Index Charts Section */}
          <div className="bg-[#E0F3F0] border border-primary rounded-xl p-4">

             <div className="grid grid-cols-2 gap-4">
                {indices.map((indexData) => (
                    <RadialProgressChart
                        key={indexData.label}
                        percentage={indexData.percentage}
                        label={indexData.label}
                        color={indexData.color}
                    />
                ))}
             </div>
          </div>

          {/* Skills Chart Section */}
          {/* SkillsBarChart uses defaultProps for data */}
          <SkillsBarChart
  skills={[
    { name: 'Innovation', value: 40, color: 'bg-purple-400' },
    { name: 'Strategic\nThinking', value: 90, color: 'bg-teal-300' },
    { name: 'Subject\nKnowledge', value: 75, color: 'bg-black' },
    { name: 'Sustainability\nQuotient', value: 100, color: 'bg-blue-300' },
    { name: 'Teamwork', value: 35, color: 'bg-blue-200' },
  ]}
/>
 {/* Pass empty array or actual data */}

        </div>
      </ScrollArea>
       {/* AI Mentor Button - Fixed at the bottom of the panel */}
       <div className="p-4 border-t border-gray-200">

        {/* AI Mentor Button */}
            <button className="w-full bg-gradient-to-r from-[#386C8D] via-[#036664] to-[#060B0B] text-white px-4 py-2.5 rounded-xl shadow-lg flex items-center justify-center gap-3 hover:shadow-xl transition-all hover:scale-105"
              onClick={onOpenChat}
            >
              <Icon iconNode={elephantFace} size={28} />
              <span className="text-base font-semibold capitalize">AI Mentor</span>
            </button>
            {/* <Button
                onClick={onOpenChat} // Use the passed function
                className="w-full bg-teal-700 hover:bg-teal-800 text-white rounded-md h-10 flex items-center justify-center gap-2"
                aria-label="Ask AI Mentor"
            >
                <Bot size={18} />
                <span className="font-medium">AI Mentor</span>
            </Button> */}
       </div>
    </div>
  );
};

export default RightStatsPanel;
