import React, { useRef, useEffect } from 'react';
import * as d3 from 'd3';
import { motion } from 'framer-motion';

interface ChartData {
    [key: string]: any; // Allow dynamic properties
}

interface ChartProps {
    type: 'line' | 'bar' | 'donut' | 'streamgraph';
    data: ChartData[];
    xKey?: string;
    yKey?: string;
    dataKey?: string;
    labels?: string;
    keys?: string[];
}

const ChartComponent: React.FC<ChartProps> = ({
    type,
    data,
    xKey,
    yKey,
    dataKey,
    labels,
    keys,
}) => {
    const svgRef = useRef<SVGSVGElement>(null);

    useEffect(() => {
        if (!data || data.length === 0) return;

        if ((type === 'line' || type === 'bar') && (!xKey || !yKey)) {
            console.error('xKey and yKey are required for line and bar charts.');
            return;
        }

        if (type === 'donut' && (!dataKey || !labels)) {
            console.error('dataKey and labels are required for donut charts.');
            return;
        }

        if (type === 'streamgraph' && !keys) {
            console.error('keys are required for streamgraph charts.');
            return;
        }

        const svgElement = svgRef.current;
        if (!svgElement) return;

        const svg = d3.select(svgElement);
        svg.selectAll('*').remove(); // Clear previous chart

        const width = svgElement.getBoundingClientRect().width || 0;
        const height = svgElement.getBoundingClientRect().height || 0;
        const margin = { top: 20, right: 20, bottom: 30, left: 40 };
        const chartWidth = width - margin.left - margin.right;
        const chartHeight = height - margin.top - margin.bottom;

        const g = svg
            .append('g')
            .attr('transform', `translate(${margin.left},${margin.top})`);

        if (type === 'line') {
            const x = d3
                .scalePoint<string>()
                .range([0, chartWidth])
                .domain(data.map((d) => String(d[xKey!])));

            const y = d3
                .scaleLinear()
                .range([chartHeight, 0])
                .domain([
                    d3.min(data, (d) => +d[yKey!]) ?? 0,
                    d3.max(data, (d) => +d[yKey!]) ?? 0,
                ]);

            const line = d3
                .line<ChartData>()
                .x((d) => x(String(d[xKey!]))!)
                .y((d) => y(+d[yKey!])!)
                .curve(d3.curveMonotoneX); // Smooth curve

            g.append('g')
                .attr('transform', `translate(0, ${chartHeight})`)
                .call(d3.axisBottom(x))
                .selectAll('text')
                .style('text-anchor', 'end')
                .attr('dx', '-.8em')
                .attr('dy', '.15em')
                .attr('transform', 'rotate(-65)');

            g.append('g').call(d3.axisLeft(y));

            g.append('path')
                .datum(data)
                .attr('fill', 'none')
                .attr('stroke', 'steelblue')
                .attr('stroke-width', 3)
                .attr('d', line);

            g.selectAll('.dot')
                .data(data)
                .enter()
                .append('circle')
                .attr('class', 'dot')
                .attr('cx', (d) => x(String(d[xKey!]))!)
                .attr('cy', (d) => y(+d[yKey!])!)
                .attr('r', 4)
                .attr('fill', 'white')
                .attr('stroke', 'steelblue')
                .attr('stroke-width', 2);
        } else if (type === 'bar') {
            const x = d3
                .scaleBand<string>()
                .range([0, chartWidth])
                .domain(data.map((d) => String(d[xKey!])))
                .padding(0.1);

            const y = d3
                .scaleLinear()
                .range([chartHeight, 0])
                .domain([0, d3.max(data, (d) => +d[yKey!]) ?? 0]);

            g.append('g')
                .attr('transform', `translate(0, ${chartHeight})`)
                .call(d3.axisBottom(x))
                .selectAll('text')
                .style('text-anchor', 'end')
                .attr('dx', '-.8em')
                .attr('dy', '.15em')
                .attr('transform', 'rotate(-65)');

            g.append('g').call(d3.axisLeft(y));

            g.selectAll('.bar')
                .data(data)
                .enter()
                .append('rect')
                .attr('class', 'bar')
                .attr('x', (d) => x(String(d[xKey!]))!)
                .attr('y', (d) => y(+d[yKey!])!)
                .attr('width', x.bandwidth())
                .attr('height', (d) => chartHeight - y(+d[yKey!])!)
                .attr('fill', 'steelblue');
        } else if (type === 'donut') {
            const radius = Math.min(chartWidth, chartHeight) / 2;
            const colorScale = d3.scaleOrdinal<number, string>().range(d3.schemeCategory10);

            const arc = d3
                .arc<d3.PieArcDatum<ChartData>>()
                .innerRadius(radius * 0.6) // Make it a donut
                .outerRadius(radius);

            const pie = d3
                .pie<ChartData>()
                .value((d) => +d[dataKey!])
                .sort(null);

            const arcs = g
                .selectAll('.arc')
                .data(pie(data))
                .enter()
                .append('g')
                .attr('class', 'arc')
                .attr(
                    'transform',
                    `translate(${chartWidth / 2},${chartHeight / 2})`
                );

            arcs
                .append('path')
                .attr('d', arc)
                .attr('fill', (d, i) => colorScale(i));

            arcs
                .append('text')
                .attr('transform', (d) => `translate(${arc.centroid(d)})`)
                .attr('text-anchor', 'middle')
                .text((d) => `${d.data[labels!]} (${d.value})`)
                .style('font-size', '0.8em');
        } else if (type === 'streamgraph') {
            const parseDate = d3.timeParse('%Y-%m-%d');

            data.forEach((d) => {
                d.date = parseDate(d.date);
            });

            const x = d3
                .scaleTime()
                .domain(d3.extent(data, (d) => d.date) as [Date, Date])
                .range([0, chartWidth]);

            const stack = d3
                .stack()
                .keys(keys!)
                .order(d3.stackOrderNone)
                .offset(d3.stackOffsetWiggle);

            const series = stack(data);

            const y = d3
                .scaleLinear()
                .domain([
                    d3.min(series, (layer) => d3.min(layer, (d) => d[0])) ?? 0,
                    d3.max(series, (layer) => d3.max(layer, (d) => d[1])) ?? 0,
                ])
                .range([chartHeight, 0]);

            const area = d3
                .area<d3.SeriesPoint<ChartData>>()
                .x((d) => x(d.data.date))
                .y0((d) => y(d[0]))
                .y1((d) => y(d[1]))
                .curve(d3.curveBasis);

            const colorScale = d3.scaleOrdinal<string>().range(d3.schemeCategory10);

            g.selectAll('path')
                .data(series)
                .enter()
                .append('path')
                .attr('d', area)
                .attr('fill', (_, i) => colorScale(String(i)));

            g.append('g')
                .attr('transform', `translate(0, ${chartHeight})`)
                .call(d3.axisBottom(x));
        }
    }, [data, type, xKey, yKey, dataKey, labels, keys]);

    return (
        <motion.svg
            ref={svgRef}
            width="100%"
            height="300px"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="w-full"
        />
    );
};

export default ChartComponent;

export const LineChart: React.FC<Omit<ChartProps, 'type'>> = ({
    data,
    xKey,
    yKey,
}) => <ChartComponent type="line" data={data} xKey={xKey} yKey={yKey} />;

export const BarChart: React.FC<Omit<ChartProps, 'type'>> = ({
    data,
    xKey,
    yKey,
}) => <ChartComponent type="bar" data={data} xKey={xKey} yKey={yKey} />;

export const DonutChart: React.FC<Omit<ChartProps, 'type'>> = ({
    data,
    dataKey,
    labels,
}) => (
    <ChartComponent
        type="donut"
        data={data}
        dataKey={dataKey}
        labels={labels}
    />
);

export const StreamGraph: React.FC<Omit<ChartProps, 'type'>> = ({
    data,
    keys,
}) => <ChartComponent type="streamgraph" data={data} keys={keys} />;