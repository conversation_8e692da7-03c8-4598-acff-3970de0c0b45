export interface Reply {
  answerText: string;
  teacherName: string;
}

export interface QA {
  id: string;
  queryText: string;
  createdOn: string;
  updatedon: string;
  isAnswered: boolean;
  studentName: string;
  reply?: Reply;
}

export interface Classroom {
  id: string;
  name: string;
}

export interface QueryResponse {
  resultObject: QA[];
}

export interface ClassroomResponse {
  resultObject: Classroom[];
}
