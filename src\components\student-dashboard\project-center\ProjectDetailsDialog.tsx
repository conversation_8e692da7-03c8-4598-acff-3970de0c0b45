import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Search, X } from 'lucide-react';
import { ProjectActivity } from '@/types/projectCenter';

interface ProjectDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  project: ProjectActivity | null;
}

const ProjectDetailsDialog: React.FC<ProjectDetailsDialogProps> = ({ 
  isOpen, 
  onClose, 
  project 
}) => {
  if (!project) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] p-0 bg-stone-50 rounded-xl overflow-hidden border-2 border-teal-700">
        <Tabs defaultValue="overview" className="w-full">
          <div className="bg-teal-700 px-6 pt-4 rounded-t-xl">
            <div className="flex justify-between items-center">
              <DialogTitle className="text-white text-xl font-semibold">
                {project.projectName}
              </DialogTitle>
              <DialogClose asChild>
                <Button variant="ghost" size="icon" className="text-white hover:bg-teal-600 rounded-full">
                  <X size={24} />
                </Button>
              </DialogClose>
            </div>
            <div className="relative mt-3">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
              <Input
                type="search"
                placeholder="Search..."
                className="w-full bg-white border-gray-300 text-gray-900 placeholder-gray-500 pl-10 h-10 rounded-xl focus:ring-teal-400 focus:border-teal-400"
              />
            </div>
            <TabsList className="grid w-full grid-cols-4 text-gray-300 bg-teal-700  pb-2 mt-3">
              {['Overview', 'Tasks', 'Resources', 'Group Info'].map(tab => (
                <TabsTrigger 
                  key={tab} 
                  value={tab.toLowerCase().replace(' ', '')}
                  className="data-[state=active]:bg-teal-600 data-[state=active]:text-white data-[state=active]:font-semibold rounded-xl py-2 text-sm mb-3"
                >
                  {tab}
                </TabsTrigger>
              ))}
            </TabsList>
            <div className='h-3 w-full bg-teal-700' />
          </div>
          
          <DialogDescription asChild>
            <ScrollArea className="h-[calc(100vh-250px)]">
              <div className="p-6">
                <TabsContent value="overview">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-zinc-800">Academics</h3>
                    <p className="text-zinc-700 text-sm leading-relaxed">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                    </p>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="font-medium text-zinc-500">Type</p>
                        <p className="text-zinc-800 font-semibold">{project.type}</p>
                      </div>
                      <div>
                        <p className="font-medium text-zinc-500">Submission Status</p>
                        <div className="flex items-center gap-1.5">
                          <span className={`w-2 h-2 ${project.submissionStatus.statusColor.replace('text-', 'bg-')} rounded-full`}></span>
                          <span className={`${project.submissionStatus.statusColor} font-semibold`}>
                            {project.submissionStatus.count}/{project.submissionStatus.total} In Progress
                          </span>
                        </div>
                      </div>
                      <div>
                        <p className="font-medium text-zinc-500">Peer Review Completion</p>
                        <p className="text-zinc-800 font-semibold">
                          {Math.round((project.peerReviewStatus.count / project.peerReviewStatus.total) * 100)}%
                        </p>
                      </div>
                    </div>
                    <h3 className="text-xl font-semibold text-zinc-800 pt-2">Instructor Remarks</h3>
                    <p className="text-zinc-700 text-sm leading-relaxed">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                    </p>
                  </div>
                </TabsContent>
                <TabsContent value="tasks">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-zinc-800">Tasks</h3>
                    <p className="text-zinc-700 text-sm leading-relaxed">
                      Task details will be displayed here.
                    </p>
                  </div>
                </TabsContent>
                <TabsContent value="resources">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-zinc-800">Resources</h3>
                    <p className="text-zinc-700 text-sm leading-relaxed">
                      Resource details will be displayed here.
                    </p>
                  </div>
                </TabsContent>
                <TabsContent value="groupinfo">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-zinc-800">Group Information</h3>
                    <p className="text-zinc-700 text-sm leading-relaxed">
                      Group member details and other information will be displayed here.
                    </p>
                  </div>
                </TabsContent>
              </div>
            </ScrollArea>
          </DialogDescription>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default ProjectDetailsDialog; 