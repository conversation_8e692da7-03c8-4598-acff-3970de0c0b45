import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { SummaryInfo } from '@/types/projectCenter';

interface SummaryCardProps extends SummaryInfo {}

const SummaryCard: React.FC<SummaryCardProps> = ({ 
  title, 
  value, 
  iconSrc, 
  iconBgColor, 
  filterOptions 
}) => {
  return (
    <Card className="rounded-2xl shadow-[0px_0px_4px_0px_rgba(0,0,0,0.25)] border-lime-500 border-[0.5px]">
      <CardContent className="p-[18px] flex flex-col justify-between h-full">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-zinc-600 text-sm font-normal">{title}</p>
            <p className="text-zinc-800 text-4xl font-semibold">{value}</p>
          </div>
          <div className={`${iconBgColor} rounded-full flex items-center justify-center w-16 h-16 overflow-hidden`}>
            <img src={iconSrc} alt={title} className="w-10 h-10 object-contain" />
          </div>
        </div>
        {filterOptions && (
          <div className="mt-auto pt-2">
            <Tabs defaultValue="academic" className="w-full">
              <TabsList className="grid w-full grid-cols-2 h-8 bg-white border border-slate-600 border-[0.79px] p-0 rounded-xl overflow-hidden">
                <TabsTrigger 
                  value="academic" 
                  className="text-xs data-[state=active]:bg-slate-600 data-[state=active]:text-white rounded-l-lg h-full py-1.5 px-2.5"
                >
                  {filterOptions.academicLabel}
                </TabsTrigger>
                <TabsTrigger 
                  value="industry" 
                  className="text-xs data-[state=active]:bg-slate-600 data-[state=active]:text-white rounded-r-lg h-full py-1.5 px-2.5"
                >
                  {filterOptions.industryLabel}
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SummaryCard; 