import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, RefreshCw, Save, ChevronDown, ChevronRight, BookOpen, List, FileText, HelpCircle, FolderPlus } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Badge } from "@/components/ui/badge";
import DOMPurify from 'dompurify';
import { Maximize2, Minimize2 } from "lucide-react";
import { ImproveContentDialog } from './ImproveContentDialog';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";

export interface CourseContentPreview {
    courseName: string;
    courseDescription: string;
    chapters: Array<{
        name: string;
        description: string;
        topics: Array<{
            name: string;
            description: string;
            content: string;
            type: number;
            contentPreview?: string;
        }>;
    }>;
}

interface GenerateContentModalProps {
    courseName: string;
    courseDescription: string;
    numChapters?: number;
    onSave: (content: CourseContentPreview) => void;
    onClose: () => void;
}

const ContentPreview: React.FC<{
    content: string;
    expanded: boolean;
    onToggle: () => void;
}> = ({ content, expanded, onToggle }) => {
    const sanitizedContent = DOMPurify.sanitize(content);

    return (
        <div className="mt-2 space-y-2">
            <div className="flex items-center justify-between">
                <p className="text-sm font-medium">Content Preview:</p>
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={onToggle}
                    className="h-8 px-2 hover:bg-secondary/80"
                >
                    {expanded ? (
                        <Minimize2 className="h-4 w-4" />
                    ) : (
                        <Maximize2 className="h-4 w-4" />
                    )}
                </Button>
            </div>
            <div
                className={`overflow-hidden transition-all duration-200 ${expanded ? 'max-h-[500px]' : 'max-h-[100px]'
                    }`}
            >
                <div
                    className={`p-3 bg-secondary/20 rounded-md prose prose-sm max-w-none ${!expanded && 'mask-linear-gradient'
                        }`}
                    dangerouslySetInnerHTML={{ __html: sanitizedContent }}
                />
            </div>
            {!expanded && content.length > 200 && (
                <p className="text-xs text-muted-foreground text-center">
                    Click expand to see more
                </p>
            )}
        </div>
    );
};

export const GenerateContentModal: React.FC<GenerateContentModalProps> = ({
    courseName,
    courseDescription,
    numChapters = 5,
    onSave,
    onClose
}) => {
    const [isGenerating, setIsGenerating] = useState(false);
    const [previewContent, setPreviewContent] = useState<CourseContentPreview | null>(null);
    const [chapterCount, setChapterCount] = useState(numChapters);
    const [expandedChapters, setExpandedChapters] = useState<Set<number>>(new Set());
    const [expandedContent, setExpandedContent] = useState<Set<string>>(new Set());
    const [improveDialogConfig, setImproveDialogConfig] = useState<{
        isOpen: boolean;
        target: string;
        targetType: 'chapter' | 'topic' | 'content';
    }>({
        isOpen: false,
        target: '',
        targetType: 'content'
    });
    const [isGeneratingQuestion, setIsGeneratingQuestion] = useState(false);
    const [isGeneratingChapter, setIsGeneratingChapter] = useState(false);
    const [generatingChapterState, setGeneratingChapterState] = useState<{
        isLoading: boolean;
        progress: string;
    }>({
        isLoading: false,
        progress: ''
    });

    // New states for streaming
    const [streamingProgress, setStreamingProgress] = useState<{
        progress: number;
        message: string;
        chaptersGenerated: number;
        totalChapters: number;
    }>({
        progress: 0,
        message: '',
        chaptersGenerated: 0,
        totalChapters: 0
    });
    const eventSourceRef = useRef<EventSource | null>(null);

    const toggleChapter = (index: number) => {
        const newExpanded = new Set(expandedChapters);
        if (newExpanded.has(index)) {
            newExpanded.delete(index);
        } else {
            newExpanded.add(index);
        }
        setExpandedChapters(newExpanded);
    };

    const toggleContent = (chapterIdx: number, topicIdx: number) => {
        const key = `${chapterIdx}-${topicIdx}`;
        const newExpanded = new Set(expandedContent);
        if (newExpanded.has(key)) {
            newExpanded.delete(key);
        } else {
            newExpanded.add(key);
        }
        setExpandedContent(newExpanded);
    };

    // Cleanup function for the EventSource
    useEffect(() => {
        return () => {
            if (eventSourceRef.current) {
                eventSourceRef.current.close();
            }
        };
    }, []);

    const generateContent = async () => {
        // Reset states
        setIsGenerating(true);
        setPreviewContent(null);
        setStreamingProgress({
            progress: 0,
            message: 'Initiating content generation...',
            chaptersGenerated: 0,
            totalChapters: chapterCount
        });

        // Close any existing connection
        if (eventSourceRef.current) {
            eventSourceRef.current.close();
        }

        try {
            // Create EventSource for SSE streaming
            const queryParams = new URLSearchParams({
                courseName,
                courseDescription,
                numChapters: chapterCount.toString(),
            }).toString();

            const eventSource = new EventSource(
                `https://lcf.stmin.dev/api/generate-course-stream?${queryParams}`,
                { withCredentials: false }
            );
            eventSourceRef.current = eventSource;

            // Initialize empty content
            let currentContent: CourseContentPreview = {
                courseName,
                courseDescription,
                chapters: []
            };

            // Handle the stream events
            eventSource.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    console.log('Stream data received:', data);

                    switch (data.type) {
                        case 'init':
                            // Initial content structure
                            currentContent = data.content;
                            setPreviewContent({ ...currentContent });
                            break;

                        case 'progress':
                            // Update progress message
                            setStreamingProgress(prev => ({
                                ...prev,
                                message: data.message
                            }));
                            break;

                        case 'chapter':
                            // Add new chapter
                            currentContent.chapters.push(data.content);
                            setPreviewContent({ ...currentContent });

                            // Auto-expand the new chapter
                            setExpandedChapters(prev => new Set([...Array.from(prev), currentContent.chapters.length - 1]));

                            // Update progress
                            setStreamingProgress(prev => ({
                                ...prev,
                                chaptersGenerated: prev.chaptersGenerated + 1,
                                progress: ((prev.chaptersGenerated + 1) / prev.totalChapters) * 100,
                            }));
                            break;

                        case 'error':
                            toast({
                                title: "Error",
                                description: data.message,
                                variant: "destructive"
                            });
                            break;

                        case 'complete':
                            // Content generation complete
                            setStreamingProgress(prev => ({
                                ...prev,
                                progress: 100,
                                message: 'Content generation complete!'
                            }));

                            toast({
                                title: "Content generated successfully",
                                description: "Review and save the generated content",
                                variant: "default"
                            });

                            // Close the connection
                            eventSource.close();
                            eventSourceRef.current = null;
                            setIsGenerating(false);
                            break;
                    }
                } catch (error) {
                    console.error('Error processing stream data:', error);
                }
            };

            eventSource.onerror = (error) => {
                console.error('EventSource error:', error);
                toast({
                    title: "Stream error",
                    description: "There was an error with the content stream",
                    variant: "destructive"
                });

                eventSource.close();
                eventSourceRef.current = null;
                setIsGenerating(false);
            };

        } catch (error) {
            console.error('Error setting up EventSource:', error);
            toast({
                title: "Failed to generate content",
                description: "Please try again later",
                variant: "destructive"
            });
            setIsGenerating(false);
        }
    };

    const generateQuestion = async () => {
        setIsGeneratingQuestion(true);
        try {
            const response = await fetch("https://lcf.stmin.dev/api/generate-question", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    courseName,
                    courseDescription,
                    existingContent: previewContent
                })
            });

            if (!response.ok) throw new Error("Error generating question");

            const data = await response.json();
            // Add new question as a topic to the first chapter or create new chapter
            const updatedContent = {
                ...previewContent!,
                chapters: [
                    {
                        name: "Questions",
                        description: "Generated questions for the course",
                        topics: [{
                            name: "Question " + (previewContent?.chapters[0]?.topics?.length || 1),
                            description: data.question,
                            content: `${data.question}\n\nAnswer: ${data.answer}`,
                            type: 1
                        }],
                        ...(previewContent?.chapters[0] || {})
                    },
                    ...(previewContent?.chapters.slice(1) || [])
                ]
            };

            setPreviewContent(updatedContent);
            toast({
                title: "Question generated successfully",
                description: "New question has been added to the content",
                variant: "default"
            });
        } catch (error) {
            toast({
                title: "Failed to generate question",
                description: "Please try again later",
                variant: "destructive"
            });
        } finally {
            setIsGeneratingQuestion(false);
        }
    };

    const generateNewChapter = async () => {
        setGeneratingChapterState({ isLoading: true, progress: 'Initiating chapter generation...' });
        try {
            const response = await fetch("https://lcf.stmin.dev/api/generate-chapter", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    courseName,
                    courseDescription,
                    existingContent: previewContent,
                    numTopics: 3
                })
            });

            if (!response.ok) throw new Error("Error generating chapter");

            setGeneratingChapterState(prev => ({
                ...prev,
                progress: 'Processing generated content...'
            }));

            const data = await response.json();

            // Add new chapter to existing content
            const updatedContent = {
                ...previewContent!,
                chapters: [
                    ...previewContent!.chapters,
                    {
                        name: data.name,
                        description: data.description,
                        topics: data.topics.map((topic: any) => ({
                            ...topic,
                            content: enrichContentWithHTML(topic.content)
                        }))
                    }
                ]
            };

            setPreviewContent(updatedContent);
            // Automatically expand the new chapter
            setExpandedChapters(prev => new Set([...Array.from(prev), updatedContent.chapters.length - 1]));

            toast({
                title: "Chapter generated successfully",
                description: "New chapter has been added to the course",
                variant: "default"
            });
        } catch (error) {
            toast({
                title: "Failed to generate chapter",
                description: "Please try again later",
                variant: "destructive"
            });
        } finally {
            setGeneratingChapterState({ isLoading: false, progress: '' });
        }
    };

    const getContentTypeIcon = (type: number) => {
        switch (type) {
            case 1: return <FileText className="w-4 h-4" />;
            case 2: return <BookOpen className="w-4 h-4" />;
            default: return <List className="w-4 h-4" />;
        }
    };

    const getContentTypeLabel = (type: number) => {
        switch (type) {
            case 1: return "Text";
            case 2: return "SCORM";
            case 3: return "Video";
            case 4: return "Audio";
            default: return "Other";
        }
    };

    const handleImproveComplete = (improvedContent: CourseContentPreview) => {
        setPreviewContent(improvedContent);
        setImproveDialogConfig({ isOpen: false, target: '', targetType: 'content' });
        toast({
            title: "Content improved successfully",
            variant: "default",
        });
    };

    // Helper function to enrich content with HTML formatting
    const enrichContentWithHTML = (content: string): string => {
        // Add proper HTML formatting to the content
        return content
            .split('\n\n')
            .map(paragraph => `<p>${paragraph}</p>`)
            .join('\n');
    };

    return (
        <Dialog open onOpenChange={(open) => { if (!open) onClose(); }}>
            <DialogContent className="max-w-5xl max-h-[90vh] flex flex-col p-6">
                <DialogHeader className="pb-4 border-b">
                    <div className="flex items-center justify-between">
                        <DialogTitle className="text-xl">Generate Course Content</DialogTitle>
                        {previewContent && (
                            <div className="flex items-center gap-2">
                                {generatingChapterState.isLoading && (
                                    <p className="text-sm text-muted-foreground animate-pulse">
                                        {generatingChapterState.progress}
                                    </p>
                                )}
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={generateNewChapter}
                                                disabled={generatingChapterState.isLoading || isGenerating}
                                                className="flex items-center gap-2"
                                            >
                                                {generatingChapterState.isLoading ? (
                                                    <Loader2 className="h-4 w-4 animate-spin" />
                                                ) : (
                                                    <FolderPlus className="h-4 w-4" />
                                                )}
                                                Generate Chapter
                                            </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>Generate a new chapter with topics</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            </div>
                        )}
                    </div>
                </DialogHeader>

                <div className="flex-1 min-h-0 py-4">
                    {isGenerating && (
                        <div className="mb-6 space-y-4 bg-secondary/20 p-4 rounded-md">
                            <div className="flex items-center gap-2">
                                <Loader2 className="w-4 h-4 animate-spin text-primary" />
                                <p className="text-sm font-medium">{streamingProgress.message}</p>
                            </div>
                            <div className="space-y-1">
                                <Progress value={streamingProgress.progress} />
                                <p className="text-xs text-muted-foreground text-right">
                                    {streamingProgress.chaptersGenerated} / {streamingProgress.totalChapters} chapters
                                </p>
                            </div>
                        </div>
                    )}

                    {(!previewContent && !isGenerating) ? (
                        <div className="space-y-4 p-4">
                            <div className="space-y-2">
                                <Label>Number of Chapters</Label>
                                <Input
                                    type="number"
                                    min={1}
                                    max={10}
                                    value={chapterCount}
                                    onChange={(e) => setChapterCount(parseInt(e.target.value) || 1)}
                                />
                            </div>
                            <p className="text-sm text-muted-foreground">
                                Click "Generate" to create sample chapters and topics for this course.
                            </p>
                        </div>
                    ) : previewContent ? (
                        <ScrollArea className="h-[calc(90vh-12rem)] pr-4">
                            <Card className="mb-6 bg-secondary/20">
                                <CardHeader className="pb-2">
                                    <CardTitle className="text-lg">{previewContent.courseName}</CardTitle>
                                    <p className="text-sm text-muted-foreground">{previewContent.courseDescription}</p>
                                </CardHeader>
                            </Card>

                            <div className="space-y-6">
                                {previewContent.chapters.map((chapter, idx) => (
                                    <Collapsible
                                        key={idx}
                                        open={expandedChapters.has(idx)}
                                        onOpenChange={() => toggleChapter(idx)}
                                    >
                                        <Card className="border-l-4 border-l-primary">
                                            <CardHeader className="p-4">
                                                <CollapsibleTrigger className="flex items-center justify-between w-full">
                                                    <div className="flex items-center gap-3">
                                                        {expandedChapters.has(idx) ?
                                                            <ChevronDown className="w-5 h-5 shrink-0" /> :
                                                            <ChevronRight className="w-5 h-5 shrink-0" />}
                                                        <div>
                                                            <h4 className="font-semibold text-left">Chapter {idx + 1}: {chapter.name}</h4>
                                                            <p className="text-sm text-muted-foreground text-left">{chapter.description}</p>
                                                        </div>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                setImproveDialogConfig({
                                                                    isOpen: true,
                                                                    target: `chapter-${idx}`,
                                                                    targetType: 'chapter'
                                                                });
                                                            }}
                                                            className="ml-2"
                                                        >
                                                            <RefreshCw className="w-4 h-4" />
                                                        </Button>
                                                    </div>
                                                </CollapsibleTrigger>
                                            </CardHeader>
                                            <CollapsibleContent>
                                                <CardContent className="pl-12 pr-4 pb-4">
                                                    <div className="space-y-4">
                                                        {chapter.topics?.map((topic, topicIdx) => (
                                                            <Card key={topicIdx} className="p-4 hover:shadow-md transition-shadow">
                                                                <div className="space-y-3">
                                                                    <div className="flex items-center justify-between">
                                                                        <div className="flex-1">
                                                                            <h5 className="font-medium">{topic.name}</h5>
                                                                            <p className="text-sm text-muted-foreground mt-1">
                                                                                {topic.description}
                                                                            </p>
                                                                        </div>
                                                                        <div className="flex items-center gap-2 ml-4">
                                                                            <Badge className="flex items-center gap-1">
                                                                                {getContentTypeIcon(topic.type)}
                                                                                {getContentTypeLabel(topic.type)}
                                                                            </Badge>
                                                                            <Button
                                                                                variant="ghost"
                                                                                size="sm"
                                                                                onClick={() => setImproveDialogConfig({
                                                                                    isOpen: true,
                                                                                    target: `topic-${idx}-${topicIdx}`,
                                                                                    targetType: 'topic'
                                                                                })}
                                                                            >
                                                                                <RefreshCw className="w-4 h-4" />
                                                                            </Button>
                                                                        </div>
                                                                    </div>
                                                                    {topic.content && (
                                                                        <ContentPreview
                                                                            content={topic.content}
                                                                            expanded={expandedContent.has(`${idx}-${topicIdx}`)}
                                                                            onToggle={() => toggleContent(idx, topicIdx)}
                                                                        />
                                                                    )}
                                                                </div>
                                                            </Card>
                                                        ))}
                                                    </div>
                                                </CardContent>
                                            </CollapsibleContent>
                                        </Card>
                                    </Collapsible>
                                ))}
                            </div>
                        </ScrollArea>
                    ) : null
                    }
                </div >

                <DialogFooter className="pt-4 border-t">
                    <div className="flex justify-end gap-2">
                        <Button
                            variant="secondary"
                            onClick={generateContent}
                            disabled={isGenerating}
                        >
                            <RefreshCw className={`w-4 h-4 mr-1 ${isGenerating ? 'animate-spin' : ''}`} />
                            {previewContent ? "Regenerate" : "Generate"}
                        </Button>

                        {
                            previewContent && (
                                <Button onClick={() => onSave(previewContent)} disabled={isGenerating}>
                                    <Save className="w-4 h-4 mr-1" />
                                    Save
                                </Button>
                            )
                        }

                        <Button variant="outline" onClick={onClose}>
                            Cancel
                        </Button>
                    </div >
                </DialogFooter >
                {
                    improveDialogConfig.isOpen && previewContent && (
                        <ImproveContentDialog
                            content={previewContent}
                            target={improveDialogConfig.target}
                            targetType={improveDialogConfig.targetType}
                            onClose={() => setImproveDialogConfig({ ...improveDialogConfig, isOpen: false })}
                            onImproveComplete={handleImproveComplete}
                        />
                    )
                }
            </DialogContent >
        </Dialog >
    );
};
