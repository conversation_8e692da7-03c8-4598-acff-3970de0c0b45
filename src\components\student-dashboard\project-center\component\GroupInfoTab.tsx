import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useGetUserByIdQuery, useGetAssignmentSubmissionQuery } from '@/APIConnect';
import { User } from '@/types/projectCenter';
import { Edit } from 'lucide-react';

interface GroupMember {
  id: string;
  name: string;
  avatarUrl: string;
}

interface StudentMemberProps {
  studentId: string;
  index: number;
}

// Component to fetch and display individual student data
const StudentMember: React.FC<StudentMemberProps> = ({ studentId, index }) => {
  const {
    data: userData,
    isLoading,
    error
  } = useGetUserByIdQuery(studentId);

  // Get student name or fallback
  const getStudentName = (): string => {
    if (userData && !error) {
      const user: User = userData;
      return `${user.firstName} ${user.lastName}`;
    }
    return `Member ${index + 1}`; // Fallback to generic name
  };

  const studentName = getStudentName();

  // Show loading state
  if (isLoading) {
    return (
      <Card className="p-3 flex items-center gap-3 bg-white shadow-sm rounded-[8px] border border-gray-200 min-w-[150px]">
        <div className="inline-block h-7 w-7 rounded-full bg-gray-200 animate-pulse border border-[#347468]"></div>
        <div className="h-4 bg-gray-200 animate-pulse rounded w-16"></div>
      </Card>
    );
  }

  return (
    <Card className="p-3 flex items-center gap-3 bg-white shadow-sm rounded-[8px] border border-gray-200 min-w-[150px]">
      <img
        className="inline-block h-7 w-7 rounded-full ring-1 ring-white border border-[#347468]"
        src={`https://i.pravatar.cc/28?u=${studentId}`}
        alt={studentName}
      />
      <span className="font-medium text-sm text-gray-700">{studentName}</span>
    </Card>
  );
};

// Component to fetch and display individual assignment submission status
interface AssignmentSubmissionProps {
  groupId: string;
  assignmentId: string;
  assignmentTitle: string;
  index: number;
  onEditAssignment?: (assignmentId: string, assignmentTitle: string) => void;
}

const AssignmentSubmissionStatus: React.FC<AssignmentSubmissionProps> = ({
  groupId,
  assignmentId,
  assignmentTitle,
  index,
  onEditAssignment
}) => {
  const {
    data: submissionData,
    isLoading,
    error
  } = useGetAssignmentSubmissionQuery({ groupId, assignmentId });

  // Determine status based on submission data
  const getSubmissionStatus = (): string => {
    if (error || !submissionData?.resultObject) {
      return 'Submission Pending';
    }

    const submission = submissionData.resultObject;

    // Check instructor review first (highest priority)
    if (submission.instructorReviewed) {
      return 'Instructor Reviewed';
    }

    // Check peer review next
    if (submission.peerReviewed) {
      return 'Upcoming Peer Review';
    }

    // Check if file is submitted
    if (submission.fileUrl && submission.fileUrl.trim() !== '') {
      return 'Submitted';
    }

    // Default to pending
    return 'Submission Pending';
  };

  // Get formatted date from updatedon field
  const getFormattedDate = (): string => {
    if (submissionData?.resultObject?.updatedon) {
      const date = new Date(submissionData.resultObject.updatedon);
      return date.toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      });
    }
    return 'No date';
  };

  const status = getSubmissionStatus();
  const formattedDate = getFormattedDate();

  // Show loading state
  if (isLoading) {
    return (
      <Card className="p-4 flex items-center justify-between bg-white shadow-sm rounded-[8px] border border-[#347468] hover:border-[#347468]/60 transition-colors">
        <div>
          <div className="h-3 bg-gray-200 animate-pulse rounded w-32 mb-2"></div>
          <div className="h-4 bg-gray-200 animate-pulse rounded w-24"></div>
        </div>
        <div className="inline-block h-7 w-7 rounded-full bg-gray-200 animate-pulse"></div>
      </Card>
    );
  }

  const showEditButton = status === 'Submitted' && onEditAssignment;

  return (
    <Card className="p-4 flex items-center justify-between bg-white shadow-sm rounded-[8px] border border-[#347468] hover:border-[#347468]/60 transition-colors">
      <div className="flex-1">
        <p className="text-xs text-gray-500">
          {status} | <span className="font-medium text-[#2D2D2D]">{formattedDate}</span>
        </p>
        <h4 className="font-semibold text-md text-[#347468]">{assignmentTitle}</h4>
      </div>
      <div className="flex items-center gap-3">
        {showEditButton && (
          <Button
            onClick={() => onEditAssignment(assignmentId, assignmentTitle)}
            variant="outline"
            size="sm"
            className="h-8 w-8 p-0 border-[#347468] text-[#347468] hover:bg-[#347468] hover:text-white"
          >
            <Edit className="h-4 w-4" />
          </Button>
        )}
        <img
          className="inline-block h-7 w-7 rounded-full ring-1 ring-white"
          src={`https://i.pravatar.cc/28?u=assignment${index + Math.random()}`}
          alt={assignmentTitle}
        />
      </div>
    </Card>
  );
};

interface SubmissionItem {
  id: string;
  status: string;
  date: string;
  title: string;
  avatarUrl: string;
}

interface GroupInfoTabProps {
  groupData?: any; // Group data from API
  projectAssignments?: any[]; // Project assignments data
  onEditAssignment?: (assignmentId: string, assignmentTitle: string) => void; // Edit handler
}

// Fallback data for when no real data is available
const fallbackGroupMembersData: GroupMember[] = [
  { id: '1', name: 'John Doe', avatarUrl: '/icons/users.png' },
  { id: '2', name: 'Wick Doe', avatarUrl: '/icons/willow.png' },
  { id: '3', name: 'Barry Doe', avatarUrl: '/icons/navbar-icons/Icons 4.png' },
  { id: '4', name: 'Alan Doe', avatarUrl: '/icons/navbar-icons/Icons 3.png' },
  { id: '5', name: 'Peter Doe', avatarUrl: '/icons/project-center-icons/users.png' },
];

const submissionStatusData: SubmissionItem[] = [
  {
    id: '1',
    status: 'Submission Pending',
    date: '1 May 2025',
    title: 'Types of Noun',
    avatarUrl: '/icons/users.png',
  },
  {
    id: '2',
    status: 'Upcoming Peer Review',
    date: '11 May 2025',
    title: 'English Assignment',
    avatarUrl: '/icons/willow.png',
  },
  {
    id: '3',
    status: 'Submission',
    date: '14 May 2025',
    title: 'Type of Pronoun',
    avatarUrl: '/icons/navbar-icons/Icons 4.png',
  },
];

const GroupInfoTab: React.FC<GroupInfoTabProps> = ({ groupData, projectAssignments, onEditAssignment }) => {
  // Get student IDs from group data
  const getStudentIds = (): string[] => {
    if (groupData && groupData.studentIds && Array.isArray(groupData.studentIds)) {
      return groupData.studentIds;
    }
    return [];
  };

  const studentIds = getStudentIds();

  // Helper function to convert MongoDB ObjectId to string
  const objectIdToString = (id: any): string => {
    if (typeof id === 'string') return id;
    if (id && typeof id === 'object' && id.timestamp) {
      const timestamp = id.timestamp.toString(16).padStart(8, '0');
      const machine = id.machine.toString(16).padStart(6, '0');
      const pid = id.pid.toString(16).padStart(4, '0');
      const increment = id.increment.toString(16).padStart(6, '0');
      return `${timestamp}${machine}${pid}${increment}`;
    }
    return String(id);
  };

  // Get group ID as string
  const groupId = groupData?.id ? objectIdToString(groupData.id) : null;

  return (
    <div className="space-y-8 py-6 text-[#2D2D2D]">
      {/* Group Members */}
      <div>
        <h3 className="text-lg font-semibold text-[#347468] mb-4">Group Members:</h3>
        <div className="flex flex-wrap gap-4">
          {studentIds.length > 0 ? (
            studentIds.map((studentId, index) => (
              <StudentMember key={studentId} studentId={studentId} index={index} />
            ))
          ) : (
            // Fallback to mock data if no real student IDs
            fallbackGroupMembersData.map((member, index) => (
              <Card key={member.id} className="p-3 flex items-center gap-3 bg-white shadow-sm rounded-[8px] border border-gray-200 min-w-[150px]">
                <img
                  className="inline-block h-7 w-7 rounded-full ring-1 ring-white border border-[#347468]"
                  src={`https://i.pravatar.cc/28?u=member${index + Math.random()}`}
                  alt={member.name}
                />
                <span className="font-medium text-sm text-gray-700">{member.name}</span>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* Submission Status */}
      <div>
        <h3 className="text-xl font-semibold text-[#347468] mb-4">Submission Status:</h3>
        <div className="space-y-3">
          {projectAssignments && projectAssignments.length > 0 && groupId ? (
            projectAssignments.map((assignment, index) => {
              const assignmentId = objectIdToString(assignment.id);
              const assignmentTitle = assignment.assignment?.title || `Assignment ${index + 1}`;

              return (
                <AssignmentSubmissionStatus
                  key={assignmentId}
                  groupId={groupId}
                  assignmentId={assignmentId}
                  assignmentTitle={assignmentTitle}
                  index={index}
                  onEditAssignment={onEditAssignment}
                />
              );
            })
          ) : (
            // Fallback to mock data if no real assignments
            submissionStatusData.map((item, index) => (
              <Card key={item.id} className="p-4 flex items-center justify-between bg-white shadow-sm rounded-[8px] border border-[#347468] hover:border-[#347468]/60 transition-colors">
                <div>
                  <p className="text-xs text-gray-500">
                    {item.status} | <span className="font-medium text-[#2D2D2D]">{item.date}</span>
                  </p>
                  <h4 className="font-semibold text-md text-[#347468]">{item.title}</h4>
                </div>
                <img
                  key={index} /* Using index as key for the image element */
                  className="inline-block h-7 w-7 rounded-full ring-1 ring-white" /* Styling as per your example */
                  src={`https://i.pravatar.cc/28?u=submission${index + Math.random()}`} /* Dynamic image URL, using a different seed prefix */
                  alt={item.title} /* Using item's title for alt text */
                />
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default GroupInfoTab;
