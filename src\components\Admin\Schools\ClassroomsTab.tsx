import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Plus, Loader2, Search, Pencil, Trash2, Users } from 'lucide-react';
import {
    useGetSchoolClassroomsQuery,
    useCreateClassroomMutation,
    useDeleteClassroomMutation,
    useUpdateClassroomMutation,
    useGetAdminCoursesQuery,
    useGetSchoolByIdQuery,
    useAddStudentToClassroomMutation,
    useGetSchoolCoursesQuery,
    useRemoveStudentFromClassroomMutation
} from 'src/APIConnect';
import { Classroom, ClassCreationRequest, User } from './types';
import { Course } from '../Courses/types';
import { toast } from "@/hooks/use-toast";
import { useNavigate } from 'react-router-dom';

interface ClassroomsTabProps {
    schoolId: string;
    isSchoolAdmin?: boolean;
}

interface FormErrors {
    name?: string;
    courseId?: string;
}

const ClassroomsTab = ({ schoolId, isSchoolAdmin = false }: ClassroomsTabProps) => {
    const navigate = useNavigate();
    const [searchQuery, setSearchQuery] = useState('');
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [selectedClassroom, setSelectedClassroom] = useState<ClassCreationRequest | null>(null);
    const [formData, setFormData] = useState<ClassCreationRequest>({
        name: '',
        description: '',
        courseId: '',
        schoolId,
        teacherIds: [],
        studentIds: [],
        createdOn: new Date().toISOString()
    });
    const [errors, setErrors] = useState<FormErrors>({});

    const { data: classroomsData, isLoading: isLoadingClassrooms, refetch } = useGetSchoolClassroomsQuery(schoolId);
    const { data: coursesData, isLoading: isLoadingCourses } = useGetSchoolCoursesQuery(schoolId);
    const { data: schoolData } = useGetSchoolByIdQuery(schoolId);
    const [createClassroom, { isLoading: isCreating }] = useCreateClassroomMutation();
    const [updateClassroom, { isLoading: isUpdating }] = useUpdateClassroomMutation();
    const [addStudentToClassroom, { isLoading: isAddingStudentToClassroom }] = useAddStudentToClassroomMutation();
    const [removeStudentFromClassroom, { isLoading: isRemovingStudentFromClassroom }] = useRemoveStudentFromClassroomMutation();
    const [deleteClassroom] = useDeleteClassroomMutation();

    const teachers = schoolData?.users?.filter(({ user }: { user: User }) => user.userRoleId === 3) || [];
    const students = schoolData?.users?.filter(({ user }: { user: User }) => user.userRoleId === 4) || [];

    const handleDelete = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this classroom?')) {
            try {
                await deleteClassroom(id).unwrap();
                toast({
                    title: "Classroom deleted successfully",
                    variant: "default",
                });
                refetch();
            } catch (error) {
                console.error('Error deleting classroom:', error);
                toast({
                    title: "Failed to delete classroom",
                    description: "Please try again later",
                    variant: "destructive",
                });
            }
        }
    };

    const handleEdit = (classroom: Classroom) => {
        setSelectedClassroom({
            id: classroom.id,
            name: classroom.name,
            description: classroom.description,
            courseId: classroom.courseId,
            schoolId,
            teacherIds: classroom.teachers?.map(teacher => teacher.user.id) || [],
            studentIds: classroom.students?.map(student => student.user.id) || [],
            createdOn: classroom.createdOn
        });
        setFormData({
            name: classroom.name,
            description: classroom.description,
            courseId: classroom.courseId,
            schoolId,
            teacherIds: classroom.teachers?.map(teacher => teacher.user.id) || [],
            studentIds: classroom.students?.map(student => student.user.id) || [],
            createdOn: classroom.createdOn
        });
        setIsEditDialogOpen(true);
    };

    const validateForm = (): boolean => {
        const newErrors: FormErrors = {};
        let isValid = true;

        if (!formData.name.trim()) {
            newErrors.name = "Classroom name is required";
            isValid = false;
        }

        if (!formData.courseId) {
            newErrors.courseId = "Course is required";
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            toast({
                title: "Please fix the errors",
                description: "Some required fields are missing or invalid",
                variant: "destructive",
            });
            return;
        }

        try {
            if (selectedClassroom) {
                await updateClassroom({ id: selectedClassroom.id, ...formData }).unwrap();
                toast({
                    title: "Classroom updated successfully",
                    variant: "default",
                });
                setIsEditDialogOpen(false);
            } else {
                await createClassroom(formData).unwrap();
                toast({
                    title: "Classroom created successfully",
                    variant: "default",
                });
                setIsCreateDialogOpen(false);
            }
            setFormData({
                name: '',
                description: '',
                courseId: '',
                schoolId,
                teacherIds: [],
                studentIds: [],
                createdOn: new Date().toISOString()
            });
            setSelectedClassroom(null);
            refetch();
        } catch (error) {
            console.error('Error saving classroom:', error);
            toast({
                title: `Failed to ${selectedClassroom ? 'update' : 'create'} classroom`,
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const filteredClassrooms = (classroomsData?.resultObject || []).filter((classroom: Classroom) => {
        const matchesSearch = searchQuery
            ? classroom.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            classroom.description.toLowerCase().includes(searchQuery.toLowerCase())
            : true;
        return matchesSearch;
    });

    const renderForm = () => (
        <form onSubmit={handleSubmit} className="space-y-4">
            <div>
                <Label>Classroom Name</Label>
                <Input
                    value={formData.name}
                    onChange={(e) => {
                        setFormData((prev) => ({ ...prev, name: e.target.value }));
                        if (errors.name) {
                            setErrors((prev) => ({ ...prev, name: undefined }));
                        }
                    }}
                    placeholder="Enter classroom name..."
                    className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                    <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                )}
            </div>

            <div>
                <Label>Description</Label>
                <Textarea
                    value={formData.description}
                    onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter classroom description..."
                />
            </div>

            <div>
                <Label>Course</Label>
                <Select
                    value={formData.courseId}
                    disabled={selectedClassroom !== null}
                    onValueChange={(value) => {
                        setFormData((prev) => ({ ...prev, courseId: value }));
                        if (errors.courseId) {
                            setErrors((prev) => ({ ...prev, courseId: undefined }));
                        }
                    }}
                >
                    <SelectTrigger className={errors.courseId ? "border-red-500" : ""}>
                        <SelectValue placeholder="Select course" />
                    </SelectTrigger>
                    <SelectContent>
                        {coursesData?.resultObject?.map((course: Course) => (
                            <SelectItem key={course.id} value={course.id}>
                                {course.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
                {errors.courseId && (
                    <p className="text-sm text-red-500 mt-1">{errors.courseId}</p>
                )}
            </div>

            <div>
                <Label>Teachers</Label>
                <Select
                    value={formData.teacherIds[0] || ''}
                    disabled={selectedClassroom !== null}
                    onValueChange={(value) => {
                        setFormData((prev) => ({ ...prev, teacherIds: [value] }));
                    }}
                >
                    <SelectTrigger>
                        <SelectValue placeholder="Select teacher" />
                    </SelectTrigger>
                    <SelectContent>
                        {teachers.map(({ user }: { user: User }) => (
                            <SelectItem key={user.id} value={user.id}>
                                {`${user.firstName} ${user.lastName}`}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>

            <div>
                <Label>Students</Label>
                <ScrollArea className="h-40 border rounded-md p-2">
                    <div className="space-y-2">
                        {students.map(({ user }: { user: User }) => (
                            <div key={user.id} className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id={user.id}
                                    checked={formData.studentIds.includes(user.id)}
                                    disabled={isAddingStudentToClassroom || isRemovingStudentFromClassroom}
                                    onChange={(e) => {
                                        if (selectedClassroom) {
                                            if (e.target.checked) {
                                                addStudentToClassroom({ classroomId: selectedClassroom?.id || '', studentId: user.id })
                                                    .then((res) => {
                                                        if (!res.error) {
                                                            console.log(res);
                                                            toast({
                                                                title: "Student added to classroom",
                                                                variant: "default",
                                                            })
                                                            setFormData((prev) => ({ ...prev, studentIds: [...prev.studentIds, user.id] }));
                                                            refetch();
                                                        } else {
                                                            toast({
                                                                title: "Failed to add student to classroom",
                                                                description: "Please try again later",
                                                                variant: "destructive",
                                                            });
                                                        }

                                                    }).catch((error) => {
                                                        console.error('Error adding student to classroom:', error);
                                                        toast({
                                                            title: "Failed to add student to classroom",
                                                            description: "Please try again later",
                                                            variant: "destructive",
                                                        });
                                                    });
                                            } else {
                                                removeStudentFromClassroom({ classroomId: selectedClassroom?.id || '', studentId: user.id })
                                                    .then((res) => {
                                                        console.log(res);
                                                        if (!res.error) {
                                                            toast({
                                                                title: "Student removed from classroom",
                                                                variant: "default",
                                                            })
                                                            setFormData((prev) => ({ ...prev, studentIds: prev.studentIds.filter(id => id !== user.id) }));
                                                            refetch();
                                                        } else {
                                                            toast({
                                                                title: "Failed to remove student from classroom",
                                                                description: "Please try again later",
                                                                variant: "destructive",
                                                            });
                                                        }
                                                    }).catch((error) => {
                                                        console.error('Error removing student from classroom:', error);
                                                        toast({
                                                            title: "Failed to remove student from classroom",
                                                            description: "Please try again later",
                                                            variant: "destructive",
                                                        });
                                                    });
                                            }
                                        } else {
                                            if (e.target.checked) {
                                                setFormData((prev) => ({ ...prev, studentIds: [...prev.studentIds, user.id] }));
                                            } else {
                                                setFormData((prev) => ({ ...prev, studentIds: prev.studentIds.filter(id => id !== user.id) }));
                                            }
                                        }

                                    }}
                                />
                                <label htmlFor={user.id}>
                                    {`${user.firstName} ${user.lastName}`}
                                </label>
                            </div>
                        ))}
                    </div>
                </ScrollArea>
            </div>

            <div className="flex justify-end space-x-2">
                <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                        selectedClassroom ? setIsEditDialogOpen(false) : setIsCreateDialogOpen(false);
                        setSelectedClassroom(null);
                    }}
                >
                    Cancel
                </Button>
                {!selectedClassroom && <Button type="submit" disabled={isCreating || isUpdating}>
                    {(isCreating || isUpdating) ? (
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                        <Plus className="w-4 h-4 mr-2" />
                    )}
                    {selectedClassroom ? 'Update' : 'Create'}
                </Button>}
            </div>
        </form>
    );

    return (
        <div className="space-y-6">
            {!isSchoolAdmin && <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
                <h2 className="text-xl font-semibold flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    Classrooms
                </h2>
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full md:w-auto">
                    <div className="relative flex-1 sm:flex-none">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                            placeholder="Search classrooms..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-8"
                        />
                    </div>
                    <Dialog open={isCreateDialogOpen} onOpenChange={(o) => {
                        if (o) {
                            setSelectedClassroom(null);
                            setFormData({
                                name: '',
                                description: '',
                                courseId: '',
                                schoolId,
                                teacherIds: [],
                                studentIds: [],
                                createdOn: new Date().toISOString()
                            });
                            setErrors({});
                        }
                        setIsCreateDialogOpen(o);
                    }}>
                        <DialogTrigger asChild>
                            <Button className="w-full sm:w-auto">
                                <Plus className="w-4 h-4 mr-2" />
                                Add Classroom
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Add New Classroom</DialogTitle>
                            </DialogHeader>
                            {renderForm()}
                        </DialogContent>
                    </Dialog>
                </div>
            </div>}

            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Edit Classroom</DialogTitle>
                    </DialogHeader>
                    {renderForm()}
                </DialogContent>
            </Dialog>

            <ScrollArea className="border rounded-md">
                {isLoadingClassrooms ? (
                    <div className="flex justify-center items-center h-32">
                        <Loader2 className="w-8 h-8 animate-spin" />
                    </div>
                ) : (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Description</TableHead>
                                <TableHead>Students</TableHead>
                                <TableHead>Teachers</TableHead>
                                <TableHead>Created On</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredClassrooms.map((classroom: Classroom) => (
                                <TableRow key={classroom.id}>
                                    <TableCell className="font-medium">{classroom.name}</TableCell>
                                    <TableCell>{classroom.description}</TableCell>
                                    <TableCell>{classroom.students?.length || 0}</TableCell>
                                    <TableCell>{classroom.teachers?.length || 0}</TableCell>
                                    <TableCell>{new Date(classroom.createdOn).toLocaleDateString()}</TableCell>
                                    <TableCell className="text-right">
                                        <div className="flex justify-end space-x-2">
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => handleEdit(classroom)}
                                            >
                                                <Pencil className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => handleDelete(classroom.id)}
                                            >
                                                <Trash2 className="h-4 w-4 text-red-500" />
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                            {filteredClassrooms.length === 0 && (
                                <TableRow>
                                    <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                                        No classrooms found
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                )}
            </ScrollArea>
        </div>
    );
};

export default ClassroomsTab;
