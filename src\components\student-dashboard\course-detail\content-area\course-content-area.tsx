// filepath: /Users/<USER>/Desktop/Projects/viswin_global/learnido-client/src/components/student-dashboard/course-detail/content-area/course-content-area.tsx
import React from 'react';
import { Button } from '@/components/ui/button';
import { Bookmark, NotebookText, BookmarkCheck } from 'lucide-react';
import QuizRenderer from '../../components/quiz/quiz-renderer';
import { CaseStudyRenderer, SelfReflectiveJournalRenderer, DiscussionForumRenderer } from '@/components/student-dashboard/components/assignments';
import ScormLesson from '@/components/scorm-content';

interface ContentData {
  id: string;
  name: string;
  type: 'video' | 'scorm' | 'text' | 'assessment' | 'html' | number;
  url?: string;
  contentUrl?: string;
  htmlContent?: string;
  content?: string;
  completed?: boolean;
  // Add other necessary content fields
}

interface CourseContentAreaProps {
  selectedTopic: any | null;
  selectedSubTopic: any | null;
  selectedAssessment: any | null;
  contentData: any | null;
  isContentLoading: boolean;
  isTopicBookmarked: boolean;
  onBookmarkClick: () => void;
  onUnbookmarkClick: () => void;
  onNotesClick: () => void;
  quizData?: any | null;
  isQuizLoading?: boolean;
  courseId?: string;
  classroomId?: string;
  onQuizCompleted?: () => void;
  onQuizCancelled?: () => void;
}

// Helper function to map content type numbers to their string representations
const mapContentTypeNumber = (typeNum: number): string => {
  switch (typeNum) {
    case 1: return 'html';
    case 2: return 'video';
    case 3: return 'audio';
    case 4: return 'scorm';
    case 5: return 'quiz';
    default: return 'html';
  }
};

const CourseContentArea: React.FC<CourseContentAreaProps> = ({
  selectedTopic,
  selectedSubTopic,
  selectedAssessment,
  contentData,
  isContentLoading,
  isTopicBookmarked,
  onBookmarkClick,
  onUnbookmarkClick,
  onNotesClick,
  quizData,
  isQuizLoading,
  courseId,
  classroomId,
  onQuizCompleted,
  onQuizCancelled,
}) => {
  // The active content should ONLY be the selected subtopic or assessment, NOT the topic
  const activeContent = selectedSubTopic || selectedAssessment;
  
  // Log the available content for debugging
  React.useEffect(() => {
    console.log('Active content:', activeContent);
    console.log('Content data:', contentData);
    console.log('Selected subtopic:', selectedSubTopic);
  }, [activeContent, contentData, selectedSubTopic]);
  
  // Loading state
  if (isContentLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 min-h-[400px] flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="h-8 w-8 rounded-full border-4 border-emerald-500 border-t-transparent animate-spin mb-4"></div>
          <p className="text-slate-500">Loading content...</p>
        </div>
      </div>
    );
  }
  
  // No subtopic or assessment selected - show a clear prompt message
  if (!activeContent) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 min-h-[400px] flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="flex justify-center mb-3">
            <div className="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <h3 className="text-lg font-medium text-slate-800 mb-2">Select a subtopic to begin</h3>
          <p className="text-slate-500">Choose a specific lesson from the module list on the right to view its content.</p>
        </div>
      </div>
    );
  }
  
  // Render assignment based on type
  const renderAssignment = (assignmentType: number, assignmentData: any) => {
    const commonProps = {
      title: assignmentData?.title || 'Assignment',
      description: assignmentData?.assignmentText || 'Complete this assignment',
    };

    switch (assignmentType) {
      case 1: // Quiz
        if (quizData && quizData.id) {
          // Check quiz type to determine which renderer to use
          const quizType = quizData.type || 'REGULAR';
          console.log('Quiz type detected:', quizType);

          if (quizType === 'SELF_REFLECTIVE_JOURNAL') {
            return (
              <div className="py-4">
                <SelfReflectiveJournalRenderer
                  {...commonProps}
                  quizData={quizData}
                  courseId={courseId}
                  onCompleted={onQuizCompleted || (() => {})}
                />
              </div>
            );
          } else {
            return (
              <div className="py-4">
                <QuizRenderer
                  quiz={quizData}
                  courseId={courseId}
                  onQuizSuccessfullyCompleted={onQuizCompleted || (() => {})}
                  onQuizCancelled={onQuizCancelled}
                />
              </div>
            );
          }
        } else if (isQuizLoading) {
          return (
            <div className="py-4">
              <div className="flex items-center justify-center min-h-[200px]">
                <div className="h-8 w-8 rounded-full border-4 border-emerald-500 border-t-transparent animate-spin mb-4"></div>
                <p className="text-slate-500 ml-4">Loading quiz...</p>
              </div>
            </div>
          );
        } else {
          return (
            <div className="py-4">
              <h3 className="text-lg font-medium text-slate-800 mb-4">Quiz: {assignmentData?.title || 'Quiz'}</h3>
              <div className="bg-slate-50 p-6 rounded-md border border-slate-200">
                <p className="text-slate-600 mb-4">Quiz data is not available for this assignment.</p>
                <p className="text-sm text-slate-500">Quiz ID: {assignmentData?.quizId || 'Not found'}</p>
              </div>
            </div>
          );
        }

      case 2: // Case Study
        return (
          <CaseStudyRenderer
            {...commonProps}
            assignmentId={selectedAssessment?.id}
            classroomId={classroomId}
            courseId={courseId}
            onCompleted={onQuizCompleted || (() => {})}
          />
        );

      case 4: // Self Reflective Journal
        if (quizData && quizData.id) {
          // For assignment type 4, always use Self Reflective Journal renderer
          return (
            <div className="py-4">
              <SelfReflectiveJournalRenderer
                {...commonProps}
                quizData={quizData}
                courseId={courseId}
                onCompleted={onQuizCompleted || (() => {})}
              />
            </div>
          );
        } else if (isQuizLoading) {
          return (
            <div className="py-4">
              <div className="flex items-center justify-center min-h-[200px]">
                <div className="h-8 w-8 rounded-full border-4 border-emerald-500 border-t-transparent animate-spin mb-4"></div>
                <p className="text-slate-500 ml-4">Loading self-reflective journal...</p>
              </div>
            </div>
          );
        } else {
          return <SelfReflectiveJournalRenderer {...commonProps} />;
        }

      case 5: // Discussion Forum
        return (
          <div className="py-4">
            <DiscussionForumRenderer
              title={assignmentData?.title || 'Discussion Forum'}
              description={assignmentData?.assignmentText || 'Participate in the discussion forum'}
              assignmentId={selectedAssessment?.id}
              courseId={courseId}
              discussionId={assignmentData?.discussionId}
              projectId={assignmentData?.projectId}
              onCompleted={onQuizCompleted || (() => {})}
            />
          </div>
        );

      case 3: // Team Project
      case 6: // Live Presentation
      case 7: // External Challenge
      case 8: // Instructor Led Reflection
      case 9: // Experiential Learning Journal
      case 10: // Scenario Based Learning
        return (
          <div className="py-4">
            <h3 className="text-lg font-medium text-slate-800 mb-4">
              Assignment: {assignmentData?.title || 'Assignment'}
            </h3>
            <div className="bg-slate-50 p-6 rounded-md border border-slate-200">
              <p className="text-slate-600 mb-4">
                This assignment type is not yet implemented.
              </p>
              <p className="text-sm text-slate-500">
                Assignment Type: {assignmentType}
              </p>
            </div>
          </div>
        );

      default:
        return (
          <div className="py-4">
            <h3 className="text-lg font-medium text-slate-800 mb-4">Unknown Assignment</h3>
            <div className="bg-slate-50 p-6 rounded-md border border-slate-200">
              <p className="text-slate-600">Unknown assignment type: {assignmentType}</p>
            </div>
          </div>
        );
    }
  };

  // Render content based on type
  const renderContent = () => {
    // Check if this is an assignment/assessment
    if (selectedAssessment) {
      // Check for assignment in the selected assessment
      if (selectedAssessment.assignment) {
        return renderAssignment(selectedAssessment.assignment.type, selectedAssessment.assignment);
      }
      // Legacy quiz handling
      else if (selectedAssessment.quizId) {
        return renderAssignment(1, {
          title: 'Quiz',
          assignmentText: 'Complete this quiz',
          quizId: selectedAssessment.quizId
        });
      }
    }

    // If no content data available
    if (!contentData) {
      return (
        <div className="text-center py-12">
          <p className="text-slate-500">Content not available. Please try again later.</p>
        </div>
      );
    }

    // Determine content type as a number (from API) or string
    const contentType = typeof contentData.type === 'number'
      ? mapContentTypeNumber(contentData.type)
      : contentData.type;

    console.log('Rendering content of type:', contentType, contentData);
      
    // Render different content based on type
    switch (contentType) {
      case 'video':
        // Handle both contentUrl (API format) and url (component format)
        const videoUrl = contentData.contentUrl || contentData.url;
        return (
          <div className="aspect-video w-full bg-black rounded-md overflow-hidden">
            {videoUrl ? (
              <iframe
                src={videoUrl.includes('youtube') ? videoUrl : `https://www.youtube.com/embed/${videoUrl.split('v=')[1] || ''}`}
                title={contentData.name || 'Video Content'}
                allowFullScreen
                className="w-full h-full"
              ></iframe>
            ) : (
              <div className="flex items-center justify-center h-full text-white">
                Video not available
              </div>
            )}
          </div>
        );
        
      case 'scorm':
        const scormUrl = contentData.contentUrl || contentData.url;
        return (
          <div className="w-full h-full min-h-[600px]">
            {scormUrl ? (
              <ScormLesson url={scormUrl} />
            ) : (
              <div className="flex items-center justify-center h-[400px] bg-gray-100 rounded-lg">
                <p className="text-gray-600">SCORM content not available</p>
              </div>
            )}
          </div>
        );
        
      case 'assessment':
      case 'quiz':
        // Legacy handling - this should now be handled by renderAssignment
        return (
          <div className="py-4">
            <h3 className="text-lg font-medium text-slate-800 mb-4">Assessment: {contentData.name || 'Quiz'}</h3>
            <div className="bg-slate-50 p-6 rounded-md border border-slate-200">
              <p className="text-slate-600 mb-4">This content should be handled as an assignment.</p>
            </div>
          </div>
        );

      case 'audio':
        const audioUrl = contentData.contentUrl || contentData.url;
        return (
          <div className="py-4">
            <h3 className="text-lg font-medium text-slate-800 mb-4">Audio: {contentData.name || 'Audio Content'}</h3>
            {audioUrl ? (
              <audio 
                controls 
                className="w-full mt-2"
                src={audioUrl}
              >
                Your browser does not support the audio element.
              </audio>
            ) : (
              <p className="text-slate-600">Audio content is not available.</p>
            )}
          </div>
        );
      
      case 'html':
      case 'text':
      default:
        // Handle both content (API format) and htmlContent (component format)
        const htmlContent = contentData.content || contentData.htmlContent;
        return (
          <div className="prose max-w-none">
            {htmlContent ? (
              <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
            ) : (
              <p className="text-slate-600">{activeContent.name || 'Content'} is not available.</p>
            )}
          </div>
        );
    }
  };
  
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      {/* Content Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-slate-800">
          {selectedSubTopic ? 
            (selectedSubTopic.subTopicName || selectedSubTopic.name) : 
            (activeContent.name || 'Content')}
        </h2>
        <div className="flex space-x-2">
          {/* Notes Button */}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onNotesClick}
            className="text-slate-600 hover:text-slate-700"
          >
            <NotebookText className="h-4 w-4 mr-1" />
            Notes
          </Button>
          
          {/* Bookmark/Unbookmark Button - Only show for topics, not subtopics */}
          {!selectedSubTopic && (
            isTopicBookmarked ? (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onUnbookmarkClick}
                className="text-emerald-600 hover:text-emerald-700"
              >
                <BookmarkCheck className="h-4 w-4 mr-1" />
                Bookmarked
              </Button>
            ) : (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onBookmarkClick}
                className="text-slate-600 hover:text-slate-700"
              >
                <Bookmark className="h-4 w-4 mr-1" />
                Bookmark
              </Button>
            )
          )}
        </div>
      </div>
      
      {/* Content Display */}
      <div className="min-h-[300px]">
        {renderContent()}
      </div>
    </div>
  );
};

export default CourseContentArea;
