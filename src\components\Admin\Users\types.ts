export interface User {
  id: string;
  name: string;
  firstName?: string;
  lastName?: string;
  email: string;
  roleId: string;
  schoolId?: string;
  schoolName?: string;
  createdOn: string;
  userRole?: {
    id: number;
    role: string;
  };
}

export interface UserCreationRequest {
  // name: string;
  email: string;
  // roleId: string;
  schoolId?: string;
}

export interface UserUpdateRequest {
  name?: string;
  email?: string;
  // roleId?: string;
  schoolId?: string;
}

export interface School {
  id: string;
  name: string;
  description: string;
  createdOn: string;
}

export const RoleTypes = {
  APP_ADMIN: "1",
  SCHOOL_ADMIN: "2",
  TEACHER: "3",
  STUDENT: "4",
} as const;

export type RoleType = (typeof RoleTypes)[keyof typeof RoleTypes];

export const getRoleName = (roleId: RoleType | string): string => {
  switch (roleId) {
    case RoleTypes.APP_ADMIN:
      return "App Admin";
    case RoleTypes.SCHOOL_ADMIN:
      return "School Admin";
    case RoleTypes.TEACHER:
      return "Teacher";
    case RoleTypes.STUDENT:
      return "Student";
    default:
      return "Unknown";
  }
};
