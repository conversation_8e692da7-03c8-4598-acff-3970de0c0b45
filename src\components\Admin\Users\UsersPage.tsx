import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Plus, Loader2, Search, Pencil, Trash2, UserCircle2, Users2 } from 'lucide-react';
import { useGetUsersQuery, useDeleteUserMutation, useGetSchoolByIdQuery, useGetSchoolsQuery, useGetUserByIdQuery } from '@/APIConnect';
import { User, RoleTypes, getRoleName } from './types';
import { toast } from '@/hooks/use-toast';
import { useAuth0 } from "@auth0/auth0-react";

// const STATIC_SCHOOL_ID = "cb9f8588-5c23-46a8-85b0-96ba343801b5";

const UserRow = ({ user, allSchools, handleEdit, handleDelete, isSchoolAdmin }: {
    user: User,
    allSchools: any,
    handleEdit: (user: User) => void,
    handleDelete: (id: string) => void,
    isSchoolAdmin: boolean
}) => {
    const { data: userDetails, isLoading: isLoadingUserDetail } = useGetUserByIdQuery(user.id);
    if (isLoadingUserDetail) {
        return (
            <TableRow>
                <TableCell colSpan={isSchoolAdmin ? 4 : 5}>
                    <Loader2 className="w-5 h-5 animate-spin" />
                </TableCell>
            </TableRow>
        );
    }
    // Ensure allSchools is always an array
    const schoolList = Array.isArray(allSchools)
        ? allSchools
        : allSchools?.resultObject ? allSchools.resultObject : [];
    const mergedUser = { ...user, ...userDetails };
    const schoolName = schoolList.find((s: any) => s.id === mergedUser.schoolId)?.name || 'N/A';

    return (
        <TableRow className="group hover:bg-muted/50 transition-colors">
            <TableCell>
                <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-lg bg-muted flex items-center justify-center">
                        <UserCircle2 className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div className="font-medium text-foreground">
                        {`${mergedUser.firstName} ${mergedUser.lastName ?? ""}`}
                    </div>
                </div>
            </TableCell>
            <TableCell className="text-muted-foreground">
                {mergedUser.email}
            </TableCell>
            <TableCell className="text-muted-foreground">
                {getRoleName(`${mergedUser.userRole?.id}`)}
            </TableCell>
            <TableCell className="text-muted-foreground">
                {schoolName}
            </TableCell>
            <TableCell>
                <div className="flex justify-end space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(mergedUser)}
                        className="hover:bg-muted hover:text-foreground"
                    >
                        <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(mergedUser.id)}
                        className="hover:bg-muted hover:text-destructive"
                    >
                        <Trash2 className="h-4 w-4" />
                    </Button>
                </div>
            </TableCell>
        </TableRow>
    );
};

const UsersPage = () => {
    const navigate = useNavigate();
    const [searchQuery, setSearchQuery] = useState('');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
    const [selectedRole, setSelectedRole] = useState<string>('');
    const { user } = useAuth0();
    const schoolId = user?.["http://learnido-app/schoolId"] as string | undefined;
    // Safe access to user properties with type checking
    const roleId = user?.["http://learnido-app/roleId"] as string | undefined;
    const isSchoolAdmin = roleId === "2";

    // For SchoolAdmin, use the static school ID and fetch school details
    const { data: schoolData, isLoading: isLoadingSchool } = useGetSchoolByIdQuery(
        schoolId,
        { skip: !isSchoolAdmin }
    );
    // For non-school admin fetch all schools
    const { data: allSchools, isLoading: isLoadingSchools } = useGetSchoolsQuery(undefined, {
        skip: isSchoolAdmin
    });
    const { data: usersData, isLoading: isLoadingUsers, refetch } = useGetUsersQuery('', {
        skip: isSchoolAdmin
    });
    const [deleteUser] = useDeleteUserMutation();

    const handleEdit = (user: User) => {
        const basePath = isSchoolAdmin ? "/school-admin" : "/admin";
        navigate(`${basePath}/users/${user.id}`);
    };

    const handleDelete = async (id: string) => {
        if (!window.confirm('Are you sure you want to delete this user?')) return;
        try {
            await deleteUser({ id }).unwrap();
            toast({
                title: "User deleted successfully",
                variant: "default",
            });
            refetch();
        } catch (error) {
            console.error('Error deleting user:', error);
            toast({
                title: "Failed to delete user",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const handleCreate = () => {
        const basePath = isSchoolAdmin ? "/school-admin" : "/admin";
        navigate(`${basePath}/users/create`);
    };

    const filteredUsers = useMemo(() => {
        let users = [];
        if (isSchoolAdmin && schoolData?.users) {
            users = schoolData.users.map((u: any) => ({
                ...u.user,
                schoolName: schoolData.name // Add school name from school data
            }));
        } else if (usersData?.resultObject) {
            // Ensure allSchools is an array for mapping
            const schoolList = Array.isArray(allSchools)
                ? allSchools
                : allSchools?.resultObject ? allSchools.resultObject : [];
            users = usersData.resultObject.map((u: any) => ({
                ...u,
                schoolName: schoolList.find((school: any) => school.id === u.schoolId)?.name || 'N/A'
            }));
        }

        return users
            .filter((user: User) => {
                const matchesRole = selectedRole && selectedRole !== "all"
                    ? user.userRole?.id === Number(selectedRole)
                    : true;
                const matchesSearch = searchQuery
                    ? user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    user.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    user.lastName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    `${user.firstName ?? ""} ${user.lastName ?? ""}`.toLowerCase().includes(searchQuery.toLowerCase())
                    : true;

                return matchesSearch && matchesRole;
            })
            .sort((a: User, b: User) => {
                const roleA = getRoleName(`${a.userRole?.id}`).toLowerCase();
                const roleB = getRoleName(`${b.userRole?.id}`).toLowerCase();

                if (sortDirection === 'asc') {
                    return roleA.localeCompare(roleB);
                }
                return roleB.localeCompare(roleA);
            });
    }, [schoolData, usersData, allSchools, searchQuery, sortDirection, selectedRole]);

    // Adjust loader to check for all necessary data
    if (!roleId || (isSchoolAdmin && isLoadingSchool) || (!isSchoolAdmin && (isLoadingUsers || isLoadingSchools))) {
        return (
            <div className="flex justify-center items-center h-screen">
                <Loader2 className="w-8 h-8 animate-spin" />
            </div>
        );
    }

    return (
        <div className="container mx-auto p-6">
            <div className="flex flex-col space-y-8">
                <Card className="p-6">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
                        <div>
                            <h1 className="text-3xl font-semibold tracking-tight text-foreground">
                                {isSchoolAdmin ? `Users - ${schoolData?.name}` : 'Users'}
                            </h1>
                            <p className="text-sm text-muted-foreground mt-1">
                                Manage and organize user accounts
                            </p>
                        </div>
                        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full md:w-auto">
                            <div className="flex flex-1 sm:flex-none space-x-3">
                                <div className="relative flex-1">
                                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        placeholder="Search users..."
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="pl-9 w-full sm:w-[260px] bg-background"
                                    />
                                </div>
                                <Select value={selectedRole} onValueChange={setSelectedRole}>
                                    <SelectTrigger className="w-[180px]">
                                        <SelectValue placeholder="Filter by role" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Roles</SelectItem>
                                        <SelectItem value="1">Super Admin</SelectItem>
                                        <SelectItem value="2">School Admin</SelectItem>
                                        <SelectItem value="3">Teacher</SelectItem>
                                        <SelectItem value="4">Student</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <Button
                                onClick={handleCreate}
                                className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-primary-foreground transition-colors"
                            >
                                <Plus className="w-4 h-4 mr-2" />
                                Create User
                            </Button>
                        </div>
                    </div>
                </Card>

                <Card>
                    <ScrollArea className="h-[calc(100vh-280px)] rounded-md">
                        {isLoadingUsers ? (
                            <div className="flex justify-center items-center h-32">
                                <div className="flex flex-col items-center space-y-3">
                                    <Loader2 className="w-8 h-8 animate-spin text-muted-foreground" />
                                    <p className="text-sm text-muted-foreground">Loading users...</p>
                                </div>
                            </div>
                        ) : (
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-muted/50">
                                        <TableHead className="w-[300px]">User Details</TableHead>
                                        <TableHead>Email</TableHead>
                                        <TableHead
                                            className="w-[120px] cursor-pointer hover:bg-muted/50 transition-colors"
                                            onClick={() => setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')}
                                        >
                                            <div className="flex items-center">
                                                Role
                                                {sortDirection === 'asc' ? (
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="16"
                                                        height="16"
                                                        viewBox="0 0 24 24"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        strokeWidth="2"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        className="ml-1"
                                                    >
                                                        <path d="m3 16 4 4 4-4" />
                                                        <path d="M7 20V4" />
                                                        <path d="m21 8-4-4-4 4" />
                                                        <path d="M17 4v16" />
                                                    </svg>
                                                ) : (
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="16"
                                                        height="16"
                                                        viewBox="0 0 24 24"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        strokeWidth="2"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        className="ml-1"
                                                    >
                                                        <path d="m21 16-4 4-4-4" />
                                                        <path d="M17 20V4" />
                                                        <path d="m3 8 4-4 4 4" />
                                                        <path d="M7 4v16" />
                                                    </svg>
                                                )}
                                            </div>
                                        </TableHead>
                                        {!isSchoolAdmin && (
                                            <TableHead className="w-[200px]">School</TableHead>
                                        )}
                                        <TableHead className="w-[100px] text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredUsers.map((user: User) => (
                                        isSchoolAdmin ? (
                                            <TableRow key={user.id} className="group hover:bg-muted/50 transition-colors">
                                                <TableCell>
                                                    <div className="flex items-center space-x-3">
                                                        <div className="h-10 w-10 rounded-lg bg-muted flex items-center justify-center">
                                                            <UserCircle2 className="h-5 w-5 text-muted-foreground" />
                                                        </div>
                                                        <div className="font-medium text-foreground">
                                                            {`${user.firstName} ${user.lastName ?? ""}`}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-muted-foreground">
                                                    {user.email}
                                                </TableCell>
                                                <TableCell className="text-muted-foreground">
                                                    {getRoleName(`${user.userRole?.id}`)}
                                                </TableCell>
                                                {!isSchoolAdmin && (
                                                    <TableCell className="text-muted-foreground">
                                                        {user.schoolName || 'N/A'}
                                                    </TableCell>
                                                )}
                                                <TableCell>
                                                    <div className="flex justify-end space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            onClick={() => handleEdit(user)}
                                                            className="hover:bg-muted hover:text-foreground"
                                                        >
                                                            <Pencil className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            onClick={() => handleDelete(user.id)}
                                                            className="hover:bg-muted hover:text-destructive"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            <UserRow
                                                key={user.id}
                                                user={user}
                                                allSchools={allSchools}
                                                handleEdit={handleEdit}
                                                handleDelete={handleDelete}
                                                isSchoolAdmin={isSchoolAdmin}
                                            />
                                        )
                                    ))}
                                    {filteredUsers.length === 0 && (
                                        <TableRow>
                                            <TableCell colSpan={isSchoolAdmin ? 4 : 5}>
                                                <div className="flex flex-col items-center justify-center py-8 space-y-2">
                                                    <Users2 className="h-8 w-8 text-muted-foreground" />
                                                    <p className="text-muted-foreground text-sm">No users found</p>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        )}
                    </ScrollArea>
                </Card>
            </div>
        </div>
    );
};

export default UsersPage;
