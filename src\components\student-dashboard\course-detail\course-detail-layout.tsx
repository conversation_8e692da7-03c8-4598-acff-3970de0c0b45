import React from 'react';
import {
  ArrowLeft,
  Calendar,
  Clock,
  FileText,
  ClipboardCheck, // Changed from HelpCircle
  Bot,
  Video,
  BookOpen, // Changed from Lightbulb for non-assessment
  Lock,
  Loader2,
  ChevronDown, // For accordion trigger
  ChevronUp, // For accordion trigger
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger as ShadcnAccordionTrigger, // Rename original trigger
} from '@/components/ui/accordion';
import { cn } from '@/lib/utils';
import { ResultObject, Topic, SubTopic, Chapter } from '../types';

// Custom Accordion Trigger to prevent default styling conflicts and allow custom icons
const AccordionTrigger = React.forwardRef<HTMLButtonElement, React.ComponentPropsWithoutRef<typeof ShadcnAccordionTrigger> & { open?: boolean }>(({ children, className, open, ...props }, ref) => (
  <h3> {/* Use h3 for semantic structure, matches Shadcn */} 
    <ShadcnAccordionTrigger
      ref={ref}
      className={cn(
        "flex flex-1 items-center justify-between py-4 font-medium transition-all hover:no-underline [&[data-state=open]>svg.chevron]:rotate-180",
        className // Allow overriding classes
      )}
      {...props}
    >
      {children}
      {/* Use ChevronDown/Up or custom icons based on state if needed, Shadcn handles rotation by default */}
      {/* <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200 chevron" /> */}
    </ShadcnAccordionTrigger>
  </h3>
));
AccordionTrigger.displayName = 'AccordionTrigger';


interface CourseDetailLayoutProps {
  // ... (keep existing props)
  courseData: ResultObject;
  selectedTopicId?: string | null;
  selectedSubTopicId?: string | null;
  selectedAssessmentId?: string | null;
  isContentLoading: boolean;
  isSubtopicListLoading: boolean;
  isLoadingLiveSession: boolean;
  onBackClick: () => void;
  onResumeClick: () => void;
  onTopicSelect: (topic: Topic) => void;
  onSubTopicSelect: (subTopic: SubTopic) => void;
  onAssessmentSelect: (assessment: any) => void;
  onAiMentorClick: () => void;
  isChapterLocked: (chapterId: string) => boolean;
  isTopicLocked: (topicId: string) => boolean;
  isSubTopicLocked: (subTopicId: string) => boolean;
  onJoinLiveSession: (classroomId: string) => void;
  activeTab: string;
  onTabChange: (tab: string) => void;
  onCalendarClick: () => void;
  openChapters: string[];
  onChapterToggle: (value: string[]) => void;
  openTopics: string[];
  onTopicToggle: (value: string[]) => void;
  instructorName: string;
  totalHours: number;
  totalTopics: number;
  totalAssessments: number;
  lessonsCompleted: number;
  lessonsTotal: number;
  progressPercent: number;
  timeLeft: string;
  courseDescription: string;
  courseObjectives: string[];
  topicContentMap: Record<string, (SubTopic | any)[]>;
}

const CourseDetailLayout: React.FC<CourseDetailLayoutProps> = ({
  courseData,
  selectedTopicId,
  selectedSubTopicId,
  selectedAssessmentId,
  isContentLoading,
  isSubtopicListLoading,
  isLoadingLiveSession,
  onBackClick,
  onResumeClick,
  onTopicSelect,
  onSubTopicSelect,
  onAssessmentSelect,
  onAiMentorClick,
  isChapterLocked,
  isTopicLocked,
  isSubTopicLocked,
  onJoinLiveSession,
  activeTab,
  onTabChange,
  onCalendarClick,
  openChapters,
  onChapterToggle,
  openTopics,
  onTopicToggle,
  instructorName,
  totalHours,
  totalTopics,
  totalAssessments,
  lessonsCompleted,
  lessonsTotal,
  progressPercent,
  timeLeft,
  courseDescription,
  courseObjectives,
  topicContentMap,
}) => {
  const { name: courseName, chapters } = courseData;

  const renderSubItem = (item: SubTopic | any, parentTopicId: string) => {
    const isAssessment = item.type === 'assessment' || item.subTopicName?.toLowerCase().includes('quiz') || item.subTopicName?.toLowerCase().includes('assessment');
    const isSelected = isAssessment ? item.id === selectedAssessmentId : item.id === selectedSubTopicId;
    const locked = isSubTopicLocked(item.id);

    const handleClick = () => {
      if (locked || isContentLoading) return;
      if (isAssessment) {
        onAssessmentSelect(item);
      } else {
        onSubTopicSelect(item as SubTopic);
      }
    };

    // Define icon based on type
    let IconComponent = BookOpen; // Default to BookOpen for non-assessment
    if (isAssessment) {
      IconComponent = ClipboardCheck; // Use ClipboardCheck for assessments
    } else if (item.type === 'video') { // Example: Check for video type if available
      IconComponent = Video;
    }

    return (
      <button
        key={item.id}
        onClick={handleClick}
        disabled={locked || isContentLoading}
        className={cn(
          "flex items-center space-x-2 py-3 px-4 text-sm w-full text-left rounded-md transition-colors duration-150", // Adjusted padding
          locked ? "text-gray-400 cursor-not-allowed opacity-60" : "text-gray-700 hover:bg-gray-100",
          // Use the specific rgba color for selected background from Figma
          isSelected && !locked && "bg-[rgba(52,116,104,0.10)] text-teal-800 font-medium hover:bg-[rgba(52,116,104,0.15)]",
          isContentLoading && isSelected && "cursor-wait opacity-70"
        )}
      >
        {isContentLoading && isSelected ? (
          <Loader2 className="h-4 w-4 flex-shrink-0 animate-spin text-teal-700" />
        ) : (
          <IconComponent className="h-5 w-5 flex-shrink-0 text-gray-600" /> // Adjusted icon size and color
        )}
        <span className="flex-grow truncate font-medium text-gray-800">{item.subTopicName || item.title || `Item ${item.id}`}</span>
        {locked && <Lock className="h-3 w-3 text-gray-400 flex-shrink-0 ml-auto" />}
      </button>
    );
  };

  return (
    // Use bg-white as the base, specific sections will have their own backgrounds
    <div className="min-h-screen bg-white p-4 md:p-6 lg:p-8">
      {/* Header Area (Below Main App Header) */}
      <div className="mb-6 flex justify-between items-center border-b-2 border-gray-200 pb-4">
        {/* Back Button - Styled like Figma */}
        <Button
          variant="outline"
          onClick={onBackClick}
          className="bg-gradient-to-r from-[#347468] to-[#347468] text-white hover:from-[#2a5f56] hover:to-[#2a5f56] border-[#347468] rounded-lg px-4 py-2 text-base font-semibold capitalize"
        >
          <ArrowLeft className="mr-2 h-5 w-5" />
          Back
        </Button>
        {/* Calendar Button - Styled like Figma */}
        <Button
          variant="outline"
          onClick={onCalendarClick}
          className="bg-white text-[#347468] border-[#347468] hover:bg-gray-50 rounded-full px-4 py-2 text-lg font-semibold capitalize"
        >
          <Calendar className="mr-2 h-5 w-5" />
          Calendar
        </Button>
      </div>

      {/* Main Content Grid */}
      {/* Adjusted grid columns slightly for better balance based on Figma proportions */}
      <div className="grid grid-cols-1 lg:grid-cols-[minmax(0,_2.5fr)_minmax(0,_1fr)] gap-6 lg:gap-8">

        {/* Left Column: Main Content Area */}
        {/* Added border matching Figma */}
        <div className="lg:col-span-1 border-2 border-gray-200 rounded-lg p-6">
          <div className="flex flex-col md:flex-row justify-between gap-6 mb-8">
            {/* Left side: Title, Instructor, Progress, Resume */}
            <div className="flex-1 flex flex-col gap-6">
              {/* Course Title & Instructor */}
              <div className="flex flex-col gap-2">
                <h1 className="text-3xl md:text-4xl font-semibold text-gray-800 capitalize">{courseName}</h1>
                <p className="text-xl md:text-2xl">
                  <span className="text-gray-700 font-medium capitalize">Instructor - </span>
                  <span className="text-[#347468] font-semibold capitalize">{instructorName}</span>
                </p>
              </div>

              {/* Progress */}
              <div className="flex flex-col gap-3">
                <div className="flex justify-between items-center text-base">
                  <span className="text-gray-700">
                    <span className="font-semibold text-gray-900">{lessonsCompleted}</span>/
                    <span className="text-gray-600">{lessonsTotal} lessons</span>
                    <span className="text-gray-300 mx-1">|</span>
                    <span className="font-semibold text-gray-900"> {progressPercent}%</span>
                  </span>
                  <span className="font-bold text-[#347468]">{timeLeft}</span>
                </div>
                {/* Progress Bar - Styled like Figma - Removed indicatorClassName */}
                <Progress
                  value={progressPercent}
                  className="h-4 rounded-full bg-teal-100 [&>*]:bg-[#347468] [&>*]:rounded-full" // Style indicator using child selector
                />
              </div>

              {/* Resume Button - Styled like Figma */}
              <Button
                onClick={onResumeClick}
                className="w-full md:w-auto bg-[#347468] hover:bg-[#2a5f56] text-white text-xl font-semibold capitalize px-4 py-3 rounded-lg"
              >
                Resume Course
              </Button>
            </div>

            {/* Right side: Stats */}
            <div className="flex flex-col gap-4 pt-2 md:items-end">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-[#347468]" />
                <span className="text-xl font-medium text-gray-800">{totalHours} Hours</span>
              </div>
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-[#347468]" />
                <span className="text-xl font-medium text-gray-800">{totalTopics} Topics</span>
              </div>
              <div className="flex items-center gap-2">
                <ClipboardCheck className="h-5 w-5 text-[#347468]" />
                <span className="text-xl font-medium text-gray-800">{totalAssessments} Assessment</span>
              </div>
            </div>
          </div>

          {/* Tabs Section - Styled like Figma */}
          <div className="mt-8">
            {/* Tab List Container with Gradient Background */}
            <div className="inline-flex items-center justify-center rounded-lg p-1 mb-6 bg-gradient-to-r from-[#386C8D] via-[#036664] via-[#0E3D35] to-[#060B0B]">
              {['Overview', 'Instructor', 'Performance'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => onTabChange(tab)}
                  className={cn(
                    "inline-flex items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-base font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 capitalize",
                    activeTab === tab
                      ? "bg-white text-[#347468] shadow-sm border border-gray-300" // Active tab style from Figma
                      : "text-white hover:bg-white/10" // Inactive tab style from Figma
                  )}
                  data-state={activeTab === tab ? 'active' : 'inactive'}
                >
                  {tab}
                </button>
              ))}
            </div>

            {/* Tab Content Area - Styled like Figma */}
            <div className="mt-4 min-h-[250px] bg-white p-6 rounded-lg border-2 border-gray-200">
              {activeTab === 'Overview' && (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-lg mb-2 text-[#347468]">Course Description:</h3>
                    <p className="text-gray-800 text-sm font-medium leading-relaxed">{courseDescription || 'No description available.'}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-2 text-[#347468]">Course Objectives:</h3>
                    {courseObjectives && courseObjectives.length > 0 ? (
                      <ul className="list-disc list-inside space-y-1 text-gray-800 text-sm font-medium leading-relaxed">
                        {courseObjectives.map((obj, index) => (
                          <li key={index}>{obj}</li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-gray-500 text-sm">No objectives listed.</p>
                    )}
                  </div>
                </div>
              )}
              {activeTab === 'Instructor' && (
                <div>
                  <h3 className="font-semibold text-lg mb-3 text-gray-800">Instructor</h3>
                  <p className="text-gray-700">Details about {instructorName || 'the instructor'} will be shown here.</p>
                </div>
              )}
              {activeTab === 'Performance' && (
                <div>
                  <h3 className="font-semibold text-lg mb-3 text-gray-800">Performance</h3>
                  <p className="text-gray-700">Your performance metrics and analytics will be displayed here.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Column: Sidebar - Course Content */}
        <div className="lg:col-span-1">
          {/* Added border matching Figma */}
          <div className="bg-white rounded-lg p-4 border-2 border-gray-200 sticky top-6">
            <h3 className="font-semibold text-xl mb-4 text-gray-800 capitalize px-2">Course Content</h3>

            <Accordion
              type="multiple"
              value={openChapters}
              onValueChange={onChapterToggle}
              className="w-full space-y-1"
            >
              {!chapters || chapters.length === 0 ? (
                 <p className="text-sm text-gray-500 px-3 py-2">No chapters found for this course.</p>
              ) : chapters.map((chapter) => {
                const chapterLocked = isChapterLocked(chapter.id);
                const isChapterOpen = openChapters.includes(chapter.id);
                return (
                  // Chapter Item - Added border
                  <AccordionItem key={chapter.id} value={chapter.id} className="border-2 border-gray-200 rounded-lg overflow-hidden mb-2">
                    <AccordionTrigger
                      open={isChapterOpen} // Pass open state for potential icon changes
                      disabled={chapterLocked}
                      className={cn(
                        "flex justify-between items-center w-full font-semibold text-base py-4 px-6 hover:bg-gray-50 transition-colors data-[state=open]:bg-gray-100 data-[state=open]:border-b-2 data-[state=open]:border-gray-200", // Adjusted padding/font
                        chapterLocked ? "text-gray-400 cursor-not-allowed opacity-70" : "text-gray-800",
                        "hover:no-underline"
                      )}
                    >
                      <span className="flex-grow text-left truncate pr-2 capitalize">{chapter.name}</span>
                      <div className="flex items-center gap-3 flex-shrink-0 ml-4">
                        {/* Removed chapter.duration as it's not in the type */}
                        {/* <span className="text-sm font-normal text-gray-600">{chapter.duration || '-- min'}</span> */}
                        {chapterLocked ? (
                          <Lock className="h-4 w-4 text-gray-400 flex-shrink-0" />
                        ) : (
                          // Use ChevronDown from lucide, Shadcn handles rotation
                          <ChevronDown className="h-5 w-5 text-gray-500 transition-transform duration-200 chevron" />
                        )}
                      </div>
                    </AccordionTrigger>
                    {/* Chapter Content (Topics) - Adjusted padding */}
                    <AccordionContent className="pt-0 pb-2 pl-6 pr-4 bg-white">
                      {!chapter.topics || chapter.topics.length === 0 ? (
                         <p className="text-xs text-gray-500 px-2 py-1">No topics in this chapter.</p>
                      ) : (
                        <Accordion
                          type="multiple"
                          value={openTopics}
                          onValueChange={onTopicToggle}
                          className="w-full space-y-0.5 border-l-2 border-gray-200 pl-4 ml-2"
                        >
                          {chapter.topics.map((topic, index) => {
                            const topicLocked = isTopicLocked(topic.id);
                            const isTopicOpen = openTopics.includes(topic.id);
                            const isLoadingThisTopicContent = isSubtopicListLoading && selectedTopicId === topic.id;
                            const subItems = topicContentMap[topic.id];

                            return (
                              // Topic Item - No extra border needed
                              <AccordionItem key={topic.id} value={topic.id} className="border-b-0">
                                <AccordionTrigger
                                  open={isTopicOpen}
                                  disabled={topicLocked}
                                  onClick={(e) => {
                                    // Only trigger topic select logic if not locked
                                    if (!topicLocked) {
                                      onTopicSelect(topic);
                                    } else {
                                      e.preventDefault(); // Prevent accordion opening if locked
                                    }
                                  }}
                                  className={cn(
                                    "flex justify-between items-center w-full text-base font-semibold py-3 px-0 hover:bg-gray-50 transition-colors data-[state=open]:bg-gray-100 rounded-md -ml-1", // Adjusted padding/font
                                    topicLocked ? "text-gray-400 cursor-not-allowed opacity-70" : "text-gray-700",
                                    selectedTopicId === topic.id && !topicLocked && "bg-gray-100",
                                    "hover:no-underline"
                                  )}
                                >
                                  {/* Added topic number and fixed span structure */}
                                  <span className="flex-grow text-left truncate pr-2 capitalize">
                                    {String(index + 1).padStart(2, '0')}: {topic.name}
                                  </span>
                                  {/* Icons moved outside the main span */}
                                  {isLoadingThisTopicContent ? (
                                      <Loader2 className="h-4 w-4 text-gray-400 flex-shrink-0 animate-spin mr-2" />
                                  ) : topicLocked ? (
                                      <Lock className="h-4 w-4 text-gray-400 flex-shrink-0 mr-2" />
                                  ) : (
                                      <ChevronDown className="h-5 w-5 text-gray-500 transition-transform duration-200 chevron mr-2" />
                                  )}
                                </AccordionTrigger>
                                {/* Topic Content (SubItems) - Adjusted padding */}
                                <AccordionContent className="pl-2 pt-1 pb-1 space-y-1">
                                  {isLoadingThisTopicContent ? (
                                    <div className="flex items-center justify-center py-2">
                                      <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
                                      <span className="text-xs text-gray-500 ml-2">Loading content...</span>
                                    </div>
                                  ) : subItems === undefined && selectedTopicId === topic.id ? (
                                      // Show loading specifically when subItems are undefined *and* this topic is selected (meaning fetch was triggered)
                                      <div className="flex items-center justify-center py-2">
                                          <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
                                          <span className="text-xs text-gray-500 ml-2">Loading...</span>
                                      </div>
                                  ) : subItems && subItems.length > 0 ? (
                                      // Render subitems if they exist
                                      subItems.map((subItem) => renderSubItem(subItem, topic.id))
                                  ) : subItems ? (
                                      // Render 'no sub-topics' if subItems is an empty array (fetch completed with no results)
                                      <p className="text-xs text-gray-500 px-2 py-1">No sub-topics or assessments found.</p>
                                  ) : (
                                      // Render placeholder if subItems is undefined and topic is not selected (fetch not yet triggered)
                                      <p className="text-xs text-gray-400 px-2 py-1 italic">Select topic to load content.</p>
                                  )}
                                </AccordionContent>
                              </AccordionItem>
                            ); // End return for topic map
                          })} 
                        </Accordion>
                      )} 
                    </AccordionContent>
                  </AccordionItem>
                ); // End return for chapter map
              })} 
            </Accordion>

            {/* AI Mentor Button - Styled like Figma */}
            <Button
              onClick={onAiMentorClick}
              className="w-full mt-6 bg-gradient-to-r from-[#386C8D] via-[#036664] via-[#0E3D35] to-[#060B0B] text-white text-lg font-semibold capitalize px-4 py-3 rounded-lg shadow-md border border-gray-400"
            >
              <Bot className="mr-3 h-5 w-5" />
              AI Mentor
            </Button>
          </div>
        </div>
      </div>
    </div> // End main container div
  ); // End return for component
};

export default CourseDetailLayout;
