import React, { useState, useEffect } from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useSelector } from "react-redux";

const GuestRoute = ({ component: Component, ...rest }) => {
  const userToken = useSelector((state) => state.user.userToken);
  const userIsRendered = useSelector((state) => state.user.userIsRendered);
  const [isRendered, setIsRendered] = useState(false);
  useEffect(() => {
    if (userIsRendered) {
      setIsRendered(true);
    }
  }, [userIsRendered]);

  if (!isRendered) return null;

  return userToken ? <Navigate to="/" /> : <Outlet />;
};

export default GuestRoute;
