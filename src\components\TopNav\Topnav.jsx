import React, { useEffect, useState } from "react";
import "./Topnav.css";
import { useDispatch, useSelector } from "react-redux";
import { setTopNavSelection } from "./topNavSlice";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { logoutURL, JWTToken } from "../../constant/AppConstant";
import {
  Home,
  LogOut,
  Bell,
  Calendar,
  Award,
  Menu,
  UserIcon,
  X,
  BookOpen,
  HelpCircle,
  School as SchoolIcon,
  Users as UsersIcon,
  MessageSquare,
  Presentation,
  MessageCircle,
  UserCircle,
  LayoutDashboard,
  FileText,
  BarChart2,
  ClipboardList,
  Lightbulb,
  LifeBuoy,
} from "lucide-react";
import { useAuth0 } from "@auth0/auth0-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { setUserIsRendered, setUserToken } from "../User/userSlice";
import { motion, AnimatePresence } from "framer-motion";
import { QAPage } from "../Student/QA/QAPage";
import CalendarView from "../student-dashboard/components/calender/CalendarView";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../ui/popover";

const iconAnimation = {
  hover: { scale: 1.1 },
};
const Topnav = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const { logout } = useAuth0();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { user } = useAuth0();
  const roleId = user["http://learnido-app/roleId"];
  const [discussionPath, setDiscussionPath] = useState("/discussions");
  const topNavSelection = useSelector((state) => state.topNav.topNavSelection);

  const getDiscussionsPath = (roleId) => {
    if (roleId === "2") {
      return "/school-admin/discussions"; // For School Admin
    } else if (roleId === "3") {
      return "/teacher/discussions"; // For Teacher
    } else {
      return "/discussions"; // For Students and default
    }
  };

  useEffect(() => {
    if (user) {
      var rid = user["http://learnido-app/roleId"];
      setDiscussionPath(getDiscussionsPath(rid));
    }
  }, [user]);

  useEffect(() => {
    const timerId = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timerId);
  }, []);

  const navbar =
    roleId === "1"
      ? [
          {
            name: "Courses",
            icon: Home,
            path: "/admin/courses",
          },
          {
            name: "Questions",
            icon: ClipboardList,
            path: "/admin/questions",
          },
          {
            name: "Schools",
            icon: SchoolIcon,
            path: "/admin/schools",
          },
          {
            name: "Subjects",
            icon: BookOpen,
            path: "/admin/subjects",
          },

          {
            name: "Think Space",
            icon: MessageSquare,
            path: "/school-admin/discussions",
          },
        ]
      : roleId === "2"
      ? [
          {
            name: "Courses",
            icon: Home,
            path: "/school-admin/courses",
          },
          {
            name: "Users",
            icon: UsersIcon,
            path: "/school-admin/users",
          },
          {
            name: "Classrooms",
            icon: Presentation,
            path: "/school-admin/classrooms",
          },

          {
            name: "Think Space",
            icon: MessageSquare,
            path: getDiscussionsPath(),
          },
        ]
      : roleId === "3"
      ? [
          {
            name: "Classroom",
            icon: Presentation,
            path: "/teacher/classroom",
          },
          {
            name: "Queries",
            icon: HelpCircle,
            path: "/teacher/queries",
          },
          {
            name: "My Space",
            icon: LayoutDashboard,
            path: "/teacher/my-space",
          },

          {
            name: "Think Space",
            icon: MessageSquare,
            path: getDiscussionsPath(),
          },
        ]
      : [
          { name: "Home", icon: Home, path: "/" },
          {
            name: "Report Card",
            icon: FileText,
            path: "/reportCard",
          },
          // { name: "Doubts & Queries", icon: Lightbulb, path: "/qa" },
          { name: "Think Space", icon: MessageSquare, path: "/discussions" },
        ];

  const handleSetNavBar = (item) => {
    dispatch(setTopNavSelection(item.name));
    navigate(item.path);
    setMobileMenuOpen(false);
  };

  const handleLogout = () => {
    localStorage.removeItem(JWTToken);
    localStorage.removeItem("IdToken");
    logout().then(() => {
      dispatch(setUserIsRendered(false));
      dispatch(setUserToken(null));
      setTimeout(() => {
        navigate("/login");
      }, 1000);
    });
  };

  useEffect(() => {
    // Update the topNavSelection when the route changes
    const path = location.pathname;
    console.log("path", path);
    if (path === "/") {
      dispatch(setTopNavSelection("Home"));
    } else {
      const currentRoute = navbar
        .filter((item) => item.path !== "/")
        .find((item) => path.includes(item.path));
      console.log("currentRoute", currentRoute);
      if (currentRoute) {
        dispatch(setTopNavSelection(currentRoute.name));
      } else {
        dispatch(setTopNavSelection(null));
      }
    }
  }, [location.pathname, navbar]);

  const menuAnimation = {
    hidden: { opacity: 0, y: -20 },
    show: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
  };

  const textAnimation = {
    collapsed: {
      width: 0,
      opacity: 0,
      marginLeft: 0,
      transition: { duration: 0.2 },
    },
    expanded: {
      width: "auto",
      opacity: 1,
      marginLeft: 8,
      transition: { duration: 0.2 },
    },
  };

  return (
    <header className="topnav">
      <div className="topnav-content">
        <motion.div
          className="logo-container"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <img src={"/logo.png"} alt="Learnido Logo" className="logo" />
        </motion.div>

        <nav className="desktop-menu">
          <AnimatePresence>
            {navbar.map((item, index) => (
              <motion.button
                key={index}
                className={`nav-item ${
                  topNavSelection === item.name ? "active" : ""
                }`}
                onClick={() => handleSetNavBar(item)}
                initial="hidden"
                animate="show"
                exit="exit"
                variants={menuAnimation}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="flex items-center">
                  {React.createElement(item.icon, {
                    className: "nav-icon",
                    alt: item.name,
                  })}
                  <motion.span
                    initial="collapsed"
                    animate={
                      topNavSelection === item.name ? "expanded" : "collapsed"
                    }
                    whileHover="expanded"
                    variants={textAnimation}
                    className="nav-text whitespace-nowrap"
                  >
                    {item.name}
                  </motion.span>
                </span>
              </motion.button>
            ))}
          </AnimatePresence>
        </nav>

        {roleId !== "1" && roleId !== "2" && (
          <motion.div
            className="user-actions"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <div className="digital-clock text-sm mr-3 text-white flex items-center">
              {currentTime.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })}
            </div>
            <motion.button
              className="icon-button"
              aria-label="Support"
              whileHover="hover"
              variants={iconAnimation}
              onClick={() => navigate('/qa')}
            >
              <HelpCircle size={18} />
            </motion.button>
            <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
              <PopoverTrigger asChild>
                <motion.button
                  className="icon-button"
                  aria-label="Calendar"
                  whileHover="hover"
                  variants={iconAnimation}
                >
                  <Calendar size={18} />
                </motion.button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto p-0 bg-transparent border-none shadow-none calendar-popover-content"
                align="end"
                sideOffset={8}
              >
                <CalendarView />
              </PopoverContent>
            </Popover>
            {/* <motion.button
              className="icon-button"
              aria-label="Awards"
              whileHover="hover"
              variants={iconAnimation}
            >
              <Award size={18} />
            </motion.button> */}
            <UserMenu user={user} handleLogout={handleLogout} />
          </motion.div>
        )}

        {(roleId === "1" || roleId === "2") && (
          <motion.div
            className="user-actions"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <UserMenu user={user} handleLogout={handleLogout} />
          </motion.div>
        )}

        <motion.button
          className="mobile-menu-button"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          aria-expanded={mobileMenuOpen}
          aria-controls="mobile-menu"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </motion.button>
      </div>

      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.nav
            className="mobile-menu"
            id="mobile-menu"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            {navbar.map((item, index) => (
              <motion.button
                key={index}
                className={`nav-item ${
                  topNavSelection === item.name ? "active" : ""
                }`}
                onClick={() => handleSetNavBar(item)}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <span>
                  {React.createElement(item.icon, {
                    className: "nav-icon",
                    alt: item.name,
                  })}
                  <span>{item.name}</span>
                </span>
              </motion.button>
            ))}
          </motion.nav>
        )}
      </AnimatePresence>
    </header>
  );
};

const UserMenu = ({ user, handleLogout }) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <motion.div whileHover="hover" variants={iconAnimation}>
        <Avatar className="w-7 h-7 cursor-pointer">
          <AvatarImage src={user.picture} alt={user.name} />
          <AvatarFallback>{user.name?.[0]}</AvatarFallback>
        </Avatar>
      </motion.div>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end" className="w-56">
      <DropdownMenuLabel className="font-normal">
        <div className="flex flex-col space-y-1">
          <p className="text-sm font-medium leading-none">{user.name}</p>
          <p className="text-xs leading-none text-muted-foreground">
            {user.email}
          </p>
        </div>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuItem asChild>
        <Link to="/profile" className="flex items-center">
          <UserIcon size={16} className="mr-2" />
          <span>Profile</span>
        </Link>
      </DropdownMenuItem>
      <DropdownMenuItem onClick={handleLogout} className="text-red-600">
        <LogOut size={16} className="mr-2" />
        <span>Log out</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
);
export default Topnav;
