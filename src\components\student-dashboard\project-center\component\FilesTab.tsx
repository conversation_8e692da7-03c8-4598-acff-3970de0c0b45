'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { ImageIcon, FileTextIcon, SheetIcon, VideoIcon } from 'lucide-react';

interface FileCategory {
  id: string;
  name: string;
  count: number;
  icon: React.ElementType;
  bgColor: string;
  iconColor: string;
}

interface FileCounts {
  images: number;
  documents: number;
  sheets: number;
  videos: number;
}

interface FilesTabProps {
  fileCounts?: FileCounts;
}

const FilesTab: React.FC<FilesTabProps> = ({ fileCounts }) => {
  const fileCategoriesData: FileCategory[] = [
    { id: '1', name: 'Images', count: fileCounts?.images || 0, icon: ImageIcon, bgColor: 'bg-[#FF8B00]', iconColor: 'text-white' },
    { id: '2', name: 'Documents', count: fileCounts?.documents || 0, icon: FileTextIcon, bgColor: 'bg-[#049DFF]', iconColor: 'text-white' },
    { id: '3', name: 'Sheets', count: fileCounts?.sheets || 0, icon: SheetIcon, bgColor: 'bg-[#08CC88]', iconColor: 'text-white' },
    { id: '4', name: 'Videos', count: fileCounts?.videos || 0, icon: VideoIcon, bgColor: 'bg-[#FDBD00]', iconColor: 'text-white' },
  ];
  return (
    <div className="pt-2 px-4 text-[#2D2D2D] overflow-y-auto h-full">
      <h3 className="text-xl font-semibold text-[#347468] mb-6">Files:</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        {fileCategoriesData.map((category) => (
          <Card
            key={category.id}
            className="p-2 flex items-start gap-2 shadow-sm rounded-[8px] border border-gray-200 min-w-0"
          >
            <div className={`rounded-[8px] p-2 ${category.bgColor} flex items-center justify-center`}>
              <category.icon className="h-8 w-8 text-white" />
            </div>
            <div className="flex flex-col">
              <span className="font-semibold text-gray-700 text-md">{category.name}</span>
              <div className="text-xs text-gray-500">{category.count} Files</div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default FilesTab;
