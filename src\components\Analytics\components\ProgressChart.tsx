import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';

interface ProgressChartProps {
  className?: string;
}

// Mock data for demonstration
const mockData = {
  employability: [
    { month: 'Jan', value: 0 },
    { month: 'Feb', value: 45 },
    { month: 'Mar', value: 80 },
    { month: 'Apr', value: 65 },
  ],
  higherEducation: [
    { month: 'Jan', value: 0 },
    { month: 'Feb', value: 30 },
    { month: 'Mar', value: 55 },
    { month: 'Apr', value: 70 },
  ]
};

const ProgressChart: React.FC<ProgressChartProps> = ({ className = '' }) => {
  const [selectedIndex, setSelectedIndex] = useState('Employability Index');
  const [viewMode, setViewMode] = useState<'Monthly' | 'Weekly'>('Monthly');
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Get chart data based on selected index
  const chartData = selectedIndex === 'Higher Education Index' 
    ? mockData.higherEducation 
    : mockData.employability;

  // Chart dimensions
  const chartHeight = 220;
  const paddingX = 35;
  const paddingY = 30;
  // Ref for responsive width measurement
  const chartContainerRef = React.useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = React.useState(0);
  
  // Update width on mount and resize
  React.useEffect(() => {
    const updateWidth = () => {
      if (chartContainerRef.current) {
        setContainerWidth(chartContainerRef.current.clientWidth);
      }
    };
    
    // Set initial width
    updateWidth();
    
    // Add resize listener
    window.addEventListener('resize', updateWidth);
    
    // Cleanup
    return () => window.removeEventListener('resize', updateWidth);
  }, []);
  
  // Calculate dynamic graph dimensions
  const chartWidth = Math.max(containerWidth - 30, 0); // 30px for left margin and padding
  const graphWidth = chartWidth - (paddingX * 2);
  const graphHeight = chartHeight - (paddingY * 2);

  // Animation variants
  const fadeIn = {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: 0.5 } }
  };

  // Generate smooth curved path for the line chart
  const generatePath = () => {
    if (chartData.length === 0) return '';
    
    let path = `M ${paddingX},${chartHeight - paddingY - 10 - (chartData[0].value / 100 * graphHeight)}`;
    
    for (let i = 0; i < chartData.length - 1; i++) {
      const x1 = i * (graphWidth / (chartData.length - 1)) + paddingX;
      const y1 = chartHeight - paddingY - 10 - (chartData[i].value / 100 * graphHeight);
      
      const x2 = (i + 1) * (graphWidth / (chartData.length - 1)) + paddingX;
      const y2 = chartHeight - paddingY - 10 - (chartData[i + 1].value / 100 * graphHeight);
      
      // Control points for curve
      const cx1 = x1 + (x2 - x1) / 2;
      const cy1 = y1;
      const cx2 = x1 + (x2 - x1) / 2;
      const cy2 = y2;
      
      path += ` C ${cx1},${cy1} ${cx2},${cy2} ${x2},${y2}`;
    }
    
    return path;
  };

  // Generate area below the line
  const generateAreaPath = () => {
    const linePath = generatePath();
    return `${linePath} L ${paddingX + graphWidth},${chartHeight - paddingY - 10} L ${paddingX},${chartHeight - paddingY - 10} Z`;
  };

  return (
    <motion.div 
      className={`bg-white rounded-[10px] border border-[#97C48A] shadow-sm p-5 w-full ${className}`}
      initial="initial"
      animate="animate"
      variants={fadeIn}
    >
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-3">
          <h3 className="text-[18px] sm:text-[20px] font-semibold text-[#2D2D2D]">Progress Over Time</h3>
          <div className="relative">
            <button
              className="flex items-center gap-1 text-sm bg-white border border-[#347468] text-[#347468] px-3 py-1 rounded-full font-medium w-full sm:w-auto"
              onClick={() => setDropdownOpen(!dropdownOpen)}
            >
              <span className="truncate">{selectedIndex}</span>
              <ChevronDown size={14} className="flex-shrink-0" />
            </button>
            
            {dropdownOpen && (
              <div className="absolute top-full left-0 mt-1 bg-white shadow-md rounded-md z-10 w-56 border border-gray-100">
                {['Employability Index', 'Higher Education Index'].map((index) => (
                  <div 
                    key={index}
                    className="px-4 py-2 hover:bg-gray-50 cursor-pointer text-sm"
                    onClick={() => {
                      setSelectedIndex(index);
                      setDropdownOpen(false);
                    }}
                  >
                    {index}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        <div className="flex rounded-[10px] bg-[#f1f5f9] border border-[#347468] self-start sm:self-auto overflow-hidden">
          <button 
            className={`px-3 py-2 sm:py-1 text-xs font-medium rounded-[8px] ${viewMode === 'Monthly' ? 'bg-[#347468] text-white' : 'text-[#347468]'}`}
            onClick={() => setViewMode('Monthly')}
          >
            Monthly
          </button>
          <button 
            className={`px-3 py-2 sm:py-1 text-xs font-medium rounded-[8px] ${viewMode === 'Weekly' ? 'bg-[#347468] text-white' : 'text-[#347468]'}`}
            onClick={() => setViewMode('Weekly')}
          >
            Weekly
          </button>
        </div>
      </div>
      
      <div className="relative" style={{ height: chartHeight }}>
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-400 pt-[30px] pb-[30px] ">
          <span className="text-[#347468]">100%</span>
          <span className="text-[#347468]">50%</span>
          <span className="text-[#347468]">25%</span>
          <span className="text-[#347468]">0</span>
        </div>
        
        {/* Chart area */}
        <div ref={chartContainerRef} className="ml-7 pb-2 w-full" style={{ height: chartHeight }}>
          <svg width="100%" height={chartHeight - 10} preserveAspectRatio="xMidYMid meet" viewBox={`0 0 ${Math.max(chartWidth, 300)} ${chartHeight - 10}`}>
            {/* Horizontal grid lines */}
            <line x1={paddingX} y1={paddingY} x2={graphWidth + paddingX} y2={paddingY} stroke="#e5e7eb" strokeWidth="1" strokeDasharray="4" />
            <line x1={paddingX} y1={chartHeight - paddingY - 10 - (graphHeight * 0.5)} x2={graphWidth + paddingX} y2={chartHeight - paddingY - 10 - (graphHeight * 0.5)} stroke="#e5e7eb" strokeWidth="1" strokeDasharray="4" />
            <line x1={paddingX} y1={chartHeight - paddingY - 10 - (graphHeight * 0.25)} x2={graphWidth + paddingX} y2={chartHeight - paddingY - 10 - (graphHeight * 0.25)} stroke="#e5e7eb" strokeWidth="1" strokeDasharray="4" />
            <line x1={paddingX} y1={chartHeight - paddingY - 10} x2={graphWidth + paddingX} y2={chartHeight - paddingY - 10} stroke="#e5e7eb" strokeWidth="1" />
            
            {/* Area fill under the line */}
            <path
              d={generateAreaPath()}
              fill="url(#progressGradient)"
              opacity="0.15"
            />
            
            {/* Line chart */}
            <path
              d={generatePath()}
              fill="none"
              stroke="#347468"
              strokeWidth="2"
              strokeLinecap="round"
            />
            
            {/* Data points */}
            {chartData.map((point, index) => {
              const x = index * (graphWidth / (chartData.length - 1)) + paddingX;
              const y = chartHeight - paddingY - 10 - (point.value / 100 * graphHeight);
              return (
                <circle
                  key={index}
                  cx={x}
                  cy={y}
                  r={3}
                  fill="#ffffff"
                  stroke="#347468"
                  strokeWidth="1.5"
                />
              );
            })}
            
            {/* Linear gradient for area fill */}
            <defs>
              <linearGradient id="progressGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#347468" stopOpacity="0.5" />
                <stop offset="100%" stopColor="#347468" stopOpacity="0.05" />
              </linearGradient>
            </defs>
          </svg>
          
          {/* X-axis labels */}
          <div className="flex relative" style={{ width: graphWidth, marginLeft: paddingX }}>
            {chartData.map((point, index) => {
              // Calculate exact position for each label to align with data points
              const position = (index / (chartData.length - 1) * 100);
              return (
                <span 
                  key={index}
                  className="absolute text-xs text-[#347468]"
                  style={{
                    left: `${position}%`,
                    transform: 'translateX(-50%)',
                  }}
                >
                  {point.month}
                </span>
              );
            })}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ProgressChart;
