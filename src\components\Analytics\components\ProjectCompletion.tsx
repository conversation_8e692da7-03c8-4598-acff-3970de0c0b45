import React from 'react';
import { motion } from 'framer-motion';
import { Star } from 'lucide-react';

// Animation variants
const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.5 } },
};

interface ProjectCompletionProps {
  className?: string;
}

const ProjectCompletion: React.FC<ProjectCompletionProps> = ({ className = '' }) => {
  // Mock data
  const score = 80;
  const maxScore = 100;
  
  return (
    <motion.div
      className={`bg-white rounded-2xl border border-[#97C48A] shadow-sm p-5 ${className}`}
      variants={fadeIn}
      initial="initial"
      animate="animate"
    >
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold text-[#2D2D2D]">Project Completion</h3>
        <span className="text-sm font-medium text-gray-700">{score}/{maxScore}</span>
      </div>
      
      <div className="relative mt-4">
        {/* Progress bar background */}
        <div className="w-full h-10 bg-gray-100 border border-gray-300 rounded-[14px] overflow-hidden">
          {/* Progress bar fill */}
          <div 
            className="h-full rounded-[14px] relative" 
            style={{
              width: `${(score / maxScore) * 100}%`,
              background: 'linear-gradient(90deg, #86C3B8 0%, #347468 100%)'
            }}
          >
            
          </div>
        </div>
        
        {/* Milestone text */}
        <div className="absolute right-2 bottom-[-22px] flex items-center">
          <span className="text-xs font-medium">Milestone Achieved</span>
          <Star className="h-3 w-3 ml-1 text-yellow-400 fill-yellow-400" />
        </div>
      </div>
    </motion.div>
  );
};

export default ProjectCompletion;
