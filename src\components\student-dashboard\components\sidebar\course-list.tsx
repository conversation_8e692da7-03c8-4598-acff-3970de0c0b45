import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>les, Lightbulb, Rocket, <PERSON><PERSON><PERSON>, <PERSON>, Puzzle, Video } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';
import { ResultObject } from '../../types';
import { Badge } from '@/components/ui/badge';

interface CourseListProps {
    courses: any[];
    onCourseClick: (course: ResultObject) => void;
    onJoinLiveSession?: (classroomId: string) => void;
    isLoadingLiveSession?: boolean;
}

const CourseList = ({ courses, onCourseClick, onJoinLiveSession, isLoadingLiveSession }: CourseListProps) => {
    // Function to get a unique icon for each course based on its index
    const getCourseIcon = (index: number) => {
        const icons = [
            <Lightbulb className="w-5 h-5" />,
            <Rocket className="w-5 h-5" />,
            <Shapes className="w-5 h-5" />,
            <Code className="w-5 h-5" />,
            <Puzzle className="w-5 h-5" />,
            <Sparkles className="w-5 h-5" />
        ];
        return icons[index % icons.length];
    };

    return (
        <div className="space-y-3">
            {courses?.map((course, index) => (
                <Card
                    key={course.id}
                    className="group relative overflow-hidden transition-all hover:shadow-md"
                >
                    <button
                        onClick={() => onCourseClick(course)}
                        className="w-full text-left p-4"
                    >
                        <div className="flex items-start gap-3">
                            <div className="rounded-lg bg-secondary/50 p-2 text-secondary-foreground">
                                {getCourseIcon(index)}
                            </div>
                            <div className="flex-1 min-w-0">
                                <h3 className="font-medium text-sm truncate pr-8">
                                    {course.name}
                                </h3>
                                <div className="mt-2 space-y-2">
                                    <Progress
                                        value={course.courseProgress}
                                        className="h-1.5"
                                    />
                                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                                        <span>{course.courseProgress}% completed</span>
                                        {course.courseProgress === 100 && (
                                            <Badge variant="secondary" className="text-xs">
                                                Completed
                                            </Badge>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 transition-opacity group-hover:opacity-100">
                            <ArrowRight className="w-5 h-5 text-secondary" />
                        </div>
                    </button>
                    
                    {/* Join Live Lecture Button */}
                    {onJoinLiveSession && (
                        <div className="px-4 pb-3 mt-1">
                            <Button 
                                variant="outline"
                                size="sm"
                                className="w-full flex items-center justify-center gap-2 border-dashed"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onJoinLiveSession(course.id);
                                }}
                                disabled={isLoadingLiveSession}
                            >
                                {isLoadingLiveSession ? (
                                    <Sparkles className="h-4 w-4 animate-pulse" />
                                ) : (
                                    <Video className="h-4 w-4" />
                                )}
                                Join Live Lecture
                            </Button>
                        </div>
                    )}
                </Card>
            ))}
            {courses?.length === 0 && (
                <div className="text-center py-8">
                    <Sparkles className="w-12 h-12 text-muted-foreground/50 mx-auto mb-3" />
                    <p className="text-sm text-muted-foreground">No courses available</p>
                </div>
            )}
        </div>
    );
};

export default CourseList;
