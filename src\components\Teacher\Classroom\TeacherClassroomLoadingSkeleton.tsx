import React from "react";
import { Skeleton } from "../../ui/skeleton";
import { Card } from "../../ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "../../ui/tabs";

export const TeacherClassroomLoadingSkeleton = () => {
    return (
        <div className="container mx-auto p-4 animate-in fade-in-50 duration-500">
            <div className="flex flex-col space-y-6">
                {/* Breadcrumb */}
                <div className="flex items-center space-x-2">
                    <Skeleton className="h-8 w-24" />
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-20" />
                </div>

                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="space-y-2">
                        <Skeleton className="h-8 w-48" />
                        <Skeleton className="h-4 w-64" />
                    </div>
                </div>

                {/* Tabs */}
                <div className="w-full">
                    <Tabs defaultValue="tab-0">
                        <TabsList className="w-full">
                            {Array.from({ length: 4 }).map((_, i) => (
                                <TabsTrigger value={`tab-${i}`} key={i} disabled>
                                    <Skeleton className="h-4 w-24" />
                                </TabsTrigger>
                            ))}
                        </TabsList>
                        {Array.from({ length: 4 }).map((_, i) => (
                            <TabsContent key={i} value={`tab-${i}`}>
                                <Skeleton className="h-32 w-full" />
                            </TabsContent>
                        ))}
                    </Tabs>
                </div>

                {/* Content Area */}
                <Card className="p-6">
                    <div className="grid gap-6">
                        {Array.from({ length: 3 }).map((_, i) => (
                            <div key={i} className="flex items-center space-x-4">
                                <Skeleton className="h-12 w-12 rounded-full" />
                                <div className="space-y-2">
                                    <Skeleton className="h-4 w-48" />
                                    <Skeleton className="h-4 w-32" />
                                </div>
                            </div>
                        ))}
                    </div>
                </Card>
            </div>
        </div>
    );
};
