import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { LucideIcon } from 'lucide-react';

interface ChartCardProps {
    title: string;
    icon: LucideIcon;
    children: React.ReactNode;
    className?: string;
    fullWidth?: boolean;
}

export const ChartCard: React.FC<ChartCardProps> = ({
    title,
    icon: Icon,
    children,
    className = '',
    fullWidth = false
}) => {
    return (
        <Card className={`${className} ${fullWidth ? 'lg:col-span-2' : ''}`}>
            <CardHeader className="flex flex-row items-center gap-2">
                <Icon className="w-5 h-5 text-blue-500" />
                <CardTitle>{title}</CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
                {children}
            </CardContent>
        </Card>
    );
};

// Wrapper component for consistent chart container sizing
export const ChartContainer: React.FC<{
    children: React.ReactNode;
    height?: number;
}> = ({ children, height = 300 }) => (
    <div className={`h-[${height}px] w-full`}>
        {children}
    </div>
);
