import { JWTToken } from "@/constant/AppConstant";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { useState } from "react";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const uploadFile = async (file: File, fileName: string) => {
  const accessToken = localStorage.getItem(JWTToken) ?? "";
  const idToken = localStorage.getItem("IdToken") ?? "";
  const azureToken = await fetch(
    "https://nexo-file.azurewebsites.net/.auth/login/auth0",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        access_token: accessToken,
        id_token: idToken,
      }),
    }
  );
  const azureAuthResponse = await azureToken.json();
  const azureAccessToken = azureAuthResponse.authenticationToken;

  const presignedUrlReq = await fetch(
    "https://nexo-file.azurewebsites.net/api/UploadFile",
    {
      method: "POST",
      body: JSON.stringify({
        fileName: fileName,
      }),
      headers: {
        "X-ZUMO-AUTH": `${azureAccessToken}`,
        "Content-Type": "application/json",
      },
    }
  );
  const presignedUrl = await presignedUrlReq.json();

  const response = await fetch(presignedUrl.presignedUrl, {
    method: "PUT",
    headers: {
      "Content-Type": file.type,
      "x-ms-blob-type": "BlockBlob",
    },
    body: file,
  });

  return response;
};
// utils.ts
export const useFileUpload = () => {
  const [progress, setProgress] = useState<number>(0);

  const uploadFile = async (file: File, fileName: string): Promise<string> => {
    // Fetch Azure access token
    const accessToken = localStorage.getItem(JWTToken) ?? "";
    const idToken = localStorage.getItem("IdToken") ?? "";
    const azureToken = await fetch(
      "https://nexo-file.azurewebsites.net/.auth/login/auth0",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          access_token: JSON.parse(accessToken).accessToken,
          id_token: idToken,
        }),
      }
    );
    const azureAuthResponse = await azureToken.json();
    const azureAccessToken = azureAuthResponse.authenticationToken;

    // Get presigned URL
    const presignedUrlReq = await fetch(
      "https://nexo-file.azurewebsites.net/api/UploadFile",
      {
        method: "POST",
        body: JSON.stringify({
          fileName: fileName,
        }),
        headers: {
          "X-ZUMO-AUTH": `${azureAccessToken}`,
          "Content-Type": "application/json",
        },
      }
    );
    const presignedUrl = await presignedUrlReq.json();
    const finalUrl = presignedUrl.getUrl;

    // Upload the file using XMLHttpRequest
    return new Promise<string>((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open("PUT", presignedUrl.presignedUrl, true);
      xhr.setRequestHeader("Content-Type", file.type);
      xhr.setRequestHeader("x-ms-blob-type", "BlockBlob");

      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const percentComplete = (event.loaded / event.total) * 100;
          setProgress(percentComplete);
        }
      };

      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          setProgress(100);
          resolve(finalUrl);
        } else {
          setProgress(0);
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      };

      xhr.onerror = () => {
        setProgress(0);
        reject(new Error("Upload failed"));
      };

      xhr.send(file);
    });
  };

  return { uploadFile, progress };
};

// Utility function to strip HTML tags
export const stripHtmlTags = (html: string) => {
  return html.replace(/<[^>]+>/g, "");
};
