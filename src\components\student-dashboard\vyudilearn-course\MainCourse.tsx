import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import LeftSidebar from '@/components/student-dashboard/vyudilearn-course/LeftSidebar';
import RightSidebar from '@/components/student-dashboard/vyudilearn-course/RightSidebar';
import { CircleCheck, PlayCircle, Menu, ChevronLeft } from 'lucide-react';
import ExpertCard from './component/ExpertCard';
import CertificateCard from './component/CertificateCard';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useLazyGetAllCourseDataQuery, useLazyGetCourseDetailsDataQuery, useGetStudentDashboardQuery, useGetSchoolCoursesQuery } from '@/APIConnect';
import { useAuth0 } from '@auth0/auth0-react';
import { useNavigate } from 'react-router-dom';

// Types for course data
interface CourseData {
    id: string;
    title: string;
    students: number;
    hours: number;
    lessons: number;
    totalLessons: number;
    progress: number;
    mastery: string;
    image: string;
    interested?: boolean;
    classroomId?: string;
    isEnrolled: boolean;
    totalChapters: number;
}

interface ApiCourse {
    id: string;
    name: string;
    description: string;
    imageUrl?: string;
}

interface ApiChapter {
    id: string;
    name: string;
    topics?: ApiTopic[];
}

interface ApiTopic {
    id: string;
    name: string;
    completed: boolean;
    subTopics?: ApiSubTopic[];
}

interface ApiSubTopic {
    id: string;
    subTopicName: string;
    completed: boolean;
}

const MainCourse = () => {
    const { user } = useAuth0();
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState('courses');
    const [isLeftSidebarOpen, setIsLeftSidebarOpen] = useState(false);
    const [isRightSidebarOpen, setIsRightSidebarOpen] = useState(false);
    const [courses, setCourses] = useState<CourseData[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    // Extract studentId and schoolId from Auth0 user
    const studentId = user?.sub?.replace("auth0|", "");
    const schoolId = user?.["http://learnido-app/schoolId"];

    // API hooks for fetching all school courses and student enrolled courses
    const { data: schoolCoursesData, isLoading: isSchoolCoursesLoading } = useGetSchoolCoursesQuery(
        schoolId,
        { skip: !schoolId }
    );

    // API hooks for student enrolled courses
    const [getAllCoursesData] = useLazyGetAllCourseDataQuery();
    const [getCourseDetailsData] = useLazyGetCourseDetailsDataQuery();

    // Also get dashboard data as fallback for classroomId
    const { data: dashboardData } = useGetStudentDashboardQuery(
        { studentId },
        { skip: !studentId }
    );

    const certificates = [
        {
            title: 'English Lecturer Certificate',
            image: 'icons/project-center-icons/certificate.png'
        },
        {
            title: 'Data Science Certificate',
            image: 'icons/project-center-icons/certificate.png'
        },
        {
            title: 'Project Management Certificate',
            image: 'icons/project-center-icons/certificate.png'
        },
        {
            title: 'Web Development Certificate',
            image: 'icons/project-center-icons/certificate.png'
        }
    ];

    const experts = [
        {
            name: 'John Doe',
            title: 'English Lecturer',
            university: 'ABC University',
            bio: 'I am John Doe, an English Lecturer at ABC University with over 12 years of teaching experience. My areas of expertise include British Literature, Modern Literary Theory, and Academic Writing. Over the years, I have published more than 15 research papers in reputed journals across the globe, contributing to the ongoing discourse in English studies.',
            image: 'icons/project-center-icons/johndowstockimg.png'
        },
        {
            name: 'Jane Smith',
            title: 'Data Scientist',
            university: 'XYZ University',
            bio: 'Jane Smith is a leading expert in machine learning and data analytics. With a PhD in Computer Science, she has contributed to numerous open-source projects and has authored several influential papers on AI ethics and fairness. Her work focuses on developing transparent and accountable AI systems.',
            image: 'icons/project-center-icons/johndowstockimg.png'
        },
    ];
    // Process school courses and student enrolled courses
    useEffect(() => {
        const processAllCourses = async () => {
            if (schoolCoursesData?.resultObject && studentId) {
                console.log("Processing school courses:", schoolCoursesData);
                console.log("School courses array:", schoolCoursesData.resultObject);
                setIsLoading(true);

                // First, get student's enrolled courses
                let enrolledCourses: any[] = [];
                try {
                    const { data: studentCoursesData } = await getAllCoursesData(studentId);
                    enrolledCourses = studentCoursesData || [];
                    console.log("Student enrolled courses:", enrolledCourses);
                    console.log("Sample enrolled course structure:", enrolledCourses[0]);
                } catch (error) {
                    console.error("Error fetching student enrolled courses:", error);
                }

                // Create a set of enrolled course IDs for quick lookup and a map for classroomIds
                const enrolledCourseIds = new Set(enrolledCourses.map((course: any) => course.id));
                const enrolledCourseClassrooms = new Map(enrolledCourses.map((course: any) => [course.id, course.classroomId]));

                console.log("Enrolled course IDs:", Array.from(enrolledCourseIds));
                console.log("Enrolled course classrooms map:", Array.from(enrolledCourseClassrooms.entries()));

                // Also add classroomIds from dashboard data as fallback
                if (dashboardData?.courses) {
                    console.log("Dashboard courses:", dashboardData.courses);
                    dashboardData.courses.forEach((dashCourse: any) => {
                        if (dashCourse.classroomId) {
                            enrolledCourseClassrooms.set(dashCourse.id, dashCourse.classroomId);
                            console.log(`Added classroomId from dashboard: ${dashCourse.id} -> ${dashCourse.classroomId}`);
                        }
                    });
                }

                // Transform all school courses
                const transformedCourses: CourseData[] = await Promise.all(
                    schoolCoursesData.resultObject.map(async (course: any) => {
                        let lessons = 0;
                        let totalLessons = 0;
                        let totalHours = 4; // Default
                        let totalChapters = 0;
                        let actualProgress = 0;
                        const isEnrolled = enrolledCourseIds.has(course.id);

                        try {
                            // Get course details to calculate lesson counts and chapters
                            const { data: courseDetails } = await getCourseDetailsData({ courseId: course.id });

                            if (courseDetails?.resultObject) {
                                const chapters = courseDetails.resultObject.chapters || [];
                                totalChapters = chapters.length;

                                // Only calculate progress for enrolled courses
                                if (isEnrolled) {
                                    // Calculate total lessons and completed lessons
                                    chapters.forEach((chapter: any) => {
                                        if (chapter.topics) {
                                            chapter.topics.forEach((topic: any) => {
                                                totalLessons++;
                                                if (topic.completed) {
                                                    lessons++;
                                                }
                                                // Add subtopics to lesson count if they exist
                                                if (topic.subTopics && topic.subTopics.length > 0) {
                                                    topic.subTopics.forEach((subTopic: any) => {
                                                        totalLessons++;
                                                        if (subTopic.completed) {
                                                            lessons++;
                                                        }
                                                    });
                                                }
                                            });
                                        }
                                    });

                                    // Calculate actual progress based on lessons completed
                                    actualProgress = totalLessons > 0 ? Math.round((lessons / totalLessons) * 100) : 0;
                                } else {
                                    // For non-enrolled courses, just count total lessons
                                    chapters.forEach((chapter: any) => {
                                        if (chapter.topics) {
                                            totalLessons += chapter.topics.length;
                                            chapter.topics.forEach((topic: any) => {
                                                if (topic.subTopics && topic.subTopics.length > 0) {
                                                    totalLessons += topic.subTopics.length;
                                                }
                                            });
                                        }
                                    });
                                }

                                // Use total hours from API if available
                                totalHours = courseDetails.resultObject.totalHours || 4;

                                // Store the classroomId from course details if available
                                if (isEnrolled && courseDetails.resultObject.classroomId) {
                                    enrolledCourseClassrooms.set(course.id, courseDetails.resultObject.classroomId);
                                }
                            }
                        } catch (error) {
                            console.error(`Error fetching details for course ${course.id}:`, error);
                            // Use default values if details fetch fails
                        }

                        const finalClassroomId = enrolledCourseClassrooms.get(course.id) || 'default-classroom';
                        console.log(`Course ${course.id} (${course.name}) - isEnrolled: ${isEnrolled}, classroomId: ${finalClassroomId}`);

                        return {
                            id: course.id,
                            title: course.name || 'Untitled Course',
                            students: 120, // Default value
                            hours: totalHours,
                            lessons: lessons,
                            totalLessons: totalLessons,
                            progress: actualProgress,
                            mastery: course.description || 'Course Description',
                            image: course.imageUrl || `https://picsum.photos/seed/${course.id}/800/300`,
                            interested: !isEnrolled && totalLessons > 0,
                            classroomId: finalClassroomId, // Use real classroomId from enrolled courses
                            isEnrolled: isEnrolled,
                            totalChapters: totalChapters
                        };
                    })
                );

                setCourses(transformedCourses);
                setIsLoading(false);
            } else if (schoolCoursesData && !schoolCoursesData.resultObject) {
                // Handle case where API returns data but no resultObject
                console.log("School courses data received but no resultObject:", schoolCoursesData);
                setCourses([]);
                setIsLoading(false);
            }
        };

        processAllCourses();
    }, [schoolCoursesData, studentId, dashboardData, getAllCoursesData, getCourseDetailsData]);

    // Handle enrollment action
    const handleEnrollNow = (courseId: string) => {
        // TODO: Implement enrollment logic
        console.log("Enrolling in course:", courseId);
        // You can add enrollment API call here
    };

    // Handle course navigation to course detail page
    const handleCourseClick = (course: CourseData) => {
        if (!course.isEnrolled) {
            console.log("Cannot navigate to non-enrolled course");
            return;
        }

        const courseId = course.id;
        const classroomId = course.classroomId;

        if (classroomId && classroomId !== 'default-classroom') {
            navigate(`/courses?courseId=${courseId}&classroomId=${classroomId}`);
        } else {
            console.log("No valid classroomId found for course:", courseId);
        }
    };

    return (
        <div className="flex h-screen bg-gray-100">
            <div className="hidden lg:block flex-shrink-0">
                <LeftSidebar />
            </div>

            {/* Mobile Sidebar */}
            <div
                className={`fixed inset-y-0 left-0 z-50 bg-white max-w-sm shadow-md transform transition-transform duration-300 ease-in-out lg:hidden ${isLeftSidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
                    <div className="flex justify-between p-2">
                        <div></div>
                        <button onClick={() => setIsLeftSidebarOpen(false)} className="text-[#347468]">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                <LeftSidebar />
            </div>
            {isLeftSidebarOpen && (
                <div
                    className="fixed inset-0 bg-black opacity-50 z-40 lg:hidden"
                    onClick={() => setIsLeftSidebarOpen(false)}
                ></div>
            )}

            <div className="flex-1 flex flex-col overflow-hidden">
                {/* Header */}
                <header className="p-4 bg-white border-b border-[#E6E6E6]">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center">
                            <button
                                className="lg:hidden mr-4 text-gray-600"
                                onClick={() => setIsLeftSidebarOpen(!isLeftSidebarOpen)}>
                                <Menu className="h-6 w-6" />
                            </button>
                            <h1 className="text-xl md:text-2xl font-semibold ml-6 ">Courses</h1>
                        </div>
                        <div className="flex items-center">
                            <button className="text-sm text-gray-600 flex items-center pr-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#347468" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-calendar-days-icon lucide-calendar-days h-5 w-5 mr-1"><path d="M8 2v4" /><path d="M16 2v4" /><rect width="18" height="18" x="3" y="4" rx="2" /><path d="M3 10h18" /><path d="M8 14h.01" /><path d="M12 14h.01" /><path d="M16 14h.01" /><path d="M8 18h.01" /><path d="M12 18h.01" /><path d="M16 18h.01" /></svg>
                                Calender
                            </button>

                            <div className="lg:hidden">
                                <Sheet open={isRightSidebarOpen} onOpenChange={setIsRightSidebarOpen}>
                                    <SheetTrigger asChild>
                                        <button className="ml-4 text-[#347468] hover:text-[#347468] transition-colors ">
                                            <ChevronLeft className="h-6 w-6" />
                                        </button>
                                    </SheetTrigger>
                                    <SheetContent side="right" className="w-[300px] pt-12 px-2 pb-8 bg-white shadow-lg border-l border-gray-100 rounded-l-[10px]">
                                        <RightSidebar />
                                    </SheetContent>
                                </Sheet>
                            </div>
                        </div>
                    </div>
                </header>

                {/* Main Body */}
                <div className="flex flex-1 overflow-hidden">

                    <main className="flex-1 p-2 md:p-6 overflow-y-auto ">
                         
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.2 }}
                            className="bg-gradient-to-br from-[#386c8d] via-[#036664] to-[#060b0b] rounded-xl p-4 mb-6"
                        >
                            <div className="flex flex-wrap items-center gap-2 md:gap-4">
                                <Button
                                    variant={activeTab === 'courses' ? 'secondary' : 'ghost'}
                                    className={`text-sm md:text-base px-2 py-1 md:px-3 md:py-2 rounded-lg ${activeTab === 'courses'
                                        ? 'bg-white/10 border border-white/40 text-white'
                                        : 'text-white hover:bg-white/10'
                                        }`}
                                    onClick={() => setActiveTab('courses')}
                                >
                                    Courses
                                </Button>
                                <Button
                                    variant={activeTab === 'experts' ? 'secondary' : 'ghost'}
                                    className={`text-sm md:text-base px-2 py-1 md:px-3 md:py-2 rounded-lg ${activeTab === 'experts'
                                        ? 'bg-white/10 border border-white/40 text-white'
                                        : 'text-white hover:bg-white/10'
                                        }`}
                                    onClick={() => setActiveTab('experts')}
                                >
                                    Experts
                                </Button>
                                <Button
                                    variant={activeTab === 'certificates' ? 'secondary' : 'ghost'}
                                    className={`text-sm md:text-base px-2 py-1 md:px-3 md:py-2 rounded-lg ${activeTab === 'certificates'
                                        ? 'bg-white/10 border border-white/40 text-white'
                                        : 'text-white hover:bg-white/10'
                                        }`}
                                    onClick={() => setActiveTab('certificates')}
                                >
                                    Certificates
                                </Button>
                            </div>
                        </motion.div>

                        {activeTab === 'courses' && (
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.3 }}
                                className="space-y-4"
                            >
                                {(isSchoolCoursesLoading || isLoading) ? (
                                    // Loading skeleton
                                    <div className="space-y-4">
                                        {[1, 2, 3].map((index) => (
                                            <div key={index} className="bg-white p-3 rounded-[10px] shadow flex flex-col md:flex-row animate-pulse">
                                                <div className="relative flex-shrink-0">
                                                    <div className="w-full md:w-64 h-40 md:h-40 bg-gray-300 rounded-[10px]"></div>
                                                </div>
                                                <div className="mt-3 md:mt-0 md:ml-4 flex-1">
                                                    <div className="h-6 bg-gray-300 rounded w-3/4 mb-2"></div>
                                                    <div className="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
                                                    <div className="h-4 bg-gray-300 rounded w-2/3 mb-2"></div>
                                                    <div className="h-2 bg-gray-300 rounded w-full"></div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : courses.length > 0 ? (
                                    courses.map((course, index) => {
                                        // Calculate hours left based on actual progress
                                        const hoursLeft = course.progress !== undefined && course.progress < 100
                                            ? course.hours * (100 - course.progress) / 100
                                            : course.progress === 100 ? 0 : course.hours;
                                        return (
                                            <div
                                                key={course.id || index}
                                                className="bg-white p-3 rounded-[10px] shadow flex flex-col md:flex-row cursor-pointer hover:shadow-lg transition-shadow duration-300 relative"
                                                onClick={() => course.isEnrolled ? handleCourseClick(course) : undefined}
                                            >
                                                {/* Enrollment Badge - Moved to inside the content area */}

                                                <div className="relative flex-shrink-0 w-full md:w-64 h-40 md:h-40 overflow-hidden rounded-[10px]">
                                                    <img
                                                        src={course.image}
                                                        alt={course.title}
                                                        className="w-full h-full object-cover"
                                                        style={{ objectPosition: 'center center' }}
                                                    />
                                                    <div className="absolute inset-0 flex items-center justify-center hover:bg-black hover:bg-opacity-30 cursor-pointer opacity-80 hover:opacity-100 transition-opacity duration-300">
                                                        <PlayCircle className="w-12 h-12 text-white" />
                                                    </div>
                                                </div>
                                                <div className="mt-3 md:mt-0 md:ml-4 flex-1">
                                                    <div className="flex justify-between items-start mb-2">
                                                        <h3 className="text-lg font-semibold text-black flex-1 pr-2">{course.title}</h3>

                                                        {/* Enrollment Badge */}
                                                        {course.isEnrolled && (
                                                            <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-semibold whitespace-nowrap">
                                                                Enrolled
                                                            </span>
                                                        )}

                                                        {/* Enroll Now Button for non-enrolled courses */}
                                                        {!course.isEnrolled && (
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleEnrollNow(course.id);
                                                                }}
                                                                className="bg-[#347468] text-white text-xs px-3 py-1 rounded-full font-semibold hover:bg-[#2a5d56] transition-colors duration-200 whitespace-nowrap"
                                                            >
                                                                Enroll Now
                                                            </button>
                                                        )}
                                                    </div>

                                                    <p className="text-sm text-gray-500 flex flex-wrap items-center pt-2">
                                                        <CircleCheck className="mr-1 h-4 w-4 text-[#347468]" /> . {course.students} Students Enrolled . <span className="text-[#347468] pr-1 pl-1">{course.hours}</span> Hrs . <span className="text-[#347468] pr-1 pl-1">{course.totalChapters}</span> Chapters
                                                        {course.interested && (
                                                            <span className="text-sm text-[#347468] font-semibold pl-2">Interested?</span>
                                                        )}
                                                    </p>
                                                    <p className="text-sm text-black font-semibold pt-2">{course.mastery}</p>
                                                    {course.isEnrolled && course.progress !== undefined && !course.interested && course.totalLessons > 0 && (
                                                        <div className="mt-2">
                                                            <div className="flex justify-between text-sm text-gray-500 font-semibold">
                                                                <span className='text-black'>{course.lessons}/<span className="text-gray-500">{course.totalLessons} lessons </span> <span>{course.progress}%</span></span>
                                                                {hoursLeft !== undefined && hoursLeft > 0 && (
                                                                    <span className='text-[#347468] text-sm font-semibold'>{hoursLeft.toFixed(1)}hr left</span>
                                                                )}
                                                                {hoursLeft !== undefined && hoursLeft === 0 && (
                                                                    <span className='text-[#347468] text-sm font-semibold'>Complete</span>
                                                                )}
                                                            </div>
                                                            <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                                                <div className="bg-[#347468] h-1.5 rounded-full" style={{ width: `${course.progress}%` }}></div>
                                                            </div>
                                                        </div>
                                                    )}
                                                    {course.isEnrolled && course.progress !== undefined && course.totalLessons === 0 && !course.interested && (
                                                        <div className="mt-2">
                                                            <div className="flex justify-between text-sm text-gray-500 font-semibold">
                                                                <span className='text-black'>Course content loading...</span>
                                                                <span className='text-[#347468] text-sm font-semibold'>{course.progress}% complete</span>
                                                            </div>
                                                            <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                                                <div className="bg-[#347468] h-1.5 rounded-full" style={{ width: `${course.progress}%` }}></div>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        );
                                    })
                                ) : (
                                    // No courses found
                                    <div className="bg-white p-8 rounded-[10px] shadow text-center">
                                        <p className="text-gray-500 text-lg">No courses available</p>
                                        <p className="text-gray-400 text-sm mt-2">No courses are currently available in your school.</p>
                                    </div>
                                )}
                            </motion.div>
                        )}
                        {activeTab === 'experts' && (
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.3 }}
                                className="space-y-4"
                            >
                                {experts.map((expert, index) => (
                                    <ExpertCard key={index} expert={expert} />
                                ))}
                            </motion.div>
                        )}
                        {activeTab === 'certificates' && (
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.3 }}
                                className="grid grid-cols-1 sm:grid-cols-2 gap-4"
                            >
                                {certificates.map((cert, index) => (
                                    <CertificateCard key={index} certificate={cert} />
                                ))}
                            </motion.div>
                        )}
                    </main>
                    <div className="hidden lg:block flex-shrink-0">
                        <RightSidebar />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default MainCourse;
