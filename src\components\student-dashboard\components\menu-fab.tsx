import React from 'react';
import { motion } from 'framer-motion';
import { Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface MenuFABProps {
    onClick: () => void;
}

const MenuFAB = ({ onClick }: MenuFABProps) => {
    return (
        <motion.div
            className="fixed bottom-4 left-4 md:hidden z-50"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
        >
            <Button
                size="icon"
                onClick={onClick}
                className="h-14 w-14 rounded-full shadow-lg bg-gradient-to-r from-primary to-primary/80"
            >
                <Menu className="h-6 w-6 text-white" />
            </Button>
        </motion.div>
    );
};

export default MenuFAB;
