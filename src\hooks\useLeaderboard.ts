import { useState, useEffect } from 'react';
import { useGetLeaderboardQuery, useGetUserLeaderboardStatsQuery } from '../services/discussionsAPIjs';

export type TimeRange = 'daily' | 'weekly' | 'monthly' | 'alltime';

interface LeaderboardEntry {
  userId: string;
  name: string;
  profilePicture?: string;
  score: number;
  rank: number;
  commentCount: number;
  updatedAt: string;
  badges?: string[];
}

interface UserActivity {
  commentCount: number;
  discussionsParticipated: number;
  moderationScore: number;
  voteScore: number;
}

interface LeaderboardStats {
  topUsers: LeaderboardEntry[];
  userRank?: LeaderboardEntry;
  userActivity?: UserActivity;
  timeRange: TimeRange;
  updatedAt: string;
}

export function useLeaderboard(timeRange: TimeRange = 'weekly') {
  const [leaderboardStats, setLeaderboardStats] = useState<LeaderboardStats | null>(null);
  
  // Fetch leaderboard data using RTK Query
  const { 
    data: leaderboardData, 
    isLoading: loadingLeaderboard,
    error: leaderboardError
  } = useGetLeaderboardQuery(timeRange);
  
  // Fetch current user's leaderboard stats
  const { 
    data: userLeaderboardData, 
    isLoading: loadingUserStats,
    error: userStatsError
  } = useGetUserLeaderboardStatsQuery(timeRange);

  // Process data when it arrives
  useEffect(() => {
    if (leaderboardData && userLeaderboardData) {
      // In a real app, we'd transform the API response here
      // For now, we'll use mock data to demonstrate the structure
      
      const mockLeaderboardStats: LeaderboardStats = {
        topUsers: [
          {
            userId: 'user-1',
            name: 'Jamie Rodriguez',
            profilePicture: '',
            score: 92.5,
            rank: 1,
            commentCount: 18,
            updatedAt: new Date().toISOString(),
            badges: ['top-contributor', 'insightful']
          },
          {
            userId: 'user-3',
            name: 'Morgan Smith',
            profilePicture: '',
            score: 88.7,
            rank: 2,
            commentCount: 15,
            updatedAt: new Date().toISOString(),
            badges: ['rising-star']
          },
          {
            userId: 'user-2',
            name: 'Alex Chen',
            profilePicture: '',
            score: 85.2,
            rank: 3,
            commentCount: 12,
            updatedAt: new Date().toISOString(),
            badges: ['helpful']
          }
        ],
        userRank: {
          userId: 'current-user',
          name: 'Current User',
          profilePicture: '',
          score: 72.8,
          rank: 8,
          commentCount: 5,
          updatedAt: new Date().toISOString()
        },
        userActivity: {
          commentCount: 5,
          discussionsParticipated: 3,
          moderationScore: 85,
          voteScore: 12
        },
        timeRange,
        updatedAt: new Date().toISOString()
      };
      
      setLeaderboardStats(mockLeaderboardStats);
    }
  }, [leaderboardData, userLeaderboardData, timeRange]);

  return {
    leaderboardStats,
    isLoading: loadingLeaderboard || loadingUserStats,
    error: leaderboardError || userStatsError,
    timeRange
  };
}