import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import SunEditor from "suneditor-react";
import useDebounce from "@/helpers/useDebounce";
import {
  setQuestionsValue,
  setQuestionsValueMany,
} from "components/Admin/QuestionCreation/questionSlice";
import circle from "./assets/circle.svg";

const Answers = ({ handleAnswerComplete, handleCancel }) => {
  const dispatch = useDispatch();
  const questionText = useSelector(
    (state) => state.questions.questions.questionText
  );
  const answers = useSelector((state) => state.questions.questions.answers);
  const questionType = useSelector(
    (state) => state.questions.questions.questionType
  );
  const correctAnswer = useSelector(
    (state) => state.questions.questions.correctAnswer
  );
  const singleChoiceAns = useSelector(
    (state) => state.questions.questions.singleChoiceAns
  );
  const questionText2 = useSelector(
    (state) => state.questions.questions.questionText2
  );

  const [answer, setAnswer] = useState(null);
  const [isCorrect, setIsCorrect] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  const [matchAnswer1, setMatchAnswer1] = useState(null);
  const [matchAnswer2, setMatchAnswer2] = useState(null);
  const [isNextDisabled, setIsNextDisabled] = useState(true);
  const answerTextDebounceVal = useDebounce(answer);

  const handleAnswerSubmit = () => {
    let ansObj = {};
    let answersData = [];
    const obj = {
      answerText: answerTextDebounceVal,
    };
    if (isEdit) {
      if (questionType === "SINGLECHOICE" || questionType === "MULTICHOICE") {
        answersData = answers?.map((item, index) => {
          if (index === editIndex) {
            return {
              ...item,
              answerText: answerTextDebounceVal,
            };
          } else {
            return {
              ...item,
            };
          }
        });
        ansObj["answers"] = answersData;
      }

      switch (questionType) {
        case "SINGLECHOICE":
          ansObj = singleChoiceEditSubmit(ansObj);
          break;
        case "MULTICHOICE":
          ansObj = multiChoiceEditSubmit(ansObj);
          break;
        case "TEXTINPUT":
          ansObj = textInputAnswerSubmit(obj, ansObj);
          break;
        case "MATCH":
          ansObj = matchAnswerEditSubmit(ansObj);
          break;
      }
    } else {
      if (questionType === "SINGLECHOICE" || questionType === "MULTICHOICE") {
        let localAnswers = answers;
        ansObj["answers"] = [...localAnswers, obj];
      }
      switch (questionType) {
        case "SINGLECHOICE":
          ansObj = singleChoiceAnswerSubmit(ansObj);
          break;
        case "MULTICHOICE":
          ansObj = multiChoiceAnswerSubmit(obj, ansObj);
          break;
        case "TEXTINPUT":
          ansObj = textInputAnswerSubmit(obj, ansObj);
          break;
        case "MATCH":
          ansObj = matchAnswerSubmit(ansObj);
          break;
      }
    }
    dispatch(setQuestionsValueMany(ansObj));
    handleCancelEdit();
    setAnswer("");
    setMatchAnswer1("");
    setMatchAnswer2("");
  };

  const singleChoiceEditSubmit = (ansObj) => {
    ansObj["correctAnswer"] =
      editIndex === correctAnswer
        ? isCorrect
          ? editIndex
          : null
        : isCorrect
        ? editIndex
        : correctAnswer;

    let index = ansObj["correctAnswer"];
    let data = ansObj["answers"]?.[index];
    ansObj["singleChoiceAns"] = data?.answerText;
    return ansObj;
  };

  const multiChoiceEditSubmit = (ansObj) => {
    ansObj["correctAnswer"] = isCorrect
      ? correctAnswer?.includes(answer)
        ? correctAnswer
        : [...correctAnswer, answer]
      : correctAnswer?.includes(answer)
      ? correctAnswer?.filter((ans) => ans !== answer)
      : correctAnswer;
    return ansObj;
  };

  const singleChoiceAnswerSubmit = (ansObj, localAnswers) => {
    ansObj["correctAnswer"] = isCorrect
      ? answers?.length === 0
        ? 0
        : answers?.length
      : correctAnswer;
    let index = ansObj["correctAnswer"];

    let data = ansObj["answers"]?.[index];
    ansObj["singleChoiceAns"] = data?.answerText;
    return ansObj;
  };

  const multiChoiceAnswerSubmit = (obj, ansObj) => {
    ansObj["correctAnswer"] = isCorrect
      ? correctAnswer?.length > 0
        ? [...correctAnswer, obj?.answerText]
        : [obj?.answerText]
      : correctAnswer;

    return ansObj;
  };

  const textInputAnswerSubmit = (obj, ansObj) => {
    ansObj["correctAnswer"] = obj?.answerText;
    return ansObj;
  };

  const matchAnswerEditSubmit = (ansObj) => {
    let tempObj = { ...correctAnswer };

    Object.keys(tempObj).forEach(function (key, index) {
      if (index === editIndex) {
        let newkey = matchAnswer1;
        tempObj[newkey] = matchAnswer2;
        if (key !== newkey) {
          delete tempObj[key];
        }
      }
    });
    ansObj["correctAnswer"] = tempObj;
    return ansObj;
  };

  const matchAnswerSubmit = (ansObj) => {
    let obj = {};
    if (correctAnswer && Object?.keys(correctAnswer)?.length > 0) {
      let keyValue = matchAnswer1?.replace(/ /g, "");

      let tempObj = {
        [keyValue]: matchAnswer2,
      };
      obj = { ...correctAnswer, ...tempObj };
    } else {
      let keyValue = matchAnswer1?.replace(/ /g, "");
      obj = {
        [keyValue]: matchAnswer2,
      };
    }
    ansObj["correctAnswer"] = obj;
    return ansObj;
  };

  const handleIsCorrect = (item, indexval) => {
    let ansObj = {};
    switch (questionType) {
      case "SINGLECHOICE":
        ansObj = singleChoiceIsCorrectChecked(indexval, ansObj);
        break;
      case "MULTICHOICE":
        ansObj = multiChoiceIsCorrectChecked(item, ansObj);
        break;
    }
    dispatch(setQuestionsValueMany(ansObj));
  };

  const singleChoiceIsCorrectChecked = (indexval, ansObj) => {
    ansObj["correctAnswer"] = correctAnswer === indexval ? null : indexval;

    let index = ansObj["correctAnswer"];
    let data = answers?.[index];
    ansObj["singleChoiceAns"] = data?.answerText;
    return ansObj;
  };

  const multiChoiceIsCorrectChecked = (item, ansObj) => {
    let data =
      correctAnswer?.length > 0
        ? correctAnswer?.includes(item?.answerText)
          ? correctAnswer?.filter((ans) => ans !== item?.answerText)
          : [...correctAnswer, item?.answerText]
        : [item?.answerText];
    ansObj["correctAnswer"] = data;
    return ansObj;
  };

  const getCheckedValue = (indexval, ans) => {
    switch (questionType) {
      case "SINGLECHOICE":
        return singleChoiceChecked(indexval);
      case "MULTICHOICE":
        return multiChoiceChecked(ans);
    }
  };

  const singleChoiceChecked = (indexval) => {
    return indexval === correctAnswer ? true : false;
  };

  const multiChoiceChecked = (ans) => {
    return correctAnswer?.includes(ans);
  };

  const handleEditSubmit = (index) => {
    setIsEdit(true);
    setEditIndex(index);
    const answersData = [...answers];
    setAnswer(answersData?.[index].answerText);
    setCheckboxWhileEdit(index, answersData?.[index].answerText);
  };

  const setCheckboxWhileEdit = (index, answerText) => {
    switch (questionType) {
      case "SINGLECHOICE":
        setIsCorrect(index === correctAnswer);
        break;
      case "MULTICHOICE":
        setIsCorrect(correctAnswer?.includes(answerText));
        break;
    }
  };

  const handleTextInputEditSubmit = () => {
    setIsEdit(true);
    setAnswer(correctAnswer);
  };

  const handleMatchInputEditSubmit = (keyVal, index) => {
    setIsEdit(true);
    setEditIndex(index);
    setMatchAnswer1(keyVal);
    setMatchAnswer2(correctAnswer?.[keyVal]);
  };

  const handleTextInputDeleteSubmit = () => {
    const ansObj = {
      correctAnswer: null,
    };
    dispatch(setQuestionsValue(ansObj));
  };

  const handleMatchInputDeleteSubmit = (keyVal) => {
    let obj = { ...correctAnswer };
    delete obj[keyVal];
    const ansObj = {
      correctAnswer: Object?.keys(obj)?.length > 0 ? obj : null,
    };

    dispatch(setQuestionsValue(ansObj));
  };

  const getNextDisabledStatus = () => {
    if (questionType === "MULTICHOICE" && correctAnswer?.length == 0) {
      return true;
    } else if (correctAnswer !== null || questionType === "BINARY") {
      return false;
    }
    return true;
  };
  const handleDeleteSubmit = (indexVal, item) => {
    let ansObj = {};
    let filteredData = [];
    if (questionType === "SINGLECHOICE" || questionType === "MULTICHOICE") {
      const answersData = [...answers];
      filteredData = answersData.filter((item, index) => index !== indexVal);
      ansObj = {
        answers: filteredData,
      };
    }
    switch (questionType) {
      case "SINGLECHOICE":
        singleChoiceDelete(indexVal, ansObj, filteredData);
        break;
      case "MULTICHOICE":
        multiChoiceDelete(item, ansObj);
        break;
    }
  };

  const getAnswerTextIndex = (filteredData) => {
    let index = filteredData.findIndex(
      (item) => item.answerText === singleChoiceAns
    );
    return index;
  };

  const singleChoiceDelete = (indexVal, ansObj, filteredData) => {
    ansObj["correctAnswer"] =
      indexVal === correctAnswer ? null : getAnswerTextIndex(filteredData);

    dispatch(setQuestionsValueMany(ansObj));
  };

  const multiChoiceDelete = (item, ansObj) => {
    if (correctAnswer?.includes(item?.answerText)) {
      let filteredData = correctAnswer?.filter(
        (ans) => ans !== item?.answerText
      );
      ansObj["correctAnswer"] = filteredData;
      dispatch(setQuestionsValueMany(ansObj));
    }
  };

  const getAddAnsDisabled = () => {
    if (questionType === "TEXTINPUT") {
      if (correctAnswer?.length > 0) {
        return true;
      }
    }
    if (questionType === "MATCH") {
      if (matchAnswer1?.length && matchAnswer2?.length) {
        return false;
      } else {
        return true;
      }
    }
    return !answer?.replace(/<[^>]+>/g, "")?.length;
  };

  const handleEditAnswer = () => {
    handleAnswerSubmit();
  };

  const handleNext = () => {
    handleAnswerComplete(2);
  };

  const handleCancelEdit = () => {
    setIsEdit(false);
    setEditIndex(null);
    setAnswer(null); // Clear the answer state
    setIsCorrect(false);
    setMatchAnswer1("");
    setMatchAnswer2("");
  };

  const getSaveAnsDisabled = () => {
    if (questionType === "MATCH") {
      if (matchAnswer1?.length && matchAnswer2?.length) {
        return false;
      } else {
        return true;
      }
    }
    return !answerTextDebounceVal?.length;
  };

  useEffect(() => {
    if (questionType === "BINARY") {
      let ansObj = {};
      if (isCorrect) {
        ansObj["correctAnswer"] = isCorrect;
      } else {
        ansObj["correctAnswer"] = false;
      }

      dispatch(setQuestionsValue(ansObj));
    }
  }, [isCorrect]);

  useEffect(() => {
    if (questionType === "BINARY") {
      setIsCorrect(correctAnswer ? correctAnswer : false);
    }
  }, []);

  return (
    <>
      <p className="question-label">Question </p>

      <div
        className="html-content question-text"
        dangerouslySetInnerHTML={{
          __html: questionText + " " + questionText2,
        }}
      />
      {answers?.length > 0 && (
        <>
          <div style={{ marginTop: 20 }}>
            <span className="answer-label">Answer Options </span>
          </div>
          {answers?.map((item, index) => {
            return (
              <div key={index} className="answer-option">
                <div
                  className="html-content answer-text"
                  dangerouslySetInnerHTML={{
                    __html: item?.answerText,
                  }}
                />
                {(questionType === "MULTICHOICE" ||
                  questionType === "SINGLECHOICE") && (
                  <div className="checkbox-toggle">
                    <input
                      type="checkbox"
                      id={`answer-${index}`}
                      // checked={item?.isCorrect}
                      checked={getCheckedValue(index, item?.answerText)}
                      //   onChange={() => handleIsCorrect(item?.isCorrect, index)}
                      onChange={() => handleIsCorrect(item, index)}
                    />

                    <label
                      htmlFor={`answer-${index}`}
                      className="toggle-label"
                    ></label>
                  </div>
                )}
                <button
                  onClick={() => handleEditSubmit(index)}
                  className="edit-button"
                >
                  Edit
                </button>
                <button
                  onClick={() => handleDeleteSubmit(index, item)}
                  className="delete-button"
                >
                  Delete
                </button>
              </div>
            );
          })}
        </>
      )}

      {correctAnswer?.length > 0 && questionType === "TEXTINPUT" && (
        <>
          <div style={{ marginTop: 20 }}>
            <span className="answer-label">Answer Options </span>
          </div>

          <div className="answer-option">
            <div
              className="html-content answer-text"
              dangerouslySetInnerHTML={{
                __html: correctAnswer,
              }}
            />

            <button
              onClick={() => handleTextInputEditSubmit()}
              className="edit-button"
            >
              Edit
            </button>
            <button
              onClick={() => handleTextInputDeleteSubmit()}
              className="delete-button"
            >
              Delete
            </button>
          </div>
        </>
      )}

      {questionType === "MATCH" &&
        correctAnswer &&
        Object.keys(correctAnswer)?.length > 0 && (
          <>
            <div style={{ marginTop: 20 }}>
              <span className="answer-label">Answer Options </span>
            </div>

            {Object.keys(correctAnswer).map((keyName, index) => (
              <div className="answer-option">
                <div
                  key={index}
                  style={{ display: "flex" }}
                  className="html-content"
                >
                  <div
                    className="html-content answer-text"
                    dangerouslySetInnerHTML={{
                      __html: keyName,
                    }}
                  />
                  <div
                    className="html-content answer-text"
                    dangerouslySetInnerHTML={{
                      __html: correctAnswer[keyName],
                    }}
                  />
                </div>
                <button
                  onClick={() => handleMatchInputEditSubmit(keyName, index)}
                  className="edit-button"
                >
                  Edit
                </button>
                <button
                  onClick={() => handleMatchInputDeleteSubmit(keyName)}
                  className="delete-button"
                >
                  Delete
                </button>
              </div>
            ))}
          </>
        )}

      {questionType !== "BINARY" && (
        <>
          <div style={{ marginTop: 10, height: 30 }}>
            <span className="answer-label">Answer</span>
          </div>
          {/* <div className='html-content answer-text'  style={{marginTop:10}} >
      {answerTextDebounceVal}
      </div> */}
          <div
            className="html-content answer-text"
            dangerouslySetInnerHTML={{
              __html: answerTextDebounceVal,
            }}
          />
        </>
      )}
      {isEdit ? (
        <>
          <button
            onClick={handleEditAnswer}
            className="save-button"
            disabled={getSaveAnsDisabled()}
          >
            Save Answer
          </button>
          <button onClick={handleCancelEdit} className="cancel-button">
            Cancel Edit
          </button>
        </>
      ) : (
        <>
          {questionType !== "BINARY" && (
            <>
              <button
                onClick={handleAnswerSubmit}
                disabled={getAddAnsDisabled()}
                className="add-answer-button"
              >
                Add Answer
              </button>
            </>
          )}
        </>
      )}
      {(questionType === "MULTICHOICE" ||
        questionType === "SINGLECHOICE" ||
        questionType === "TEXTINPUT") && (
        <div className="question-input-cont">
          <textarea
            className="question-input"
            value={answer || ""}
            onChange={(e) => setAnswer(e.target.value)}
          />
        </div>
      )}
      {questionType === "MATCH" && (
        <div className="question-input-cont match-input-cont">
          <textarea
            className="question-input-match"
            value={matchAnswer1}
            onChange={(e) => setMatchAnswer1(e.target.value)}
          />
          <img src={circle} alt={"circle"} />

          <textarea
            className="question-input-match"
            value={matchAnswer2}
            onChange={(e) => setMatchAnswer2(e.target.value)}
          />
        </div>
      )}
      {/* <SunEditor
        setOptions={{
          buttonList: [
            ["undo", "redo"],
            ["font", "fontSize", "formatBlock"],
            ["bold", "underline", "italic", "strike"],
            ["fontColor", "hiliteColor"],
            ["align", "list", "table"],
            ["link", "image", "video"],
            ["fullScreen", "showBlocks", "codeView"],
            ["preview", "print"],
          ],
          height: "70%",
          minHeight: "100px",
        }}
        setContents={answer}
        onChange={(textContent) => setAnswer(textContent)}
      /> */}
      <br />

      {(questionType === "MULTICHOICE" ||
        questionType === "SINGLECHOICE" ||
        questionType === "BINARY") && (
        <div className="answer-option">
          <div className="checkbox-toggle">
            <input
              type="checkbox"
              id="toggleSwitch"
              checked={isCorrect}
              onChange={() => setIsCorrect(!isCorrect)}
            />
            <label className="toggle-label" htmlFor="toggleSwitch"></label>
          </div>
          <span className="correct-answer-label">
            Is this a Correct Answer?
          </span>
        </div>
      )}
      <div className="button-container">
        <div className="align-right">
          <button
            id="cancelbutton"
            className="cancel-button"
            onClick={() => handleCancel()}
          >
            Cancel
          </button>
        </div>
        <div id="nextbutton" className="align-left">
          <button onClick={handleNext} disabled={getNextDisabledStatus()}>
            Next
          </button>
        </div>
      </div>
    </>
  );
};

export default Answers;
