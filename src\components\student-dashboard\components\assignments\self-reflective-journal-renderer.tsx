import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import {
  Upload,
  Calendar,
  ChevronRight,
  ChevronLeft,
  CheckCircle
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { FileFolderSDK } from '@/lib/FileFolderSdk';
import {
  useLazyGetStudentSubmissionQuery,
  useCreateSubmissionMutation,
  useUpdateSubmissionMutation
} from '@/APIConnect';

interface Question {
  id: string;
  questionText: string;
  answer?: string;
  type?: string;
  studentAnswer?: string | null;
}

interface QuizData {
  id: string;
  type?: string;
  questionSet?: Question[];
  title?: string;
}

interface SelfReflectiveJournalRendererProps {
  title?: string;
  description?: string;
  deadline?: string;
  questions?: Question[];
  quizData?: QuizData;
  courseId?: string;
  assignmentId?: string; // subtopicId
  classroomId?: string;
  onFileUpload?: (files: FileList) => void;
  onCompleted?: () => void;
  onSubmit?: (answers: Record<string, string>, files?: FileList) => void;
}

const SelfReflectiveJournalRenderer: React.FC<SelfReflectiveJournalRendererProps> = ({
  title = "Self Reflective Journal",
  description = "Reflect on your learning journey and provide thoughtful responses to the following questions.",
  deadline = "26th June 2025, 12:00 AM",
  questions: propQuestions = [
    { id: "1", questionText: "What were the key concepts you learned in this module?" },
    { id: "2", questionText: "How can you apply these concepts in real-world scenarios?" },
    { id: "3", questionText: "What challenges did you face during your learning process?" },
    { id: "4", questionText: "What strategies helped you overcome these challenges?" },
    { id: "5", questionText: "How has this learning experience changed your perspective?" }
  ],
  quizData,
  courseId,
  assignmentId,
  classroomId,
  onFileUpload,
  onSubmit,
  onCompleted
}) => {
  // Determine which questions to use - from quiz data or default props
  const questions = React.useMemo(() => {
    if (quizData?.questionSet && quizData.questionSet.length > 0) {
      // Filter only text input questions for self-reflective journal
      return quizData.questionSet
        .filter(q => q.type === 'TEXTINPUT')
        .map(q => ({
          id: q.id,
          questionText: q.questionText,
          answer: q.studentAnswer || '',
          type: q.type
        }));
    }
    return propQuestions;
  }, [quizData, propQuestions]);

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>(() => {
    // Initialize answers with existing student answers if available
    const initialAnswers: Record<string, string> = {};
    questions.forEach(q => {
      if (q.answer) {
        initialAnswers[q.id] = q.answer;
      }
    });
    return initialAnswers;
  });
  const [showUpload, setShowUpload] = useState(false); // Track if we're in upload phase
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<FileList | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [fileUploaded, setFileUploaded] = useState(false);
  const [uploadedFileUrl, setUploadedFileUrl] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [existingSubmission, setExistingSubmission] = useState<any>(null);
  const [submissionSuccess, setSubmissionSuccess] = useState(false);
  const [isLoadingSubmission, setIsLoadingSubmission] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const fileUrlRef = useRef<string>(''); // Store file URL in ref for immediate access

  // Initialize FileFolderSDK and API hooks
  const fileFolderSDK = new FileFolderSDK();
  const [getStudentSubmission] = useLazyGetStudentSubmissionQuery();
  const [createSubmission] = useCreateSubmissionMutation();
  const [updateSubmission] = useUpdateSubmissionMutation();

  const currentQuestion = questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === questions.length - 1;
  const isFirstQuestion = currentQuestionIndex === 0;
  const allQuestionsAnswered = questions.every(q => answers[q.id]?.trim());

  // Calculate progress including upload phase
  const totalSteps = questions.length + 1; // questions + upload
  const currentStep = showUpload ? questions.length + 1 : currentQuestionIndex + 1;
  const progress = (currentStep / totalSteps) * 100;

  // Load existing submission on component mount
  useEffect(() => {
    if (assignmentId && classroomId) {
      loadExistingSubmission();
    }
  }, [assignmentId, classroomId]);

  const loadExistingSubmission = async () => {
    if (!assignmentId || !classroomId) return;

    setIsLoadingSubmission(true);
    try {
      console.log('Loading existing submission for:', { assignmentId, classroomId });
      const result = await getStudentSubmission({
        classroomId,
        assignmentId
      });

      console.log('Existing submission result:', result);

      if (result.data && result.data.resultObject) {
        const submission = result.data.resultObject;
        setExistingSubmission(submission);

        // Pre-populate answers from existing submission
        if (submission.selfReflectiveAnswers && submission.selfReflectiveAnswers.length > 0) {
          const existingAnswers: Record<string, string> = {};
          submission.selfReflectiveAnswers.forEach((answer: any) => {
            existingAnswers[answer.questionId] = answer.answer;
          });
          setAnswers(existingAnswers);
        }

        // Set file info if exists
        if (submission.fileUrl) {
          setUploadedFileUrl(submission.fileUrl);
          fileUrlRef.current = submission.fileUrl;
          setFileUploaded(true);
        }

        console.log('Existing submission loaded:', submission);
      }
    } catch (error) {
      console.error('Error loading existing submission:', error);
    } finally {
      setIsLoadingSubmission(false);
    }
  };

  const handleAnswerChange = (questionId: string, answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleNext = () => {
    if (isLastQuestion && allQuestionsAnswered) {
      // Move to upload phase
      setShowUpload(true);
    } else if (!isLastQuestion) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (showUpload) {
      // Go back to last question
      setShowUpload(false);
    } else if (!isFirstQuestion) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setUploadedFiles(e.dataTransfer.files);
      handleFileUpload(e.dataTransfer.files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setUploadedFiles(e.target.files);
      handleFileUpload(e.target.files[0]);
    }
  };

  const handleFileUpload = async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);
    setFileUploaded(false);

    try {
      // Upload file to get URL
      const uniquePrefix = Date.now();
      const fileName = `self-reflective-journals/${assignmentId}/${uniquePrefix}-${file.name}`;

      console.log('Uploading file:', fileName);
      const fileUrl = await fileFolderSDK.uploadFile(
        file,
        fileName,
        (progress) => setUploadProgress(progress)
      );

      console.log('File uploaded successfully:', fileUrl);
      fileUrlRef.current = fileUrl; // Store in ref for immediate access
      setUploadedFileUrl(fileUrl);
      setIsUploading(false);
      setFileUploaded(true);

      console.log('File upload state updated:', {
        uploadedFileUrl: fileUrl,
        fileUrlRef: fileUrlRef.current,
        fileUploaded: true,
        fileName: file.name
      });
    } catch (error) {
      console.error('Error uploading file:', error);
      setIsUploading(false);
      setFileUploaded(false);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const handleSubmit = async () => {
    // Use ref as fallback for file URL
    const finalFileUrl = uploadedFileUrl || fileUrlRef.current;

    console.log('Submit button clicked - Debug data:', {
      assignmentId,
      classroomId,
      uploadedFileUrl,
      fileUrlRef: fileUrlRef.current,
      finalFileUrl,
      fileUploaded,
      uploadedFiles: uploadedFiles?.length,
      questionsAnswered: questions.length,
      answersCount: Object.keys(answers).length
    });

    // Check each field individually for better debugging
    if (!assignmentId) {
      console.error('Missing assignmentId:', assignmentId);
      return;
    }
    if (!classroomId) {
      console.error('Missing classroomId:', classroomId);
      return;
    }
    if (!finalFileUrl) {
      console.error('Missing file URL:', { uploadedFileUrl, fileUrlRef: fileUrlRef.current });
      return;
    }
    if (!fileUploaded) {
      console.error('Missing fileUploaded flag:', fileUploaded);
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare self-reflective answers
      const selfReflectiveAnswers = questions.map(q => ({
        questionId: q.id,
        answer: answers[q.id] || ''
      }));

      // Create submission data
      const submissionData = {
        classroomId,
        assignmentId,
        fileUrl: finalFileUrl,
        additionalFiles: [],
        selfReflectiveAnswers
      };

      console.log('Creating submission:', submissionData);

      let result;
      if (existingSubmission) {
        console.log('Updating existing submission...');
        result = await updateSubmission(submissionData);
      } else {
        console.log('Creating new submission...');
        result = await createSubmission(submissionData);
      }

      if (result.error) {
        throw new Error(`Submission failed: ${JSON.stringify(result.error)}`);
      }

      console.log('Submission successful:', result);

      // Show success message
      setSubmissionSuccess(true);

      // Update existing submission with the new data
      if (result.data?.resultObject) {
        setExistingSubmission(result.data.resultObject);
      }

      // Call completion callback
      onCompleted?.();

      // Also call legacy onSubmit if provided
      onSubmit?.(answers, uploadedFiles || undefined);

    } catch (error) {
      console.error('Error submitting self-reflective journal:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="flex items-center justify-end p-6 border-b border-gray-200">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Calendar className="w-4 h-4" />
          <span>Deadline- {deadline}</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6 max-w-4xl mx-auto">
        {/* Title and Description */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{title}</h1>
          <p className="text-gray-700 leading-relaxed mb-6">{description}</p>
        </motion.div>

        {/* Loading State */}
        {isLoadingSubmission && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card>
              <CardContent className="p-8 text-center">
                <div className="h-8 w-8 rounded-full border-4 border-[#347468] border-t-transparent animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600">Loading your submission...</p>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Success Message */}
        {submissionSuccess && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-green-800">Submission Successful!</h3>
                    <p className="text-green-600">Your self-reflective journal has been submitted successfully.</p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setSubmissionSuccess(false)}
                  className="text-green-700 border-green-300 hover:bg-green-100"
                >
                  Continue Editing
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Existing Submission Display */}
        {existingSubmission && !submissionSuccess && !isLoadingSubmission && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-blue-800 flex items-center gap-2">
                  <CheckCircle className="w-5 h-5" />
                  Previously Submitted
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-blue-600 mb-2">
                      <strong>Submitted on:</strong> {new Date(existingSubmission.createdOn).toLocaleDateString()} at {new Date(existingSubmission.createdOn).toLocaleTimeString()}
                    </p>
                    {existingSubmission.score !== undefined && (
                      <p className="text-sm text-blue-600 mb-2">
                        <strong>Score:</strong> {existingSubmission.score}
                      </p>
                    )}
                  </div>

                  {existingSubmission.fileUrl && (
                    <div>
                      <p className="text-sm font-medium text-blue-800 mb-2">Submitted File:</p>
                      <a
                        href={existingSubmission.fileUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 underline text-sm"
                      >
                        View Submitted File
                      </a>
                    </div>
                  )}

                  {existingSubmission.selfReflectiveAnswers && existingSubmission.selfReflectiveAnswers.length > 0 && (
                    <div>
                      <p className="text-sm font-medium text-blue-800 mb-2">Your Answers:</p>
                      <div className="space-y-2">
                        {existingSubmission.selfReflectiveAnswers.map((answer: any, index: number) => (
                          <div key={answer.questionId} className="bg-white p-3 rounded border">
                            <p className="text-xs text-gray-500 mb-1">Question {index + 1}</p>
                            <p className="text-sm text-gray-800">{answer.answer}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="pt-4 border-t border-blue-200">
                    <p className="text-sm text-blue-600 mb-3">
                      You can update your submission by making changes below and resubmitting.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Progress */}
        {!submissionSuccess && !isLoadingSubmission && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-gray-700">
                {showUpload ? `Upload Supporting Files` : `Question ${currentQuestionIndex + 1} of ${questions.length}`}
              </span>
              <span className="text-sm font-bold text-gray-900">{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </motion.div>
        )}

        {/* Main Content Area - Questions or Upload */}
        {!submissionSuccess && !isLoadingSubmission && (
          <AnimatePresence mode="wait">
            {!showUpload ? (
            // Question Card
            <motion.div
              key={currentQuestionIndex}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800">
                    {currentQuestion?.questionText}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Type your reflection here..."
                    value={answers[currentQuestion?.id] || ''}
                    onChange={(e) => handleAnswerChange(currentQuestion?.id, e.target.value)}
                    className="min-h-[200px] resize-none"
                  />
                </CardContent>
              </Card>
            </motion.div>
          ) : (
            // Upload Card
            <motion.div
              key="upload"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="text-xl text-gray-800">
                    Upload Supporting Files
                  </CardTitle>
                  <p className="text-gray-600 mt-2">
                    Please upload a file to support your reflections. This is required to complete your journal.
                  </p>
                </CardHeader>
                <CardContent>
                  {/* Hidden File Input */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    onChange={handleFileSelect}
                    className="hidden"
                    accept="image/*,video/*,.pdf,.doc,.docx,.txt"
                  />

                  {/* Upload Area */}
                  <div
                    className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${
                      dragActive ? 'border-[#347468] bg-[#347468]/10' : 'border-gray-300 bg-gray-50'
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    {isUploading ? (
                      // Upload Progress
                      <div className="flex flex-col items-center gap-4">
                        <div className="h-8 w-8 rounded-full border-4 border-[#347468] border-t-transparent animate-spin"></div>
                        <div className="w-full max-w-xs">
                          <p className="text-sm font-medium text-gray-700 mb-2">Uploading...</p>
                          <Progress value={uploadProgress} className="h-2" />
                        </div>
                        <span className="text-sm text-gray-500">{Math.round(uploadProgress)}%</span>
                      </div>
                    ) : fileUploaded && uploadedFiles ? (
                      // File Uploaded Successfully
                      <div className="flex flex-col items-center gap-4">
                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                          <CheckCircle className="w-6 h-6 text-green-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-green-800">File uploaded successfully!</p>
                          <p className="text-xs text-gray-600 mt-1">{uploadedFiles[0].name}</p>
                        </div>
                        <Button
                          variant="outline"
                          onClick={openFileDialog}
                          className="text-[#347468] border-[#347468] hover:bg-[#347468] hover:text-white"
                        >
                          Upload Different File
                        </Button>
                      </div>
                    ) : (
                      // Upload Interface
                      <div className="flex flex-col items-center gap-4">
                        <Upload className="w-12 h-12 text-gray-400" />
                        <div>
                          <p className="text-lg font-medium text-gray-700 mb-2">Upload your supporting file</p>
                          <p className="text-sm text-gray-500 mb-4">
                            Drag and drop your file here, or click to browse
                          </p>
                        </div>
                        <Button
                          onClick={openFileDialog}
                          className="bg-[#347468] hover:bg-[#2a5d54] text-white px-8 py-3"
                        >
                          Choose File
                        </Button>
                        <p className="text-xs text-gray-400 mt-2">
                          Supports: Images, Videos, PDF, Word documents
                        </p>
                      </div>
                    )}

                    {/* Drag Overlay */}
                    {dragActive && (
                      <div className="absolute inset-0 bg-[#347468]/10 border-2 border-dashed border-[#347468] rounded-lg flex items-center justify-center">
                        <div className="text-center">
                          <Upload className="w-12 h-12 text-[#347468] mx-auto mb-2" />
                          <p className="text-[#347468] font-medium">Drop file here to upload</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
        )}

        {/* Navigation Buttons */}
        {!submissionSuccess && !isLoadingSubmission && (
        <div className="flex justify-between mb-8">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={isFirstQuestion && !showUpload}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="w-4 h-4" />
            Previous
          </Button>

          {showUpload ? (
            // Submit button - only enabled after file upload
            <Button
              onClick={handleSubmit}
              disabled={!fileUploaded || isSubmitting}
              className="bg-[#347468] hover:bg-[#2a5d54] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Journal'}
            </Button>
          ) : !isLastQuestion ? (
            // Next button for questions
            <Button
              onClick={handleNext}
              className="flex items-center gap-2 bg-[#347468] hover:bg-[#2a5d54]"
            >
              Next
              <ChevronRight className="w-4 h-4" />
            </Button>
          ) : (
            // Next button to go to upload (only enabled when all questions answered)
            <Button
              onClick={handleNext}
              disabled={!allQuestionsAnswered}
              className="flex items-center gap-2 bg-[#347468] hover:bg-[#2a5d54] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
              <ChevronRight className="w-4 h-4" />
            </Button>
          )}
        </div>
        )}

      </div>
    </div>
  );
};

export default SelfReflectiveJournalRenderer;
