@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display&display=swap');
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');


.courseholder {
  margin-top: -40px;
  width: 100%;
  max-width: 55%;
  margin-right: 70px;
  margin-bottom: 40px;
  animation:chapterPopup 0.5s ease-in-out; 
  
    /* background-color: #703D3D; */
    /* background-color: red; */
    /* border-bottom-width: 0px; */
    
}


.courseName {
  width: 100%;
  /* background-color: rgb(255, 255, 255); */
  /* border-radius: 16px; */
  border: 2px solid rgba(52, 51, 48, 1);
  padding: 40px;
  padding-bottom: 0px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
  font-family: 'DM Sans', sans-serif;
  text-align: left;
  position: relative;
  /* display: flex; */
  background-color: #703D3D;
  /* background-color: #fff; */
  border-bottom-width: 0px;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  border-color: #703D3D;
  /* border-radius: 16px; */
  height: 80px;
}

.courseName-container {
  display: flex;
  justify-content: space-between;
  margin: -10px;
  width: 100%;
}

.courseName-text {
  width: 50%;
  color: #ffffff;
  font-weight: bold;
  font-size: 26px;
  font-family: 'DM Serif Display';
}

.chapterholder {
  margin-top: -40px;
  width: 100%;
  max-width: 55%;
  margin-right: 70px;
  margin-bottom: 40px;
  animation:chapterPopup 0.5s ease-in-out; 
    background-color: rgb(255, 255, 255);
}
@keyframes chapterPopup {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.chapterholder h2 {
  font-size: 1.7em;
  color: #343330;
  font-family: 'DM Serif Display', serif;
}

.chapter {
  width: 100%;
  background-color: rgb(255, 255, 255);
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  /* border-radius: 16px; */
  border: 2px solid rgba(52, 51, 48, 1);
  padding: 40px;
  padding-bottom: 0px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
  font-family: 'DM Sans', sans-serif;
  text-align: left;
  position: relative;
  border-color: #703D3D;
}

.chapter h3 {
  margin: 0;
  font-size: 1.4em;
  color: #2c3e50;
  font-weight: 500;
}

.chapter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.noChapters {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}

.noChapters h3 {
 justify-content: center;
}

.left-chapter {
  display: flex;
  justify-content: space-between;
}

.chapter-progress-bar {
  height: 7px;
  background-color: #A1BFB7;
  border-radius: 10px;
  overflow: visible; 
  width: 50%;
  position: relative;
  margin-top: 30px;
}

.progress {
  background-color: #14281D;
  height: 100%;
  display: flex;
  border-radius: 10px;
  align-items: center;
  justify-content: flex-end; 
  position: relative;
  width: 80%;
}

.progress-percentage {
  font-size: 0.8em;
  background-color: rgba(19, 109, 98, 1);
  border: 2px solid rgba(19, 109, 98, 1);
  color: #fff;
  position: absolute;
  right: -10px; 
  top: 50%;
  transform: translateY(-50%);
  padding: 2px 6px;
  border-radius: 10px;
  white-space: nowrap;
}

.course-level {
  display: flex;
  flex-direction: column;
  /* margin-top: 40px; */
  position: relative;
  overflow: visible;
   /* z-index: 1 !important; */
  /* opacity: 0; 
  transform: translateX(-100px); 
  animation: slideInFromLeft 0.8s ease forwards;   */
}

.course-level-locked {
  background-color: #703D3D33;
  height: 125px;
  border-color: #703D3D;
  border-radius: 20px;
  border-style: solid;
  z-index: 2;
  position: absolute;
  margin-top: 100px;
  margin-left: 55px;
}

.level {
  display: flex;
  overflow: visible;
  align-items: center;
  background-color: #BEF9FF;
  border-radius: 25px;
  border: 1px solid rgb(37, 126, 115);
  padding: 25px;
  margin-bottom: 80px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, background-color 0.3s ease;
  position: relative;
  z-index: 1 !important;
  cursor: pointer;
  
}

.level-buttons {
  display: flex;
  flex-direction: row;
  
  gap: 10px;
  /* position: absolute; */
  top: 5%;
  left:600px;
  z-index: 10;
  right: 10px;
  align-items: center;
  margin-bottom: 10px;
}

.level-buttons img {
  width: 40px;
}


.level-buttons1 {
  display: flex;
  flex-direction: row;
  
  gap: 10px;
  /* position: absolute; */
  top: 50%;
  left:600px;
  z-index: 10;
  right: 10px;
  align-items: center;
}

.level-buttons1 img {
  width: 40px;
}

.level-button {
  border-radius: 50%;
  padding: 10px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.level-button:first-child {
  background-color:#703D3D;
  ;  
  border: 2px solid #703D3D; 
}

.level-button:last-child {
  background-color: white; 
  border: 2px solid black; 
}

.level-button img {
  width: 100%;
  height: auto;
}

.level-button:hover {
  transform: scale(1.1);
}

.level.locked {
  cursor: not-allowed;
  opacity: 0.5;
  pointer-events: none;
}

.level.clickable:hover {
  transform: scale(1.02);
}

.level-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 55px;
  height: 55px;
  background-color: #95dad7;
  border-radius: 50%;
  margin-right: 15px;
  position: relative;
  background-repeat: no-repeat;
  background-size: cover; 
  background-size: 30px;
  background-position: center;
  background-repeat: no-repeat;
}

.level-info h4 {
  margin: 0;
  font-size: 1.5em;
  font-weight: 500;
  color: rgba(52, 51, 48, 1);
  width: 95%;
}

.level-info p {
  margin: 5px 0 0 0;
  color: rgba(52, 51, 48, 1);
  font-size: 1.2em;
  width: 95%;
}

.course-level {
  position: relative; 
}
.modal2 {
  position: absolute;
  background: linear-gradient(180deg, #703D3D 0%, #703D3D 100%);
  border-top-right-radius: 10px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  /* box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15); */
  box-shadow: 0 4px 6px #703D3D;
  padding: 15px;
  font-family: 'DM Sans', sans-serif;
  animation: dropdownFade 0.7s ease;
  right: 50px !important; 
  width: 322px !important; 
  box-sizing: border-box;
  z-index: 3 !important;
  /* background-color: #703D3D; */
}

.modal-content {
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  font-size: 0.8em;
  margin-bottom: 10px;
}

.modal-header-container {
  display: flex;
}

.modal-header-container img {
  margin-right: 10px;
}

.modal-content h4 {
  margin: 0;
  font-size: 1.2em;
  font-weight: 600;
}

.modal-content p {
  margin: 10px 0;
  font-size: 0.9em;
}

.modal-content button {
  background-color: #fff;
  color: rgba(52, 51, 48, 1);
  border: none;
  width: 100%;
  border-radius: 32px;
  padding: 6px 20px;
  cursor: pointer;
  font-family: 'DM Sans', sans-serif;
  font-size: 1.1em;
}

.modal-content button:hover {
  background-color: #f7f7f7;
}

.modal-body {
  left: 0px;
  margin-left: -150px;
}

.modal-arrow {
  position: absolute;
  top: -30px;
  left: -0.2px;
}

@keyframes dropdownFade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Adjustments */
/* While working in mobile will change this responsive  */

/* @media (max-width: 1378px) {
  .chapterholder {
    width: 100%;
    max-width: 65%;
    margin-right: 100px;
  }

  .chapter {
    padding: 40px;
    height: auto;
  }

  .chapterholder h2 {
    font-size: 2em;
  }

  .chapter h3 {
    font-size: 1.7em;
  }

  .chapter-progress-bar {
    height: 6px;
    width: 35%;
  }
  .chapter-header {
    display: block; 
  }

  .chapter-progress-bar {
    height: 6px;
    width: 100%;
    margin-top: 20px; 
  }
}

@media (max-width: 768px) {
  .chapterholder {
    width: 100%;
    max-width: 75%;
    margin-right: 60px;
  }

  .chapter {
    padding: 40px;
    height: auto;
  }

  .chapterholder h2 {
    font-size: 1.7em;
  }

  .chapter h3 {
    font-size: 1.5em;
  }

}

@media (max-width: 605px) {
  .chapterholder {
    width: 70%;
    max-width: 95%;
  }

  .chapter {
    padding: 40px;
    height: auto;
  }

  .chapterholder h2 {
    font-size: 1.7em;
  }

  .chapter h3 {
    font-size: 1.5em;
  }

  .chapter-header {
    display: block; 
  }

  .chapter-progress-bar {
    height: 6px;
    width: 100%;
    margin-top: 20px;
  }
}
@media (max-width: 458px) {
  .chapter h3{
    font-size:1.3em;
  }
  .level-info h4 {
    font-size: 1.2em;
  }
}

@media (max-width: 868px) {
  .modal2 {
    width: 200px !important;
  }

  .modal-content h4 {
    font-size: 1em;
  }

  .modal-content p {
    font-size: 0.8em;
  }

  .modal-content button {
    padding: 8px 16px;
    font-size: 0.8em;
  }
}

@media (max-width: 648px) {
  .modal2 {
    padding: 10px;
  }

  .modal-content h4 {
    font-size: 0.9em;
  }

  .modal-content p {
    font-size: 0.7em;
  }

  .modal-content button {
    padding: 6px 12px;
    font-size: 0.7em;
  }
} */
