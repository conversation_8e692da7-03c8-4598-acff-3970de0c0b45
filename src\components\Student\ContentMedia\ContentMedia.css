@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');

.content-media {
    background-color: #FEFAE0;
    width: 100%;
    height: 100%;
}
/* Global styles */
.content-media-container body {
    font-family: 'DM Sans', sans-serif;
    background-color: #FEFAE0;

    color: #333;
    /* margin: 0; */
    padding: 0;
    box-sizing: border-box;
}

/* Container and wrapper styles */
.content-media-container {
    /* margin-top: 50px; */
    max-width: 100%;
    /* margin: 50px auto; */
    text-align: center;
    position: relative;
}

.content-media-back-button {
    color: black;
    font-family: 'DM Sans', sans-serif;
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 20px;
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    align-items: center;
}

.back-button-text {
    display: inline;
    color: rgba(52, 51, 48, 1);
    font-size: x-large;
    margin-left: 8px; /* Adjust spacing between icon and text */
}

.back-button-icon {
    height: 20px;
}

/* Content heading styling */
.content-media-heading {
    font-size: 24px;
    font-weight: 700;
    font-family: 'DM Sans', sans-serif;
    color: rgba(52, 51, 48, 1);
    /* margin-bottom: 30px; */
}

/* Content wrapper to align video and notes */
.content-media-wrapper {
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

/* Video container styling */
.content-media-video-container {
    position: relative;
    /* width: 65%; */
    border: 1px solid rgba(52, 51, 48, 1);
    border-radius: 24px;
    overflow: visible;
    background: rgba(255, 245, 239, 1);
    z-index: 1;
    margin-top: 50px;
    margin-bottom: 70px;
}

.content-media-video-container iframe {
    height: 350px;
    /* width: 100%; */
    border: none;
    /* height: 300px; */
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
    width: 100%;
}

.content-media-video-caption {
    padding: 25px;
    font-weight: 500;
    font-size: 18px;
}

/* Add to My Space Button Styling */
.content-media-add-to-space-button {
    position: absolute;
    bottom: -15px; /* Adjust this value to move the button vertically */
    right: 30px; /* Adjust this value to move the button horizontally */
    background: rgba(19, 109, 98, 1);
    color: #fff;
    padding: 8px 16px;
    border: none;
    border-radius: 31px;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    transition: background 0.3s ease-in-out, transform 0.2s ease-in-out;
    z-index: 2;
}

.content-media-icon {
    width: 20px; /* Adjust the size as needed */
    height: 20px;
    margin-right: 8px;
}

.content-media-button-text {
    display: inline-block;
}

.content-media-add-to-space-button:hover {
    background: rgba(19, 109, 98, 0.8);
}

/* Notes container styling */
.content-media-notes-container {
    width: 30%;
    height: 355px;
    border: 1px solid rgba(0, 0, 0, 0.42);
    border-radius: 20px;
    background-color: #ffffff;
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* Ensures the save button is placed at the bottom */
}
.content-media-notes-incontainer{
   overflow:auto;
}
/* Ensure the editor fills the entire container */
.content-media-notes-container .sun-editor {
    flex-grow: 1;
    border: none !important;
}

.content-media-notes-container .sun-editor .se-container {
    height: calc(100% - 60px); /* Adjust based on the height of the save button */
    border: none !important;
    background-color: #ffffff; /* Adjust as needed */
}

.content-media-notes-container .sun-editor .se-toolbar {
    background-color: #ffffff; /* Adjust as needed */
    border-radius: 20px;
}

.content-media-save-button {
    align-self: flex-end; /* Aligns the save button to the bottom right */
    background: rgba(19, 109, 98, 1);
    color: #fff;
    padding: 8px 20px;
    border: none;
    border-radius: 31px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s ease-in-out, transform 0.2s ease-in-out;
    margin-top: 10px;
}
/* Footer styling */
.content-media-footer {
    background: rgba(251, 251, 251, 1);
  
    box-shadow: 0px -4px 4px rgba(19, 109, 98, 0.1);
    padding: 20px;
    display: flex;
    justify-content: flex-end;
    position: relative;
    margin-top: -50;
}


.content-media-continue-button {
    margin-right: 60px;
    background: #703D3D;
    color: #fff;
    padding: 12px 28px;
    border: none;
    border-radius: 31px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s ease-in-out, transform 0.2s ease-in-out;
}

.content-media-continue-button:hover {
    background: #703D3D;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .content-media-back-button {
        position: static; /* Reset positioning on small screens */
        margin-bottom: 15px;
    }

    .back-button-text {
        display: none; /* Hide the text on smaller screens */
    }

    .back-button-icon {
        height: 24px; /* Adjust size as needed */
        margin-right: 0; /* Remove margin */
    }

    .content-media-wrapper {
        flex-direction: column;
        align-items: center;
    }

    .content-media-video-container,
    .content-media-notes-container {
        width: 90%;
        margin-bottom: 20px;
    }

    .content-media-add-to-space-button {
        padding: 8px;
        right: 10px;
    }

    .content-media-icon {
        margin-right: 0;
    }

    .content-media-button-text {
        display: none; 
    }

    .content-media-heading {
        font-size: 20px;
    }
}

.content-media-container .sun-editor .se-toolbar .se-btn,
.content-media-container .sun-editor .se-toolbar .se-btn-group {
    border: none !important;
    box-shadow: none !important;
}

/* Remove border from the SunEditor's container */
.content-media-container .sun-editor {
    border: none !important;
}

/* Adjust the toolbar's background color if needed */
.content-media-container .sun-editor .se-toolbar {
    background-color: #ffffff; /* Adjust as needed */
    border-radius: 20px;
}

/* Optional: Adjust the background color of the editor area */
.content-media-container .sun-editor .se-container {
    background-color: #ffffff; /* Adjust as needed */
}
