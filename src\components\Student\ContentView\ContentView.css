@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');

.maincontentview {
width: 100%;
height: 100%;
background-color: #FEFAE0;
}

.contentview * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
   
}
  
.contentview {
    display: flex;
    font-family: 'DM Sans', sans-serif;
    flex-direction: row;
    overflow: hidden;
}
  

  
.back-button:hover {
    color: #f39c12;
}
  
.contentview .contentContainer {
    margin-left: 250px;
    display: flex;
    flex-direction: row;
    height: 100%;
    overflow: hidden;
    width: 100%;
}
  
.contentview .contentSidebar {
    position: fixed;
    top: 110px;
    left: 20px;
    width: 220px;
    /* height: calc(100vh - 60px); */
    padding: 20px;
    background-color: #fff;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
    z-index: 100;
    border:1px solid;
    border-color:#703D3D;
    justify-content: center;
    text-align: center;
    border-radius: 24px;
    /* margin-left: ; */
}

.contentview .contentSidebar::-webkit-scrollbar {
    width: 0;
    background: transparent;
}
.contentview .contentSidebar {
    scrollbar-width: none;
}

.contentview .contentSidebar ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}
  
.contentview .contentSidebar li {
    cursor: pointer;
    margin-bottom: 10px;
    color: #000;
    transition: transform 0.3s ease, color 0.3s ease;
    text-align: left;
}
  
.contentSidebar {
    height: 500px;
}

.contentSideBar-container {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
}

.contentSidebar-header {
    display: flex;
    text-align: left;
}

.contentSidebar-header-text {
    color: #703D3D;
    margin-left: 5px;
}

.contentSidebar-header-icon {
    margin-left: auto;
}

.contentview .contentSidebar .contentTopic:hover {
    transform: scale(1.05);
}
  
.contentview .contentSidebar .contentChapter {
    font-weight: bold;
    color: rgba(19, 109, 98, 1);
    margin-bottom: 5px;
    font-size: 22px;
   
}
  
.contentview .contentSidebar .contentChapter.active {
    color: #f39c12;
}
  
.contentview .contentSidebar .contentTopic {
    
    color: #000;
    font-size: 18px;
}
  
.contentview .contentSidebar .contentTopic.selected {
    color: #f39c12;
}
  
.contentview .contents {
    flex: 1;
    padding: 20px 20px 20px 20px;
    overflow-y: auto;
    height: calc(100vh - 60px);
    position: relative;
    text-align: left;
    box-sizing: border-box;
    width: 100%;
    height: 72vh;
    overflow-y: scroll;
}


.contents-topicName {
    margin-bottom: 40px;
}

.contentview .contents::-webkit-scrollbar {
    width: 0;
    background: transparent;
}
.contentview .contents {
    scrollbar-width: none;
}
  
.contentview .contents h1 {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
}
  
.contentview .contents h2,
.contentview .contents p {
    margin: 0;
}
  
.contentview .contents h2 {
    color: rgba(19, 109, 98, 1);
    margin-bottom: 10px;
}
  
.contentview .contents p {
    margin-bottom: 20px;
    color: #495057;
}
  
.contentview .next-button {
    border: none;
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 24px;
    background-color:#703D3D;
  
    border-radius: 40px;
    color: white;

   
    font-size: 18px;
    cursor: pointer;
    z-index: 1000;
}
  
.contentview .next-button:hover {
    background-color: #703D3D
}

.contentview .note-button {
    border: none;
    position: fixed;
    bottom: 20px;
    right: 150px;
    padding: 12px 24px;
    background-color:#703D3D;
  
    border-radius: 40px;
    color: white;

   
    font-size: 18px;
    cursor: pointer;
    z-index: 1000;
}
  
.contentview .note-button:hover {
    background-color: #703D3D
}
#backButton {
    display: none;
}

.content-media-video {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
}

.content-media-video-container {
    width: 640px;
    height: 70%;
}

.content-media-video-container iframe {
    width: 640px;
    height: 100%;
    justify-content: center;
}

@media (min-width: 990px) and (max-width: 1101px) {
    .contentContainer, .contents {
        max-width: 100%;
        padding: 20px;
    }
    
    .next-button {
        position: relative;
        z-index: 10;
    }
  
    html, body {
        overflow-x: hidden;
    }
}

@media (max-width: 768px) {
    .contentview .contentContainer {
        margin-left: 0;
        flex-direction: column;
    }
  
    .contentview .contentSidebar {
        display: none;
        width: 100%;
        height: auto;
        border-right: none;
    }
  
    .contentview .contents {
        width: 100%;
        padding: 10px;
    }
  
    .contentview .contents .chapter-title {
        font-size: 20px;
    }
  
    .contentview .contents h2 {
        font-size: 20px;
    }
  
    .contentview .contents p {
        font-size: 16px;
    }
  
    .contentview .next-button {
        width: auto;
    }
  
    #backButton {
        display: block;
        top: 80px;
        left: 20px;
        font-size: 17px;
        font-family: 'DM Sans', sans-serif;
        font-weight: bold;
        color: #000;
        background: none;
        border: none;
        cursor: pointer;
        z-index: 101;
    }
}
  
@media (max-width: 480px) {
    .contentview .contentSidebar {
        display: none;
        width: 100%;
        height: auto;
    }
  
    .contentview .contents {
        width: 100%;
        padding: 10px;
    }
  
    .contentview .contents h1 {
        font-size: 18px;
    }
  
    .contentview .contents h2 {
        font-size: 16px;
    }
  
    .contentview .contents p {
        font-size: 14px;
    }
  
    .contentview .next-button {
        width: auto;
    }
  
    #backButton {
        font-size: 15px;
    }
}

@media (max-width: 370px) {
    .contentview .contents {
        padding: 8px;
    }
  
    .contentview .contents h1 {
        font-size: 16px;
    }
  
    .contentview .contents h2 {
        font-size: 14px;
    }
  
    .contentview .contents p {
        font-size: 12px;
    }
  
    .contentview .next-button {
        padding: 10px 20px;
        font-size: 16px;
    }
  
    #backButton {
        font-size: 13px;
    }
}

.content-media-notes-container {
    width: 90%;
}

.chapterName {
    background-color: #703D3D;
    width: 100%;
    color: #FFFFFF;
    border-radius: 6px;
    height: 30px;
    display: flex;
    
    /* align-content: center;
    justify-content: center; */
}
.chapterName-text {
 
    align-content: center;
}

.contentSidebar .chapterIconCotainer {
    margin-right: 10px;
    align-content: center;
     height:100%;
     margin-left: 18px;
 
}

.contentSidebar .chapterIcon {
    margin-top: 5px;
}


.chapterName {
    align-content: center;
}

.contentContainer .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .back-button {
    
    top: 80px;
    left: 20px;
    font-size: 20px;
    font-family: 'DM Sans', sans-serif;
    font-weight: bold;
    color: #000;
    background: none;
    border: none;
    cursor: pointer;
    z-index: 101;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }


  .quiz-back-button {
    font-size: 20px;
    color: #555;
    font-family: 'DM Sans', sans-serif;
    font-weight: bold;
    color: #000;
    background: none;
    border: none;
    cursor: pointer;
  }
  
  .quiz-name {
    flex: 1;
    text-align: center;
    font-size: 20px;
    color: #333;
  }
  
  .timer {
    font-size: 20px;
    color: #555;
    font-weight: bold;
  }

/* Note overlay */
.note-modal, .notedetail-modal {
    position: fixed;
    top: 100px;
    right: 15px;
    width: 300px;
    height: 70%;
    background-color: #f9f9f9;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.3);
    padding: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    border-radius: 20px;
}

.editor-container {
    width: 100%;
    overflow-y: scroll;
    height: 85%;
}

.noteModalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.headerLeft {
    display: flex;
    align-items: center;
}

.headerIcon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
}

.closeButton {
    background: none;
    border: none;
    cursor: pointer;
}

.noteList {
    list-style: none;
    padding: 0;
    width: 100%;
    overflow-y: scroll;
    height: 65%;
}

.noteItem {
    margin-bottom: 15px;
    background-color: #733533;
    border-radius: 10px;
    padding: 10px;
    padding-left: 20px;
    display: flex;
    align-items: center;
    color: white;
    cursor: pointer;
}

.noteContent {
    display: flex;
    align-items: center; 
}

.noteIcon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

.addNoteButton {
    background-color: #733533;
    color: white;
    width: 90%;
    padding: 15px;
    border-radius: 20px;
    border: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    bottom: 20px; 
    cursor: pointer;
}
.addNoteButton-text {
    font-size: 18px;
}

.note-button-container {
    display: flex;
}

.notescancelButton {
    background-color: #733533;
    color: white;
    padding: 12px;
    padding-left: 25px;
    padding-right: 25px;
    font-size: large;
    border-radius: 60px;
    border: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    bottom: 20px; 
    right: 130px;
    cursor: pointer;
}

.saveButton {
    background-color: #733533;
    color: white;
    padding: 12px;
    padding-left: 25px;
    padding-right: 25px;
    font-size: large;
    border-radius: 60px;
    border: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    bottom: 20px; 
    right: 20px;
    cursor: pointer;
}

.plusIcon {
    font-size: 18px;
}

.noteDetails {
    margin-top: 20px;
    background-color: #f4f4f4;
    padding: 15px;
    border-radius: 10px;
}

  /* Overlay */
.maincontentview-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.maincontentview-header-button {
    margin-left: 24px;
    margin-top: 10px;
}

.maincontentview-chapter-name {
    align-content: center;
    font-weight: 500;
}

  .maincontentview .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  /* Modal Content */
  .maincontentview .modal-content-quiz {
    background-color: #FFFFFF;
    padding: 20px;
    border-radius: 12px;
    width: 400px;
    text-align: center;
    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.2);
    animation: fadeIn 0.3s ease-in-out;
  }

.modal-content-quiz input {
    height: 20px;
    width: 90%;
    margin-bottom: 10px;
}

#nextbutton {
    display: flex;
    justify-content: flex-end;
}
  
  h4 {
    font-size: 22px;
    margin-bottom: 20px;
  }

  .fail-message-bookMark {
    font-size: 16px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 600;
  }

  .fail-message- {
    font-size: 16px;
    color: #333;
    margin-bottom: 20px;
  }


    .maincontentview  .button-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .maincontentview .align-right {
    text-align: right;
    }

.modal-overlay .takeQuiz-button{
    background-color:   #703D3D;
    color: #FFFFFF; 
    border-radius: 32px;
  
    /* display: block; */
    /* margin: 0 auto; */
    font-weight: bold;
    border: solid;
    width: 140px;
    height: 40px;
    margin: 5px;
}

.modal-overlay  .cancel-button{

    background-color:  #FFFFFF;
    color: #703D3D; 
    border-radius: 32px;
    
    /* display: block; */
    /* margin: 0 auto; */
    font-weight: bold;
    border: solid;
    width: 140px;
    height: 40px;
    margin: 5px;
  }

  .star-container {
    width: 25%;
  }

  .star {
    width: 16px;
    height: 16px;
    background-color: #703D3D;
    margin-left: 4px;
    clip-path: polygon(
      50% 0%,
      61% 35%,
      98% 35%,
      68% 57%,
      79% 91%,
      50% 70%,
      21% 91%,
      32% 57%,
      2% 35%,
      39% 35%
    );
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    opacity: 10px;
}


  .star-dull {
    margin-left: 4px;
    width: 16px;
    height: 16px;
    background-color: #C0C0C0;
    clip-path: polygon(
      50% 0%,
      61% 35%,
      98% 35%,
      68% 57%,
      79% 91%,
      50% 70%,
      21% 91%,
      32% 57%,
      2% 35%,
      39% 35%
    );
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    opacity: 10px;
    
  }

.sidebarTopicName-container {
    margin-top: 10px;
}

.sidebarTopicName {
    cursor: pointer;
    margin-bottom: 10px;
    color: #000;
    transition: transform 0.3s ease, color 0.3s ease;
    text-align: left;
    display: flex;
    width: 100%;
    overflow-y: scroll;
}

.sidebarTopicName-text {
    margin-left: 18px;
    width: 75%;
    word-wrap: break-word;
}