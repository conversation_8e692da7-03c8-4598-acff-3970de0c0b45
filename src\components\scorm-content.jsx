import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON><PERSON>ircle, Loader2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

// Helper function to set a value in a nested object based on a dot-notation path
const setNestedValue = (obj, path, value) => {
  const keys = path.split(".");
  const newObj = JSON.parse(JSON.stringify(obj)); // Deep copy to avoid mutation
  let current = newObj;
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    const nextKey = keys[i + 1];
    if (current[key] === undefined || typeof current[key] !== "object") {
      if (!isNaN(parseInt(nextKey, 10))) {
        current[key] = [];
      } else {
        current[key] = {};
      }
    }
    current = current[key];
  }
  current[keys[keys.length - 1]] = value;
  return newObj;
};

// Helper function to get a value from a nested object based on a dot-notation path
const getNestedValue = (obj, path) => {
  return path.split(".").reduce((acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined), obj);
};


const ScormPlayer = ({
  scormUrl,
  version = "1.2", // or '2004'
  onInitialized,
  onTerminated,
  onError,
}) => {
  const iframeRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [scormData, setScormData] = useState({});

  // Initialize SCORM API
  useEffect(() => {
    let scormAPI = null;

    const initializeScormAPI = () => {
      const LMSInitialize = () => {
        console.log("LMS Initialize called");
        setScormData(prevData => setNestedValue(prevData, 'cmi.completion_status', 'incomplete'));
        if (onInitialized) onInitialized();
        return "true";
      };

      const LMSFinish = () => {
        console.log("LMS Finish called. Final SCORM data:");
        if (Object.keys(scormData).length === 0) {
            console.warn("SCORM data is empty. This might indicate that the SCORM content did not communicate with the player. This could be due to a cross-origin issue if the content is hosted on a different domain.");
        } else {
            console.log(JSON.stringify(scormData, null, 2));
        }
        if (onTerminated) onTerminated();
        return "true";
      };

      const LMSGetValue = (parameter) => {
        console.log("LMS GetValue called with:", parameter);
        const value = getNestedValue(scormData, parameter);
        console.log(`> Returning value for ${parameter}:`, value);
        return value !== undefined ? value : "";
      };

      const LMSSetValue = (parameter, value) => {
        console.log("LMS SetValue called with:", parameter, value);
        setScormData(prevData => setNestedValue(prevData, parameter, value));
        return "true";
      };

      const LMSCommit = () => {
        console.log("LMS Commit called. Current SCORM data snapshot:");
        console.log(JSON.stringify(scormData, null, 2));
        // In the future, you would trigger a save to the backend here.
        return "true";
      };

      const LMSGetLastError = () => "0";
      const LMSGetErrorString = () => "No error";
      const LMSGetDiagnostic = () => "No diagnostic information";

      // Create SCORM API Instance for SCORM 1.2
      window.API = {
        LMSInitialize,
        LMSFinish,
        LMSGetValue,
        LMSSetValue,
        LMSCommit,
        LMSGetLastError,
        LMSGetErrorString,
        LMSGetDiagnostic,
      };

      // Create SCORM API Instance for SCORM 2004
      window.API_1484_11 = {
        Initialize: LMSInitialize,
        Terminate: LMSFinish,
        GetValue: LMSGetValue,
        SetValue: LMSSetValue,
        Commit: LMSCommit,
        GetLastError: LMSGetLastError,
        GetErrorString: LMSGetErrorString,
        GetDiagnostic: LMSGetDiagnostic,
      };
      
      scormAPI = window.API;
    };

    initializeScormAPI();

    // Cleanup
    return () => {
      if (scormAPI && scormAPI.LMSFinish) {
        scormAPI.LMSFinish();
      }
      delete window.API;
      delete window.API_1484_11;
    };
  }, [onInitialized, onTerminated]);

  // Handle iframe load events
  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  const handleIframeError = (err) => {
    setError("Failed to load SCORM content");
    setIsLoading(false);
    if (onError) onError(err);
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="relative w-full h-full min-h-[600px] rounded-lg overflow-hidden border">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      )}
      <iframe
        ref={iframeRef}
        src={scormUrl}
        className="w-full h-full border-none min-h-[400px] md:min-h-[600px]"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        title="SCORM Content"
        allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
        sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
      />
    </div>
  );
};

// Usage Example Component
const ScormLesson = ({ url }) => {
  const handleScormInitialized = () => {
    console.log("SCORM content initialized in parent component.");
  };

  const handleScormTerminated = () => {
    console.log("SCORM content terminated in parent component.");
  };

  const handleError = (error) => {
    console.error("SCORM error in parent component:", error);
  };

  return (
    <div className="w-full h-full min-h-[600px] ">
      <ScormPlayer
        scormUrl={url}
        onInitialized={handleScormInitialized}
        onTerminated={handleScormTerminated}
        onError={handleError}
      />
    </div>
  );
};

export default ScormLesson;
