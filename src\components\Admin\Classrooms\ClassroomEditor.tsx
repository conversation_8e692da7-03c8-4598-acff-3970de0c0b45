import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft, Save, Users, Loader2, School, BookOpen, Info } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "@/hooks/use-toast";
import { ClassCreationRequest, Classroom } from './types';
import { useAuth0 } from "@auth0/auth0-react";
import {
    useCreateClassroomMutation,
    useUpdateClassroomMutation,
    useGetClassroomByIdQuery,
    useGetSchoolsQuery,
    useGetAdminCoursesQuery
} from '@/APIConnect';

interface FormState extends ClassCreationRequest {
    name: string;
    description: string;
    courseId: string;
    schoolId: string;
    teacherIds: string[];
    studentIds: string[];
    createdOn: string;
}

interface FormErrors {
    name?: string;
    courseId?: string;
    schoolId?: string;
}

const ClassroomEditor = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const isEditMode = !!id;
    const { user } = useAuth0();

    // Safe access to user properties with type checking
    const roleId = user?.["http://learnido-app/roleId"] as string | undefined;
    const schoolId = user?.["http://learnido-app/schoolId"] ?? "cb9f8588-5c23-46a8-85b0-96ba343801b5";
    const isSchoolAdmin = roleId === "2";

    const { data: classroomData, isLoading: isLoadingClassroom } = useGetClassroomByIdQuery(id, { skip: !id });
    const { data: schools = [] } = useGetSchoolsQuery({});
    const { data: courses = [] } = useGetAdminCoursesQuery({});
    const [createClassroom, { isLoading: isCreating }] = useCreateClassroomMutation();
    const [updateClassroom, { isLoading: isUpdating }] = useUpdateClassroomMutation();

    const [formData, setFormData] = useState<FormState>({
        name: '',
        description: '',
        courseId: '',
        schoolId: isSchoolAdmin ? schoolId || '' : '',
        teacherIds: [],
        studentIds: [],
        createdOn: new Date().toISOString()
    });

    const [errors, setErrors] = useState<FormErrors>({});

    useEffect(() => {
        if (isSchoolAdmin) {
            setFormData((prev) => ({ ...prev, schoolId: schoolId || '' }));
        }
    }, [isSchoolAdmin, schoolId]);

    useEffect(() => {
        if (classroomData) {
            // For SchoolAdmin, verify they can only edit their school's classrooms
            if (isSchoolAdmin && classroomData.schoolId !== schoolId) {
                toast({
                    title: "Access Denied",
                    description: "You can only edit classrooms from your school",
                    variant: "destructive",
                });
                navigate(isSchoolAdmin ? '/school-admin/classrooms' : '/admin/classrooms');
                return;
            }

            setFormData({
                name: classroomData.name,
                description: classroomData.description,
                courseId: classroomData.courseId,
                schoolId: classroomData.schoolId,
                teacherIds: classroomData.teacherIds || [],
                studentIds: classroomData.studentIds || [],
                createdOn: classroomData.createdOn
            });
        }
    }, [classroomData, isSchoolAdmin, schoolId, navigate]);

    const validateForm = (): boolean => {
        const newErrors: FormErrors = {};
        let isValid = true;

        if (!formData.name.trim()) {
            newErrors.name = "Classroom name is required";
            isValid = false;
        }

        if (!formData.courseId) {
            newErrors.courseId = "Course is required";
            isValid = false;
        }

        if (!formData.schoolId) {
            newErrors.schoolId = "School is required";
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!validateForm()) {
            toast({
                title: "Please fix the errors",
                description: "Some required fields are missing or invalid",
                variant: "destructive",
            });
            return;
        }

        try {
            if (isEditMode) {
                await updateClassroom({ id, ...formData }).unwrap();
                toast({
                    title: "Classroom updated successfully",
                    variant: "default",
                });
            } else {
                await createClassroom(formData).unwrap();
                toast({
                    title: "Classroom created successfully",
                    variant: "default",
                });
            }
            navigate(isSchoolAdmin ? '/school-admin/classrooms' : '/admin/classrooms');
        } catch (error) {
            console.error('Error saving classroom:', error);
            toast({
                title: "Failed to save classroom",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const isLoading = isLoadingClassroom || isCreating || isUpdating;
    const filteredCourses = courses.resultObject ?? [];

    const LoadingSkeleton = () => (
        <div className="min-h-screen bg-gray-50 animate-pulse">
            <div className="sticky top-0 z-10 bg-white border-b">
                <div className="container mx-auto px-4 py-4">
                    <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                </div>
            </div>
            <div className="container mx-auto px-4 py-6">
                <Card>
                    <CardContent className="p-6 space-y-6">
                        {[1, 2, 3, 4].map((i) => (
                            <div key={i} className="space-y-2">
                                <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                                <div className="h-10 bg-gray-200 rounded"></div>
                            </div>
                        ))}
                    </CardContent>
                </Card>
            </div>
        </div>
    );

    if (isEditMode && isLoadingClassroom) {
        return <LoadingSkeleton />;
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="sticky top-0 z-10 bg-white border-b">
                <div className="container mx-auto px-4 py-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => navigate(isSchoolAdmin ? '/school-admin/classrooms' : '/admin/classrooms')}
                                disabled={isLoading}
                            >
                                <ArrowLeft className="h-5 w-5" />
                            </Button>
                            <h1 className="text-2xl font-bold flex items-center">
                                <Users className="h-6 w-6 mr-2" />
                                {isEditMode ? 'Edit Classroom' : 'Create Classroom'}
                            </h1>
                        </div>
                        <Button
                            onClick={handleSubmit}
                            className="flex items-center"
                            disabled={isLoading}
                        >
                            {isLoading ? (
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            ) : (
                                <Save className="h-4 w-4 mr-2" />
                            )}
                            {isEditMode ? 'Update' : 'Create'}
                        </Button>
                    </div>
                </div>
            </div>

            <div className="container mx-auto px-4 py-6">
                <Card className="transition-all duration-200 hover:shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-xl flex items-center gap-2">
                            <School className="h-5 w-5 text-primary" />
                            Classroom Details
                        </CardTitle>
                        <CardDescription>
                            Fill in the details to {isEditMode ? 'update' : 'create'} your classroom
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="p-6">
                        <motion.div
                            className="space-y-6"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3 }}
                        >
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Label>Classroom Name</Label>
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger>
                                                <Info className="h-4 w-4 text-gray-400" />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>Enter a unique name for your classroom</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <motion.div
                                    initial={false}
                                    animate={{ scale: errors.name ? [1, 1.02, 1] : 1 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    <Input
                                        value={formData.name}
                                        onChange={(e) => {
                                            setFormData((prev) => ({ ...prev, name: e.target.value }));
                                            if (errors.name) {
                                                setErrors((prev) => ({ ...prev, name: undefined }));
                                            }
                                        }}
                                        placeholder="Enter classroom name..."
                                        className={errors.name ? "border-red-500" : ""}
                                        disabled={isLoading}
                                    />
                                </motion.div>
                                {errors.name && (
                                    <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Label>Description</Label>
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger>
                                                <Info className="h-4 w-4 text-gray-400" />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>Provide details about the classroom's purpose and content</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <Textarea
                                    value={formData.description}
                                    onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                                    placeholder="Enter classroom description..."
                                    className="h-32"
                                    disabled={isLoading}
                                />
                            </div>

                            {!isSchoolAdmin && (
                                <div className="space-y-2">
                                    <div className="flex items-center gap-2">
                                        <Label>School</Label>
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger>
                                                    <Info className="h-4 w-4 text-gray-400" />
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Select the school this classroom belongs to</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    </div>
                                    <Select
                                        value={formData.schoolId}
                                        onValueChange={(value) => {
                                            setFormData((prev) => ({ ...prev, schoolId: value }));
                                            if (errors.schoolId) {
                                                setErrors((prev) => ({ ...prev, schoolId: undefined }));
                                            }
                                        }}
                                        disabled={isLoading || isSchoolAdmin}
                                    >
                                        <SelectTrigger className={errors.schoolId ? "border-red-500" : ""}>
                                            <SelectValue placeholder="Select a school" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {schools.resultObject?.map((school: any) => (
                                                <SelectItem key={school.id} value={school.id}>
                                                    {school.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.schoolId && (
                                        <p className="text-sm text-red-500 mt-1">{errors.schoolId}</p>
                                    )}
                                </div>
                            )}

                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Label>Course</Label>
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger>
                                                <Info className="h-4 w-4 text-gray-400" />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>Select the course to be taught in this classroom</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <Select
                                    value={formData.courseId}
                                    onValueChange={(value) => {
                                        setFormData((prev) => ({ ...prev, courseId: value }));
                                        if (errors.courseId) {
                                            setErrors((prev) => ({ ...prev, courseId: undefined }));
                                        }
                                    }}
                                    disabled={isLoading}
                                >
                                    <SelectTrigger className={errors.courseId ? "border-red-500" : ""}>
                                        <SelectValue placeholder="Select a course" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {filteredCourses.map((course: any) => (
                                            <SelectItem key={course.id} value={course.id}>
                                                {course.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.courseId && (
                                    <p className="text-sm text-red-500 mt-1">{errors.courseId}</p>
                                )}
                            </div>
                        </motion.div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default ClassroomEditor;
