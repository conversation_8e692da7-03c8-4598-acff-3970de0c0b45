// import React, { useEffect } from "react"
// import {useLazyGetTestDataQuery} from 'APIConnect'
// import { useDispatch , useSelector } from "react-redux"
// import {setTestData} from 'reducers/testSlice'

//  const Test = () => {
// const dispatch = useDispatch()
// const testData = useSelector((state) => state.test.testData)

//  const [getTestData, ] = useLazyGetTestDataQuery()

//   useEffect(() => {
//    // fetchAPI()
//    },[])

//    const fetchAPI = async() => {
//     const {data}  = await getTestData()
//     dispatch(setTestData(data))
//    }
//    console.log('testData',testData)
//     return (
//     <div >
//       <p>
//        App is Live
//       </p>
      
//   </div>
//     )
//  }

//  export default Test



// import React, { useState } from 'react';
// import { DndProvider, useDrag, useDrop } from 'react-dnd';
// import { HTML5Backend } from 'react-dnd-html5-backend';

// // Draggable Item type
// const ItemType = 'ANSWER';

// // Answer Component (Draggable)
// const DraggableAnswer = ({ answer, index, moveAnswer }) => {
//   const [{ isDragging }, drag] = useDrag({
//     type: ItemType,
//     item: { index },
//     collect: (monitor) => ({
//       isDragging: !!monitor.isDragging(),
//     }),
//   });

//   return (
//     <div
//       ref={drag}
//       style={{
//         padding: '10px',
//         margin: '5px',
//         backgroundColor: isDragging ? '#ccc' : '#fff',
//         border: '1px solid black',
//         cursor: 'move',
//       }}
//     >
//       {answer}
//     </div>
//   );
// };

// // Answer Drop Area
// const AnswerSlot = ({ answer, index, moveAnswer }) => {
//   const [, drop] = useDrop({
//     accept: ItemType,
//     hover: (draggedItem) => {
//       if (draggedItem.index !== index) {
//         moveAnswer(draggedItem.index, index);
//         draggedItem.index = index; // Update dragged item index
//       }
//     },
//   });

//   return (
//     <div
//       ref={drop}
//       style={{
//         padding: '10px',
//         margin: '5px',
//         border: '1px dashed black',
//         minHeight: '40px',
//         backgroundColor: '#f7f7f7',
//       }}
//     >
//       {answer || 'Drop here'}
//     </div>
//   );
// };

// const MatchTheFollowing = () => {
//   // Questions and Answers
//   const questions = ['Question 1', 'Question 2', 'Question 3', 'Question 4'];
//   const initialAnswers = ['Answer 1', 'Answer 2', 'Answer 3', 'Answer 4'];
  
//   // State for tracking answer positions
//   const [answers, setAnswers] = useState(initialAnswers);

//   // Move answer to new position
//   const moveAnswer = (fromIndex, toIndex) => {
//     const updatedAnswers = [...answers];
//     const [movedAnswer] = updatedAnswers.splice(fromIndex, 1);
//     updatedAnswers.splice(toIndex, 0, movedAnswer);
//     setAnswers(updatedAnswers);
//   };

//   return (
//     <DndProvider backend={HTML5Backend}>
//       <div style={{ display: 'flex', justifyContent: 'space-between' }}>
//         {/* Left side: Questions */}
//         <div>
//           <h3>Questions</h3>
//           {questions.map((question, index) => (
//             <div key={index} style={{ padding: '10px', margin: '5px' }}>
//               {question}
//             </div>
//           ))}
//         </div>

//         {/* Right side: Answers (Draggable and Droppable) */}
//         <div>
//           <h3>Answers</h3>
//           <DraggableAnswer />
//           {answers.map((answer, index) => (
//             <AnswerSlot
//               key={index}
//               answer={answer}
//               index={index}
//               moveAnswer={moveAnswer}
//             />
//           ))}
//         </div>
//       </div>
//     </DndProvider>
//   );
// };

// export default MatchTheFollowing;


import React, { useState } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

const ItemType = 'ANSWER';

const DraggableAnswer = ({ answer, index }) => {
  const [{ isDragging }, drag] = useDrag({
    type: ItemType,
    item: { index },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  });

  return (
    <div
      ref={drag}
      style={{
        padding: '10px',
        margin: '5px',
        backgroundColor: isDragging ? '#ccc' : '#fff',
        border: '1px solid black',
        cursor: 'move',
      }}
    >
      {answer}
    </div>
  );
};

const AnswerSlot = ({ answer, index, moveAnswer }) => {
  const [, drop] = useDrop({
    accept: ItemType,
    hover: (draggedItem) => {
      if (draggedItem.index !== index) {
        moveAnswer(draggedItem.index, index);
        draggedItem.index = index;
      }
    },
  });

  return (
    <div
      ref={drop}
      style={{
        padding: '10px',
        margin: '5px',
        border: '1px dashed black',
        minHeight: '40px',
        backgroundColor: '#f7f7f7',
      }}
    >
      {answer ? (
        <DraggableAnswer answer={answer} index={index} />
      ) : (
        'Drop here'
      )}
    </div>
  );
};

const MatchTheFollowing = () => {
  const questions = ['Question 1', 'Question 2', 'Question 3', 'Question 4'];
  const initialAnswers = ['Answer 1', 'Answer 2', 'Answer 3', 'Answer 4'];

  const [answers, setAnswers] = useState(initialAnswers);

  const moveAnswer = (fromIndex, toIndex) => {
    const updatedAnswers = [...answers];
    const [movedAnswer] = updatedAnswers.splice(fromIndex, 1);
    updatedAnswers.splice(toIndex, 0, movedAnswer);
    setAnswers(updatedAnswers);
  };


  return (
    <DndProvider backend={HTML5Backend}>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <h3>Questions</h3>
          {questions.map((question, index) => (
            <div key={index} style={{ padding: '10px', margin: '5px' }}>
              {question}
            </div>
          ))}
        </div>

        <div>
          <h3>Answers</h3>
          {answers.map((answer, index) => (
            <AnswerSlot
              key={index}
              answer={answer}
              index={index}
              moveAnswer={moveAnswer}
            />
          ))}
        </div>
      </div>
    </DndProvider>
  );
};

export default MatchTheFollowing;
