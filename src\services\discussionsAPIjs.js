import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

// You can adjust this based on your environment configuration
const baseUrl = process.env.REACT_APP_API_BASE_URL || "https://ldfapi.stmin.dev/api";

/**
 * @typedef {Object} Discussion
 * @property {string} id
 * @property {string} title
 * @property {string} description
 * @property {string} courseId
 * @property {string} classId
 * @property {string} topicId
 * @property {string} createdBy
 * @property {string} createdAt - ISO date string
 * @property {number} commentCount
 * @property {number} voteCount
 */

/**
 * @typedef {Object} ToneAnalysis
 * @property {number} curious
 * @property {number} critical
 * @property {number} analytical
 */

/**
 * @typedef {Object} EvaluationFeedback
 * @property {string} [engagement]
 * @property {string} [depthOfThought]
 * @property {string} [relevance]
 * @property {string} [peerInteraction]
 * @property {string} [evidence]
 * @property {string} [overall]
 */

/**
 * @typedef {Object} Evaluation
 * @property {number} engagement - Score 0-10
 * @property {number} depthOfThought - Score 0-10
 * @property {number} relevance - Score 0-10
 * @property {number} peerInteraction - Score 0-10
 * @property {number} evidence - Score 0-10
 * @property {EvaluationFeedback} [feedback]
 */

/**
 * @typedef {Object} Comment
 * @property {string} id
 * @property {string} content
 * @property {string} discussionId
 * @property {string} authorId
 * @property {string} authorName
 * @property {string} authorPictureUrl
 * @property {('PENDING'|'APPROVED'|'REJECTED')} status
 * @property {number} [aiScore]
 * @property {number} [sentimentScore]
 * @property {number} [accuracyScore]
 * @property {ToneAnalysis} [toneAnalysis]
 * @property {string} [language]
 * @property {string[]} [flaggedTerms]
 * @property {string} createdAt - ISO date string
 * @property {number} voteCount
 * @property {Evaluation} [evaluation]
 */

/**
 * @typedef {Object} LeaderboardEntry
 * @property {string} userId
 * @property {string} timeRange
 * @property {number} score
 * @property {number} [rank]
 * @property {string} updatedAt - ISO date string
 */

/**
 * @typedef {Object} Pagination
 * @property {number} total
 * @property {number} page
 * @property {number} limit
 * @property {number} pages
 */

// Custom fetch base query with auth header
const customFetchBaseQuery = fetchBaseQuery({
  baseUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().user.userToken;
    if (token) {
      headers.set("authorization", `Bearer ${token}`);
    }
    return headers;
  },
});

export const discussionsAPI = createApi({
  reducerPath: "discussionsAPI",
  baseQuery: customFetchBaseQuery,
  tagTypes: ["Discussion", "Comment", "Moderation", "Leaderboard", "System"],
  endpoints: (builder) => ({
    // System endpoints
    getSystemHealth: builder.query({
      query: () => ({
        url: "/health",
        method: "GET",
      }),
      providesTags: ["System"],
    }),

    // Discussions endpoints
    createDiscussion: builder.mutation({
      query: (params) => ({
        url: "/discussions",
        method: "POST",
        body: params,
      }),
      invalidatesTags: (result) => [
        { type: "Discussion", id: "LIST" },
        { type: "Discussion", id: result?.id },
      ],
    }),

    getDiscussions: builder.query({
      query: (params) => ({
        url: "/discussions",
        method: "GET",
        params: {
          page: params?.page || 1,
          limit: params?.limit || 20,
          courseId: params?.courseId,
          classId: params?.classId,
          topicId: params?.topicId,
          searchQuery: params?.searchQuery,
          filter: params?.filter,
          sort: params?.sort,
          following: params?.following,
        },
      }),
      providesTags: (result) =>
        result
          ? [
              { type: "Discussion", id: "LIST" },
              ...result.items.map((discussion) => ({
                type: "Discussion",
                id: discussion.id,
              })),
            ]
          : [{ type: "Discussion", id: "LIST" }],
      transformResponse: (response) => {
        // Ensure we have a consistent response format
        return {
          items: response.items || [],
          pagination: response.pagination || {
            total: 0,
            page: 1,
            limit: 20,
            pages: 0,
          },
        };
      },
    }),

    getDiscussionById: builder.query({
      query: (id) => ({
        url: `/discussions/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Discussion", id }],
    }),

    updateDiscussion: builder.mutation({
      query: ({ id, ...params }) => ({
        url: `/discussions/${id}`,
        method: "PUT",
        body: params,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Discussion", id: "LIST" },
        { type: "Discussion", id },
      ],
    }),

    deleteDiscussion: builder.mutation({
      query: (id) => ({
        url: `/discussions/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, id) => [
        { type: "Discussion", id: "LIST" },
        { type: "Discussion", id },
      ],
    }),

    // Comments endpoints
    createComment: builder.mutation({
      query: (params) => ({
        url: "/comments",
        method: "POST",
        body: params,
      }),
      invalidatesTags: (result) => [
        { type: "Comment", id: result?.discussionId },
        { type: "Discussion", id: result?.discussionId },
        "Moderation",
      ],
    }),

    getDiscussionComments: builder.query({
      query: ({ discussionId, page = 1, limit = 20 }) => ({
        url: `/comments/discussion/${discussionId}`,
        method: "GET",
        params: { page, limit },
      }),
      providesTags: (result, error, { discussionId }) => [
        { type: "Comment", id: discussionId },
      ],
      transformResponse: (response) => {
        // Ensure we have a consistent response format
        return {
          items: response.items || [],
          pagination: response.pagination || {
            total: 0,
            page: 1,
            limit: 20,
            pages: 0,
          },
        };
      },
    }),

    updateComment: builder.mutation({
      query: ({ id, ...params }) => ({
        url: `/comments/${id}`,
        method: "PUT",
        body: params,
      }),
      invalidatesTags: (result) => [
        { type: "Comment", id: result?.id },
        { type: "Comment", id: result?.discussionId },
        { type: "Discussion", id: result?.discussionId },
        "Moderation",
      ],
    }),

    voteComment: builder.mutation({
      query: ({ id, value }) => ({
        url: `/comments/${id}/vote`,
        method: "PUT",
        body: { value },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Comment", id },
        { type: "Discussion", id: result?.discussionId },
      ],
    }),

    deleteComment: builder.mutation({
      query: (id) => ({
        url: `/comments/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (result) => [
        { type: "Comment", id: result?.id },
        { type: "Comment", id: result?.discussionId },
        { type: "Discussion", id: result?.discussionId },
        "Moderation",
      ],
    }),

    // Moderation endpoints
    getPendingModeration: builder.query({
      query: ({ page = 1, limit = 20 }) => ({
        url: "/moderation/pending",
        method: "GET",
        params: { page, limit },
      }),
      providesTags: ["Moderation"],
      transformResponse: (response) => {
        // Ensure we have a consistent response format
        return {
          moderationItems: response.items || [],
          pagination: response.pagination || {
            total: 0,
            page: 1,
            limit: 20,
            pages: 0,
          },
        };
      },
    }),

    getModerationStats: builder.query({
      query: () => ({
        url: "/moderation/stats",
        method: "GET",
      }),
      providesTags: ["Moderation"],
    }),

    approveComment: builder.mutation({
      query: (id) => ({
        url: `/moderation/${id}/approve`,
        method: "PUT",
      }),
      invalidatesTags: (result) => [
        "Moderation",
        { type: "Comment", id: result?.id },
        { type: "Comment", id: result?.discussionId },
        { type: "Discussion", id: result?.discussionId },
      ],
    }),

    rejectComment: builder.mutation({
      query: (id) => ({
        url: `/moderation/${id}/reject`,
        method: "PUT",
      }),
      invalidatesTags: (result) => [
        "Moderation",
        { type: "Comment", id: result?.id },
        { type: "Comment", id: result?.discussionId },
        { type: "Discussion", id: result?.discussionId },
      ],
    }),

    // Leaderboard endpoints
    getLeaderboard: builder.query({
      query: (timeRange = "weekly") => ({
        url: "/leaderboard",
        method: "GET",
        params: { timeRange },
      }),
      providesTags: ["Leaderboard"],
    }),

    updateLeaderboard: builder.mutation({
      query: (timeRange = "weekly") => ({
        url: "/leaderboard/update",
        method: "POST",
        body: { timeRange },
      }),
      invalidatesTags: ["Leaderboard"],
    }),

    getUserLeaderboardStats: builder.query({
      query: (timeRange = "weekly") => ({
        url: "/leaderboard/user",
        method: "GET",
        params: { timeRange },
      }),
      providesTags: ["Leaderboard"],
    }),

    followDiscussion: builder.mutation({
      query: (id) => ({
        url: `/discussions/${id}/follow`,
        method: "PUT",
      }),
      invalidatesTags: (result, error, id) => [
        { type: "Discussion", id },
        { type: "Discussion", id: "LIST" },
      ],
    }),

    unfollowDiscussion: builder.mutation({
      query: (id) => ({
        url: `/discussions/${id}/unfollow`,
        method: "PUT",
      }),
      invalidatesTags: (result, error, id) => [
        { type: "Discussion", id },
        { type: "Discussion", id: "LIST" },
      ],
    }),
  }),
});

export const {
  // System hooks
  useGetSystemHealthQuery,
  
  // Discussions hooks
  useCreateDiscussionMutation,
  useGetDiscussionsQuery,
  useGetDiscussionByIdQuery,
  useUpdateDiscussionMutation,
  useDeleteDiscussionMutation,
  useFollowDiscussionMutation,
  useUnfollowDiscussionMutation,
  
  // Comments hooks
  useCreateCommentMutation,
  useGetDiscussionCommentsQuery,
  useUpdateCommentMutation,
  useVoteCommentMutation,
  useDeleteCommentMutation,
  
  // Moderation hooks
  useGetPendingModerationQuery,
  useGetModerationStatsQuery,
  useApproveCommentMutation,
  useRejectCommentMutation,
  
  // Leaderboard hooks
  useGetLeaderboardQuery,
  useUpdateLeaderboardMutation,
  useGetUserLeaderboardStatsQuery,
} = discussionsAPI;

export default discussionsAPI;