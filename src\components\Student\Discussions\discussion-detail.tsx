import { <PERSON><PERSON><PERSON><PERSON>, ThumbsUp, ThumbsDown, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { CommentForm } from './comment-form'
import { Discussion, Comment } from './types'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { formatDistanceToNow } from 'date-fns'
import { useState } from 'react'
import { useVoteCommentMutation } from '@/services/discussionsAPIjs'
import { toast } from '@/hooks/use-toast'
import { Skeleton } from '@/components/ui/skeleton'
import { Progress } from '@/components/ui/progress'
import { useAuth0 } from '@auth0/auth0-react'

interface DiscussionDetailProps {
  discussion: Discussion
  comments: Comment[]
  onBack: () => void
  onCommentSuccess: () => void
}

export function DiscussionDetail({
  discussion,
  comments,
  onBack,
  onCommentSuccess,
}: DiscussionDetailProps) {
  const [voteComment] = useVoteCommentMutation()
  const { user } = useAuth0()
  const [votingIds, setVotingIds] = useState<Set<string>>(new Set())
  
  // Get user role from Auth0
  const roleId = user?.["http://learnido-app/roleId"] || '4'
  const canSeeMetrics = ['1', '2', '3'].includes(roleId) // Admin, SchoolAdmin, Teacher

  const handleVote = async (commentId: string, value: 1 | -1) => {
    if (votingIds.has(commentId)) return
    
    setVotingIds(prev => new Set(prev).add(commentId))
    
    try {
      await voteComment({ id: commentId, value }).unwrap()
      toast({
        title: value > 0 ? 'Upvoted' : 'Downvoted',
        description: 'Your vote has been recorded',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to vote on comment',
        variant: 'destructive',
      })
    } finally {
      // Remove the ID from votingIds after a short delay to prevent double clicks
      setTimeout(() => {
        setVotingIds(prev => {
          const newSet = new Set(prev)
          newSet.delete(commentId)
          return newSet
        })
      }, 1000)
    }
  }

  const getInitials = (name = '') => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }

  return (
    <div className="space-y-6">
      <Button
        variant="ghost"
        onClick={onBack}
        className="mb-4"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Discussions
      </Button>

      <Card className="p-6 mb-6">
        <div className="flex justify-between items-start mb-4">
          <h2 className="text-2xl font-bold">{discussion.title}</h2>
          {discussion.tags && discussion.tags.length > 0 && (
            <div className="flex gap-2">
              {discussion.tags.map(tag => (
                <Badge key={tag} variant="outline">{tag}</Badge>
              ))}
            </div>
          )}
        </div>
        <p className="text-muted-foreground whitespace-pre-wrap mb-4">
          {discussion.description}
        </p>
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            {formatDistanceToNow(new Date(discussion.createdAt), { addSuffix: true })}
          </div>
          <div className="flex items-center gap-1">
            <ThumbsUp className="w-4 h-4" />
            <span>{discussion.voteCount || 0}</span>
          </div>
        </div>
      </Card>

      <div className="space-y-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Add Your Response</h3>
          <CommentForm
            discussionId={discussion.id}
            onSuccess={onCommentSuccess}
          />
        </Card>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">
            Responses ({comments?.length || 0})
          </h3>
          {comments?.length === 0 ? (
            <Card className="p-6 text-center">
              <p className="text-muted-foreground">Be the first to respond to this discussion!</p>
            </Card>
          ) : (
            comments?.map((comment) => (
              <Card key={comment.id} className="p-6">
                <div className="flex justify-between items-start">
                  <div className="flex items-start gap-3">
                    <Avatar className="h-8 w-8">
                      {comment.authorPictureUrl ? (
                        <AvatarImage src={comment.authorPictureUrl} alt={comment.authorName ?? ""} />
                      ) : (
                        <AvatarFallback>{getInitials(comment.authorName ?? "Unknown user")}</AvatarFallback>
                      )}
                    </Avatar>
                    <div>
                      <div className="font-medium">{comment.authorName || 'Anonymous'}</div>
                      <p className="mt-2 text-sm whitespace-pre-wrap">{comment.content}</p>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <div className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center mt-4 pt-3 border-t">
                  <div className="flex items-center gap-2">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleVote(comment.id, 1)}
                      disabled={votingIds.has(comment.id)}
                      className="text-muted-foreground hover:text-primary"
                    >
                      <ThumbsUp className="w-4 h-4 mr-1" />
                      <span>{comment.voteCount > 0 ? comment.voteCount : ''}</span>
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleVote(comment.id, -1)}
                      disabled={votingIds.has(comment.id)}
                      className="text-muted-foreground hover:text-destructive"
                    >
                      <ThumbsDown className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  {/* Show evaluation metrics only to teachers and admins */}
                  {canSeeMetrics && comment.evaluation && (
                    <div className="flex flex-wrap gap-x-4 gap-y-2 text-xs">
                      {comment.evaluation.engagement && (
                        <div className="flex items-center gap-1">
                          <span>Engagement:</span>
                          <Badge variant={comment.evaluation.engagement >= 7 ? 'default' : 'secondary'}>
                            {comment.evaluation.engagement}/10
                          </Badge>
                        </div>
                      )}
                      {comment.evaluation.depthOfThought && (
                        <div className="flex items-center gap-1">
                          <span>Depth:</span>
                          <Badge variant={comment.evaluation.depthOfThought >= 7 ? 'default' : 'secondary'}>
                            {comment.evaluation.depthOfThought}/10
                          </Badge>
                        </div>
                      )}
                      {comment.evaluation.relevance && (
                        <div className="flex items-center gap-1">
                          <span>Relevance:</span>
                          <Progress 
                            value={comment.evaluation.relevance * 10} 
                            className="h-1 w-16 inline-block"
                          />
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  )
}