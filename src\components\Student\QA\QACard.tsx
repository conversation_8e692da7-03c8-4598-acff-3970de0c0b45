import { format } from "date-fns";
import { motion } from "framer-motion";
import {
    MessageCircle,
    CheckCircle2,
    HelpCircle,
    Calendar,
    Clock,
} from "lucide-react";

interface Reply {
    answerText: string;
    teacherName: string;
}

interface QA {
    id: string;
    queryText: string;
    createdOn: string;
    updatedon: string;
    studentName: string;
    isAnswered: boolean;
    reply?: Reply;
}

interface QACardProps {
    qa: QA;
}

export const QACard = ({ qa }: QACardProps) => {
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            whileHover={{ scale: 1.01 }}
            transition={{ duration: 0.2 }}
            className="bg-white rounded-lg shadow-sm p-6 mb-4 hover:shadow-md transition-all border border-gray-100"
        >
            <div className="flex justify-between items-start mb-4">
                <div className="flex items-start space-x-3 flex-1">
                    <motion.div
                        className="mt-1"
                        whileHover={{ rotate: 15 }}
                        transition={{ duration: 0.2 }}
                    >
                        <div className="bg-primary/10 p-2 rounded-full">
                            <HelpCircle className="h-5 w-5 text-primary" />
                        </div>
                    </motion.div>
                    <div className="flex-1">
                        <h3 className="font-semibold text-lg flex items-center">
                            Question
                        </h3>
                        <div className="flex items-center text-sm text-gray-500 mt-2">
                            Asked by: {qa.studentName}
                        </div>
                        <p className="text-gray-700 mt-1 text-base leading-relaxed">
                            {qa.queryText}
                        </p>

                        <div className="flex items-center text-sm text-gray-500 mt-2">
                            <Calendar className="h-4 w-4 mr-1" />
                            {format(new Date(qa.createdOn), "MMM d, yyyy")}
                        </div>
                    </div>
                </div>
            </div>

            {qa.reply && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="mt-4 pt-4 border-t border-dashed"
                >
                    <div className="flex items-start space-x-3">
                        <motion.div
                            className="mt-1"
                            whileHover={{ rotate: -15 }}
                            transition={{ duration: 0.2 }}
                        >
                            <div className="bg-green-50 p-2 rounded-full">
                                <MessageCircle className="h-5 w-5 text-green-500" />
                            </div>
                        </motion.div>
                        <div className="flex-1">
                            <h4 className="font-semibold text-md mb-2 flex items-center">
                                Answer
                                <motion.div
                                    whileHover={{ scale: 1.2 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    <CheckCircle2 className="h-4 w-4 ml-2 text-green-500" />
                                </motion.div>
                            </h4>
                            <p className="text-gray-700 text-base leading-relaxed">
                                {qa.reply.answerText}
                            </p>
                            <div className="flex items-center text-sm text-gray-500 mt-2">
                                Answered by: {qa.reply.teacherName}
                            </div>
                            <div className="mt-2 text-sm text-gray-500 flex items-center">
                                <Calendar className="h-4 w-4 mr-1" />
                                Answered on {format(new Date(qa.updatedon), "MMM d, yyyy")}
                            </div>
                        </div>
                    </div>
                </motion.div>
            )}

            {!qa.reply && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="mt-4 pt-4 border-t border-dashed"
                >
                    <div className="bg-yellow-50 rounded-lg p-4">
                        <p className="text-yellow-600 text-sm flex items-center">
                            <motion.div
                                animate={{ rotate: 360 }}
                                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                            >
                                <Clock className="h-4 w-4 mr-2" />
                            </motion.div>
                            Waiting for answer... We'll notify you when your question is
                            answered! 🔔
                        </p>
                    </div>
                </motion.div>
            )}
        </motion.div>
    );
};
