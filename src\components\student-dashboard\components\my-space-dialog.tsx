import React, { useState, useMemo, useEffect } from 'react';
import { Plus, Trash, FolderPlus, Upload, Search, Download, Calendar, Image, FileText, Film, Music, File } from 'lucide-react';
// Remove old file upload hook import
// import { useFileUpload } from '@/lib/utils';
import { FileFolderSDK } from '@/lib/FileFolderSdk';
import { Progress } from '@/components/ui/progress';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import SearchInput from './common/search-input';
import { cn } from '@/lib/utils';

interface Document {
  id: string;
  name: string;
  folder: string;
  size: string;
  type: string;
  createdAt: Date;
  url?: string;
  previewUrl?: string;
}

const FilePreview = ({ file }: { file: File }) => {
  const [preview, setPreview] = useState<string | null>(null);

  React.useEffect(() => {
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
    return () => setPreview(null);
  }, [file]);

  const getFileIcon = () => {
    if (file.type.startsWith('image/')) return <Image className="w-8 h-8" />;
    if (file.type.startsWith('video/')) return <Film className="w-8 h-8" />;
    if (file.type.startsWith('audio/')) return <Music className="w-8 h-8" />;
    if (file.type.includes('pdf')) return <FileText className="w-8 h-8" />;
    return <File className="w-8 h-8" />;
  };

  return (
    <div className="relative group">
      {file.type.startsWith('image/') && preview ? (
        <img
          src={preview}
          alt={file.name}
          className="w-full h-32 object-cover rounded-lg"
        />
      ) : (
        <div className="w-full h-32 bg-muted/30 rounded-lg flex items-center justify-center">
          {getFileIcon()}
        </div>
      )}
      <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
        <p className="text-white text-sm truncate max-w-[90%] px-2">{file.name}</p>
      </div>
    </div>
  );
};

const MySpaceDialog = (
  { iconOnly = false }: { iconOnly?: boolean }
) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [folders, setFolders] = useState<string[]>(['General']);
  const [newFolder, setNewFolder] = useState('');
  const [selectedFolder, setSelectedFolder] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const isMobile = window.innerWidth < 768;
  // New progress & uploading state
  const [progress, setProgress] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  // Create SDK instance
  const fileFolderSDK = useMemo(() => new FileFolderSDK(), []);

  // Load folder list on mount
  useEffect(() => {
    fileFolderSDK.listFilesAndDirectories()
      .then(data => {
        // Parse the file list to populate documents
        const docs: Document[] = data.fileAndDirectoryList.reduce((acc: Document[], item: string) => {
          if (item.includes('.') && item.includes('/')) {
            const parts = item.split('/');
            const folder = parts[0];
            const fileName = parts.slice(1).join('/');
            acc.push({
              id: item,
              name: fileName,
              folder,
              size: "Unknown",
              type: fileName.split('.').pop() || "Unknown",
              createdAt: new Date(),
              url: ""
            });
          } else if (item.includes('.') && !item.includes('/')) {
            acc.push({
              id: item,
              name: item,
              folder: "General",
              size: "Unknown",
              type: item.split('.').pop() || "Unknown",
              createdAt: new Date(),
              url: ""
            });
          }
          return acc;
        }, [] as Document[]);
        setDocuments(docs);
        setFolders(data.directories || ['General']);
      })
      .catch(err => console.error(err));
  }, [fileFolderSDK]);

  const addFolder = async () => {
    if (newFolder && !folders.includes(newFolder)) {
      try {
        await fileFolderSDK.createDirectory(newFolder);
        setFolders(prev => [...prev, newFolder]);
        setNewFolder('');
      } catch (error) {
        console.error('Failed to create folder:', error);
      }
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      setSelectedFiles(Array.from(files));
      setUploading(true);

      try {
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const uniquePrefix = Date.now();
          const folderPrefix = (selectedFolder !== 'All' && selectedFolder !== 'General') ? `${selectedFolder}/` : '';
          const fileName = `${folderPrefix}${uniquePrefix}-${file.name}`;

          const url = await fileFolderSDK.uploadFile(file, fileName, (prog) => setProgress(prog));

          const newDoc: Document = {
            id: fileName, // Use the full fileName as ID to match server structure
            name: fileName.includes('/') ? fileName.split('/').slice(1).join('/') : fileName, // Extract just filename part
            folder: selectedFolder === 'All' ? 'General' : selectedFolder,
            size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
            type: file.type || 'Unknown',
            createdAt: new Date(),
            url: url || '' // Ensure url is never undefined
          };
          setDocuments(prev => [newDoc, ...prev]);
        }
      } catch (error) {
        console.error('Upload failed:', error);
      } finally {
        setUploading(false);
        setSelectedFiles([]);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }
    }
  };

  const handleDownload = async (doc: Document) => {
    try {
      let downloadUrl = '';

      if (doc.url && doc.url.trim() !== '') {
        // If document has a valid URL, use it directly
        downloadUrl = doc.url;
        console.log('Using stored URL:', downloadUrl);
      } else {
        // If no URL, construct it based on the actual upload pattern
        // Pattern: https://nexostorage.blob.core.windows.net/learnido-file/userId/fileName
        console.log('URL missing, attempting to construct download URL for:', doc.name);

        // Get user ID from localStorage or auth
        let userId = '';

        // Helper function to decode JWT token
        const decodeJWT = (token: string) => {
          try {
            const base64Url = token.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
              return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));
            return JSON.parse(jsonPayload);
          } catch (e) {
            console.log('Could not decode JWT token:', e);
            return null;
          }
        };

        // Try multiple sources for user ID
        const userToken = localStorage.getItem('JWTToken');
        const idToken = localStorage.getItem('IdToken');

        // Try JWTToken first
        if (userToken) {
          try {
            let tokenData;
            if (userToken.startsWith('eyJ')) {
              // It's a JWT token, decode it
              tokenData = decodeJWT(userToken);
            } else {
              // It's JSON, parse it
              tokenData = JSON.parse(userToken);
            }
            console.log('JWT Token data:', tokenData);
            if (tokenData) {
              userId = tokenData.sub || tokenData.userId || tokenData.user_id || '';
              // Remove 'auth0|' prefix if present
              if (userId.startsWith('auth0|')) {
                userId = userId.replace('auth0|', '');
              }
              // Convert to lowercase to match Azure storage convention
              userId = userId.toLowerCase();
            }
          } catch (e) {
            console.log('Could not process JWT token:', e);
          }
        }

        // If still no userId, try IdToken
        if (!userId && idToken) {
          try {
            let tokenData;
            if (idToken.startsWith('eyJ')) {
              // It's a JWT token, decode it
              tokenData = decodeJWT(idToken);
            } else {
              // It's JSON, parse it
              tokenData = JSON.parse(idToken);
            }
            console.log('ID Token data:', tokenData);
            if (tokenData) {
              userId = tokenData.sub || tokenData.userId || tokenData.user_id || '';
              if (userId.startsWith('auth0|')) {
                userId = userId.replace('auth0|', '');
              }
              // Convert to lowercase to match Azure storage convention
              userId = userId.toLowerCase();
            }
          } catch (e) {
            console.log('Could not process ID token:', e);
          }
        }

        // If still no userId, try to extract from existing documents
        if (!userId) {
          // Look for other documents that might have URLs with user IDs
          const docsWithUrls = documents.filter(d => d.url && d.url.includes('learnido-file/'));
          if (docsWithUrls.length > 0 && docsWithUrls[0].url) {
            const urlMatch = docsWithUrls[0].url.match(/learnido-file\/([^\/]+)\//);
            if (urlMatch && urlMatch[1]) {
              userId = urlMatch[1];
              console.log('Extracted userId from existing document URL:', userId);
            }
          }
        }

        // Final fallback - use a common pattern or skip download
        if (!userId) {
          console.log('No user ID found, cannot construct download URL');
          alert('Cannot download file: User ID not found. Please try logging out and back in.');
          return;
        }

        console.log('Final userId for download:', userId);

        // Try to get the download URL from the server (same way as upload)
        try {
          console.log('Attempting to get download URL from server for:', doc.name);

          // Get the auth token (try multiple sources)
          const azureToken = localStorage.getItem('azureAccessToken') ||
                           localStorage.getItem('JWTToken') ||
                           localStorage.getItem('IdToken') || '';

          console.log('Using auth token for server request:', azureToken ? 'Token found' : 'No token');

          const response = await fetch(`https://nexo-file.azurewebsites.net/api/UploadFile`, {
            method: "POST",
            headers: {
              "X-ZUMO-AUTH": azureToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ fileName: doc.name }),
          });

          if (response.ok) {
            const data = await response.json();
            console.log('Server response for file URL:', data);
            if (data.getUrl) {
              downloadUrl = data.getUrl;
              console.log('Using server-provided URL:', downloadUrl);
            } else {
              throw new Error('No getUrl in server response');
            }
          } else {
            throw new Error(`Server response: ${response.status}`);
          }
        } catch (serverError) {
          console.log('Server URL fetch failed, falling back to constructed URL:', serverError);
          // Fallback to constructed URL - try multiple case variations
          const possibleUrls = [
            `https://nexostorage.blob.core.windows.net/learnido-file/${userId}/${doc.name}`,
            `https://nexostorage.blob.core.windows.net/learnido-file/${userId.toUpperCase()}/${doc.name}`,
            `https://nexostorage.blob.core.windows.net/learnido-file/${userId.toLowerCase()}/${doc.name}`
          ];

          console.log('Trying multiple URL variations:', possibleUrls);

          // Try each URL until one works
          let urlFound = false;
          for (const testUrl of possibleUrls) {
            try {
              console.log('Testing URL:', testUrl);
              const testResponse = await fetch(testUrl, { method: 'HEAD' }); // Just check if exists
              if (testResponse.ok) {
                downloadUrl = testUrl;
                console.log('Found working URL:', downloadUrl);
                urlFound = true;
                break;
              }
            } catch (e) {
              console.log('URL test failed:', testUrl);
            }
          }

          if (!urlFound) {
            // Use the first URL as fallback
            downloadUrl = possibleUrls[0];
            console.log('No working URL found, using fallback:', downloadUrl);
          }
        }
      }

      // Force download by fetching the file and creating a blob
      console.log('Attempting to download from:', downloadUrl);

      const response = await fetch(downloadUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      // Create download link
      const link = document.createElement('a');
      link.href = url;
      link.download = doc.name.includes('/') ? doc.name.split('/').pop() || doc.name : doc.name; // Get just the filename
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('File downloaded successfully:', doc.name);
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try again or check if the file exists.');
    }
  };

  const deleteDocument = async (id: string) => {
    try {
      // Find the document to delete
      const docToDelete = documents.find(doc => doc.id === id);
      if (!docToDelete) {
        console.error('Document not found:', id);
        return;
      }



      // Extract the file path from the URL
      // The URL format is typically like: https://nexofile.blob.core.windows.net/files/filename.ext
      const url = docToDelete.url;
      if (!url || url.trim() === '') {
        // If URL is missing, use the document ID which contains the correct file path
        const fileName = docToDelete.name;
        if (fileName) {

          // Get user ID using the same logic as download
          let userId = '';
          const idToken = localStorage.getItem('IdToken');

          if (idToken) {
            try {
              const decodeJWT = (token: string) => {
                try {
                  const base64Url = token.split('.')[1];
                  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                  const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                  }).join(''));
                  return JSON.parse(jsonPayload);
                } catch (e) {
                  return null;
                }
              };

              let tokenData;
              if (idToken.startsWith('eyJ')) {
                tokenData = decodeJWT(idToken);
              } else {
                tokenData = JSON.parse(idToken);
              }

              if (tokenData) {
                userId = tokenData.sub || tokenData.userId || tokenData.user_id || '';
                if (userId.startsWith('auth0|')) {
                  userId = userId.replace('auth0|', '');
                }
                userId = userId.toLowerCase();
              }
            } catch (e) {
              console.log('Could not get user ID for delete:', e);
            }
          }

          // Use the document ID which contains the full path from listFilesAndDirectories
          const fullPath = docToDelete.id;

          try {
            await fileFolderSDK.deleteFile(fullPath);
            setDocuments(documents.filter((doc) => doc.id !== id));
          } catch (error) {
            // Try with just the filename as fallback
            try {
              await fileFolderSDK.deleteFile(fileName);
              setDocuments(documents.filter((doc) => doc.id !== id));
            } catch (fallbackError) {
              // Still remove from UI for better UX
              setDocuments(documents.filter((doc) => doc.id !== id));
            }
          }
          return;
        }
        // If all else fails, just remove from UI
        setDocuments(documents.filter((doc) => doc.id !== id));
        return;
      }

      const filePathMatch = url.match(/\/files\/(.+)$/);

      if (!filePathMatch || !filePathMatch[1]) {
        // Try using the document name as fallback
        const fileName = docToDelete.name;
        if (fileName) {

          // Get user ID for this fallback too
          let userId = '';
          const idToken = localStorage.getItem('IdToken');

          if (idToken) {
            try {
              const decodeJWT = (token: string) => {
                try {
                  const base64Url = token.split('.')[1];
                  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                  const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                  }).join(''));
                  return JSON.parse(jsonPayload);
                } catch (e) {
                  return null;
                }
              };

              let tokenData;
              if (idToken.startsWith('eyJ')) {
                tokenData = decodeJWT(idToken);
              } else {
                tokenData = JSON.parse(idToken);
              }

              if (tokenData) {
                userId = tokenData.sub || tokenData.userId || tokenData.user_id || '';
                if (userId.startsWith('auth0|')) {
                  userId = userId.replace('auth0|', '');
                }
                userId = userId.toLowerCase();
              }
            } catch (e) {
              console.log('Could not get user ID for fallback delete:', e);
            }
          }

          // Use the document ID which contains the full path from listFilesAndDirectories
          const fullPath = docToDelete.id;
          try {
            await fileFolderSDK.deleteFile(fullPath);
            setDocuments(documents.filter((doc) => doc.id !== id));
          } catch (error) {
            // Try with just the filename as fallback
            try {
              await fileFolderSDK.deleteFile(fileName);
              setDocuments(documents.filter((doc) => doc.id !== id));
            } catch (fallbackError) {
              // Still remove from UI for better UX
              setDocuments(documents.filter((doc) => doc.id !== id));
            }
          }
          return;
        }
        // If all else fails, just remove from UI
        setDocuments(documents.filter((doc) => doc.id !== id));
        return;
      }

      const filePath = decodeURIComponent(filePathMatch[1]);

      try {
        // Call the API to delete the file
        await fileFolderSDK.deleteFile(filePath);
        setDocuments(documents.filter((doc) => doc.id !== id));
      } catch (error) {
        // Try using document name as final fallback
        try {
          await fileFolderSDK.deleteFile(docToDelete.name);
          setDocuments(documents.filter((doc) => doc.id !== id));
        } catch (finalError) {
          // Still remove from UI for better UX
          setDocuments(documents.filter((doc) => doc.id !== id));
        }
      }
    } catch (error) {
      // Still remove from UI even if API call fails, for better UX
      setDocuments(documents.filter((doc) => doc.id !== id));
    }
  };

  const filteredDocuments = useMemo(() => {
    let filtered = documents;
    if (selectedFolder !== 'All') {
      filtered = filtered.filter((doc) => doc.folder === selectedFolder);
    }
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        doc =>
          doc.name.toLowerCase().includes(query) ||
          doc.type.toLowerCase().includes(query)
      );
    }
    return filtered;
  }, [documents, selectedFolder, searchQuery]);

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" className="w-full justify-start">
          📁 {iconOnly ? "" : "My Space"}
        </Button>
      </SheetTrigger>
      <SheetContent
        side="right"
        className={cn(
          "p-0",
          isMobile ? "w-full h-[85vh]" : "w-[400px] sm:w-[540px]"
        )}
      >
        <SheetHeader className="p-6 pb-0">
          <SheetTitle className="flex items-center text-2xl font-bold">
            📁 My Space
          </SheetTitle>
        </SheetHeader>
        <div className="p-6 space-y-6">
          <div className="space-y-4 bg-muted/30 p-4 rounded-2xl relative">
            <div className="flex items-center gap-2">
              <Input
                placeholder="New Folder"
                value={newFolder}
                onChange={(e) => setNewFolder(e.target.value)}
                className="rounded-xl border-muted"
              />
              <Button onClick={addFolder} size="icon" className="rounded-xl">
                <FolderPlus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex justify-center">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileUpload}
                className="hidden"
                multiple
              />
              <Button
                onClick={() => fileInputRef.current?.click()}
                className="w-full rounded-xl"
                disabled={uploading}
              >
                <Upload className="w-4 h-4 mr-2" />
                {uploading ? 'Uploading...' : 'Upload Files'}
              </Button>
              {uploading && (
                <Progress value={progress} className="w-full mt-2" />
              )}
              {selectedFiles.length > 0 && (
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {selectedFiles.map((file, index) => (
                    <FilePreview key={index} file={file} />
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex gap-4">
              <div className="w-1/3">
                <Select value={selectedFolder} onValueChange={setSelectedFolder}>
                  <SelectTrigger className="rounded-xl border-muted">
                    <SelectValue placeholder="Filter by folder" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Folders</SelectItem>
                    {folders.map((folder) => (
                      <SelectItem key={folder} value={folder}>
                        {folder}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex-1">
                <SearchInput
                  value={searchQuery}
                  onChange={setSearchQuery}
                  placeholder="Search files..."
                />
              </div>
            </div>

            <ScrollArea className={cn("pr-4", isMobile ? "h-[45vh]" : "h-[600px]")}>
              {filteredDocuments.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-[200px] text-muted-foreground">
                  <Search className="w-8 h-8 mb-2" />
                  <p>No files found</p>
                </div>
              ) : (
                filteredDocuments.map((doc) => (
                  <Card key={doc.id} className="mb-4 rounded-2xl border-none bg-muted/30 hover:bg-muted/50 transition-colors">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <div className="flex items-center gap-2 min-w-0 flex-1 pr-2">
                        📄 <CardTitle className="text-lg font-semibold truncate">{doc.name}</CardTitle>
                      </div>
                      <div className="flex gap-2 flex-shrink-0">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDownload(doc)}
                          className="hover:bg-blue-100 hover:text-blue-600 rounded-xl"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => deleteDocument(doc.id)}
                          className="hover:bg-red-100 hover:text-red-600 rounded-xl"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex justify-between items-center">
                        <div className="flex gap-2 flex-wrap">
                          <span className="text-xs px-3 py-1 bg-primary/10 text-primary rounded-full flex items-center justify-center whitespace-nowrap">
                            📁 {doc.folder}
                          </span>
                          <span className="text-xs px-3 py-1 bg-muted text-muted-foreground rounded-full flex items-center justify-center whitespace-nowrap">
                            {doc.size}
                          </span>
                        </div>
                        <span className="text-xs text-muted-foreground flex items-center flex-shrink-0">
                          <Calendar className="w-3 h-3 mr-1" />
                          {doc.createdAt.toLocaleDateString()}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </ScrollArea>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default MySpaceDialog;
