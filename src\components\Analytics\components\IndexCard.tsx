import React from 'react';
import { motion } from 'framer-motion';

interface IndexCardProps {
  title: string;
  value: number;
  maxValue?: number;
  className?: string;
}

const IndexCard = ({
  title,
  value,
  maxValue = 100,
  className = '',
}: IndexCardProps) => {
  const percentage = (value / maxValue) * 100;
  const radius = 40; // SVG circle radius
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;
  
  return (
    <motion.div
      className={`bg-[#E1FFD9] border border-[#97C48A] p-4 rounded-[10px] shadow-sm ${className}`}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="text-sm font-medium text-gray-700 mb-1">{title}</h3>
          <p className="text-2xl font-bold text-gray-900">
            {value}<span className="text-sm text-gray-500">/{maxValue}</span>
          </p>
        </div>
        
        <div className="w-20 h-20 relative">
          <svg width="80" height="80" viewBox="0 0 128 128">
            {/* Background Circle */}
            <circle
              cx="64"
              cy="64"
              r={radius}
              fill="none"
              stroke="#e5efe5"
              strokeWidth="18"
            />
            {/* Progress Circle */}
            <circle
              cx="64"
              cy="64"
              r={radius}
              fill="none"
              stroke="#347468"
              strokeWidth="18"
              strokeLinecap="round"
              strokeDasharray={circumference}
              strokeDashoffset={strokeDashoffset}
              transform="rotate(-90 64 64)"
            />
          </svg>
        </div>
      </div>
    </motion.div>
  );
};

export default IndexCard;
