import React, { useEffect, useState, useRef } from "react";
import { useLazyGetStudentQuizQuery } from "APIConnect";
import { useDispatch, useSelector } from "react-redux";
import { setQuiz } from "components/Student/Quiz/quizSlice";
import { useSaveStudentQuizMutation } from "APIConnect";
import { useNavigate } from "react-router-dom";
import QuizResultsModal from "./QuizResultsModal";
import courseGIF from "./assets/courseGIF.svg";
import "./QuizView.css";
import { isHTMLRegex, binaryTypes } from "constant/AppConstant";
import MatchTheFollowing from "./MatchTheFollowing";

const Quiz = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [getStudentQuiz, getStudentQuizResult] = useLazyGetStudentQuizQuery();
  const [saveStudentQuiz, saveStudentQuizResult] = useSaveStudentQuizMutation();
  const courseDetail = useSelector((state) => state.courses.courseDetail);
  const quizzes = useSelector((state) => state.quiz.quizzes);

  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [quizCompleteModal, setQuizCompleteModal] = useState(false);
  const [quitModal, setQuitModal] = useState(false);
  const [timer, setTimer] = useState("00:00:00");
  const [time, setTime] = useState(0);
  const [quizScore, setQuizScore] = useState(0);
  const [quizPercentage, setQuizPercentage] = useState(0);
  const [quizSucMsg, setQuizSucMsg] = useState("");
  const [matchingQuestions, setMatchingQuestions] = useState([]);
  const [matchingAnswers, setMatchingAnswers] = useState([]);
  const [isSubmit, setIsSubmit] = useState(false);

  let intervalIDRef = null;

  const divcheckRef = useRef(null);

  useEffect(() => {
    getQuiz();
    intervalIDRef = startTimer();

    return () => clearInterval(intervalIDRef);
  }, []);

  const startTimer = () => {
    return setInterval(() => {
      setTime((prevTime) => prevTime + 1);
    }, 60000);
  };

  const getQuiz = async () => {
    try {
      const obj = {
        id: "153898a5-fc2d-4630-aaee-00662a79c989",
        courseId: courseDetail?.id,
      };

      const { data } = await getStudentQuiz(obj);
      if (data) {
        let mappedData = await getAddAnswerTextData(data);
        dispatch(setQuiz(mappedData));
        // dispatch(setQuiz(data));
        //intervalIDRef =  startTimer();
      }
    } catch (err) {
      console.error(err);
    }
  };

  const getAddAnswerTextData = (data) => {
    let quizzesData = { ...data };
    let mappedData = data?.questionSet?.map((item) => {
      if (item?.type === "BINARY") {
        return {
          ...item,
          answers: binaryTypes,
        };
      } else {
        return {
          ...item,
        };
      }
    });

    quizzesData.questionSet = mappedData;
    return quizzesData;
  };

  const setMatchPrepareData = () => {
    let obj = {};

    for (let i = 0; i < matchingQuestions.length; i++) {
      obj[matchingQuestions[i]] = matchingAnswers[i];
    }
    let quizzesData = { ...quizzes };
    const changedQuestionSet = quizzes.questionSet?.map((item, index) => {
      if (index === currentQuestion) {
        return {
          ...item,
          studentAnswer: obj,
        };
      } else {
        return {
          ...item,
        };
      }
    });
    quizzesData.questionSet = changedQuestionSet;
    dispatch(setQuiz(quizzesData));
  };

  const handleContinue = () => {
    setCurrentQuestion(currentQuestion + 1);

    if (quizzes?.questionSet?.[currentQuestion]?.type === "MATCH") {
      setMatchPrepareData();
    }
  };

  useEffect(() => {
    if (isSubmit) {
      handleCallAPI();
    }
  }, [isSubmit]);

  const handleSubmit = async () => {
    if (quizzes?.questionSet?.[currentQuestion]?.type === "MATCH") {
      setMatchPrepareData();
    }


    setTextInputAnswerIntoEmptyArray()

    setIsSubmit(true);
  };

  const setTextInputAnswerIntoEmptyArray = () => {
    let quizzesData = { ...quizzes };
    const changedQuestionSet = quizzes.questionSet?.map((item, index) => {
      if (item?.type === 'BINARY') {
        return {
          ...item,
          answers: [],
        };
      } else {
        return {
          ...item,
        };
      }
    });
    quizzesData.questionSet = changedQuestionSet;
    dispatch(setQuiz(quizzesData));
  }

  const handleCallAPI = async () => {
    try {
      const obj = {
        studentId: "153898a5-fc2d-4630-aaee-00662a79c989",
        quizId: quizzes?.id,
        quizzes,
      };
      const { data } = await saveStudentQuiz(obj);
      if (data) {
        setQuizScore(data?.score);
        setQuizPercentage(data?.percentage);
        setQuizSucMsg(data?.nextTopic?.name);
        setIsSubmit(false);
        // toastMessage(
        //   `${quizSucMsg1} ${data?.nextTopic?.description || ""}`,
        //   "success"
        // );
      }
      setQuizCompleteModal(true);
      // navigate("/");
      clearInterval(intervalIDRef);
    } catch (err) {
      console.error(err);
    }
  };

  const getAnsChecked = (ans, type, studentAnswer, i) => {
    switch (type) {
      case "SINGLECHOICE":
        return singleChoiceAnsChecked(i, studentAnswer);
      case "BINARY":
        return binaryChoiceAnsChecked(ans, studentAnswer);
      case "MULTICHOICE":
        return multiChoiceAnsChecked(ans, studentAnswer);
    }
  };

  const singleChoiceAnsChecked = (indexVal, studentAnswer) => {
    return indexVal === studentAnswer;
  };

  const binaryChoiceAnsChecked = (ans, studentAnswer) => {
    return ans === studentAnswer;
  };

  const multiChoiceAnsChecked = (ans, studentAnswer) => {
    return Array.isArray(studentAnswer) ? studentAnswer?.includes(ans) : false;
  };

  const handleAnswerChange = (e, type, i) => {
    const { value } = e?.target;
    let quizzesData = { ...quizzes };
    let changedQuestionSet = quizzes.questionSet;
    switch (type) {
      case "SINGLECHOICE":
        changedQuestionSet = singleChoiceChoose(i);
        break;
      case "TEXTINPUT":
        changedQuestionSet = textInputChoiceChoose(value);
        break;
      case "BINARY":
        changedQuestionSet = binaryChoiceChoose(value);
        break;
      case "MULTICHOICE":
        changedQuestionSet = multiChoiceChoose(value);
        break;
    }

    quizzesData.questionSet = changedQuestionSet;
    dispatch(setQuiz(quizzesData));
  };

  const singleChoiceChoose = (indexVal) => {
    const changedQuestionSet = quizzes.questionSet?.map((item, index) => {
      if (index === currentQuestion) {
        return {
          ...item,
          studentAnswer: indexVal,
        };
      } else {
        return {
          ...item,
        };
      }
    });
    return changedQuestionSet;
  };

  const binaryChoiceChoose = (value) => {
    const changedQuestionSet = quizzes.questionSet?.map((item, index) => {
      if (index === currentQuestion) {
        return {
          ...item,
          studentAnswer: value?.toString() === "true" ? true : false,
        };
      } else {
        return {
          ...item,
        };
      }
    });
    return changedQuestionSet;
  };

  const textInputChoiceChoose = (value) => {
    const changedQuestionSet = quizzes.questionSet?.map((item, index) => {
      if (index === currentQuestion) {
        return {
          ...item,
          studentAnswer: value,
        };
      } else {
        return {
          ...item,
        };
      }
    });
    return changedQuestionSet;
  };

  const multiChoiceChoose = (value) => {
    const changedQuestionSet = quizzes.questionSet?.map((item, index) => {
      if (index === currentQuestion) {
        return {
          ...item,
          studentAnswer:
            item?.studentAnswer?.length > 0
              ? item?.studentAnswer?.includes(value)
                ? item?.studentAnswer?.filter((ans) => ans !== value)
                : [...item?.studentAnswer, value]
              : [value],
        };
      } else {
        return {
          ...item,
        };
      }
    });
    return changedQuestionSet;
  };

  const handleQuit = () => {
    navigate("/");
    clearInterval(intervalIDRef);
  };

  const handleBack = () => {
    setQuitModal(true);
  };

  const getSeconds = (time) => {
    return `0${time % 60}`.slice(-2);
  };

  const getMinutes = (time) => {
    return Math.floor(time) + " mins";
    // return Math.floor(time / 60);
  };

  const onReviewQuiz = () => {
    setQuizCompleteModal(false);
    navigate("/");
  };

  const onRetryQuiz = () => {
    setCurrentQuestion(0);
    dispatch(setQuiz([]));
    setQuizCompleteModal(false);

    getQuiz();
  };

  const getQuestionText = () => {
    if(quizzes?.questionSet?.[currentQuestion]?.type ==='TEXTINPUT' ) {
      let questionArray =  quizzes?.questionSet?.[currentQuestion]?.questionText?.split('|')
      return  (  
        <>
        <>{questionArray[0]}</> 
      <input
      autoFocus
  
      style={{
        border: 'none',
        borderBottom: '2px solid white',
        backgroundColor: 'transparent',
        color: '#fff',
        fontSize: '20px',
        width: '200px', 
        marginLeft: '20px',
        marginRight: '20px',
        outline: 'none'

      }}
      onChange={(e) =>
        handleAnswerChange(
          e,
          quizzes?.questionSet?.[currentQuestion]?.type
        )
      }
    /> 
    <>{questionArray[1]}</>
    </>
      )
    }
    if (
      isHTMLRegex.test(quizzes?.questionSet?.[currentQuestion]?.questionText)
    ) {
      return (
        <span
          dangerouslySetInnerHTML={{
            __html: quizzes?.questionSet?.[currentQuestion]?.questionText,
          }}
        />
      );
    }
    return quizzes?.questionSet?.[currentQuestion]?.questionText;
  };

  const getAnswerOption = (ansOpt, type) => {
    if (isHTMLRegex.test(ansOpt)) {
      return (
        <span
          dangerouslySetInnerHTML={{
            __html: ansOpt,
          }}
        />
      );
    }
    if (type === "BINARY") {
      return (
        ansOpt?.toString()?.charAt(0)?.toUpperCase() +
        ansOpt?.toString()?.slice(1)
      );
    }
    return ansOpt;
  };

  useEffect(() => {
    let questonArr = [],
      ansArr = [];

    if (quizzes?.questionSet?.[currentQuestion]?.type === "MATCH") {
      let correctAnswer =
        quizzes?.questionSet?.[currentQuestion]?.correctAnswer;
      if (correctAnswer) {
        Object.keys(correctAnswer).map(
          (keyName) => (
            questonArr.push(keyName), ansArr.push(correctAnswer[keyName])
          )
        );
      }

      setMatchingQuestions([...questonArr]);
      setMatchingAnswers([...ansArr]);
    }
  }, [quizzes?.questionSet?.[currentQuestion]?.type]);

  return (
    <>
      {(getStudentQuizResult?.isLoading ||
        saveStudentQuizResult?.isLoading) && <div className="loading"></div>}

      {quizzes?.questionSet?.length > 0 && !getStudentQuizResult?.isLoading && (
        <div className="quiz-container">
          <div className="quiz-header">
            <button className="quiz-back-button" onClick={handleBack}>
              &lt; Back
            </button>
            <h1 className="quiz-name">Quiz name</h1>
            <div className="timer">
              {/* {getMinutes(time)}:{getSeconds(time)} */}
              {getMinutes(time)}
            </div>
          </div>
          <h4 className="no-of-question">
            {currentQuestion + 1}/{quizzes?.questionSet?.length} Question
          </h4>
          <div className="progress-container">
          {/* <div
              className="progress-line"
              style={{
                  divcheckRef?.current?.getBoundingClientRect()?.width - 60,
                
              }}
            ></div> */}

            <div
              className="progress-dot-maincontainer"
           
              ref={divcheckRef}
            >
              {quizzes?.questionSet?.map((_, i) => (
                <>
                   
                
                        <div
              className= {quizzes?.questionSet?.length !== i+1  ? "progress-line" : 'progress-line1'}
              style={{
                width:55,
                 // divcheckRef?.current?.getBoundingClientRect()?.width - 60,

              }}
            >
              <div
                  key={i}
                  className={`progress-dot ${
                    i <= currentQuestion ? "active" : ""
                  }`}
                
                > </div>
            </div>


                </>
                
               
                
              ))}
            </div>
          </div>

          <div className="question-container">
            <h2 className="question-title">Question {currentQuestion + 1}</h2>
            <p
              className="question-text"
              style={{
                marginBottom: isHTMLRegex.test(
                  quizzes?.questionSet?.[currentQuestion]?.questionText
                )
                  ? "-10px"
                  : "10px",
                marginTop: isHTMLRegex.test(
                  quizzes?.questionSet?.[currentQuestion]?.questionText
                )
                  ? "-5px"
                  : "5px",
              }}
            >
              {getQuestionText()}
              
            </p>
            
          </div>
          
          <div
            className="options-maincontainer"
            style={{
              height:
                quizzes?.questionSet?.[currentQuestion]?.type === "MATCH"
                  ? 250
                  : 150,
            }}
          >
            
            <div className="options-container">
              {/* created a text input separteley  */}
              {/* {quizzes?.questionSet?.[currentQuestion]?.type ===
                "TEXTINPUT" && (
                <input
                  autoFocus
                  className="fill-in-blank-input"
                  onChange={(e) =>
                    handleAnswerChange(
                      e,
                      quizzes?.questionSet?.[currentQuestion]?.type
                    )
                  }
                />
              )} */}
              {quizzes?.questionSet?.[currentQuestion]?.type === "MATCH" && (
                <MatchTheFollowing
                  matchingAnswers={matchingAnswers}
                  matchingQuestions={matchingQuestions}
                  setMatchingAnswersFromChild={setMatchingAnswers}
                />
              )}

              {quizzes?.questionSet?.[currentQuestion]?.answers?.map(
                (option, i) => (
                  <div
                    key={i}
                    className="option"
                    style={{
                      marginBottom: isHTMLRegex.test(option.answerText)
                        ? "-15px"
                        : "15px",
                      marginTop: isHTMLRegex.test(option.answerText)
                        ? "0px"
                        : "5px",
                    }}
                  >
                    <input
                      type={
                        quizzes?.questionSet?.[currentQuestion]?.type ===
                        "MULTICHOICE"
                          ? "checkbox"
                          : "radio"
                      }
                      //  type="radio"
                      id={option}
                      value={option?.answerText}
                      checked={
                        getAnsChecked(
                          option?.answerText,
                          quizzes?.questionSet?.[currentQuestion]?.type,
                          quizzes?.questionSet[currentQuestion]?.studentAnswer,
                          i
                        )
                        // option?.answerText ===
                        // quizzes?.questionSet[currentQuestion]?.studentAnswer
                      }
                      onChange={(e) =>
                        handleAnswerChange(
                          e,
                          quizzes?.questionSet?.[currentQuestion]?.type,
                          i
                        )
                      }
                    />
                    <label htmlFor={option} className="option-label">
                      {getAnswerOption(
                        option.answerText,
                        quizzes?.questionSet?.[currentQuestion]?.type
                      )}
                      {/* {option.answerText} */}
                    </label>
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      )}
      {quizzes?.questionSet?.length === 0 &&
        !getStudentQuizResult?.isLoading && (
          <div className="quiz-container">
            {!getStudentQuizResult?.isLoading && (
              <div className="no-data">There is no quiz for now</div>
            )}
          </div>
        )}

      {quizCompleteModal && (
        <QuizResultsModal
          score={quizScore}
          points={quizPercentage}
          passingScore={80}
          onReviewQuiz={onReviewQuiz}
          onRetryQuiz={onRetryQuiz}
          msg={quizSucMsg}
        />
      )}

      {quitModal && (
        <div className="modal-overlay">
          <div className="modal-content-quiz">
            <h4 className="modal-quiz-result">
              Are you sure you want to quit?
            </h4>
            <div className="modal-quiz-img-container">
              <img src={courseGIF} alt="" />
            </div>
            <div className="button-container">
              <button
                //  style={{width:'80%'}}
                className="review-btn"
                onClick={handleQuit}
              >
                Yes
              </button>
              <button className="retry-btn" onClick={() => setQuitModal(false)}>
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {quizzes?.questionSet?.length > 0 && !getStudentQuizResult?.isLoading && (
        <>
          {quizzes?.questionSet?.length === currentQuestion + 1 ? (
            <div className="quiz-footer">
              <div className="continue-button1">
                <div className="align-right">
                  <button className="continue-button" onClick={handleSubmit}>
                    Submit
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="quiz-footer">
              <div className="continue-button1">
                <div className="align-right">
                  <button
                    className="continue-button"
                    onClick={() => handleContinue()}
                  >
                    Continue
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {quizzes?.questionSet?.length > 0 &&
        !getStudentQuizResult?.isLoading &&
        quizzes?.questionSet?.[currentQuestion]?.type !== "MATCH" && (
          <div className="quiz-result" />
        )}
    </>
  );
};

export default Quiz;

