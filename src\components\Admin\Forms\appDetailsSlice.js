import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  allSubjects: [],
  allCourses: [],
  allChapters: [],
  allTopics: [],
};

export const appDetailsSlice = createSlice({
  initialState,
  name: "appDetails",
  reducers: {
    setAllSubjects: (state, action) => {
      state.allSubjects = action.payload;
    },
    setAllCourses: (state, action) => {
      state.allCourses = action.payload;
    },
    setAllChapters: (state, action) => {
      state.allChapters = action.payload;
    },
    setAllTopics: (state, action) => {
      state.allTopics = action.payload;
    },
  },
});

export const { setAllSubjects, setAllCourses, setAllChapters, setAllTopics } =
  appDetailsSlice.actions;

export default appDetailsSlice;
