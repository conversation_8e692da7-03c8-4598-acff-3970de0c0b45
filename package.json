{"name": "learnido-client", "version": "0.1.0", "private": true, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@babel/core": "^7.16.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@hookform/resolvers": "^3.9.0", "@livekit/components-react": "^2.9.0", "@livekit/components-styles": "^1.1.5", "@lucide/lab": "^0.1.2", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@react-spring/web": "^9.7.5", "@reduxjs/toolkit": "^2.2.6", "@svgr/webpack": "^5.5.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/diff": "^7.0.1", "@types/dompurify": "^3.0.5", "@types/jszip": "^3.4.1", "auth0-js": "^9.26.1", "axios": "^1.8.4", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "d3": "^7.9.0", "date-fns": "^3.6.0", "diff": "^7.0.0", "dompurify": "^3.2.4", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "embla-carousel-react": "^8.3.0", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "framer-motion": "^11.11.9", "fs-extra": "^10.0.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "input-otp": "^1.2.4", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "jszip": "^3.10.1", "livekit-client": "^2.9.9", "lucide-react": "^0.451.0", "mini-css-extract-plugin": "^2.4.5", "next-themes": "^0.3.0", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prompts": "^2.4.2", "react": "^18.3.1", "react-app-polyfill": "^3.0.0", "react-day-picker": "^8.10.1", "react-dev-utils": "^12.0.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-hook-form": "^7.53.0", "react-markdown": "^10.1.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-refresh": "^0.11.0", "react-resizable-panels": "^2.1.4", "react-router-dom": "^6.25.0", "react-select": "^5.8.0", "react-speech-recognition": "^3.10.0", "react-toastify": "^10.0.5", "recharts": "^2.15.2", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "sass-loader": "^12.3.0", "semver": "^7.3.5", "sonner": "^1.5.0", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "suneditor": "^2.47.0", "suneditor-react": "^3.6.1", "tailwind-merge": "^2.5.3", "tailwindcss": "^3.0.2", "tailwindcss-animate": "^1.0.7", "terser-webpack-plugin": "^5.2.5", "vaul": "^1.0.0", "web-vitals": "^2.1.4", "webpack": "^5.64.4", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1", "zod": "^3.23.8"}, "scripts": {"start": "node scripts/start.js", "build": "node scripts/build.js", "test": "node scripts/test.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/d3": "^7.4.3", "@types/react-speech-recognition": "^3.9.5", "typescript": "^5.4.2"}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": ["/Users/<USER>/Desktop/Projects/viswin_global/learnido-client/src"], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}, "babel": {"presets": ["react-app"]}}