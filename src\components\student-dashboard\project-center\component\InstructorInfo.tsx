import React from 'react';
import { useGetUserByIdQuery } from '@/APIConnect';
import { User } from '@/types/projectCenter';

interface InstructorInfoProps {
  instructorId?: string;
}

const InstructorInfo: React.FC<InstructorInfoProps> = ({ instructorId }) => {
  // Fetch instructor data if instructorId is provided
  const {
    data: instructorData,
    isLoading: isInstructorLoading,
    error: instructorError
  } = useGetUserByIdQuery(instructorId, {
    skip: !instructorId || instructorId === ""
  });

  // Get instructor info or use fallback
  const getInstructorInfo = () => {
    if (instructorData && !instructorError) {
      const instructor: User = instructorData;
      return {
        name: `${instructor.firstName} ${instructor.lastName}`,
        role: instructor.userRole?.role || 'Instructor',
        description: `${instructor.firstName} ${instructor.lastName} is an experienced ${instructor.userRole?.role?.toLowerCase()} dedicated to providing quality education and guidance to students. With expertise in their field, they bring valuable knowledge and support to help students achieve their learning objectives.`
      };
    }

    // Fallback data when no instructor is assigned or data is unavailable
    return {
      name: 'No Instructor Assigned',
      role: 'Instructor',
      description: 'No instructor has been assigned to this project yet. An instructor will be assigned soon to provide guidance and support throughout the project.'
    };
  };

  const instructorInfo = getInstructorInfo();

  // Loading state
  if (isInstructorLoading) {
    return (
      <div className="bg-white/5 rounded-[8px] shadow-lg flex flex-col md:flex-row md:items-stretch text-white border border-[#347468] overflow-hidden">
        <div className="flex w-full h-full">
          <div className="w-full h-full bg-gray-200 animate-pulse"></div>
        </div>
        <div className="flex-grow p-6">
          <div className="h-6 bg-gray-200 animate-pulse rounded mb-1"></div>
          <div className="h-4 bg-gray-200 animate-pulse rounded mb-4 w-1/2"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 animate-pulse rounded"></div>
            <div className="h-4 bg-gray-200 animate-pulse rounded"></div>
            <div className="h-4 bg-gray-200 animate-pulse rounded w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/5 rounded-[8px] shadow-lg flex flex-col md:flex-row md:items-stretch text-white border border-[#347468] overflow-hidden">
      <div className="flex w-full h-full">
        <img
          src={instructorData ? `https://i.pravatar.cc/150?u=${instructorData.id}` : "/icons/project-center-icons/johndowstockimg.png"}
          alt={instructorInfo.name}
          className="w-full h-full object-cover"
        />
      </div>
      <div className="flex-grow p-6">
        <h2 className="text-lg font-semibold mb-1 text-[#2D2D2D]">{instructorInfo.name}</h2>
        <p className="text-sm font-semibold text-[#347468] mb-4">{instructorInfo.role}</p>
        <p className="text-sm text-[#2D2D2D] leading-relaxed font-medium">
          {instructorInfo.description}
        </p>
      </div>
    </div>
  );
};

export default InstructorInfo;

