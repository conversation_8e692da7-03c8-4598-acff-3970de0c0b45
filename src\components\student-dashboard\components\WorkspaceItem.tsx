import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface WorkspaceItemProps {
  title: string;
  icon: React.ReactNode; // Allow passing Lucide icons or other elements
  itemCount: number;
  imagePreviews?: string[]; // Array of image URLs for previews
  textPreviews?: string[]; // Array of text lines for previews
  onAddNewClick?: () => void;
  small?: boolean;
}

const WorkspaceItem: React.FC<WorkspaceItemProps> = ({
  title,
  icon,
  itemCount,
  imagePreviews = [],
  textPreviews = [],
  onAddNewClick,
  small = false,
}) => {
  const MAX_PREVIEWS = 3; // Show max 3 previews + count
  const previewsToShow = imagePreviews.slice(0, MAX_PREVIEWS - 1);
  const textToShow = textPreviews.slice(0, MAX_PREVIEWS);
  const remainingCount = imagePreviews.length - previewsToShow.length;

  return (
    <div className={small ? "border border-primary rounded-lg overflow-hidden" : "border border-primary rounded-xl overflow-hidden"}>
      {/* Header */}
      <div className={small ? "p-2 flex justify-between items-center border-b border-gray-100" : "p-3 flex justify-between items-center border-b border-gray-100"}>
        <div className="flex items-center gap-1">
          {icon}
          <h3 className={small ? "font-medium text-xs" : "font-medium text-sm"}>{title}</h3>
        </div>
        {onAddNewClick && (
          <Button
            variant="outline"
            size={small ? "icon" : "sm"}
            className={small ? "h-6 w-6 px-0 text-xs flex items-center gap-1 bg-primary text-white" : "h-7 px-2 text-xs flex items-center gap-1 bg-primary text-white"}
            onClick={onAddNewClick}
          >
            <Plus size={small ? 12 : 14} />
            {!small && 'Add New'}
          </Button>
        )}
      </div>
      <div className={small ? 'bg-primary w-full h-[1px]' : 'bg-primary w-full h-[1px]'} />
      {/* Content - Text Previews (if provided) */}
      {textPreviews && textPreviews.length > 0 && (
        <div className={small ? "p-2 flex flex-col gap-1" : "p-3 flex flex-col gap-2"}>
          {textToShow.map((text, index) => (
            <div key={index} className="truncate text-xs text-gray-700 font-medium">
              {text}
            </div>
          ))}
        </div>
      )}
      {/* Content - Image Previews (if no textPreviews) */}
      {(!textPreviews || textPreviews.length === 0) && imagePreviews.length > 0 && (
        <div className={small ? "p-2 flex gap-1" : "p-3 flex gap-2"}>
          {previewsToShow.map((src, index) => (
            <div key={index} className={small ? "w-8 h-8 rounded overflow-hidden bg-gray-100" : "w-12 h-12 rounded overflow-hidden bg-gray-100"}>
              <img src={src} alt={`${title} preview ${index + 1}`} className="w-full h-full object-cover" />
            </div>
          ))}
          {remainingCount > 0 && (
            <div className={small ? "w-8 h-8 rounded bg-gray-200 flex items-center justify-center text-gray-600 text-xs font-medium" : "w-12 h-12 rounded bg-gray-200 flex items-center justify-center text-gray-600 text-xs font-medium"}>
              +{remainingCount} More
            </div>
          )}
        </div>
      )}
      {/* Fallback text if no previews but items exist */}
      {imagePreviews.length === 0 && textPreviews.length === 0 && itemCount > 0 && (
        <div className={small ? "p-2 text-xs text-gray-500" : "p-3 text-xs text-gray-500"}>
          {itemCount} item(s) available.
        </div>
      )}
      {/* Placeholder if no items */}
      {itemCount === 0 && (
        <div className={small ? "p-2 text-xs text-gray-400" : "p-3 text-xs text-gray-400"}>
          No items yet.
        </div>
      )}
    </div>
  );
};

export default WorkspaceItem;
