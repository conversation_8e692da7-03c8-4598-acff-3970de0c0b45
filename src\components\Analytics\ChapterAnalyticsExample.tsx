import React from 'react';
import ChapterAnalytics from './ChapterAnalytics';
import { useGetClassroomAndChapterAnalyticsQuery } from '@/APIConnect';
import { Card } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';

interface ChapterAnalyticsExampleProps {
    classroomId: string;
    chapterId: string;
}

export const ChapterAnalyticsExample: React.FC<ChapterAnalyticsExampleProps> = ({
    classroomId,
    chapterId,
}) => {
    const { data, isLoading, error } = useGetClassroomAndChapterAnalyticsQuery({
        classroomId,
        chapterId,
    });

    if (isLoading) {
        return (
            <div className="space-y-4 p-6">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {[...Array(4)].map((_, i) => (
                        <Card key={i} className="p-6">
                            <Skeleton className="h-4 w-[100px] mb-4" />
                            <Skeleton className="h-8 w-[60px]" />
                        </Card>
                    ))}
                </div>
                <Card className="p-6">
                    <Skeleton className="h-[400px]" />
                </Card>
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive">
                <AlertDescription>
                    Failed to load analytics data. Please try again later.
                </AlertDescription>
            </Alert>
        );
    }

    if (!data?.resultObject?.[0]) {
        return (
            <Alert>
                <AlertDescription>
                    No analytics data available for this chapter.
                </AlertDescription>
            </Alert>
        );
    }

    return <ChapterAnalytics data={data.resultObject[0]} />;
};

export default ChapterAnalyticsExample;
