import React, { useState, useEffect, useMemo } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, DonutChart, StreamGraph } from './ChartComponent';
import { format } from 'date-fns';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { GraduationCap } from 'lucide-react';

interface AnalyticsDataItem {
    classroomId: string;
    courseId: string;
    chapterId: string;
    topicId: string;
    averageScore: number | null;
    averagePercentage: number;
    averageTimeTaken: number;
    averageSuccessfulSubmissions: number;
    averageRepitionCount: number;
    averageLvl1Count?: {
        averagePassCount: number;
        averageTotalCount: number;
        averageTotalTimeTaken: number;
    };
    averageLvl2Count?: {
        averagePassCount: number;
        averageTotalCount: number;
        averageTotalTimeTaken: number;
    };
    averageLvl3Count?: {
        averagePassCount: number;
        averageTotalCount: number;
        averageTotalTimeTaken: number;
    };
    averageLvl4Count?: {
        averagePassCount: number;
        averageTotalCount: number;
        averageTotalTimeTaken: number;
    };
    averageLvl5Count?: {
        averagePassCount: number;
        averageTotalCount: number;
        averageTotalTimeTaken: number;
    };
    averageMultipleChoiceCount?: {
        averagePassCount: number;
        averageTotalCount: number;
        averageTotalTimeTaken: number;
    };
    averageSingleChoiceCount?: {
        averagePassCount: number;
        averageTotalCount: number;
        averageTotalTimeTaken: number;
    };
    averageTextInputCount?: {
        averagePassCount: number;
        averageTotalCount: number;
        averageTotalTimeTaken: number;
    };
    averageMatchCount?: {
        averagePassCount: number;
        averageTotalCount: number;
        averageTotalTimeTaken: number;
    };
    averageBinaryCount?: {
        averagePassCount: number;
        averageTotalCount: number;
        averageTotalTimeTaken: number;
    };
    averageSoundbasedCount?: {
        averagePassCount: number;
        averageTotalCount: number;
        averageTotalTimeTaken: number;
    };
    averagePictureMatchCount?: {
        averagePassCount: number;
        averageTotalCount: number;
        averageTotalTimeTaken: number;
    };
    id: {
        timestamp: number;
        machine: number;
        pid: number;
        increment: number;
        creationTime: string;
    };
    createdOn: string;
    updatedon: string;
}

interface TeacherAnalyticsItem {
    studentId: string;
    averageScore: number;
    participationCount: number;
}
interface AnalyticsResponse {
    resultObject: AnalyticsDataItem[];
    uniqueStudentCount: number;
}
interface TeacherAnalyticsResponse {
    resultObject: TeacherAnalyticsItem[]
}

const AnalyticsPage: React.FC = () => {
    const [analyticsData, setAnalyticsData] = useState<AnalyticsDataItem[] | null>(null);
    const [teacherAnalyticsData, setTeacherAnalyticsData] = useState<TeacherAnalyticsItem[] | null>(null);
    const [uniqueStudentCount, setUniqueStudentCount] = useState<number | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchAnalyticsData = async () => {
            setLoading(true);
            try {
                // Mock Api Call, please replace with actual calls
                const response = new Promise<any>(resolve => setTimeout(() => {
                    resolve({
                        json: async () => ({
                            "resultObject": [
                                {
                                    "classroomId": "d8fe1f7c-9e82-4a04-8e42-fac124512169",
                                    "courseId": "18d65301-e7e1-43de-ad85-17473dd1b468",
                                    "chapterId": "3498359c-f08f-46a6-8263-67a7246578f9",
                                    "topicId": "a0fe3b06-ccff-4957-bc55-39ac05e7a54d",
                                    "averageScore": 6,
                                    "averagePercentage": 66.66667,
                                    "averageTimeTaken": 6000000,
                                    "averageSuccessfulSubmissions": 1,
                                    "averageRepitionCount": 5,
                                    "averageLvl1Count": {
                                        "averagePassCount": 6,
                                        "averageTotalCount": 9,
                                        "averageTotalTimeTaken": 22000
                                    },
                                    "averageLvl2Count": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "averageLvl3Count": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "averageLvl4Count": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "averageLvl5Count": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "averageMultipleChoiceCount": {
                                        "averagePassCount": 1,
                                        "averageTotalCount": 1,
                                        "averageTotalTimeTaken": 3000
                                    },
                                    "averageSingleChoiceCount": {
                                        "averagePassCount": 2,
                                        "averageTotalCount": 4,
                                        "averageTotalTimeTaken": 12000
                                    },
                                    "averageTextInputCount": {
                                        "averagePassCount": 2,
                                        "averageTotalCount": 3,
                                        "averageTotalTimeTaken": 5000
                                    },
                                    "averageMatchCount": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "averageBinaryCount": {
                                        "averagePassCount": 1,
                                        "averageTotalCount": 1,
                                        "averageTotalTimeTaken": 2000
                                    },
                                    "averageSoundbasedCount": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "averagePictureMatchCount": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "id": {
                                        "timestamp": 1732607900,
                                        "machine": 12006727,
                                        "pid": -26580,
                                        "increment": 3018114,
                                        "creationTime": "2024-11-26T07:58:20Z"
                                    },
                                    "createdOn": "2024-11-26T07:58:20.466689+00:00",
                                    "updatedon": "2024-11-26T08:25:56.214997+00:00"
                                },
                                {
                                    "classroomId": "d8fe1f7c-9e82-4a04-8e42-fac124512169",
                                    "courseId": "18d65301-e7e1-43de-ad85-17473dd1b468",
                                    "chapterId": "3498359c-f08f-46a6-8263-67a7246578f9",
                                    "topicId": "a0fe3b06-ccff-4957-bc55-39ac05e7a54d",
                                    "averageScore": 7,
                                    "averagePercentage": 76.66667,
                                    "averageTimeTaken": 7000000,
                                    "averageSuccessfulSubmissions": 2,
                                    "averageRepitionCount": 5,
                                    "averageLvl1Count": {
                                        "averagePassCount": 7,
                                        "averageTotalCount": 9,
                                        "averageTotalTimeTaken": 22000
                                    },
                                    "averageLvl2Count": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "averageLvl3Count": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "averageLvl4Count": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "averageLvl5Count": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "averageMultipleChoiceCount": {
                                        "averagePassCount": 1,
                                        "averageTotalCount": 1,
                                        "averageTotalTimeTaken": 3000
                                    },
                                    "averageSingleChoiceCount": {
                                        "averagePassCount": 2,
                                        "averageTotalCount": 4,
                                        "averageTotalTimeTaken": 12000
                                    },
                                    "averageTextInputCount": {
                                        "averagePassCount": 2,
                                        "averageTotalCount": 3,
                                        "averageTotalTimeTaken": 5000
                                    },
                                    "averageMatchCount": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "averageBinaryCount": {
                                        "averagePassCount": 1,
                                        "averageTotalCount": 1,
                                        "averageTotalTimeTaken": 2000
                                    },
                                    "averageSoundbasedCount": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "averagePictureMatchCount": {
                                        "averagePassCount": 0,
                                        "averageTotalCount": 0,
                                        "averageTotalTimeTaken": 0
                                    },
                                    "id": {
                                        "timestamp": 1732607900,
                                        "machine": 12006727,
                                        "pid": -26580,
                                        "increment": 3018114,
                                        "creationTime": "2024-11-26T07:58:20Z"
                                    },
                                    "createdOn": "2024-11-27T07:58:20.466689+00:00",
                                    "updatedon": "2024-11-27T08:25:56.214997+00:00"
                                }
                            ],
                            "uniqueStudentCount": 10,
                        })
                    })
                }));
                const data = await (await response).json();
                setAnalyticsData(data.resultObject);
                setUniqueStudentCount(data.uniqueStudentCount);
            } catch (error) {
                console.error('Error fetching analytics data:', error);
            } finally {
                setLoading(false)
            }
        };
        const fetchTeacherAnalyticsData = async () => {
            try {
                // Mock Api Call, please replace with actual calls
                const response = new Promise<any>(resolve => setTimeout(() => {
                    resolve({
                        json: async () => ({
                            "resultObject": [
                                {
                                    "studentId": "student-1",
                                    "averageScore": 70,
                                    "participationCount": 5
                                },
                                {
                                    "studentId": "student-2",
                                    "averageScore": 80,
                                    "participationCount": 10
                                },
                                {
                                    "studentId": "student-3",
                                    "averageScore": 60,
                                    "participationCount": 2
                                },
                                {
                                    "studentId": "student-4",
                                    "averageScore": 90,
                                    "participationCount": 12
                                }
                            ]
                        })
                    })
                }));
                const teacherData = await (await response).json()
                setTeacherAnalyticsData(teacherData.resultObject)
            } catch (err) {
                console.log(err)
            }
        }
        fetchAnalyticsData();
        fetchTeacherAnalyticsData();
    }, []);
    const transformLeaderboardData = useMemo(() => {
        if (!analyticsData) return [];
        const groupedByClass = analyticsData.reduce((acc, curr) => {
            acc[curr.classroomId] = acc[curr.classroomId] || [];
            acc[curr.classroomId].push(curr);
            return acc;
        }, {} as { [key: string]: AnalyticsDataItem[] });
        const avgScores = Object.keys(groupedByClass).map(classroomId => {
            const classScores = groupedByClass[classroomId];
            const avgScore = classScores.reduce((acc, curr) => acc + (curr.averageScore || 0), 0) / classScores.length;
            return {
                classroomId,
                avgScore
            }
        }).sort((a, b) => b.avgScore - a.avgScore);
        return avgScores;
    }, [analyticsData]);

    const transformTeacherLeaderboardData = useMemo(() => {
        if (!teacherAnalyticsData) return [];

        return [...teacherAnalyticsData].sort((a, b) => b.averageScore - a.averageScore);
    }, [teacherAnalyticsData]);

    const transformedScoreByDate = useMemo(() => {
        if (!analyticsData) return [];
        const scoresByDate = analyticsData.reduce((acc, curr) => {
            const date = format(new Date(curr.createdOn), "yyyy-MM-dd")
            acc[date] = acc[date] || { totalScore: 0, count: 0 };
            acc[date].totalScore += curr.averageScore !== null ? curr.averageScore : 0;
            acc[date].count++;
            return acc;
        }, {} as { [key: string]: { totalScore: number; count: number } });
        return Object.keys(scoresByDate).map(date => ({
            date,
            averageScore: scoresByDate[date].totalScore / scoresByDate[date].count
        }))
    }, [analyticsData])
    const transformedAvgQuizDurationByDate = useMemo(() => {
        if (!analyticsData) return [];
        const durationByDate = analyticsData.reduce((acc, curr) => {
            const date = format(new Date(curr.createdOn), "yyyy-MM-dd")
            acc[date] = acc[date] || { totalDuration: 0, count: 0 };
            acc[date].totalDuration += curr.averageTimeTaken;
            acc[date].count++;
            return acc;
        }, {} as { [key: string]: { totalDuration: number; count: number } });
        return Object.keys(durationByDate).map(date => ({
            date,
            averageTimeTaken: durationByDate[date].totalDuration / durationByDate[date].count
        }));
    }, [analyticsData]);
    const transformedRepetitionByTopic = useMemo(() => {
        if (!analyticsData) return [];
        const repetitionByTopic = analyticsData.reduce((acc, curr) => {
            acc[curr.topicId] = acc[curr.topicId] || 0
            acc[curr.topicId] += curr.averageRepitionCount
            return acc
        }, {} as { [key: string]: number })
        return Object.keys(repetitionByTopic).map(topicId => ({ topicId, averageRepitionCount: repetitionByTopic[topicId] }))
    }, [analyticsData]);
    const transformedPassCountByDifficulty = useMemo(() => {
        if (!analyticsData) return [];
        const difficultyPassCounts = analyticsData.reduce((acc, curr) => {
            acc.averageLvl1Count = (acc.averageLvl1Count || 0) + (curr.averageLvl1Count?.averagePassCount || 0);
            acc.averageLvl2Count = (acc.averageLvl2Count || 0) + (curr.averageLvl2Count?.averagePassCount || 0);
            acc.averageLvl3Count = (acc.averageLvl3Count || 0) + (curr.averageLvl3Count?.averagePassCount || 0);
            acc.averageLvl4Count = (acc.averageLvl4Count || 0) + (curr.averageLvl4Count?.averagePassCount || 0);
            acc.averageLvl5Count = (acc.averageLvl5Count || 0) + (curr.averageLvl5Count?.averagePassCount || 0);
            return acc;
        }, {} as { [key: string]: number })
        return Object.keys(difficultyPassCounts).map(level => ({ level, averagePassCount: difficultyPassCounts[level] }))
    }, [analyticsData]);
    const transformedPassCountByQuestionType = useMemo(() => {
        if (!analyticsData) return [];
        const questionTypePassCounts = analyticsData.reduce((acc, curr) => {
            acc.averageMultipleChoiceCount = (acc.averageMultipleChoiceCount || 0) + (curr.averageMultipleChoiceCount?.averagePassCount || 0);
            acc.averageSingleChoiceCount = (acc.averageSingleChoiceCount || 0) + (curr.averageSingleChoiceCount?.averagePassCount || 0);
            acc.averageTextInputCount = (acc.averageTextInputCount || 0) + (curr.averageTextInputCount?.averagePassCount || 0);
            acc.averageMatchCount = (acc.averageMatchCount || 0) + (curr.averageMatchCount?.averagePassCount || 0);
            acc.averageBinaryCount = (acc.averageBinaryCount || 0) + (curr.averageBinaryCount?.averagePassCount || 0);
            acc.averageSoundbasedCount = (acc.averageSoundbasedCount || 0) + (curr.averageSoundbasedCount?.averagePassCount || 0);
            acc.averagePictureMatchCount = (acc.averagePictureMatchCount || 0) + (curr.averagePictureMatchCount?.averagePassCount || 0);
            return acc;
        }, {} as { [key: string]: number })
        return Object.keys(questionTypePassCounts).map(type => ({ type, averagePassCount: questionTypePassCounts[type] }))
    }, [analyticsData]);
    const transformedParticipationPlot = useMemo(() => {
        if (!analyticsData) return [];
        const participationByDate = analyticsData.reduce((acc, curr) => {
            const date = format(new Date(curr.createdOn), "yyyy-MM-dd")
            acc[date] = acc[date] || 0
            acc[date] += 1;
            return acc;
        }, {} as { [key: string]: number })
        return Object.keys(participationByDate).map(date => ({ date, count: participationByDate[date] }))
    }, [analyticsData])
    const transformStreamgraphData = useMemo(() => {
        if (!analyticsData) return [];
        const aggregatedData = analyticsData.reduce((acc, curr) => {
            const date = format(new Date(curr.createdOn), "yyyy-MM-dd");
            acc[date] = acc[date] || { date, values: {} };
            acc[date].values[curr.classroomId] = (acc[date].values[curr.classroomId] || 0) + 1;
            return acc;
        }, {} as { [key: string]: { date: string; values: { [key: string]: number } } });
        return Object.values(aggregatedData)
    }, [analyticsData])
    const transformedClassPerformance = useMemo(() => {
        if (!analyticsData) return [];
        const scoreByTopic = analyticsData.reduce((acc, curr) => {
            acc[curr.topicId] = acc[curr.topicId] || 0;
            acc[curr.topicId] += curr.averageScore || 0;
            return acc;
        }, {} as { [key: string]: number });
        return Object.keys(scoreByTopic).map(topicId => ({ topicId, averageScore: scoreByTopic[topicId] }))
    }, [analyticsData]);
    const transformedChapterWiseScore = useMemo(() => {
        if (!analyticsData) return [];
        const scoreByChapter = analyticsData.reduce((acc, curr) => {
            acc[curr.chapterId] = acc[curr.chapterId] || 0;
            acc[curr.chapterId] += curr.averageScore || 0;
            return acc;
        }, {} as { [key: string]: number });
        return Object.keys(scoreByChapter).map(chapterId => ({ chapterId, averageScore: scoreByChapter[chapterId] }))
    }, [analyticsData])
    const transformedQuestionTypeByAttemptCount = useMemo(() => {
        if (!analyticsData) return [];
        const questionTypeAttemptCounts = analyticsData.reduce((acc, curr) => {
            acc.averageMultipleChoiceCount = (acc.averageMultipleChoiceCount || 0) + (curr.averageMultipleChoiceCount?.averageTotalCount || 0);
            acc.averageSingleChoiceCount = (acc.averageSingleChoiceCount || 0) + (curr.averageSingleChoiceCount?.averageTotalCount || 0);
            acc.averageTextInputCount = (acc.averageTextInputCount || 0) + (curr.averageTextInputCount?.averageTotalCount || 0);
            acc.averageMatchCount = (acc.averageMatchCount || 0) + (curr.averageMatchCount?.averageTotalCount || 0);
            acc.averageBinaryCount = (acc.averageBinaryCount || 0) + (curr.averageBinaryCount?.averageTotalCount || 0);
            acc.averageSoundbasedCount = (acc.averageSoundbasedCount || 0) + (curr.averageSoundbasedCount?.averageTotalCount || 0);
            acc.averagePictureMatchCount = (acc.averagePictureMatchCount || 0) + (curr.averagePictureMatchCount?.averageTotalCount || 0);
            return acc;
        }, {} as { [key: string]: number })
        return Object.keys(questionTypeAttemptCounts).map(type => ({ type, averageTotalCount: questionTypeAttemptCounts[type] }))
    }, [analyticsData])
    const transformedDifficultyByAttemptCount = useMemo(() => {
        if (!analyticsData) return [];
        const difficultyAttemptCounts = analyticsData.reduce((acc, curr) => {
            acc.averageLvl1Count = (acc.averageLvl1Count || 0) + (curr.averageLvl1Count?.averageTotalCount || 0);
            acc.averageLvl2Count = (acc.averageLvl2Count || 0) + (curr.averageLvl2Count?.averageTotalCount || 0);
            acc.averageLvl3Count = (acc.averageLvl3Count || 0) + (curr.averageLvl3Count?.averageTotalCount || 0);
            acc.averageLvl4Count = (acc.averageLvl4Count || 0) + (curr.averageLvl4Count?.averageTotalCount || 0);
            acc.averageLvl5Count = (acc.averageLvl5Count || 0) + (curr.averageLvl5Count?.averageTotalCount || 0);
            return acc;
        }, {} as { [key: string]: number })
        return Object.keys(difficultyAttemptCounts).map(level => ({ level, averageTotalCount: difficultyAttemptCounts[level] }))
    }, [analyticsData])


    if (loading) {
        return <div>Loading...</div>;
    }
    if (!analyticsData) {
        return <div>Error: Unable to fetch data</div>;
    }

    return (
        <div className="container mx-auto p-4">
            <h1 className="text-2xl font-bold mb-4">Analytics Dashboard</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Leaderboard */}
                <Card>
                    <CardHeader>
                        <CardTitle>Leaderboard (Classwise)</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Rank</TableHead>
                                    <TableHead>Classroom ID</TableHead>
                                    <TableHead>Average Score</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {transformLeaderboardData.map((item, index) => (
                                    <TableRow key={index}>
                                        <TableCell>{index + 1}</TableCell>
                                        <TableCell>{item.classroomId}</TableCell>
                                        <TableCell>{item.avgScore}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>
                {/* Initial and Final Assessment Scores */}
                {analyticsData.map(item => (
                    <Card key={item.id.timestamp}>
                        <CardHeader>
                            <CardTitle>Assessment Status {item.topicId}</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {item.averageScore == null ? (
                                <Alert variant="destructive">
                                    <AlertTitle>Assessment Not Done</AlertTitle>
                                    <AlertDescription>
                                        This assessment was not done.
                                    </AlertDescription>
                                </Alert>
                            ) : (
                                <Badge variant="outline">Score: {item.averageScore}</Badge>
                            )}
                        </CardContent>
                    </Card>
                ))}

                {/* Class Performance Score by Topic */}
                <Card>
                    <CardHeader>
                        <CardTitle>Class Performance (Score by Date)</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <LineChart data={transformedScoreByDate} xKey="date" yKey="averageScore" />
                    </CardContent>
                </Card>
                {/* Avg Quiz Duration by Date */}
                <Card>
                    <CardHeader>
                        <CardTitle>Avg Quiz Duration by Date</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <LineChart data={transformedAvgQuizDurationByDate} xKey="date" yKey="averageTimeTaken" />
                    </CardContent>
                </Card>
                {/* Repetition Count by Topic */}
                <Card>
                    <CardHeader>
                        <CardTitle>Repetition Count by Topic</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <BarChart data={transformedRepetitionByTopic} xKey="topicId" yKey="averageRepitionCount" />
                    </CardContent>
                </Card>
                {/* Pass Count by Difficulty Level */}
                <Card>
                    <CardHeader>
                        <CardTitle>Pass Count by Difficulty Level</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <DonutChart data={transformedPassCountByDifficulty} dataKey="averagePassCount" labels="level" />
                    </CardContent>
                </Card>
                {/* Pass Count by Question Type */}
                <Card>
                    <CardHeader>
                        <CardTitle>Pass Count by Question Type</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <DonutChart data={transformedPassCountByQuestionType} dataKey="averagePassCount" labels="type" />
                    </CardContent>
                </Card>
                {/* Attempt Counts (This is mock since api doesnt have it)*/}
                <Card>
                    <CardHeader>
                        <CardTitle>Attempt Counts</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p>Min Attempts: <Badge variant="outline">1</Badge></p>
                        <p>Avg Attempts: <Badge variant="outline">5</Badge></p>
                        <p>Max Attempts: <Badge variant="outline">10</Badge></p>
                    </CardContent>
                </Card>

                {/* Participation plot */}
                <Card>
                    <CardHeader>
                        <CardTitle>Participation Plot</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center mb-2">
                            <GraduationCap className='h-5 w-5 mr-2' />
                            <p>Unique Students: <Badge variant="outline">{uniqueStudentCount}</Badge></p>
                        </div>
                        <LineChart data={transformedParticipationPlot} xKey="date" yKey="count" />
                    </CardContent>
                </Card>
                {/* Class Performance */}
                <Card>
                    <CardHeader>
                        <CardTitle>Class Performance (Score by Topic)</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <BarChart data={transformedClassPerformance} xKey="topicId" yKey="averageScore" />
                    </CardContent>
                </Card>
                {/* Chapter wise Score avg */}
                <Card>
                    <CardHeader>
                        <CardTitle>Chapter Wise Score Avg</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <BarChart data={transformedChapterWiseScore} xKey="chapterId" yKey="averageScore" />
                    </CardContent>
                </Card>
                {/* Question type by attempt count */}
                <Card>
                    <CardHeader>
                        <CardTitle>Question Type By Attempt Count</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <DonutChart data={transformedQuestionTypeByAttemptCount} dataKey="averageTotalCount" labels="type" />
                    </CardContent>
                </Card>
                {/* Difficulty by attempt count */}
                <Card>
                    <CardHeader>
                        <CardTitle>Difficulty by Attempt Count</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <DonutChart data={transformedDifficultyByAttemptCount} dataKey="averageTotalCount" labels="level" />
                    </CardContent>
                </Card>
                {/* Participation Streamgraph */}
                <Card>
                    <CardHeader>
                        <CardTitle>Class Participation</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <StreamGraph data={transformStreamgraphData} keys={analyticsData?.map(item => item.classroomId)} />
                    </CardContent>
                </Card>
                {/* Teacher stuentwise analytics */}
                <Card>
                    <CardHeader>
                        <CardTitle>Teacher Studentwise Analytics</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Rank</TableHead>
                                    <TableHead>Student</TableHead>
                                    <TableHead>Average Score</TableHead>
                                    <TableHead>Participation Count</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {transformTeacherLeaderboardData.map((item, index) => (
                                    <TableRow key={index}>
                                        <TableCell>{index + 1}</TableCell>
                                        <TableCell>
                                            <TooltipProvider>
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Avatar>
                                                            <AvatarImage src="https://github.com/shadcn.png" />
                                                            <AvatarFallback>
                                                                {item.studentId[0].toUpperCase()}
                                                            </AvatarFallback>
                                                        </Avatar>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>{item.studentId}</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>
                                        </TableCell>
                                        <TableCell>{item.averageScore}</TableCell>
                                        <TableCell>{item.participationCount}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default AnalyticsPage;