import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useGetSchoolClassroomsQuery, useGetStudentAnalyticsQuery, useLazyGetAllCourseDataQuery, useLazyGetCourseDetailsDataQuery, useLazyGetTopicsQuery } from '../../APIConnect';
import { mapStudentAnalyticsData } from './data/studentData';
import { Card, CardContent, CardHeader, CardTitle } from 'components/ui/card';
import { LineChart } from './charts/LineChart';
import { BarChart } from './charts/BarChart';
import { DoughnutChart } from './charts/DoughnutChart';
import { Loader2, TrendingUp, Clock, RepeatIcon, Target, Activity, BookOpen, ChevronDown } from 'lucide-react';
import { StatCard } from './components/StatCard';
import IndexCard from "./components/IndexCard";
import ProgressChart from "./components/ProgressChart";
import TopSkillTags from './components/TopSkillTags';
import SkillsMasteryMap from './components/SkillsMasteryMap';
import AssessmentTypePerformance from './components/AssessmentTypePerformance';
import IndexContributionBreakdown from './components/IndexContributionBreakdown';
import PeerPerformanceSnapshot from './components/PeerPerformanceSnapshot';
import LearningConsistency from './components/LearningConsistency';
import ProjectCompletion from './components/ProjectCompletion';
import FeedbackList from './components/FeedbackList';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';
import { StudentModeToggle } from './components/StudentModeToggle';
import { FlyingGifUrl } from '../../constant/AppConstant';

const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
};

const staggerContainer = {
    animate: {
        transition: {
            staggerChildren: 0.1
        }
    }
};

const StudentAnalytics = ({ studentId, classroomId, schoolId }: {
    studentId: string;
    classroomId: string;
    schoolId?: string;
}) => {
    const [currentClassroomId, setCurrentClassroomId] = React.useState<string | null>(classroomId);
    const { data: apiData, isLoading, isError } = useGetStudentAnalyticsQuery({ studentId, classroomId: currentClassroomId }, {
        skip: !studentId || !currentClassroomId, refetchOnMountOrArgChange: true
    });
    const [data, setData] = React.useState<any>(null);
    const {
        isLoading: isClassroomsLoading, data: classroomsData
    } = useGetSchoolClassroomsQuery(schoolId, {
        skip: !schoolId
    });

    const [getAllTopics] = useLazyGetTopicsQuery();

    const [isStudentMode, setIsStudentMode] = React.useState(() => {
        return localStorage.getItem('studentMode') === 'true';
    });

    React.useEffect(() => {
        localStorage.setItem('studentMode', isStudentMode.toString());
    }, [isStudentMode]);

    React.useEffect(() => {
        const mapData = async () => {
            if (apiData && apiData.resultObject) {
                const { data } = await getAllTopics('');
                let topicIdNameMap: { [key: string]: string } = {};
                data?.resultObject.forEach((topic: any) => {
                    const existingCount = Object.values(topicIdNameMap).filter((key) => key.endsWith(topic.name)).length;
                    topicIdNameMap[topic.id] = `Course ${existingCount + 1} -` + topic.name;
                });

                console.log('topicIdNameMap', topicIdNameMap);
                const mappedData = mapStudentAnalyticsData(apiData.resultObject, topicIdNameMap);
                setData(mappedData);
            }
        }

        mapData();
    }, [apiData]);

    useEffect(() => {
        if (!currentClassroomId && classroomsData && classroomsData['resultObject'].length > 0) {
            setCurrentClassroomId(classroomsData['resultObject'][0].id);
        }
    }, [currentClassroomId, classroomsData]);

    const LoadingState = ({ message }: { message: string }) => (
        <motion.div
            className="flex justify-center items-center min-h-[calc(100vh-4rem)]"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
        >
            <div className="text-center space-y-4">
                <div className="relative">
                    <Loader2 className="w-12 h-12 animate-spin mx-auto text-primary" />
                    <motion.div
                        className="absolute inset-0"
                        animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.5, 1, 0.5]
                        }}
                        transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                        }}
                    >
                        <Loader2 className="w-12 h-12 mx-auto text-primary/30" />
                    </motion.div>
                </div>
                <p className="text-muted-foreground font-medium">{message}</p>
            </div>
        </motion.div>
    );

    if (isClassroomsLoading) {
        return <LoadingState message="Loading classrooms..." />;
    }

    if (isLoading) {
        return <LoadingState message="Loading your analytics..." />;
    }

    return (
        <motion.div
            className={cn(
                "min-h-screen relative overflow-hidden p-4 md:p-6",
                isStudentMode ? "bg-blue-50/5" : "bg-slate-50/50"
            )}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
        >

            <motion.div className="w-full max-w-[1800px] mx-auto space-y-8 relative z-10 px-4 md:px-8">
                {/* Index Cards Section */}
                <motion.div
                    className="mb-8 mt-4"
                    variants={staggerContainer}
                    initial="initial"
                    animate="animate"
                >
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <motion.div variants={fadeInUp}>
                            <IndexCard
                                title="Employability Index"
                                value={80}
                            />
                        </motion.div>
                        <motion.div variants={fadeInUp}>
                            <IndexCard
                                title="Higher Education Index"
                                value={80}
                            />
                        </motion.div>
                        <motion.div variants={fadeInUp}>
                            <IndexCard
                                title="Sustainability Index"
                                value={80}
                            />
                        </motion.div>
                        <motion.div variants={fadeInUp}>
                            <IndexCard
                                title="Leadership Index"
                                value={80}
                            />
                        </motion.div>
                    </div>
                </motion.div>

                {/* Progress Chart */}
                <motion.div
                    variants={fadeInUp}
                    initial="initial"
                    animate="animate"
                    className="mb-8"
                >
                    <ProgressChart />
                </motion.div>

                {/* Skills Analysis Components */}
                <div className="flex flex-col lg:flex-row gap-6 mb-8">
                    {/* Top Skill Tags - 60% width */}
                    <motion.div
                        className="w-full lg:w-[60%]"
                        variants={fadeInUp}
                        initial="initial"
                        animate="animate"
                    >
                        <TopSkillTags className="md:h-[450px]" />
                    </motion.div>

                    {/* Skills Mastery Map - 40% width */}
                    <motion.div
                        className="w-full lg:w-[40%]"
                        variants={fadeInUp}
                        initial="initial"
                        animate="animate"
                    >
                        <SkillsMasteryMap className="md:h-[450px]" />
                    </motion.div>
                </div>

                {/* Assessment Type Performance */}
                <motion.div
                    className="w-full mb-8"
                    variants={fadeInUp}
                    initial="initial"
                    animate="animate"
                >
                    <AssessmentTypePerformance />
                </motion.div>

                {/* Index Contribution and Peer Performance */}
                <div className="flex flex-col lg:flex-row gap-6 mb-8">
                    {/* Index Contribution Breakdown - 60% width */}
                    <motion.div
                        className="w-full lg:w-[60%]"
                        variants={fadeInUp}
                        initial="initial"
                        animate="animate"
                    >
                        <IndexContributionBreakdown className="md:h-[450px]" />
                    </motion.div>

                    {/* Peer Performance Snapshot - 40% width */}
                    <motion.div
                        className="w-full lg:w-[40%]"
                        variants={fadeInUp}
                        initial="initial"
                        animate="animate"
                    >
                        <PeerPerformanceSnapshot className="md:h-[450px]" />
                    </motion.div>
                </div>

                {/* Learning Consistency and Project Completion */}
                <div className="flex flex-col sm:flex-row gap-6 mb-8">
                    {/* Learning Consistency & Engagement - Left side */}
                    <motion.div
                        className="w-full lg:w-[60%]"
                        variants={fadeInUp}
                        initial="initial"
                        animate="animate"
                    >
                        <LearningConsistency className="md:h-[450px]" />
                    </motion.div>

                    {/* Project Completion - Right side */}
                    <motion.div
                        className="w-full lg:w-[40%] mb-2"
                        variants={fadeInUp}
                        initial="initial"
                        animate="animate"
                    >
                        <ProjectCompletion className="md:h-[150px]" />


                        <div className="mt-3">
                            <motion.div
                                variants={fadeInUp}
                                initial="initial"
                                animate="animate"
                            >
                                <FeedbackList className="md:h-[288px]" />
                            </motion.div>
                        </div>


                    </motion.div>
                    {/* Feedback List - Full width */}

                </div>



                {/* <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
                    <div className="flex items-center gap-6">
                        <motion.img
                            src={FlyingGifUrl}
                            alt="Analytics"
                            className="w-16 h-16 object-contain hidden sm:block"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{
                                type: "spring",
                                stiffness: 260,
                                damping: 20
                            }}
                        />
                        <motion.div className="relative">
                            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                                Learning Analytics
                            </h1>
                            <p className="text-muted-foreground mt-3 text-base md:text-lg max-w-lg">
                                Track your progress and performance insights with detailed metrics and visualizations
                            </p>
                        </motion.div>
                    </div> */}

                {/* <StudentModeToggle
                        isStudentMode={isStudentMode}
                        onToggle={setIsStudentMode}
                    />
                </div> */}

                {/* {schoolId && classroomsData && (
                    <motion.div
                        className="flex items-center gap-4"
                        variants={fadeInUp}
                    >
                        <label htmlFor="classroom-select" className="text-muted-foreground font-medium">
                            Select Classroom:
                        </label>
                        <div className="relative">
                            <select
                                id="classroom-select"
                                className="appearance-none px-4 py-2 pr-10 bg-card border rounded-lg shadow-sm focus:ring-2 focus:ring-primary/20 outline-none transition-all duration-200"
                                value={currentClassroomId || ''}
                                onChange={(e) => setCurrentClassroomId(e.target.value)}
                            >
                                {classroomsData['resultObject'].map((classroom: { id: string, name: string }) => (
                                    <option key={classroom.id} value={classroom.id}>
                                        {classroom.name}
                                    </option>
                                ))}
                            </select>
                            <ChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
                        </div>
                    </motion.div>
                )} */}



                {/* {data && (
                    <motion.div
                        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 relative"
                        variants={staggerContainer}
                    >
                        <motion.div variants={fadeInUp}>
                            <StatCard
                                title="Average Attempts"
                                value={data.attemptCounts.average}
                                icon={Activity}
                                className="bg-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-[1.02]"
                            />
                        </motion.div>
                        <motion.div variants={fadeInUp}>
                            <StatCard
                                title="Minimum Attempts"
                                value={data.attemptCounts.min}
                                icon={Target}
                                className="bg-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-[1.02]"
                            />
                        </motion.div>
                        <motion.div variants={fadeInUp}>
                            <StatCard
                                title="Maximum Attempts"
                                value={data.attemptCounts.max}
                                icon={TrendingUp}
                                className="bg-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-[1.02]"
                            />
                        </motion.div>
                        <motion.div variants={fadeInUp}>
                            <StatCard
                                title="Total Topics"
                                value={data.topicRepetitions.length}
                                icon={BookOpen}
                                className="bg-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-[1.02]"
                            />
                        </motion.div>
                    </motion.div>
                )} */}

                {/* {data && (
                    <motion.div
                        className="grid gap-8 mt-12"
                        variants={staggerContainer}
                    > */}
                {/* Performance Section */}
                {/* <motion.div variants={fadeInUp}>
                            <div className="relative">
                                <div className="flex items-center gap-3 mb-6">
                                    <div className="p-2 rounded-full bg-blue-100">
                                        <TrendingUp className="w-6 h-6 text-blue-500" />
                                    </div>
                                    <h2 className="text-2xl font-semibold text-blue-500">
                                        Weekly Performance Trend
                                    </h2>
                                </div>
                                <motion.div
                                    className="bg-white rounded-3xl shadow-lg"
                                    whileHover={{ y: -5 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    <div className="relative w-full h-[400px] sm:h-[450px] md:h-[500px] p-2 sm:p-4">
                                        <LineChart
                                            data={data.weeklyScores}
                                            xLabel="Date"
                                            yLabel="Score"
                                            title=""
                                            studentMode={isStudentMode}
                                        />
                                    </div>
                                </motion.div>
                            </div>
                        </motion.div> */}

                {/* Topics Section */}
                {/* <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <motion.div variants={fadeInUp}>
                                <div className="relative">
                                    <div className="flex items-center gap-3 mb-6">
                                        <div className="p-2 rounded-full bg-purple-100">
                                            <Clock className="w-6 h-6 text-purple-500" />
                                        </div>
                                        <h2 className="text-2xl font-semibold text-purple-500">
                                            Quiz Duration by Topic
                                        </h2>
                                    </div>
                                    <motion.div
                                        className="bg-white rounded-3xl shadow-lg"
                                        whileHover={{ y: -5 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        <div className="p-6 md:p-8">
                                            <div className="h-[350px] lg:h-[400px]">
                                                <BarChart
                                                    data={data.quizDurations}
                                                    xLabel="Topic"
                                                    yLabel="Minutes"
                                                    title=""
                                                    height={400}
                                                    studentMode={isStudentMode}
                                                />
                                            </div>
                                        </div>
                                    </motion.div>
                                </div>
                            </motion.div>

                            <motion.div variants={fadeInUp}>
                                <div className="relative">
                                    <div className="flex items-center gap-3 mb-6">
                                        <div className="p-2 rounded-full bg-orange-100">
                                            <RepeatIcon className="w-6 h-6 text-orange-500" />
                                        </div>
                                        <h2 className="text-2xl font-semibold text-orange-500">
                                            Topic Repetitions
                                        </h2>
                                    </div>
                                    <motion.div
                                        className="bg-white rounded-3xl shadow-lg"
                                        whileHover={{ y: -5 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        <div className="p-6 md:p-8">
                                            <div className="h-[350px] lg:h-[400px]">
                                                <BarChart
                                                    data={data.topicRepetitions}
                                                    xLabel="Topic"
                                                    yLabel="Count"
                                                    title=""
                                                    height={400}
                                                    horizontal={true}
                                                    studentMode={isStudentMode}
                                                />
                                            </div>
                                        </div>
                                    </motion.div>
                                </div>
                            </motion.div>
                        </div>

                        {/* Patterns Section */}
                {/* <motion.div variants={fadeInUp}>
                                <div className="relative">
                                    <div className="flex items-center gap-3 mb-6">
                                        <div className="p-2 rounded-full bg-cyan-100">
                                            <Target className="w-6 h-6 text-cyan-500" />
                                        </div>
                                        <h2 className="text-2xl font-semibold text-cyan-500">
                                            Success Rate Analysis
                                        </h2>
                                    </div>
                                    <motion.div     
                                        className="bg-white rounded-3xl shadow-lg"
                                        whileHover={{ y: -5 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        <div className="p-6 md:p-8">
                                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
                                                <div className="h-[500px]">
                                                    <DoughnutChart
                                                        data={data.difficultyPassCounts}
                                                        title="By Difficulty Level"
                                                        width={600}
                                                        height={500}
                                                        studentMode={isStudentMode}
                                                    />
                                                </div>
                                                <div className="h-[500px]">
                                                    <DoughnutChart
                                                        data={data.questionTypePassCounts}
                                                        title="By Question Type"
                                                        width={600}
                                                        height={500}
                                                        studentMode={isStudentMode}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </motion.div>
                                </div>
                            </motion.div>  */}
            </motion.div>
            {/* )} */}
        </motion.div >
    );
};

export default StudentAnalytics;
