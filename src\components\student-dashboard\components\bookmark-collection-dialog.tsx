import React, { useState, useEffect } from 'react';
import { Plus, BookmarkIcon } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import {
    useLazyGetBookmarkCollectionQuery,
    useCreateBookmarkCollectionMutation,
    useCreateBookmarkMutation
} from '@/APIConnect';

interface BookmarkCollection {
    id: string;
    bookmarkCollectionName: string;
}

interface BookmarkCollectionDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    topicId: string;
    topicName: string;
    courseName: string;
    chapterName: string;
    onBookmarkCreated?: () => void;
}

const BookmarkCollectionDialog: React.FC<BookmarkCollectionDialogProps> = ({
    open,
    onOpenChange,
    topicId,
    topicName,
    courseName,
    chapterName,
    onBookmarkCreated
}) => {
    const [selectedCollectionId, setSelectedCollectionId] = useState<string>('NO_COLLECTION');
    const [newCollectionName, setNewCollectionName] = useState('');
    const [isCreatingCollection, setIsCreatingCollection] = useState(false);
    const [isCreatingBookmark, setIsCreatingBookmark] = useState(false);

    // API Hooks
    const [getCollections, { data: collectionsData }] = useLazyGetBookmarkCollectionQuery();
    const [createCollection] = useCreateBookmarkCollectionMutation();
    const [createBookmark] = useCreateBookmarkMutation();

    // Get collections from API response
    const collections: BookmarkCollection[] = collectionsData?.resultObject || [];

    useEffect(() => {
        if (open) {
            console.log('Dialog opened, fetching collections...');
            getCollections({});
        }
    }, [open, getCollections]);

    // Log collections when they change
    useEffect(() => {
        console.log('Collections updated:', collections);
    }, [collections]);

    const handleCreateCollection = async () => {
        if (!newCollectionName.trim()) return;

        setIsCreatingCollection(true);
        try {
            await createCollection({ name: newCollectionName.trim() });
            setNewCollectionName('');
            getCollections({}); // Refresh collections
        } catch (error) {
            console.error('Error creating collection:', error);
        } finally {
            setIsCreatingCollection(false);
        }
    };

    const handleCreateBookmark = async () => {
        if (!topicId) {
            console.error('Missing topic ID:', { topicId });
            return;
        }

        setIsCreatingBookmark(true);

        // Handle the special "NO_COLLECTION" value
        const isIndividualBookmark = selectedCollectionId === "NO_COLLECTION" || !selectedCollectionId;

        try {

            // For individual bookmarks, try omitting the bookmarkCollectionId field entirely
            const bookmarkData: any = {
                topicId: topicId
            };

            // Only add bookmarkCollectionId if it's not an individual bookmark
            if (!isIndividualBookmark) {
                bookmarkData.bookmarkCollectionId = selectedCollectionId;
            }

            console.log('Creating bookmark with data:', bookmarkData);
            console.log('Is individual bookmark (no collection):', isIndividualBookmark);
            console.log('Collection ID:', isIndividualBookmark ? 'None (Individual bookmark)' : selectedCollectionId);
            console.log('Topic ID:', topicId);

            const result = await createBookmark(bookmarkData);
            console.log('Bookmark creation result:', result);

            // Check if there was an error
            if (result.error) {
                console.error('API Error Response:', result.error);
                throw new Error(`API Error: ${JSON.stringify(result.error)}`);
            }

            onBookmarkCreated?.();
            onOpenChange(false);
            setSelectedCollectionId('NO_COLLECTION'); // Reset to default
        } catch (error: any) {
            console.error('Error creating bookmark:', error);
            console.error('Error details:', error?.data || error?.message);

            // If individual bookmark failed, try using or creating a default collection
            if (isIndividualBookmark) {
                console.log('Individual bookmark failed, trying to use/create default collection...');
                try {
                    // First, check if "Individual Bookmarks" collection already exists
                    let defaultCollectionId = null;
                    const existingCollection = collections.find(c => c.bookmarkCollectionName === "Individual Bookmarks");

                    if (existingCollection) {
                        console.log('Found existing Individual Bookmarks collection:', existingCollection.id);
                        defaultCollectionId = existingCollection.id;
                    } else {
                        // Create a default "Individual Bookmarks" collection
                        console.log('Creating new Individual Bookmarks collection...');
                        const defaultCollectionResult = await createCollection({ name: "Individual Bookmarks" });
                        console.log('Default collection created:', defaultCollectionResult);

                        if (defaultCollectionResult.data) {
                            defaultCollectionId = defaultCollectionResult.data.resultObject?.id || defaultCollectionResult.data.id;
                            // Refresh collections to include the new one
                            getCollections({});
                        }
                    }

                    if (defaultCollectionId) {
                        // Retry bookmark creation with the default collection
                        const retryBookmarkData = {
                            bookmarkCollectionId: defaultCollectionId,
                            topicId: topicId
                        };
                        console.log('Retrying bookmark creation with default collection:', retryBookmarkData);

                        const retryResult = await createBookmark(retryBookmarkData);
                        if (!retryResult.error) {
                            console.log('Bookmark created successfully with default collection');
                            onBookmarkCreated?.();
                            onOpenChange(false);
                            setSelectedCollectionId('NO_COLLECTION');
                            return;
                        }
                    }
                } catch (retryError) {
                    console.error('Failed to create default collection or retry bookmark:', retryError);
                }
            }
        } finally {
            setIsCreatingBookmark(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <BookmarkIcon className="h-5 w-5" />
                        Add to Bookmark Collection
                    </DialogTitle>
                </DialogHeader>
                
                <div className="space-y-4">
                    {/* Topic Info */}
                    <div className="bg-gray-50 p-3 rounded-lg">
                        <h4 className="font-medium text-sm text-gray-900">{topicName}</h4>
                        <p className="text-xs text-gray-600">{courseName} - {chapterName}</p>
                    </div>

                    {/* Collection Selection */}
                    <div className="space-y-2">
                        <label className="text-sm font-medium">Select Collection (Optional)</label>
                        <Select value={selectedCollectionId} onValueChange={setSelectedCollectionId}>
                            <SelectTrigger className="w-full">
                                <SelectValue placeholder="Choose a collection or leave empty for individual bookmark" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="NO_COLLECTION">Individual Bookmark (No Collection)</SelectItem>
                                {collections.map((collection) => (
                                    <SelectItem key={collection.id} value={collection.id}>
                                        {collection.bookmarkCollectionName}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <p className="text-xs text-gray-500">
                            Select "Individual Bookmark" to create without a collection, or choose a collection to organize your bookmarks.
                        </p>
                    </div>

                    {/* Create New Collection */}
                    <div className="space-y-2">
                        <label className="text-sm font-medium">Or Create New Collection</label>
                        <div className="flex gap-2">
                            <Input
                                placeholder="Collection name"
                                value={newCollectionName}
                                onChange={(e) => setNewCollectionName(e.target.value)}
                                onKeyDown={(e) => e.key === 'Enter' && handleCreateCollection()}
                            />
                            <Button
                                onClick={handleCreateCollection}
                                disabled={!newCollectionName.trim() || isCreatingCollection}
                                size="sm"
                            >
                                <Plus className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end gap-2 pt-4">
                        <Button
                            variant="outline"
                            onClick={() => onOpenChange(false)}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleCreateBookmark}
                            disabled={isCreatingBookmark}
                            className="bg-[#347468] hover:bg-[#2a5d54]"
                        >
                            {isCreatingBookmark ? 'Adding...' : 'Add Bookmark'}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default BookmarkCollectionDialog;
