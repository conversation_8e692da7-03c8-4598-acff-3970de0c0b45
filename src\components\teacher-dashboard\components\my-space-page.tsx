import React, { useState, useMemo, useEffect } from 'react';
import { Plus, Trash, FolderPlus, Upload, Search, Download, Calendar, FileText, Folder, HardDrive, Image, Film, Music, File } from 'lucide-react';
// Remove old file upload hook import
// import { useFileUpload } from '@/lib/utils';
import { FileFolderSDK } from '@/lib/FileFolderSdk';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

interface Document {
    id: string;
    name: string;
    folder: string;
    size: string;
    type: string;
    createdAt: Date;
    url?: string;
    previewUrl?: string;
}

const FilePreview = ({ file }: { file: File }) => {
    const [preview, setPreview] = useState<string | null>(null);

    React.useEffect(() => {
        if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
                setPreview(reader.result as string);
            };
            reader.readAsDataURL(file);
        }
        return () => setPreview(null);
    }, [file]);

    const getFileIcon = () => {
        if (file.type.startsWith('image/')) return <Image className="w-8 h-8" />;
        if (file.type.startsWith('video/')) return <Film className="w-8 h-8" />;
        if (file.type.startsWith('audio/')) return <Music className="w-8 h-8" />;
        if (file.type.includes('pdf')) return <FileText className="w-8 h-8" />;
        return <File className="w-8 h-8" />;
    };

    return (
        <div className="relative group">
            {file.type.startsWith('image/') && preview ? (
                <img
                    src={preview}
                    alt={file.name}
                    className="w-full h-32 object-cover rounded-lg"
                />
            ) : (
                <div className="w-full h-32 bg-muted/30 rounded-lg flex items-center justify-center">
                    {getFileIcon()}
                </div>
            )}
            <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                <p className="text-white text-sm truncate max-w-[90%] px-2">{file.name}</p>
            </div>
        </div>
    );
};

const MySpacePage = () => {
    const [documents, setDocuments] = useState<Document[]>([]);
    const [folders, setFolders] = useState<string[]>(['General']);
    const [newFolder, setNewFolder] = useState('');
    const [selectedFolder, setSelectedFolder] = useState('All');
    const [searchQuery, setSearchQuery] = useState('');
    const fileInputRef = React.useRef<HTMLInputElement>(null);
    // New progress & uploading state
    const [progress, setProgress] = useState(0);
    const [uploading, setUploading] = useState(false);
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

    // Create SDK instance
    const fileFolderSDK = useMemo(() => new FileFolderSDK(), []);

    // Load folders on mount
    useEffect(() => {
        fileFolderSDK.listFilesAndDirectories()
            .then(data => {
                // Process file list and build documents array
                const docs: Document[] = data.fileAndDirectoryList.reduce((acc: Document[], item: string) => {
                    if (item.includes('.') && item.includes('/')) {
                        const parts = item.split('/');
                        const folder = parts[0];
                        const fileName = parts.slice(1).join('/');
                        acc.push({
                            id: item,
                            name: fileName,
                            folder,
                            size: "Unknown",
                            type: fileName.split('.').pop() || "Unknown",
                            createdAt: new Date(),
                            url: ""
                        });
                    } else if (item.includes('.') && !item.includes('/')) {
                        acc.push({
                            id: item,
                            name: item,
                            folder: "General",
                            size: "Unknown",
                            type: item.split('.').pop() || "Unknown",
                            createdAt: new Date(),
                            url: ""
                        });
                    }
                    return acc;
                }, [] as Document[]);
                setDocuments(docs);
                setFolders(data.directories || ['General']);
            })
            .catch(err => console.error(err));
    }, [fileFolderSDK]);

    const addFolder = async () => {
        if (newFolder && !folders.includes(newFolder)) {
            try {
                await fileFolderSDK.createDirectory(newFolder);
                setFolders(prev => [...prev, newFolder]);
                setNewFolder('');
            } catch (error) {
                console.error('Failed to create folder:', error);
            }
        }
    };

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (files) {
            setSelectedFiles(Array.from(files));
            setUploading(true);

            try {
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const uniquePrefix = Date.now();
                    const folderPrefix = (selectedFolder !== 'All' && selectedFolder !== 'General') ? `${selectedFolder}/` : '';
                    const fileName = `${folderPrefix}${uniquePrefix}-${file.name}`;
                    const url = await fileFolderSDK.uploadFile(file, fileName, (prog) => setProgress(prog));

                    const newDoc: Document = {
                        id: uniquePrefix.toString(),
                        name: file.name,
                        folder: selectedFolder === 'All' ? 'General' : selectedFolder,
                        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
                        type: file.type || 'Unknown',
                        createdAt: new Date(),
                        url: url
                    };
                    setDocuments(prev => [newDoc, ...prev]);
                }
            } catch (error) {
                console.error('Upload failed:', error);
            } finally {
                setUploading(false);
                setSelectedFiles([]);
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
            }
        }
    };

    const deleteDocument = (id: string) => {
        setDocuments(documents.filter((doc) => doc.id !== id));
    };

    const filteredDocuments = useMemo(() => {
        let filtered = documents;
        if (selectedFolder !== 'All') {
            filtered = filtered.filter((doc) => doc.folder === selectedFolder);
        }
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            filtered = filtered.filter(
                doc =>
                    doc.name.toLowerCase().includes(query) ||
                    doc.type.toLowerCase().includes(query)
            );
        }
        return filtered;
    }, [documents, selectedFolder, searchQuery]);

    const totalSize = useMemo(() => {
        return documents.reduce((acc, doc) => {
            const size = parseFloat(doc.size);
            return acc + size;
        }, 0).toFixed(2);
    }, [documents]);

    return (
        <div className="container mx-auto p-6">
            <div className="flex flex-col space-y-8">
                {/* Header */}
                <Card className="p-6">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
                        <div>
                            <h1 className="text-3xl font-semibold tracking-tight flex items-center gap-2">
                                <FileText className="h-7 w-7" />
                                My Space
                            </h1>
                            <p className="text-sm text-muted-foreground mt-1">Manage your files and folders</p>
                        </div>
                        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full md:w-auto">
                            <div className="relative flex-1 sm:flex-none">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search files..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="pl-8 min-w-[300px]"
                                />
                            </div>
                            <input
                                type="file"
                                ref={fileInputRef}
                                onChange={handleFileUpload}
                                className="hidden"
                                multiple
                            />
                            <div className="flex-shrink-0 space-y-2">
                                <Button
                                    onClick={() => fileInputRef.current?.click()}
                                    className="flex-shrink-0"
                                    disabled={uploading}
                                >
                                    <Upload className="w-4 h-4 mr-2" />
                                    {uploading ? 'Uploading...' : 'Upload Files'}
                                </Button>
                                {uploading && (
                                    <Progress value={progress} className="w-full" />
                                )}
                            </div>
                        </div>
                    </div>
                </Card>

                {/* Stats */}
                <div className="grid gap-4 md:grid-cols-3">
                    <Card className="p-6">
                        <div className="flex items-center space-x-4">
                            <div className="p-3 bg-primary/10 rounded-lg">
                                <FileText className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                                <div className="text-2xl font-bold">{documents.length}</div>
                                <div className="text-sm text-muted-foreground">Total Files</div>
                            </div>
                        </div>
                    </Card>
                    <Card className="p-6">
                        <div className="flex items-center space-x-4">
                            <div className="p-3 bg-primary/10 rounded-lg">
                                <Folder className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                                <div className="text-2xl font-bold">{folders.length}</div>
                                <div className="text-sm text-muted-foreground">Folders</div>
                            </div>
                        </div>
                    </Card>
                    <Card className="p-6">
                        <div className="flex items-center space-x-4">
                            <div className="p-3 bg-primary/10 rounded-lg">
                                <HardDrive className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                                <div className="text-2xl font-bold">{totalSize} MB</div>
                                <div className="text-sm text-muted-foreground">Total Storage</div>
                            </div>
                        </div>
                    </Card>
                </div>

                {/* Main Content */}
                <Card className="p-6">
                    <div className="space-y-6">
                        {/* Folder Management */}
                        <div className="flex flex-col md:flex-row gap-4">
                            <div className="flex-1 space-y-4 bg-muted/30 p-4 rounded-2xl">
                                <div className="flex items-center gap-2">
                                    <Input
                                        placeholder="New Folder"
                                        value={newFolder}
                                        onChange={(e) => setNewFolder(e.target.value)}
                                        className="rounded-xl border-muted"
                                    />
                                    <Button onClick={addFolder} size="icon" className="rounded-xl">
                                        <FolderPlus className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                            <div className="md:w-1/3">
                                <Select value={selectedFolder} onValueChange={setSelectedFolder}>
                                    <SelectTrigger className="rounded-xl border-muted">
                                        <SelectValue placeholder="Filter by folder" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="All">All Folders</SelectItem>
                                        {folders.map((folder) => (
                                            <SelectItem key={folder} value={folder}>
                                                {folder}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        {/* Preview Grid */}
                        {selectedFiles.length > 0 && (
                            <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-4 mb-4">
                                {selectedFiles.map((file, index) => (
                                    <FilePreview key={index} file={file} />
                                ))}
                            </div>
                        )}

                        {/* File List */}
                        <ScrollArea className="h-[calc(100vh-700px)]">
                            <AnimatePresence>
                                {filteredDocuments.length === 0 ? (
                                    <motion.div
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        exit={{ opacity: 0 }}
                                        className="flex flex-col items-center justify-center h-[200px] text-muted-foreground"
                                    >
                                        <Search className="w-8 h-8 mb-2" />
                                        <p>No files found</p>
                                    </motion.div>
                                ) : (
                                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                        {filteredDocuments.map((doc) => (
                                            <motion.div
                                                key={doc.id}
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                exit={{ opacity: 0, y: -20 }}
                                                transition={{ duration: 0.2 }}
                                            >
                                                <Card className="rounded-2xl border-none bg-muted/30 hover:bg-muted/50 transition-all hover:shadow-md">
                                                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                                        <div className="flex items-center gap-2">
                                                            {doc.type.startsWith('image/') ? (
                                                                <Image className="h-4 w-4" />
                                                            ) : doc.type.startsWith('video/') ? (
                                                                <Film className="h-4 w-4" />
                                                            ) : doc.type.startsWith('audio/') ? (
                                                                <Music className="h-4 w-4" />
                                                            ) : doc.type.includes('pdf') ? (
                                                                <FileText className="h-4 w-4" />
                                                            ) : (
                                                                <File className="h-4 w-4" />
                                                            )}
                                                            <CardTitle className="text-lg font-semibold truncate">
                                                                {doc.name}
                                                            </CardTitle>
                                                        </div>
                                                        <div className="flex gap-2">
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                className="hover:bg-blue-100 hover:text-blue-600 rounded-xl"
                                                            >
                                                                <Download className="h-4 w-4" />
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => deleteDocument(doc.id)}
                                                                className="hover:bg-red-100 hover:text-red-600 rounded-xl"
                                                            >
                                                                <Trash className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </CardHeader>
                                                    <CardContent>
                                                        <div className="flex justify-between items-center">
                                                            <div className="flex gap-2">
                                                                <span className="text-xs px-2 py-1 bg-primary/10 text-primary rounded-full flex items-center">
                                                                    <Folder className="w-3 h-3 mr-1" /> {doc.folder}
                                                                </span>
                                                                <span className="text-xs px-2 py-1 bg-muted text-muted-foreground rounded-full">
                                                                    {doc.size}
                                                                </span>
                                                            </div>
                                                            <span className="text-xs text-muted-foreground flex items-center">
                                                                <Calendar className="w-3 h-3 mr-1" />
                                                                {doc.createdAt.toLocaleDateString()}
                                                            </span>
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            </motion.div>
                                        ))}
                                    </div>
                                )}
                            </AnimatePresence>
                        </ScrollArea>
                    </div>
                </Card>
            </div>
        </div>
    );
};

export default MySpacePage;
