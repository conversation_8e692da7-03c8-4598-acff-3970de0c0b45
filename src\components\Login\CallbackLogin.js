import React, { useEffect } from "react";
import { useAuth0 } from "@auth0/auth0-react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { setUserToken } from "../User/userSlice";
import { JWTToken } from "../../constant/AppConstant";

const CallbackLogin = () => {
  const { isLoading, isAuthenticated, getAccessTokenSilently, user, error } =
    useAuth0();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  useEffect(() => {
    const handleCallback = async () => {
      if (!isLoading && isAuthenticated && user) {
        try {
          console.log("Getting access token...");
          const token = await getAccessTokenSilently({
            authorizationParams: {
              audience: "learnido.dev.auth.api.v1",
              scope: "openid profile",
            },
          });
          console.log("Token received:", !!token);

          const tokenData = {
            accessToken: token,
            user: user,
          };

          localStorage.setItem(JWTToken, JSON.stringify(tokenData));
          dispatch(setUserToken(tokenData));
          navigate("/", { replace: true });
        } catch (error) {
          console.error("Error in callback:", error);
          navigate("/login", { replace: true });
        }
      } else if (!isLoading && error) {
        console.error("Auth error:", error);
        navigate("/login", { replace: true });
      }
    };

    handleCallback();
  }, [
    isLoading,
    isAuthenticated,
    user,
    getAccessTokenSilently,
    navigate,
    dispatch,
    error,
  ]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Completing login...</h2>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        </div>
      </div>
    );
  }

  return null;
};

export default CallbackLogin;
