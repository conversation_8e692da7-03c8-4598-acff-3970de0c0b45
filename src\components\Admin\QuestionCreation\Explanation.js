import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import SunEditor from "suneditor-react";
import useDebounce from "@/helpers/useDebounce";
import { setQuestionsValue } from "components/Admin/QuestionCreation/questionSlice";

const Explanation = ({ handleExplanationComplete, handleCancel }) => {
  const dispatch = useDispatch();
  const questionText = useSelector(
    (state) => state.questions.questions.questionText
  );
  const explanation = useSelector(
    (state) => state.questions.questions.explanation
  );
  const answers = useSelector((state) => state.questions.questions.answers);
  const questionType = useSelector(
    (state) => state.questions.questions.questionType
  );
  const questionText2 = useSelector(
    (state) => state.questions.questions.questionText2
  );
  const correctAnswer = useSelector(
    (state) => state.questions.questions.correctAnswer
  );
  const [explanationData, setExplanationData] = useState("");
  const explanationDebounceVal = useDebounce(explanationData);

  useEffect(() => {
    setExplanationData(explanation);
  }, []);

  useEffect(() => {
    const obj = {
      explanation: "",
    };

    if (explanationDebounceVal.replace(/<[^>]+>/g, "") !== "") {
      obj["explanation"] = explanationDebounceVal;
    }
    dispatch(setQuestionsValue(obj));
  }, [explanationDebounceVal]);

  const handleNext = () => {
    handleExplanationComplete(3);
  };

  const getCheckedValue = (indexval, ans) => {
    switch (questionType) {
      case "SINGLECHOICE":
        return singleChoiceChecked(indexval);
      case "MULTICHOICE":
        return multiChoiceChecked(ans);
    }
  };

  const singleChoiceChecked = (indexval) => {
    return indexval === correctAnswer ? true : false;
  };

  const multiChoiceChecked = (ans) => {
    return correctAnswer?.includes(ans);
  };

  return (
    <>
      <p className="exp">Question </p>
      <div
        className="html-content question-text"
        dangerouslySetInnerHTML={{
          __html: questionText + " " + questionText2,
        }}
      />
      <div style={{ marginTop: 10 }}>
        <span className="exp">Answers</span>
      </div>

      {questionType === "MATCH" &&
        correctAnswer &&
        Object.keys(correctAnswer)?.length > 0 && (
          <>
            {Object.keys(correctAnswer).map((keyName, index) => (
              <div className="answer-option">
                <div
                  key={index}
                  style={{ display: "flex" }}
                  className="html-content"
                >
                  <div
                    className="html-content answer-text"
                    dangerouslySetInnerHTML={{
                      __html: keyName,
                    }}
                  />
                  <div
                    className="html-content answer-text"
                    dangerouslySetInnerHTML={{
                      __html: correctAnswer[keyName],
                    }}
                  />
                </div>
              </div>
            ))}
          </>
        )}

      {correctAnswer !== null &&
        (questionType === "TEXTINPUT" || questionType === "BINARY") && (
          <div className="answer-option">
            <div
              className="html-content answer-text"
              dangerouslySetInnerHTML={{
                __html: correctAnswer,
              }}
            />
          </div>
        )}
      {answers?.map((item, index) => (
        <div key={item.id} className="answer-option">
          <div className="checkbox-toggle">
            {(questionType === "MULTICHOICE" ||
              questionType === "SINGLECHOICE" ||
              questionType === "BINARY") && (
              <>
                <input
                  type="checkbox"
                  id={`checkbox-${item.id}`}
                  disabled={true}
                  checked={getCheckedValue(index, item?.answerText)}
                />
                <label
                  htmlFor={`checkbox-${item.id}`}
                  className="toggle-label"
                ></label>
              </>
            )}
          </div>
          <div
            className="html-content answer-text"
            dangerouslySetInnerHTML={{
              __html: item.answerText,
            }}
          />
        </div>
      ))}

      <span className="exp">Explanation</span>
      {/* <div
        className="html-content explanation-text" style={{marginTop:10}}>
          {explanation}
        </div> */}

      <div
        className="html-content explanation-text"
        dangerouslySetInnerHTML={{
          __html: explanation,
        }}
      />

      <div style={{ marginTop: "20px" }}>
        <textarea
          style={{ width: "100%", height: "100px" }}
          value={explanationData}
          onChange={(e) => setExplanationData(e.target.value)}
        />
      </div>

      {/* <SunEditor
        setOptions={{
          buttonList: [
            ["undo", "redo"],
            ["font", "fontSize", "formatBlock"],
            ["bold", "underline", "italic", "strike"],
            ["fontColor", "hiliteColor"],
            ["align", "list", "table"],
            ["link", "image", "video"],
            ["fullScreen", "showBlocks", "codeView"],
            ["preview", "print"],
          ],
          height: "70%",
          minHeight: "100px",
        }}
        setContents={explanation}
        onChange={(textContent) => setExplanationData(textContent)}
      /> */}
      <div className="button-container">
        <div className="align-right">
          <button
            id="cancelbutton"
            className="cancel-button"
            onClick={() => handleCancel()}
          >
            Cancel
          </button>
        </div>
        <div id="nextbutton" className="align-left">
          <button onClick={handleNext} disabled={!explanation.length}>
            Next
          </button>
        </div>
      </div>
    </>
  );
};

export default Explanation;
