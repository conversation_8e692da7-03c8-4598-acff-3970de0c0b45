import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { LoadingAnimation } from "../common/loading-animation"
import SidebarHeader from "./sidebar-header"
import CourseList from "./course-list"
import TopicList from "./topic-list"
import { Topic, SubTopic, ResultObject } from "../../types"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Video } from "lucide-react"
import { Loader2 } from "lucide-react"

interface SidebarContentProps {
    isVisible: boolean
    isMobile: boolean
    isTransitioning: boolean
    loadingType: string | null
    selectedCourse: ResultObject | null
    selectedSubTopic: SubTopic | null
    openChapters: string[]
    openTopics: string[]
    userName: string
    allCourses: any[]
    isCourseLoading: boolean
    isOnline: boolean
    onBackClick: () => void
    onCourseClick: (course: ResultObject) => Promise<void>
    onSubTopicSelect: (subTopic: SubTopic) => void
    onChapterToggle: (chapterId: string) => void
    onTopicToggle: (topicId: string) => void
    isChapterLocked: (chapterId: string) => boolean
    isTopicLocked: (topicId: string) => boolean
    isSubTopicLocked: (subTopicId: string) => boolean
    onJoinLiveSession?: (classroomId: string) => void
    isLoadingLiveSession?: boolean
}

export function SidebarContent({
    isVisible,
    isMobile,
    isTransitioning,
    loadingType,
    selectedCourse,
    selectedSubTopic,
    openChapters,
    openTopics,
    userName,
    allCourses,
    isCourseLoading,
    isOnline,
    onBackClick,
    onCourseClick,
    onSubTopicSelect,
    onChapterToggle,
    onTopicToggle,
    isChapterLocked,
    isTopicLocked,
    isSubTopicLocked,
    onJoinLiveSession,
    isLoadingLiveSession,
}: SidebarContentProps) {
    const [isLiveClassDialogOpen, setIsLiveClassDialogOpen] = useState(false)
    const [classroomId, setClassroomId] = useState("")

    const handleJoinLiveSession = () => {
        if (classroomId.trim() && onJoinLiveSession) {
            onJoinLiveSession(classroomId)
            setIsLiveClassDialogOpen(false)
        }
    }

    return (
        <AnimatePresence mode="wait">
            {isVisible && (
                <motion.div
                    initial={{ x: -320, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    exit={{ x: -320, opacity: 0 }}
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    className={cn(
                        "fixed lg:relative lg:w-80 bg-background border-r z-40 max-w-[85vw] w-full",
                        "h-full flex flex-col"
                    )}
                >
                    <SidebarHeader
                        selectedCourse={selectedCourse}
                        userName={userName}
                        onBackClick={onBackClick}
                    />
                    <ScrollArea className="flex-1">
                        <div className="p-3">
                            {!selectedCourse && (
                                <Button
                                    variant="outline"
                                    className="flex items-center w-full gap-2 mb-4"
                                    onClick={() => setIsLiveClassDialogOpen(true)}
                                >
                                    <Video className="h-4 w-4" />
                                    <span>Join Live Class</span>
                                </Button>
                            )}
                            <Separator className="mb-4" />
                            {isTransitioning ? (
                                <LoadingAnimation type={loadingType} />
                            ) : selectedCourse ? (
                                <TopicList
                                    isLoading={isCourseLoading}
                                    chapters={selectedCourse.chapters}
                                    openChapters={openChapters}
                                    openTopics={openTopics}
                                    selectedSubTopic={selectedSubTopic}
                                    onChapterToggle={onChapterToggle}
                                    onTopicToggle={onTopicToggle}
                                    onSubTopicSelect={onSubTopicSelect}
                                    isChapterLocked={isChapterLocked}
                                    isTopicLocked={isTopicLocked}
                                    isSubTopicLocked={isSubTopicLocked}
                                />
                            ) : (
                                <CourseList
                                    courses={allCourses}
                                    onCourseClick={onCourseClick}
                                    onJoinLiveSession={onJoinLiveSession}
                                    isLoadingLiveSession={isLoadingLiveSession}
                                />
                            )}
                            {isTransitioning ? (
                                <LoadingAnimation type={loadingType} />
                            ) : selectedCourse ? (
                                <TopicList
                                    isLoading={isCourseLoading}
                                    chapters={selectedCourse.chapters}
                                    openChapters={openChapters}
                                    openTopics={openTopics}
                                    selectedSubTopic={selectedSubTopic}
                                    onChapterToggle={onChapterToggle}
                                    onTopicToggle={onTopicToggle}
                                    onSubTopicSelect={onSubTopicSelect}
                                    isChapterLocked={isChapterLocked}
                                    isTopicLocked={isTopicLocked}
                                    isSubTopicLocked={isSubTopicLocked}
                                />
                            ) : (
                                <CourseList
                                    courses={allCourses}
                                    onCourseClick={onCourseClick}
                                    onJoinLiveSession={onJoinLiveSession}
                                    isLoadingLiveSession={isLoadingLiveSession}
                                />
                            )}
                        </div>
                    </ScrollArea>

                    <Dialog open={isLiveClassDialogOpen} onOpenChange={setIsLiveClassDialogOpen}>
                        <DialogContent className="sm:max-w-[425px]">
                            <DialogHeader>
                                <DialogTitle>Join Live Class Session</DialogTitle>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                                <div className="grid gap-2">
                                    <label htmlFor="classroom-id" className="text-sm font-medium">
                                        Enter Classroom ID
                                    </label>
                                    <Input
                                        id="classroom-id"
                                        placeholder="Enter the classroom ID provided by your teacher"
                                        value={classroomId}
                                        onChange={(e) => setClassroomId(e.target.value)}
                                    />
                                </div>
                            </div>
                            <div className="flex justify-end gap-2">
                                <Button variant="outline" onClick={() => setIsLiveClassDialogOpen(false)}>
                                    Cancel
                                </Button>
                                <Button
                                    onClick={handleJoinLiveSession}
                                    disabled={!classroomId.trim() || isLoadingLiveSession}
                                    className="flex items-center gap-2"
                                >
                                    {isLoadingLiveSession ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                    ) : (
                                        <Video className="h-4 w-4" />
                                    )}
                                    Join Session
                                </Button>
                            </div>
                        </DialogContent>
                    </Dialog>
                </motion.div>
            )}
        </AnimatePresence>
    )
}
