import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
// Removed Next.js Image import

interface CourseCardProps {
  id: string;
  name: string;
  classroomId?: string;
  imageUrl?: string; // Optional image URL
  lessonCount?: number; // Optional total lessons
  lessonsCompleted?: number; // Optional completed lessons
  courseProgress: number;
  timeLeft?: string; // Optional time left string e.g., "2 hrs left"
  onClick: (id: string,classroomId?: string) => void;
}

const CourseCard: React.FC<CourseCardProps> = ({
  id,
  classroomId,
  name,
  imageUrl,
  lessonCount = 10, // Default based on design
  lessonsCompleted = 5, // Default based on design
  courseProgress,
  timeLeft = "2 hrs left", // Default based on design
  onClick,
}) => {
  const progressValue = courseProgress ?? 0;

  return (
    <Card
      className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer flex flex-col border border-gray-200 rounded-xl min-h-[170px]"
      onClick={() => onClick(id,classroomId)}
    >
      {/* Course Image */}
      <div className="h-24 bg-gray-100 flex items-center rounded-xl justify-center text-gray-400 overflow-hidden">
        {imageUrl ? (
          <img src={imageUrl} alt={name} className="w-full h-full object-cover " />
        ) : (
          <span className="text-xs">No Image</span>
        )}
      </div>
      {/* Course Content */}
      <CardContent className="p-2 flex-1 flex flex-col justify-between">
        {/* Title */}
        <h4 className="font-semibold mb-1 truncate text-base text-black leading-tight">{name}</h4>
        {/* Progress Info */}
        <div className="text-xs text-black mb-1 flex items-center justify-between">
          <span>{`${lessonsCompleted}/${lessonCount} lessons`}</span>
          <span className="mx-1">|</span>
          <span>{`${progressValue}%`}</span>
          {timeLeft && <span className="ml-auto font-medium text-primary whitespace-nowrap">{timeLeft}</span>}
        </div>
        {/* Progress Bar */}
        <Progress value={progressValue} className="h-[6px] bg-primary-100 [&>div]:bg-primary" />
      </CardContent>
    </Card>
  );
};

export default CourseCard;
