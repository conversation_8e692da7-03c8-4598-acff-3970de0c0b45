import { Loader2 } from "lucide-react"
import { motion } from "framer-motion"

interface LoadingAnimationProps {
    type: string | null
}

export function LoadingAnimation({ type }: LoadingAnimationProps) {
    const getLoadingAnimation = () => {
        switch (type) {
            case 'contentLoad':
                return "https://lernido-bucket.s3.amazonaws.com/animations/yoga_rende.gif";
            case 'topicContent':
                return "https://lernido-bucket.s3.amazonaws.com/animations/eye_rolling.gif";
            case 'courseLoad':
                return "https://lernido-bucket.s3.amazonaws.com/animations/waving.gif";
            default:
                return null;
        }
    };

    const animationUrl = getLoadingAnimation();

    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex flex-col items-center justify-center min-h-[450px] p-8"
        >
            {animationUrl ? (
                <motion.div
                    initial={{ scale: 0.8 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.3 }}
                    className="relative w-[588px] h-[288px] mb-4"
                >
                    <img
                        src={animationUrl}
                        alt="Loading animation"
                        className="w-full h-full object-contain"
                    />
                </motion.div>
            ) : (
                <Loader2 className="w-12 h-12 animate-spin text-primary mb-4" />
            )}
            <p className="text-muted-foreground animate-pulse text-lg">
                Loading...
            </p>
        </motion.div>
    )
}
