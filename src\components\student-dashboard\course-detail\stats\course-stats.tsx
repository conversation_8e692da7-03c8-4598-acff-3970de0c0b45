import React from 'react';
import { Clock, BookOpen, FileSpreadsheet } from 'lucide-react';

interface CourseStatsProps {
    totalHours: number;
    totalTopics: number;
    totalAssessments: number;
}

const CourseStats: React.FC<CourseStatsProps> = ({
    totalHours,
    totalTopics,
    totalAssessments
}) => {
    return (
        <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="font-semibold text-slate-800 mb-4">Course Stats</h2>
            <div className="space-y-4">
                {/* Total Hours */}
                <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-emerald-50 flex items-center justify-center flex-shrink-0">
                        <Clock className="h-5 w-5 text-emerald-600" />
                    </div>
                    <div>
                        <p className="text-sm text-slate-500">Total Hours</p>
                        <p className="font-medium text-slate-800">{totalHours} hours</p>
                    </div>
                </div>

                {/* Total Topics */}
                <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center flex-shrink-0">
                        <BookOpen className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                        <p className="text-sm text-slate-500">Topics</p>
                        <p className="font-medium text-slate-800">{totalTopics} topics</p>
                    </div>
                </div>

                {/* Total Assessments */}
                <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-purple-50 flex items-center justify-center flex-shrink-0">
                        <FileSpreadsheet className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                        <p className="text-sm text-slate-500">Assessments</p>
                        <p className="font-medium text-slate-800">{totalAssessments} quizzes</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CourseStats;
