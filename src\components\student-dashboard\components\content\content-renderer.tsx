import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import Scorm<PERSON>esson from '@/components/scorm-content';
import APIConnect, { useLazyGetContentRelatedTopicDataQuery } from '@/APIConnect';
import { Loader2Icon } from 'lucide-react';
import { useOnlineStatus } from '@/hooks/use-online-status';

interface ContentRendererProps {
    defaultTopicId: string | null;
}

const ContentRenderer = ({ defaultTopicId }: ContentRendererProps) => {
    const [topicId, setTopicId] = React.useState<string | null>(defaultTopicId);
    const [getContentRelatedTopicData, getContentRelatedTopicDataResult] =
        useLazyGetContentRelatedTopicDataQuery();
    
    // Get content data from Redux store
    const contentRelatedTopic = useSelector((state: any) => state.courses.contentRelatedTopic);
    
    const [content, setContent] = React.useState<{
        contentType: string;
        data: any;
    } | null>(null);
    const [isCaching, setIsCaching] = useState(false);
    const isOnline = useOnlineStatus();

    // Define content type mapping
    const contentTypeMap = [
        'SCORM',
        'HTML',
        'AUDIO_URL',
        'VIDEO_URL',
        'SCORM',
    ];

    // Cache the content URL in the service worker
    const cacheContent = async (contentUrl: string, contentId: string) => {
        if ('serviceWorker' in navigator && contentUrl) {
            try {
                setIsCaching(true);
                const registration = await navigator.serviceWorker.ready;
                
                // Determine whether this is a topic or subtopic based on context
                const messageType = defaultTopicId?.includes('subtopic-') 
                    ? 'CACHE_SUBTOPIC_CONTENT' 
                    : 'CACHE_TOPIC_CONTENT';
                
                await registration.active?.postMessage({
                    type: messageType,
                    topicId: contentId,
                    subTopicId: contentId,
                    contentUrl
                });
                console.log('Content cached successfully');
            } catch (error) {
                console.error('Failed to cache content:', error);
            } finally {
                setIsCaching(false);
            }
        }
    };

    React.useEffect(() => {
        // First try to use the data from Redux store if it exists
        if (contentRelatedTopic && Object.keys(contentRelatedTopic).length > 0) {
            // Use the data already fetched and stored in Redux
            const type = contentRelatedTopic.type;
            const contentData = {
                content: contentRelatedTopic.content,
                contentUrl: contentRelatedTopic.contentUrl
            };
            
            setContent({
                contentType: contentTypeMap[type] || 'HTML',
                data: contentData
            });
            
            // If this is a SCORM package, cache it
            if (contentTypeMap[type] === 'SCORM' && contentData.contentUrl) {
                cacheContent(contentData.contentUrl, defaultTopicId!);
            }
        } 
        // Fallback to API call if no data in Redux
        else if (defaultTopicId) {
            getContentRelatedTopicData(defaultTopicId);
        }
    }, [contentRelatedTopic, defaultTopicId]);
    
    // Handle API response for fallback case
    React.useEffect(() => {
        if (getContentRelatedTopicDataResult.data?.resultObject) {
            const resultObject = getContentRelatedTopicDataResult.data.resultObject;
            
            setContent({
                contentType: contentTypeMap[resultObject.type] || 'HTML',
                data: resultObject
            });
            
            // If this is a SCORM package, cache it
            if (contentTypeMap[resultObject.type] === 'SCORM' && resultObject.contentUrl) {
                cacheContent(resultObject.contentUrl, defaultTopicId!);
            }
        }
    }, [getContentRelatedTopicDataResult.data]);

    const getContentDataForType = (type: number, data: any) => {
        const contentType = contentTypeMap[type];

        // Cache the content if it's a SCORM package
        if (contentType === 'SCORM' && data.contentUrl) {
            cacheContent(data.contentUrl, defaultTopicId!);
        }

        switch (contentType) {
            case 'SCORM':
                return (
                    <div>
                        {isCaching && (
                            <div className="mb-4 flex items-center justify-center text-primary">
                                <Loader2Icon className="w-4 h-4 animate-spin mr-2" />
                                <span className="text-sm">Caching content for offline use...</span>
                            </div>
                        )}
                        <ScormLesson url={data.contentUrl} />
                    </div>
                );
            case 'HTML':
                return <div dangerouslySetInnerHTML={{ __html: data.content }} />;
            case 'IMAGE_URL':
                return <img src={data} alt="Topic illustration" />;
            case 'VIDEO_URL':
                if (!isOnline && data.contentUrl.includes('youtube')) {
                    return (
                        <div className="flex flex-col items-center justify-center h-[600px] bg-gray-100 rounded-lg">
                            <p className="text-lg text-gray-600">
                                YouTube videos are not available offline
                            </p>
                        </div>
                    );
                }
                if (data.contentUrl.includes('youtube')) {
                    const videoId = data.contentUrl.split('v=')[1];
                    return (
                        <iframe
                            src={`https://www.youtube.com/embed/${videoId}`}
                            className="w-full h-[600px] rounded-lg shadow-md"
                            title="learning content"
                        />
                    );
                }
                return (
                    <video
                        src={data.contentUrl}
                        controls
                        className="w-full h-[600px] rounded-lg shadow-md"
                        title="learning content"
                    />
                );
            case 'AUDIO_URL':
                return <audio src={data.contentUrl} controls />;
            default:
                return <p>Unsupported content type</p>;
        }
    };

    useEffect(() => {
        if (getContentRelatedTopicDataResult?.data?.resultObject) {
            console.log(getContentRelatedTopicDataResult.data.resultObject);
            const resultObject = getContentRelatedTopicDataResult.data.resultObject;
            setContent({
                contentType: resultObject.type,
                data: getContentDataForType(resultObject.type, resultObject)
            });
        }
    }, [getContentRelatedTopicDataResult]);

    if (!content?.data) {
        return (
            <div className='w-full h-[600px] flex items-center justify-center'>
                <Loader2Icon className='w-10 h-10 animate-spin text-primary' />
            </div>
        );
    }

    return (
        <div className='w-full h-full'>
            {!isOnline && (
                <div className="mb-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <p className="text-yellow-800">
                        You are currently offline. Content that has been previously cached will be available.
                    </p>
                </div>
            )}
            {content.data}
        </div>
    );
};

export default ContentRenderer;
