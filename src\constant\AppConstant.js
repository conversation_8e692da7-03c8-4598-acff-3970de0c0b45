/**
 * Collection of application constants and configurations
 * @module AppConstant
 */

/**
 * Route and section identifiers
 * @constant {string}
 */

/**
 * Authentication configuration for Auth0
 * @constant {string}
 */

/**
 * Authentication URLs
 * @constant {string}
 */

/**
 * Content type mapping
 * @constant {Object.<number, string>}
 */

/**
 * YouTube embed URL prefix
 * @constant {string}
 */

/**
 * Regular expression for HTML content detection
 * @constant {RegExp}
 */

/**
 * Quiz completion messages
 * @constant {string}
 */

/**
 * Binary answer options
 * @constant {Array<{answerText: boolean}>}
 */

/**
 * Collection of animation assets with their URLs
 * @constant {Array<{name: string, url: string}>}
 */

/**
 * Retrieves animation URL by name
 * @param {string} name - The name of the animation to find
 * @returns {string|null} The URL of the animation if found, null otherwise
 */
export const students = "Students";
export const performance = "performance";
export const course = "course";
export const courses = "courses";
export const subjects = "Subjects";
export const chapters = "chapters";
export const chapter = "chapter";
export const topics = "topics";
export const topic = "topic";
export const coursesContent = "CoursesContent";
export const quiz = "quiz";
export const questions = "questions";
export const courseid = "courseid";
export const submit = "submit";

export const originURL = window.location.origin;

export const auth0Domain = "dev-learnido.au.auth0.com";
export const auth0ClientID = "8JJS7xJDYzD1moxQSVFmdurRCbHxVgip";
export const auth0RedirectUri = `${originURL}`;
export const auth0Scope = "openid profile";
export const auth0Audience = "learnido.dev.auth.api.v1";
export const auth0ResponseType = "code";
export const auth0Nonce = "1234567";
export const auth0UseRefreshTokens = true;
export const auth0State = "STATE";
export const auth0Connection = "Dev-Lernido-UP-Authentication";
export const auth0ClientSecret =
  "****************************************************************";
export const auth0RedirectUriHome = `${originURL}`;

export const loginURL = `https://${auth0Domain}/authorize?audience=${auth0Audience}&scope=${auth0Scope}&response_type=${auth0ResponseType}&client_id=${auth0ClientID}&redirect_uri=${auth0RedirectUri}&state=${auth0State}&connection=${auth0Connection}`;
export const logoutURL = `https://${auth0Domain}/v2/logout?client_id=${auth0ClientID}&returnTo=${originURL}`;

export const accessTokenURL = "https://dev-learnido.au.auth0.com/oauth/token?";

export const discussionsBaseUrl = "https://ldfapi.stmin.dev/api";
export const JWTToken = "JWT";

export const contentType = {
  1: "html",
  2: "audio",
  3: "video",
  4: "scorm",
};

export const YtbeURL = "https://www.youtube.com/embed/";

export const isHTMLRegex = /(<([^>]+)>)/i;
export const quizSucMsg1 = "Your next topic";
export const quizSucMsg2 = " is unlocked";

export const quizComMsg = "Congratulation on completing the course";

export const binaryTypes = [
  {
    answerText: true,
  },
  { answerText: false },
];

/**
 * Animation URLs mapping
 * @constant {Array<{name: string, url: string}>}
 * Possible values for name:
 * - "Bike riding continuous"
 * - "Clapping"
 * - "Eye rolling"
 * - "Flying gif"
 * - "Flying lr"
 * - "Flyting bt"
 * - "Gun pose"
 * - "Heart"
 * - "Joy cheers"
 * - "Quiz completion recycling action"
 * - "Solar power boost"
 * - "Thumbs up"
 * - "Waving"
 * - "Yoga render"
 */
export const BikeRidingContinuousUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/bike_riding_continuous.gif";
export const ClappingUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/clapping.gif";
export const EyeRollingUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/eye_rolling.gif";
export const FlyingGifUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/flying_gif.gif";
export const FlyingLrUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/flying_lr.gif";
export const FlytingBtUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/flyting_bt.gif";
export const GunPoseUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/gun_pose.gif";
export const HeartUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/heart.gif";
export const JoyCheersUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/joy_cheers.gif";
export const QuizCompletionRecyclingUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/quiz_completion_recycling_action.gif";
export const SolarPowerBoostUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/solar_power_boost.gif";
export const ThumbsUpUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/thumbs_up.gif";
export const WavingUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/waving.gif";
export const YogaRenderUrl =
  "https://lernido-bucket.s3.amazonaws.com/animations/yoga_rende.gif";
