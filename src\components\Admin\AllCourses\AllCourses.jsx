import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const AllCourses = () => {
  const [selectedCourse, setSelectedCourse] = useState("English Courses");

  const courses = [
    {
      id: 1,
      name: "Complete English",
      description: "Beginner, intermediate and advanced lessons",
      category: "Vocabulary",
      progress: 75,
    },
    {
      id: 2,
      name: "Business English",
      description: "Professional communication skills",
      category: "Business",
      progress: 40,
    },
    {
      id: 3,
      name: "English Grammar",
      description: "Comprehensive grammar lessons",
      category: "Grammar",
      progress: 60,
    },
    {
      id: 4,
      name: "English Pronunciation",
      description: "Improve your accent and fluency",
      category: "Speaking",
      progress: 30,
    },
    {
      id: 5,
      name: "English Writing",
      description: "Essay and report writing skills",
      category: "Writing",
      progress: 50,
    },
    {
      id: 6,
      name: "English Literature",
      description: "Classic and contemporary works",
      category: "Literature",
      progress: 20,
    },
  ];

  return (
    <div className="bg-white p-4 md:p-8">
      <div className="flex items-center mb-6">
        <h2 className="text-2xl font-bold text-[#2E645B] mr-4">Courses</h2>
        <Select onValueChange={setSelectedCourse} defaultValue={selectedCourse}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select course type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="English Courses">English Courses</SelectItem>
            <SelectItem value="Science Courses">Science Courses</SelectItem>
            <SelectItem value="Math Courses">Math Courses</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {courses.map((course) => (
          <Card
            key={course.id}
            className="bg-white shadow-lg transition-all duration-300 ease-in-out transform hover:scale-105 rounded-xl overflow-hidden"
          >
            <div className="p-4">
              <h3 className="text-lg font-semibold text-black mb-2">
                {course.name}
              </h3>
              <p className="text-sm text-gray-600 mb-4">{course.description}</p>
              <div className="text-xs font-medium text-gray-500 mb-2">
                {course.category}
              </div>
              <Progress value={course.progress} className="h-2 mb-4" />
              <div className="flex justify-between items-center text-sm text-black mb-4">
                <span>Progress</span>
                <span className="font-bold">{course.progress}%</span>
              </div>
              <div className="flex justify-end">
                <Button variant="outline" className="rounded-lg">
                  Edit Course
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default AllCourses;
