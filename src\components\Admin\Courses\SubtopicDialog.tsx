import React, { useState, useEffect } from 'react';
import { skipToken } from '@reduxjs/toolkit/query';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    <PERSON>bs,
    TabsContent,
    TabsList,
    TabsTrigger,
} from "@/components/ui/tabs";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Loader2, Upload } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { useFileUpload } from '@/lib/utils';
import ContentRenderer from './ContentRenderer';
import JSZip from 'jszip';
import { ContentType, Subtopic } from './types';
import { useGetContentByIdQuery } from '@/APIConnect';

// Add YouTube video ID extraction helper
const extractYouTubeVideoId = (url: string): string | null => {
    const regExp = /^.*(?:youtu\.be\/|v\/|embed\/|watch\?v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return match && match[1].length === 11 ? match[1] : null;
};

interface SubtopicDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    selectedSubtopic: Subtopic | null;
    onSave: (data: Omit<Subtopic, 'id' | 'createdOn' | 'updateOn'>) => Promise<void>;
    courseId: string;
}

interface SubtopicFormData {
    subTopicName: string;
    topicWeight: number;
    type: string;
    content: string;
    contentUrl: string;
    assignment: {
        type: 1 | 2;
        title: string;
        assignmentText: string;
        fileUrl: string;
    };
}

interface ExtractedFile {
    path: string;
    file: File;
}

const SubtopicDialog: React.FC<SubtopicDialogProps> = ({
    open,
    onOpenChange,
    selectedSubtopic,
    onSave,
    courseId
}) => {
    const { uploadFile, progress } = useFileUpload();
    const [activeTab, setActiveTab] = useState("edit");
    const [file, setFile] = useState<File | null>(null);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [isProcessing, setIsProcessing] = useState(false);
    const [extractedFiles, setExtractedFiles] = useState<ExtractedFile[]>([]);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);

    const [subtopicData, setSubtopicData] = useState<SubtopicFormData>({
        subTopicName: "",
        topicWeight: 0,
        type: ContentType.HTML.toString(),
        content: "",
        contentUrl: "",
        assignment: {
            type: 1,
            title: "",
            assignmentText: "",
            fileUrl: ""
        }
    });

    const [hasAssignment, setHasAssignment] = useState(false);

    // Add query hook for fetching subtopic details
    const { data: subtopicDetails } = useGetContentByIdQuery(selectedSubtopic?.id || skipToken);

    useEffect(() => {
        if (selectedSubtopic) {
            // Wait for subtopicDetails to be available when editing
            const details = subtopicDetails?.resultObject;
            setSubtopicData({
                subTopicName: details?.subTopicName || selectedSubtopic.subTopicName,
                topicWeight: details?.topicWeight || selectedSubtopic.topicWeight,
                type: (details?.type || selectedSubtopic.type).toString(),
                content: details?.content || selectedSubtopic.content,
                contentUrl: details?.contentUrl || selectedSubtopic.contentUrl,
                assignment: details?.assignment || selectedSubtopic.assignment || {
                    type: 1,
                    title: "",
                    assignmentText: "",
                    fileUrl: ""
                }
            });
            setHasAssignment(!!details?.assignment || !!selectedSubtopic.assignment);
        } else {
            setSubtopicData({
                subTopicName: "",
                topicWeight: 0,
                type: ContentType.HTML.toString(),
                content: "",
                contentUrl: "",
                assignment: {
                    type: 1,
                    title: "",
                    assignmentText: "",
                    fileUrl: ""
                }
            });
            setHasAssignment(false);
        }
    }, [selectedSubtopic, subtopicDetails]);

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (!files || !files[0]) return;

        const selectedFile = files[0];
        setFile(selectedFile);

        if (parseInt(subtopicData.type) === ContentType.AUDIO_URL || parseInt(subtopicData.type) === ContentType.VIDEO_URL) {
            const url = URL.createObjectURL(selectedFile);
            setPreviewUrl(url);
        } else {
            setPreviewUrl(null);
        }

        // Handle SCORM zip file
        if (parseInt(subtopicData.type) === ContentType.SCORM && selectedFile.type === 'application/zip') {
            setIsProcessing(true);
            try {
                const zip = new JSZip();
                const zipContent = await zip.loadAsync(selectedFile);

                setExtractedFiles([]);

                let launchFile = '';
                const totalFiles = Object.keys(zipContent.files).length;
                let processedFiles = 0;

                const uploadPromises: Promise<any>[] = [];

                for (const [path, zipFile] of Object.entries(zipContent.files)) {
                    if (zipFile.dir) {
                        processedFiles++;
                        setUploadProgress((processedFiles / totalFiles) * 100);
                        continue;
                    }

                    if (path.toLowerCase().includes('index.html') ||
                        path.toLowerCase().includes('launch.html') ||
                        path.toLowerCase().includes('start.html')) {
                        launchFile = path;
                    }

                    const content = await zipFile.async('blob');
                    const extractedFile = new File([content], path.split('/').pop() || path, {
                        type: content.type || 'application/octet-stream'
                    });

                    setExtractedFiles(prev => [...prev, { path, file: extractedFile }]);

                    const uploadPromise = uploadFile(extractedFile, path).then(() => {
                        processedFiles++;
                        setUploadProgress((processedFiles / totalFiles) * 100);
                    });

                    uploadPromises.push(uploadPromise);
                }

                await Promise.all(uploadPromises);

                if (!launchFile) {
                    throw new Error('No launch file found in SCORM package');
                }

                setSubtopicData(prev => ({
                    ...prev,
                    content: launchFile
                }));

                toast({
                    title: "SCORM package processed successfully",
                    variant: "default",
                });
            } catch (error) {
                console.error('Error processing SCORM package:', error);
                toast({
                    title: "Failed to process SCORM package",
                    description: error instanceof Error ? error.message : "Please try again",
                    variant: "destructive",
                });
            } finally {
                setIsProcessing(false);
            }
        }
    };

    const handleUpload = async () => {
        if (!file) return;

        try {
            const fileName = `${Date.now()}_${file.name}`;
            const response = await uploadFile(file, fileName);

            if (typeof response === 'string' && response.startsWith("http")) {
                setSubtopicData(prev => ({
                    ...prev,
                    content: response,
                }));
                toast({
                    title: "File uploaded successfully",
                    variant: "default",
                });
            } else {
                throw new Error('Upload failed');
            }
        } catch (error) {
            console.error('Error uploading file:', error);
            toast({
                title: "Failed to upload file",
                description: "Please try again later",
                variant: "destructive",
            });
        } finally {
            if (previewUrl) {
                URL.revokeObjectURL(previewUrl);
                setPreviewUrl(null);
            }
        }
    };

    const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;

        if (parseInt(subtopicData.type) === ContentType.VIDEO_URL) {
            const videoId = extractYouTubeVideoId(value);
            if (videoId) {
                const embedUrl = `https://www.youtube.com/embed/${videoId}`;
                setSubtopicData(prev => ({ ...prev, content: embedUrl }));
                setPreviewUrl(embedUrl);
            } else {
                setSubtopicData(prev => ({ ...prev, content: value }));
                setPreviewUrl(value);
            }
        } else {
            setSubtopicData(prev => ({ ...prev, content: value }));
            setPreviewUrl(value);
        }
    };

    const modules = {
        toolbar: [
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'list': 'ordered' }, { 'list': 'bullet' }],
            [{ 'color': [] }, { 'background': [] }],
            ['link', 'image'],
            ['clean']
        ],
    };

    const handleSave = async () => {
        try {
            if (!subtopicData.subTopicName) {
                toast({
                    title: "Subtopic name is required",
                    variant: "destructive"
                });
                return;
            }

            if (subtopicData.topicWeight < 1 || subtopicData.topicWeight > 100) {
                toast({
                    title: "Weight must be between 1 and 100",
                    variant: "destructive"
                });
                return;
            }

            const payload: Omit<Subtopic, 'id' | 'createdOn' | 'updateOn'> = {
                ...subtopicData,
                type: subtopicData.type,
                courseId,
                topicId: selectedSubtopic?.topicId || '',
                assignment: hasAssignment ? subtopicData.assignment : undefined
            };

            await onSave(payload);
            onOpenChange(false);
        } catch (error) {
            console.error('Error saving subtopic:', error);
            toast({
                title: "Failed to save subtopic",
                description: "Please try again later",
                variant: "destructive"
            });
        }
    };

    const previewData = {
        resultObject: {
            type: parseInt(subtopicData.type),
            content: parseInt(subtopicData.type) === ContentType.HTML ? subtopicData.content : undefined,
            contentUrl: parseInt(subtopicData.type) !== ContentType.HTML ? subtopicData.content : undefined,
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-4xl">
                <DialogHeader>
                    <DialogTitle>{selectedSubtopic ? 'Edit Subtopic' : 'Add Subtopic'}</DialogTitle>
                </DialogHeader>

                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="edit">Edit</TabsTrigger>
                        <TabsTrigger value="preview">Preview</TabsTrigger>
                    </TabsList>

                    <TabsContent value="edit" className="space-y-4 py-4">
                        <div className="space-y-4">
                            <div>
                                <Label>Subtopic Name</Label>
                                <Input
                                    value={subtopicData.subTopicName}
                                    onChange={(e) => setSubtopicData(prev => ({ ...prev, subTopicName: e.target.value }))}
                                    placeholder="Enter subtopic name"
                                />
                            </div>

                            <div>
                                <Label>Weight (%)</Label>
                                <Input
                                    type="number"
                                    min="0"
                                    max="100"
                                    value={subtopicData.topicWeight}
                                    onChange={(e) => setSubtopicData(prev => ({ ...prev, topicWeight: parseInt(e.target.value) || 0 }))}
                                    placeholder="Enter weight (0-100)"
                                />
                            </div>

                            <div>
                                <Label>Content Type</Label>
                                <Select
                                    value={subtopicData.type}
                                    onValueChange={(value) => setSubtopicData(prev => ({
                                        ...prev,
                                        type: value,
                                        content: '',
                                    }))}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select content type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value={ContentType.HTML.toString()}>HTML</SelectItem>
                                        <SelectItem value={ContentType.AUDIO_URL.toString()}>Audio</SelectItem>
                                        <SelectItem value={ContentType.VIDEO_URL.toString()}>Video</SelectItem>
                                        <SelectItem value={ContentType.SCORM.toString()}>SCORM</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            {parseInt(subtopicData.type) === ContentType.HTML ? (
                                <div>
                                    <Label>HTML Content</Label>
                                    <div className="min-h-[200px]">
                                        <ReactQuill
                                            theme="snow"
                                            value={subtopicData.content}
                                            onChange={(content) => setSubtopicData(prev => ({
                                                ...prev,
                                                content
                                            }))}
                                            modules={modules}
                                            className="h-[150px]"
                                        />
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    <div>
                                        <Label>URL</Label>
                                        <Input
                                            value={subtopicData.content}
                                            onChange={handleUrlChange}
                                            placeholder="Enter URL or upload file"
                                        />
                                    </div>
                                    <div>
                                        <Label>Or Upload File</Label>
                                        <div className="flex items-center space-x-2">
                                            <Input
                                                type="file"
                                                onChange={handleFileChange}
                                                accept={
                                                    parseInt(subtopicData.type) === ContentType.AUDIO_URL ? "audio/*" :
                                                        parseInt(subtopicData.type) === ContentType.VIDEO_URL ? "video/*" :
                                                            parseInt(subtopicData.type) === ContentType.SCORM ? ".zip" :
                                                                undefined
                                                }
                                            />
                                            <Button
                                                type="button"
                                                onClick={handleUpload}
                                                disabled={!file || isProcessing}
                                            >
                                                {isProcessing ? (
                                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                                ) : (
                                                    <Upload className="w-4 h-4 mr-2" />
                                                )}
                                                Upload
                                            </Button>
                                        </div>
                                        {(uploadProgress > 0 || progress > 0) && (
                                            <Progress
                                                value={Math.max(uploadProgress, progress)}
                                                className="w-full"
                                            />
                                        )}
                                        {isProcessing && (
                                            <p className="text-sm text-muted-foreground">
                                                Processing SCORM package... {extractedFiles.length} files extracted
                                            </p>
                                        )}
                                    </div>
                                    {previewUrl && (
                                        <div>
                                            <Label>Preview</Label>
                                            {parseInt(subtopicData.type) === ContentType.VIDEO_URL && (
                                                previewUrl.includes('youtube.com/embed') ? (
                                                    <iframe
                                                        src={previewUrl}
                                                        className="w-full h-64"
                                                        frameBorder="0"
                                                        allowFullScreen
                                                    />
                                                ) : (
                                                    <video controls src={previewUrl} className="w-full h-auto mt-2" />
                                                )
                                            )}
                                            {parseInt(subtopicData.type) === ContentType.AUDIO_URL && (
                                                <audio controls src={previewUrl} className="w-full mt-2" />
                                            )}
                                        </div>
                                    )}
                                </div>
                            )}

                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="hasAssignment"
                                    checked={hasAssignment}
                                    onChange={(e) => setHasAssignment(e.target.checked)}
                                />
                                <Label htmlFor="hasAssignment">Include Assignment</Label>
                            </div>

                            {hasAssignment && (
                                <div className="space-y-4 border p-4 rounded-lg">
                                    <div>
                                        <Label>Assignment Type</Label>
                                        <Select
                                            value={subtopicData.assignment.type.toString()}
                                            onValueChange={(value) => setSubtopicData(prev => ({
                                                ...prev,
                                                assignment: { ...prev.assignment, type: parseInt(value) as 1 | 2 }
                                            }))}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select assignment type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="1">Submission</SelectItem>
                                                <SelectItem value="2">Quiz</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Label>Assignment Title</Label>
                                        <Input
                                            value={subtopicData.assignment.title}
                                            onChange={(e) => setSubtopicData(prev => ({
                                                ...prev,
                                                assignment: { ...prev.assignment, title: e.target.value }
                                            }))}
                                            placeholder="Enter assignment title"
                                        />
                                    </div>

                                    <div>
                                        <Label>Assignment Text</Label>
                                        <Input
                                            value={subtopicData.assignment.assignmentText}
                                            onChange={(e) => setSubtopicData(prev => ({
                                                ...prev,
                                                assignment: { ...prev.assignment, assignmentText: e.target.value }
                                            }))}
                                            placeholder="Enter assignment description"
                                        />
                                    </div>

                                    <div>
                                        <Label>File URL (Optional)</Label>
                                        <Input
                                            value={subtopicData.assignment.fileUrl}
                                            onChange={(e) => setSubtopicData(prev => ({
                                                ...prev,
                                                assignment: { ...prev.assignment, fileUrl: e.target.value }
                                            }))}
                                            placeholder="Enter file URL"
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    </TabsContent>

                    <TabsContent value="preview">
                        <ContentRenderer
                            topicId={selectedSubtopic?.topicId || ''}
                            previewData={previewData}
                        />
                    </TabsContent>
                </Tabs>

                <DialogFooter>
                    <Button variant="outline" onClick={() => onOpenChange(false)}>
                        Cancel
                    </Button>
                    <Button onClick={handleSave}>
                        {selectedSubtopic ? 'Update' : 'Add'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default SubtopicDialog;