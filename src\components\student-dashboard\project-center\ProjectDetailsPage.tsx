import React, { useState, useRef, useEffect, useLayoutEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth0 } from '@auth0/auth0-react';
import { useSelector } from 'react-redux';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import InstructorInfo from './component/InstructorInfo'; // Added import for InstructorInfo
import GroupInfoTab from './component/GroupInfoTab'; // Added import for GroupInfoTab
import FilesTab from './component/FilesTab'; // Added import for FilesTab
import ChatComponent from './component/ChatComponent'; // Added ChatComponent import
import { elephantFace } from '@lucide/lab'; // Import elephantFace from @lucide/lab
import {
    useGetStudentProjectDashboardQuery,
    useGetProjectByIdQuery,
    useGetProjectAssignmentsQuery,
    useGetGroupByIdQuery,
    useSubmitAssignmentMutation
} from '@/APIConnect';
import {
    ProjectDetailData,
    TaskItem,
    ProjectAPIResponse,
    AssignmentAPIResponse,
    GroupAPIResponse
} from '@/types/projectCenter';
import { FileFolderSDK } from '@/lib/FileFolderSdk';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from '@/components/ui/dialog';
import {
    ArrowLeft,
    Calendar,
    FileText,
    Users,
    MessageCircle,
    Upload,
    CheckCircle,
    Clock,
    Eye,
    BarChart3,
    Bot,
    Icon,
    CheckCheck,
    CheckIcon,
    ClipboardList,
    X
} from 'lucide-react';

interface ProjectDetail {
    id: string;
    name: string;
    type: 'Academic' | 'Industry';
    description: string;
    startDate: string;
    totalTasks: number;
    completedTasks: number;
    progress: number;
    reviews: number;
    tasks: TaskItem[];
    objectives: string[];
    detailedDescription: string;
}

const ProjectDetailsPage: React.FC = () => {
    const { projectId } = useParams<{ projectId: string }>();
    const navigate = useNavigate();
    const { user } = useAuth0();
    const userToken = useSelector((state: any) => state.user.userToken);

    const [activeTab, setActiveTab] = useState<'overview' | 'instructor' | 'group' | 'files' | 'chat'>('overview');
    const [isTasksSidebarOpen, setIsTasksSidebarOpen] = useState(false);
    const [projectData, setProjectData] = useState<ProjectDetail | null>(null);
    const lastTaskRef = useRef<HTMLDivElement | null>(null);
    const timelineContainerRef = useRef<HTMLDivElement | null>(null);
    const [timelineHeight, setTimelineHeight] = useState<string>('0px');

    // File counts state
    const [fileCounts, setFileCounts] = useState({
        images: 0,
        documents: 0,
        sheets: 0,
        videos: 0
    });

    // File upload states
    const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [isUploading, setIsUploading] = useState(false);
    const [selectedAssignmentId, setSelectedAssignmentId] = useState<string>('');
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Edit assignment states
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [editAssignmentId, setEditAssignmentId] = useState<string>('');
    const [editAssignmentTitle, setEditAssignmentTitle] = useState<string>('');
    const [selectedEditFile, setSelectedEditFile] = useState<File | null>(null);
    const [editUploadProgress, setEditUploadProgress] = useState(0);
    const [isEditUploading, setIsEditUploading] = useState(false);
    const editFileInputRef = useRef<HTMLInputElement>(null);

    // Success dialog states
    const [isSuccessDialogOpen, setIsSuccessDialogOpen] = useState(false);
    const [successMessage, setSuccessMessage] = useState<string>('');

    // File upload SDK
    const fileFolderSDK = React.useMemo(() => new FileFolderSDK(), []);

    // Submit assignment mutation
    const [submitAssignment] = useSubmitAssignmentMutation();

    // useLayoutEffect for timeline height calculation
    useLayoutEffect(() => {
        const timelineContainer = timelineContainerRef.current;
        const lastTaskElement = lastTaskRef.current;

        if (!timelineContainer || !lastTaskElement || !projectData || projectData.tasks.length <= 1) {
            setTimelineHeight('0px');
            return;
        }

        const calculateHeight = () => {
            const height = lastTaskElement.offsetTop + (lastTaskElement.offsetHeight / 2);
            setTimelineHeight(`${height}px`);
        };

        calculateHeight(); // Initial calculation

        const resizeObserver = new ResizeObserver(calculateHeight);
        resizeObserver.observe(timelineContainer);

        return () => {
            resizeObserver.disconnect();
        };
    }, [projectData?.tasks]);

    // Fetch dashboard data to get group information
    const {
        data: dashboardData,
        isLoading: isDashboardLoading,
        error: dashboardError
    } = useGetStudentProjectDashboardQuery(undefined, {
        skip: !userToken || !user
    });

    // Find the group that matches the projectId from the URL
    const currentGroup = React.useMemo(() => {
        if (!dashboardData?.resultObject?.groups || !projectId) return null;

        // Helper function to convert MongoDB ObjectId to string
        const objectIdToString = (id: any): string => {
            if (typeof id === 'string') return id;
            if (id && typeof id === 'object' && id.timestamp) {
                const timestamp = id.timestamp.toString(16).padStart(8, '0');
                const machine = id.machine.toString(16).padStart(6, '0');
                const pid = id.pid.toString(16).padStart(4, '0');
                const increment = id.increment.toString(16).padStart(6, '0');
                return `${timestamp}${machine}${pid}${increment}`;
            }
            return String(id);
        };

        return dashboardData.resultObject.groups.find((group: any) => {
            const groupId = objectIdToString(group.id);
            return groupId === projectId;
        });
    }, [dashboardData, projectId]);

    // Fetch project details if we have a current group
    const {
        data: projectApiData,
        isLoading: isProjectLoading,
        error: projectError
    } = useGetProjectByIdQuery(
        currentGroup?.project?.id ?
            (typeof currentGroup.project.id === 'string' ?
                currentGroup.project.id :
                `${currentGroup.project.id.timestamp.toString(16).padStart(8, '0')}${currentGroup.project.id.machine.toString(16).padStart(6, '0')}${currentGroup.project.id.pid.toString(16).padStart(4, '0')}${currentGroup.project.id.increment.toString(16).padStart(6, '0')}`) :
            null,
        {
            skip: !currentGroup?.project?.id
        }
    );

    // Fetch assignments for the project
    const {
        data: assignmentsData,
        isLoading: isAssignmentsLoading,
        error: assignmentsError
    } = useGetProjectAssignmentsQuery(
        currentGroup?.project?.id ?
            (typeof currentGroup.project.id === 'string' ?
                currentGroup.project.id :
                `${currentGroup.project.id.timestamp.toString(16).padStart(8, '0')}${currentGroup.project.id.machine.toString(16).padStart(6, '0')}${currentGroup.project.id.pid.toString(16).padStart(4, '0')}${currentGroup.project.id.increment.toString(16).padStart(6, '0')}`) :
            null,
        {
            skip: !currentGroup?.project?.id
        }
    );

    // Function to analyze file types and count them
    const analyzeProjectFiles = (assignmentsData: any) => {
        const counts = {
            images: 0,
            documents: 0,
            sheets: 0,
            videos: 0
        };

        if (assignmentsData?.resultObject) {
            assignmentsData.resultObject.forEach((assignment: any) => {
                const fileUrl = assignment.assignment?.fileUrl;
                if (fileUrl && fileUrl.trim() !== '') {
                    const fileName = fileUrl.toLowerCase();
                    const extension = fileName.split('.').pop() || '';

                    // Categorize files based on extension
                    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'ico', 'tiff', 'tif'].includes(extension)) {
                        counts.images++;
                    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v', '3gp', 'ogv'].includes(extension)) {
                        counts.videos++;
                    } else if (['xls', 'xlsx', 'csv', 'ods', 'xlsm', 'xlsb', 'xltx', 'xltm'].includes(extension)) {
                        counts.sheets++;
                    } else if (['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt', 'ppt', 'pptx', 'zip', 'rar', '7z', 'tar', 'gz', 'docm', 'dotx', 'dotm', 'pptm', 'potx', 'potm', 'ppsx', 'ppsm'].includes(extension)) {
                        counts.documents++;
                    }
                }
            });
        }

        setFileCounts(counts);
    };

    // Transform API data to component format
    useEffect(() => {
        if (currentGroup && projectApiData?.resultObject) {
            const project = projectApiData.resultObject;
            const assignments = assignmentsData?.resultObject || [];

            console.log('Debug - Raw assignments data:', assignments);

            // Convert assignments to tasks
            const tasks: TaskItem[] = assignments.map((assignment: any, index: number) => {
                // Always use assignment.id as provided by backend
                const assignmentId = assignment.id;

                console.log(`Debug - Processing assignment ${index}:`, {
                    rawId: assignment.id,
                    processedId: assignmentId,
                    title: assignment.assignment?.title,
                    projectId: assignment.projectId
                });

                return {
                    id: assignmentId,
                    title: assignment.assignment?.title || `Assignment ${index + 1}`,
                    description: assignment.assignment?.assignmentText || 'No description available',
                    completed: false, // This would need to be determined from submission data
                    dueDate: assignment.dueDate,
                    type: assignment.assignment?.type
                };
            });

            // Use real submission data from dashboard API instead of calculated values
            const totalAssignments = currentGroup.totalAssignments || 0;
            const totalSubmissions = currentGroup.totalSubmissions || 0;
            const progress = totalAssignments > 0 ? Math.round((totalSubmissions / totalAssignments) * 100) : 0;

            console.log('Debug - Progress calculation:', {
                totalAssignments,
                totalSubmissions,
                progress,
                currentGroup: currentGroup
            });

            const transformedData: ProjectDetail = {
                id: projectId || '',
                name: project.projectName || 'Unknown Project',
                type: project.projectType === 'ACADEMIC' ? 'Academic' : 'Industry',
                description: project.projectText || 'No description available',
                startDate: new Date(project.createdOn).toLocaleDateString() || 'Unknown',
                totalTasks: totalAssignments, // Use real totalAssignments from API
                completedTasks: totalSubmissions, // Use real totalSubmissions from API
                progress: progress,
                reviews: currentGroup.totalPeerReviews || 0, // Use real totalPeerReviews from API
                tasks: tasks,
                objectives: project.projectText ? [project.projectText] : [
                    'Complete all assigned tasks on time.',
                    'Collaborate effectively with team members.',
                    'Apply learned concepts to real-world scenarios.',
                    'Demonstrate understanding through peer and instructor reviews.'
                ],
                detailedDescription: project.projectText || 'No detailed description available'
            };

            setProjectData(transformedData);

            // Analyze files and update counts
            analyzeProjectFiles(assignmentsData);
        }
    }, [currentGroup, projectApiData, assignmentsData, projectId]);

    // Loading state
    if (isDashboardLoading || isProjectLoading || isAssignmentsLoading) {
        return (
            <div className="min-h-screen bg-white flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#347468] mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading project details...</p>
                </div>
            </div>
        );
    }

    // Error state
    if (dashboardError || projectError || assignmentsError || !currentGroup) {
        return (
            <div className="min-h-screen bg-white flex items-center justify-center">
                <div className="text-center">
                    <div className="text-red-500 mb-4">
                        <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <p className="text-gray-600 mb-4">
                        {!currentGroup ? 'Project not found' : 'Failed to load project details'}
                    </p>
                    <Button
                        onClick={() => navigate('/project-center')}
                        className="bg-[#347468] hover:bg-[#347468]/90 text-white"
                    >
                        Back to Project Center
                    </Button>
                </div>
            </div>
        );
    }

    // No project data yet
    if (!projectData) {
        return (
            <div className="min-h-screen bg-white flex items-center justify-center">
                <div className="text-center">
                    <p className="text-gray-600">No project data available</p>
                </div>
            </div>
        );
    }

    const handleBackClick = () => {
        navigate('/');
    };

    const handleUpload = async () => {
        setIsUploadDialogOpen(true);

        // Debug: Let's check what assignments are actually available to this group
        console.log('Debug - Checking group assignments availability...');
        console.log('Debug - Current group data:', currentGroup);
        console.log('Debug - Project data tasks:', projectData?.tasks);

        // Debug: Check which assignments have fileUrl
        if (assignmentsData?.resultObject) {
            console.log('Debug - All assignments:', assignmentsData.resultObject.map((a: any) => ({
                id: a.id,
                title: a.assignment?.title,
                hasFileUrl: !!(a.assignment?.fileUrl && a.assignment.fileUrl.trim() !== ''),
                fileUrl: a.assignment?.fileUrl
            })));
        }

        if (projectData?.tasks && projectId) {
            for (const task of projectData.tasks) {
                try {
                    // Use the correct assignment ID from the project assignments API
                    const assignmentId = task.id; // This should be the assignment ID from project API
                    const groupId = projectId; // projectId from URL params is actually the group ID

                    console.log(`Debug - Checking assignment: ${task.title}`);
                    console.log(`Debug - Assignment ID: ${assignmentId}`);
                    console.log(`Debug - Group ID: ${groupId}`);

                    const response = await fetch(
                        `https://nexo-dev-fbf0cvc7bhehbuft.australiacentral-01.azurewebsites.net/StudentGroup/${groupId}/assignment/${assignmentId}`,
                        {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${userToken}`,
                                'Content-Type': 'application/json'
                            }
                        }
                    );

                    console.log(`Debug - Assignment ${task.title} (${assignmentId}): ${response.status}`);

                    if (response.ok) {
                        const data = await response.json();
                        console.log(`Debug - Assignment submission data:`, data);
                    } else {
                        const errorData = await response.text();
                        console.log(`Debug - Assignment error response:`, errorData);
                    }
                } catch (error) {
                    console.log(`Debug - Error checking assignment ${task.id}:`, error);
                }
            }
        }
    };

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setSelectedFile(file);
        }
    };

    const handleEditFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setSelectedEditFile(file);
        }
    };

    const handleEditAssignment = (assignmentId: string, assignmentTitle: string) => {
        setEditAssignmentId(assignmentId);
        setEditAssignmentTitle(assignmentTitle);
        setSelectedEditFile(null);
        setEditUploadProgress(0);
        setIsEditDialogOpen(true);

        // Clear edit file input
        if (editFileInputRef.current) {
            editFileInputRef.current.value = '';
        }
    };

    const handleFileUploadAndSubmit = async () => {
        if (!selectedFile || !selectedAssignmentId || !projectId) {
            alert('Please select a file and assignment');
            return;
        }

        console.log('Debug - Upload attempt:', {
            groupId: projectId,
            assignmentId: selectedAssignmentId,
            fileName: selectedFile.name,
            projectData: projectData?.tasks.find(t => t.id === selectedAssignmentId)
        });

        setIsUploading(true);
        try {
            // Step 1: Upload file to get URL
            const uniquePrefix = Date.now();
            const fileName = `assignments/${projectId}/${uniquePrefix}-${selectedFile.name}`;
            const fileUrl = await fileFolderSDK.uploadFile(
                selectedFile,
                fileName,
                (progress) => setUploadProgress(progress)
            );

            console.log('Debug - File uploaded successfully:', fileUrl);

            // Step 2: Submit assignment using POST (creates new submission record for this group)
            // --- Assignment Submission Logic ---
            // assignmentId: always comes from the selected assignment in the project (GET /Project/{projectId}/assignments)
            // groupId: always the group to which the student belongs (NOT the projectId, but the groupId)
            // The group does NOT "have" assignments; assignments belong to the project. Submission is always:
            // POST /StudentGroup/{groupId}/submission/{assignmentId} with { FileUrl }

            const assignmentId = selectedAssignmentId; // User must select from assignmentsData.resultObject
            const groupId = currentGroup?.id; // This is the groupId, not projectId

            console.log('Debug - All assignment IDs (from assignments API):', assignmentsData?.resultObject?.map((a: any) => a.id));
            console.log('Debug - Creating new submission record for group...');
            console.log('Debug - Group ID (submission target):', groupId);
            console.log('Debug - Assignment ID (from project assignments):', assignmentId);
            console.log('Debug - File URL to submit:', fileUrl);

            if (!groupId) {
                throw new Error('No valid groupId found for submission!');
            }
            if (!assignmentId) {
                throw new Error('No valid assignmentId selected for submission!');
            }
            if (!fileUrl) {
                throw new Error('No file URL available for submission!');
            }

            // --- SUBMISSION: POST /StudentGroup/{groupId}/submission/{assignmentId} ---
            const response = await fetch(
                `https://nexo-dev-fbf0cvc7bhehbuft.australiacentral-01.azurewebsites.net/StudentGroup/${groupId}/submission/${assignmentId}`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${userToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ FileUrl: fileUrl })
                }
            );

            if (!response.ok) {
                const errorData = await response.json();
                console.error('Debug - POST submission failed:', errorData);

                // Check if it's because submission already exists
                if (response.status === 409 || (errorData.errors && errorData.errors.some((e: string) => e.includes('already exists')))) {
                    console.log('Debug - Submission already exists, trying PUT to update...');

                    // Try to update existing submission
                    const updateResponse = await fetch(
                        `https://nexo-dev-fbf0cvc7bhehbuft.australiacentral-01.azurewebsites.net/StudentGroup/${groupId}/submission/${selectedAssignmentId}`,
                        {
                            method: 'PUT',
                            headers: {
                                'Authorization': `Bearer ${userToken}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ FileUrl: fileUrl })
                        }
                    );

                    if (!updateResponse.ok) {
                        const updateErrorData = await updateResponse.json();
                        console.error('Debug - PUT submission failed:', updateErrorData);
                        throw new Error(`Update submission failed: ${JSON.stringify(updateErrorData)}`);
                    }

                    console.log('Debug - Submission updated successfully!');
                } else {
                    throw new Error(`Submission failed: ${JSON.stringify(errorData)}`);
                }
            } else {
                console.log('Debug - New submission created successfully!');
            }

            // --- UPDATE ASSIGNMENT FILE URL IN PROJECT ASSIGNMENT ---
            // Only update the fileUrl in the assignment object for this assignmentId
            try {
                const assignment = assignmentsData?.resultObject?.find((a: any) => a.id === assignmentId);
                if (assignment) {
                    const updateAssignmentBody = {
                        DueDate: assignment.dueDate,
                        Assignment: {
                            Type: assignment.assignment.type,
                            Tags: assignment.assignment.tags,
                            Title: assignment.assignment.title,
                            AssignmentText: assignment.assignment.assignmentText,
                            FileUrl: fileUrl
                        }
                    };
                    const updateAssignmentResponse = await fetch(
                        `https://nexo-dev-fbf0cvc7bhehbuft.australiacentral-01.azurewebsites.net/Project/${assignment.projectId}/assignment/${assignmentId}`,
                        {
                            method: 'PUT',
                            headers: {
                                'Authorization': `Bearer ${userToken}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(updateAssignmentBody)
                        }
                    );
                    if (!updateAssignmentResponse.ok) {
                        const updateAssignmentError = await updateAssignmentResponse.json();
                        console.error('Debug - Assignment fileUrl update failed:', updateAssignmentError);
                    } else {
                        console.log('Debug - Assignment fileUrl updated in project assignment!');
                    }
                }
            } catch (err) {
                console.error('Debug - Error updating assignment fileUrl in project assignment:', err);
            }

            // Success feedback
            setSuccessMessage('Assignment submitted successfully!');
            setIsSuccessDialogOpen(true);

            // Reset states
            setSelectedFile(null);
            setSelectedAssignmentId('');
            setIsUploadDialogOpen(false);
            setUploadProgress(0);

            // Clear file input
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }

        } catch (error: any) {
            console.error('Upload/submission failed:', error);
            console.log('Debug - Error details:', {
                status: error?.status,
                data: error?.data,
                groupId: projectId,
                assignmentId: selectedAssignmentId
            });

            // Provide specific error message based on the system architecture
            if (error && error.message && error.message.includes('Assignment not found')) {
                alert(`Unable to submit assignment "${projectData?.tasks.find(t => t.id === selectedAssignmentId)?.title}".\n\nSystem Architecture:\n- Projects and assignments are universal\n- Groups create unique submission records\n\nPossible issues:\n1. Assignment ID mismatch between project and submission system\n2. Group doesn't have permission to submit to this assignment\n3. Assignment might not be active for submissions yet\n\nTechnical Details:\n- Group ID: ${projectId}\n- Assignment ID: ${selectedAssignmentId}\n- Project has assignments, but group can't create submission record\n\nPlease contact your system administrator.`);
            } else {
                alert(`Failed to submit assignment. Error: ${error?.message || 'Unknown error'}\n\nIf this persists, please contact your instructor or system administrator.`);
            }
        } finally {
            setIsUploading(false);
        }
    };

    const handleEditFileUploadAndSubmit = async () => {
        if (!selectedEditFile || !editAssignmentId || !projectId) {
            alert('Please select a file');
            return;
        }

        console.log('Debug - Edit upload attempt:', {
            groupId: projectId,
            assignmentId: editAssignmentId,
            fileName: selectedEditFile.name,
            assignmentTitle: editAssignmentTitle
        });

        // Debug: Check if this assignment exists in the assignments data
        const assignmentExists = assignmentsData?.resultObject?.find((a: any) => a.id === editAssignmentId);
        console.log('Debug - Assignment exists in project assignments:', !!assignmentExists);
        console.log('Debug - All available assignment IDs:', assignmentsData?.resultObject?.map((a: any) => a.id));

        setIsEditUploading(true);
        try {
            // Step 1: Upload file to get URL
            const uniquePrefix = Date.now();
            const fileName = `assignments/${projectId}/${uniquePrefix}-${selectedEditFile.name}`;
            const fileUrl = await fileFolderSDK.uploadFile(
                selectedEditFile,
                fileName,
                (progress) => setEditUploadProgress(progress)
            );

            console.log('Debug - Edit file uploaded successfully:', fileUrl);

            // Step 2: Update existing submission using PUT, or create if doesn't exist
            const groupId = currentGroup?.id;

            console.log('Debug - Group ID for submission:', groupId);
            console.log('Debug - Current group data:', currentGroup);

            if (!groupId) {
                throw new Error('No valid groupId found for submission update!');
            }

            // First try to update existing submission with PUT
            let submissionUpdateSuccess = false;

            try {
                const updateSubmissionResponse = await fetch(
                    `https://nexo-dev-fbf0cvc7bhehbuft.australiacentral-01.azurewebsites.net/StudentGroup/${groupId}/submission/${editAssignmentId}`,
                    {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${userToken}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ FileUrl: fileUrl })
                    }
                );

                if (updateSubmissionResponse.ok) {
                    console.log('Debug - Submission updated successfully with PUT!');
                    submissionUpdateSuccess = true;
                } else {
                    const updateErrorData = await updateSubmissionResponse.json();
                    console.log('Debug - PUT submission update failed, trying POST:', updateErrorData);
                }
            } catch (putError) {
                console.log('Debug - PUT request failed, trying POST:', putError);
            }

            // If PUT failed, try to create new submission with POST
            if (!submissionUpdateSuccess) {
                try {
                    const createSubmissionResponse = await fetch(
                        `https://nexo-dev-fbf0cvc7bhehbuft.australiacentral-01.azurewebsites.net/StudentGroup/${groupId}/submission/${editAssignmentId}`,
                        {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${userToken}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ FileUrl: fileUrl })
                        }
                    );

                    if (createSubmissionResponse.ok) {
                        console.log('Debug - Submission created successfully with POST!');
                        submissionUpdateSuccess = true;
                    } else {
                        const createErrorData = await createSubmissionResponse.json();
                        console.error('Debug - POST submission creation failed:', createErrorData);
                        throw new Error(`Submission update/creation failed: ${JSON.stringify(createErrorData)}`);
                    }
                } catch (postError) {
                    console.error('Debug - POST request also failed:', postError);
                    throw new Error(`Both PUT and POST submission requests failed: ${postError}`);
                }
            }

            // Step 3: Update assignment file URL in project assignment
            try {
                const assignment = assignmentsData?.resultObject?.find((a: any) => a.id === editAssignmentId);
                if (assignment) {
                    const updateAssignmentBody = {
                        DueDate: assignment.dueDate,
                        Assignment: {
                            Type: assignment.assignment.type,
                            Tags: assignment.assignment.tags,
                            Title: assignment.assignment.title,
                            AssignmentText: assignment.assignment.assignmentText,
                            FileUrl: fileUrl
                        }
                    };
                    const updateAssignmentResponse = await fetch(
                        `https://nexo-dev-fbf0cvc7bhehbuft.australiacentral-01.azurewebsites.net/Project/${assignment.projectId}/assignment/${editAssignmentId}`,
                        {
                            method: 'PUT',
                            headers: {
                                'Authorization': `Bearer ${userToken}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(updateAssignmentBody)
                        }
                    );
                    if (!updateAssignmentResponse.ok) {
                        const updateAssignmentError = await updateAssignmentResponse.json();
                        console.error('Debug - Assignment fileUrl update failed:', updateAssignmentError);
                    } else {
                        console.log('Debug - Assignment fileUrl updated in project assignment!');
                    }
                }
            } catch (err) {
                console.error('Debug - Error updating assignment fileUrl in project assignment:', err);
            }

            // Success feedback
            setSuccessMessage('Assignment updated successfully!');
            setIsSuccessDialogOpen(true);

            // Reset states
            setSelectedEditFile(null);
            setEditAssignmentId('');
            setEditAssignmentTitle('');
            setIsEditDialogOpen(false);
            setEditUploadProgress(0);

            // Clear file input
            if (editFileInputRef.current) {
                editFileInputRef.current.value = '';
            }

        } catch (error: any) {
            console.error('Edit upload/submission failed:', error);
            alert(`Failed to update assignment. Error: ${error?.message || 'Unknown error'}\n\nIf this persists, please contact your instructor or system administrator.`);
        } finally {
            setIsEditUploading(false);
        }
    };

    const handleTaskClick = (taskId: string) => {
        // Handle task navigation
        console.log('Task clicked:', taskId);
    };

    return (
        <div className="min-h-screen bg-white">
            <div className="flex flex-col lg:flex-row">
                {/* Main Content */}
                <div className="flex-1 p-4 pt-8 md:p-6 lg:p-9">
                    {/* Back Button */}
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        className="mb-6 flex justify-between items-center"
                    >
                        <Button
                            onClick={handleBackClick}
                            className="bg-[#347468] hover:bg-[#347468]/90 text-white px-3 py-2 rounded-lg flex items-center gap-2"
                        >
                            <ArrowLeft className="h-5 w-5" />
                            Back
                        </Button>
                        <Button
                            onClick={() => setIsTasksSidebarOpen(true)}
                            className="lg:hidden bg-[#347468] hover:bg-[#347468]/90 text-white px-3 py-2 rounded-lg flex items-center gap-2"
                        >
                            <ClipboardList className="h-5 w-5" />
                            Tasks
                        </Button>
                    </motion.div>

                    {/* Project Header */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mb-12"
                    >
                        <div className="mb-6">
                            <h1 className="text-[#2d2d2d] text-2xl md:text-[32px] font-semibold capitalize mb-2">
                                {projectData.name}
                            </h1>
                            <div className="flex items-center gap-1">
                                <span className="text-[#2d2d2d] text-xl md:text-2xl font-medium capitalize">Type - </span>
                                <span className="text-[#347468] text-xl md:text-2xl font-semibold capitalize">{projectData.type}</span>
                            </div>
                        </div>

                        <p className="text-[#2d2d2d] text-sm font-medium leading-relaxed mb-12">
                            {projectData.description}
                        </p>

                        {/* Progress and Upload Section */}
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-end gap-6 sm:gap-10">
                            <div className="flex-1">
                                <div className="flex justify-between items-center mb-3">
                                    <div className="flex items-center gap-1">
                                        <span className="text-[#2d2d2d] text-base font-semibold">{projectData.completedTasks}</span>
                                        <span className="text-[#717171] text-base font-normal">/{projectData.totalTasks} Completed </span>
                                        <span className="text-[#eeeeee] text-base font-normal">|</span>
                                        <span className="text-[#2d2d2d] text-base font-semibold ml-1">{projectData.progress}%</span>
                                    </div>
                                </div>
                                <div className="w-full bg-[#347468]/20 rounded-[32px] h-4 overflow-hidden">
                                    <div
                                        className="h-full bg-[#347468] rounded-2xl transition-all duration-300"
                                        style={{ width: `${projectData.progress}%` }}
                                    />
                                </div>
                            </div>

                            <Button
                                onClick={handleUpload}
                                className="bg-[#347468] hover:bg-[#347468]/90 text-white px-4 py-3 rounded-lg flex items-center gap-2"
                            >
                                <span className="text-xl font-semibold capitalize">Upload</span>
                                <Upload className="h-6 w-6" />
                            </Button>
                        </div>
                    </motion.div>

                    {/* Project Info Icons */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                        className="mb-6 flex flex-col gap-6"
                    >
                        <div className="flex items-center gap-2">
                            <Calendar className="h-5 w-5 text-[#347468]" />
                            <span className="text-[#2d2d2d] text-xl font-medium">{projectData.startDate}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <FileText className="h-5 w-5 text-[#347468]" />
                            <span className="text-[#2d2d2d] text-xl font-medium">{projectData.totalTasks} Tasks</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Users className="h-5 w-5 text-[#347468]" />
                            <span className="text-[#2d2d2d] text-xl font-medium">{projectData.reviews} Reviews</span>
                        </div>
                    </motion.div>

                    {/* Tabs Section */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className="bg-gradient-to-br from-[#386c8d] via-[#036664] to-[#060b0b] rounded-xl p-4 mb-6"
                    >
                        <div className="flex flex-wrap items-center gap-2 md:gap-4">
                            <Button
                                variant={activeTab === 'overview' ? 'secondary' : 'ghost'}
                                className={`text-sm md:text-base px-2 py-1 md:px-3 md:py-2 rounded-lg ${activeTab === 'overview'
                                    ? 'bg-white/10 border border-white/40 text-white'
                                    : 'text-white hover:bg-white/10'
                                    }`}
                                onClick={() => setActiveTab('overview')}
                            >
                                Overview
                            </Button>
                            <Button
                                variant={activeTab === 'instructor' ? 'secondary' : 'ghost'}
                                className={`text-sm md:text-base px-2 py-1 md:px-3 md:py-2 rounded-lg ${activeTab === 'instructor'
                                    ? 'bg-white/10 border border-white/40 text-white'
                                    : 'text-white hover:bg-white/10'
                                    }`}
                                onClick={() => setActiveTab('instructor')}
                            >
                                Instructor
                            </Button>
                            <Button
                                variant={activeTab === 'group' ? 'secondary' : 'ghost'}
                                className={`text-sm md:text-base px-2 py-1 md:px-3 md:py-2 rounded-lg ${activeTab === 'group'
                                    ? 'bg-white/10 border border-white/40 text-white'
                                    : 'text-white hover:bg-white/10'
                                    }`}
                                onClick={() => setActiveTab('group')}
                            >
                                Group Info
                            </Button>
                            <Button
                                variant={activeTab === 'files' ? 'secondary' : 'ghost'}
                                className={`text-sm md:text-base px-2 py-1 md:px-3 md:py-2 rounded-lg ${activeTab === 'files'
                                    ? 'bg-white/10 border border-white/40 text-white'
                                    : 'text-white hover:bg-white/10'
                                    }`}
                                onClick={() => setActiveTab('files')}
                            >
                                Files
                            </Button>
                            <Button
                                variant={activeTab === 'chat' ? 'secondary' : 'ghost'}
                                className={`text-sm md:text-base px-2 py-1 md:px-3 md:py-2 rounded-lg md:ml-auto ${activeTab === 'chat' ? 'bg-white/10 border border-white/40 text-white' : 'text-white hover:bg-white/10'}`}
                                onClick={() => setActiveTab('chat')}
                            >
                                <MessageCircle className="h-4 w-4 md:h-5 md:w-5 mr-2" />
                                Chat
                            </Button>
                        </div>
                    </motion.div>

                    {/* Content Sections */}
                    {activeTab === 'overview' && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                            className="space-y-6 mt-6"
                        >
                            <div>
                                <h3 className="text-[#347468] text-lg font-semibold mb-3">Project Description:</h3>
                                <p className="text-[#2d2d2d] text-sm font-medium leading-relaxed">
                                    {projectData.detailedDescription}
                                </p>
                            </div>

                            <div>
                                <h3 className="text-[#347468] text-lg font-semibold mb-3">Project Objectives:</h3>
                                <div className="text-[#2d2d2d] text-sm font-medium">
                                    <p className="mb-2">Upon completion of this course, you will be able to:</p>
                                    <ul className="space-y-1">
                                        {projectData.objectives.map((objective, index) => (
                                            <li key={index}>{objective}</li>
                                        ))}
                                    </ul>
                                </div>
                            </div>
                        </motion.div>
                    )}

                    {activeTab === 'instructor' && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                            className="mt-6"
                        >
                            <InstructorInfo instructorId={currentGroup?.instructorId} />
                        </motion.div>
                    )}

                    {activeTab === 'group' && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                            className="mt-6"
                        >
                            <GroupInfoTab
                                groupData={currentGroup}
                                projectAssignments={assignmentsData?.resultObject || []}
                                onEditAssignment={handleEditAssignment}
                            />
                        </motion.div>
                    )}

                    {activeTab === 'files' && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                            className="mt-6"
                        >
                            <FilesTab fileCounts={fileCounts} />
                        </motion.div>
                    )}

                    {activeTab === 'chat' && (
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                            className="mt-6 h-auto" // Added h-full to allow ChatComponent to fill height
                        >
                            <ChatComponent />
                        </motion.div>
                    )}
                </div>

                {/* Right Sidebar - Tasks */}
                <div className="hidden lg:block w-full lg:w-[393px] bg-[#fcfff6] border-l-0 lg:border-l border-graey-200 p-6">
                    <div className="mb-6">
                        <h2 className="text-[#2d2d2d] text-xl font-semibold capitalize mb-4">Tasks</h2>
                        <div className="w-full h-px bg-[#347468]"></div>
                    </div>

                    <ScrollArea className="h-full">
                        <div className="relative">
                            {/* Vertical Timeline Line */}
                            {projectData.tasks.length > 1 && (
                                <div
                                    className="absolute left-6 top-0 w-1 bg-[#347468]"
                                    style={{ height: timelineHeight }}
                                ></div>
                            )}

                            {/* Tasks */}
                            <div ref={timelineContainerRef} className="space-y-5">
                                <TaskListItems
                                    tasks={projectData.tasks}
                                    lastTaskRef={lastTaskRef}
                                    handleTaskClick={handleTaskClick}
                                />
                            </div>
                        </div>
                    </ScrollArea>


                </div>
            </div>

            {/* Mobile Task Sidebar */}
            <AnimatePresence>
                {isTasksSidebarOpen && (
                    <motion.div
                        initial={{ x: '100%' }}
                        animate={{ x: 0 }}
                        exit={{ x: '100%' }}
                        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                        className="fixed top-0 right-0 h-full w-[calc(100%-30px)] max-w-sm bg-[#fcfff6] z-40 px-6 pb-6 pt-12 lg:hidden shadow-lg flex flex-col"
                    >
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-[#2d2d2d] text-xl font-semibold capitalize">Tasks</h2>
                            <Button onClick={() => setIsTasksSidebarOpen(false)} variant="ghost" size="icon">
                                <X className="h-6 w-6" />
                            </Button>
                        </div>
                        <div className="w-full h-px bg-[#347468] mb-6"></div>
                        <div className="flex-1 min-h-[calc(100%-120px)]">
                            <TaskSidebarContent
                                projectData={projectData}
                                lastTaskRef={lastTaskRef}
                                timelineHeight={timelineHeight}
                                handleTaskClick={handleTaskClick}
                            />
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* AI Mentor Button */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="fixed bottom-6 right-6 z-50"
            >
                <button className="w-auto bg-gradient-to-r from-[#386C8D] via-[#036664] to-[#060B0B] text-white px-4 py-2.5 rounded-xl shadow-lg flex items-center justify-center gap-3 hover:shadow-xl transition-all hover:scale-105"
                >
                    <Icon iconNode={elephantFace} size={28} />
                    <span className="text-base font-semibold capitalize">AI Mentor</span>
                </button>
            </motion.div>

            {/* Upload Dialog */}
            <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>Submit Assignment</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        {/* Assignment Selection */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Select Assignment
                            </label>
                            <select
                                value={selectedAssignmentId}
                                onChange={(e) => setSelectedAssignmentId(e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#347468] focus:border-[#347468]"
                                disabled={isUploading}
                            >
                                <option value="">Choose an assignment...</option>
                                {assignmentsData?.resultObject?.filter((assignment: any) => {
                                    // Only show assignments that don't have a fileUrl or have an empty fileUrl
                                    const hasFileUrl = assignment.assignment?.fileUrl && assignment.assignment.fileUrl.trim() !== '';
                                    return !hasFileUrl;
                                }).map((assignment: any) => (
                                    <option key={assignment.id} value={assignment.id}>
                                        {assignment.assignment?.title || 'Untitled Assignment'}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* File Selection */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Select File
                            </label>
                            <input
                                ref={fileInputRef}
                                type="file"
                                onChange={handleFileSelect}
                                className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#347468] focus:border-[#347468]"
                                disabled={isUploading}
                                accept=".pdf,.doc,.docx,.txt,.zip,.rar"
                            />
                            {selectedFile && (
                                <p className="text-sm text-gray-600 mt-1">
                                    Selected: {selectedFile.name}
                                </p>
                            )}
                        </div>

                        {/* Upload Progress */}
                        {isUploading && (
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Upload Progress
                                </label>
                                <Progress value={uploadProgress} className="w-full" />
                                <p className="text-sm text-gray-600 mt-1">
                                    {uploadProgress}% uploaded
                                </p>
                            </div>
                        )}

                        {/* Action Buttons */}
                        <div className="flex gap-3 pt-4">
                            <Button
                                onClick={() => setIsUploadDialogOpen(false)}
                                variant="outline"
                                disabled={isUploading}
                                className="flex-1"
                            >
                                Cancel
                            </Button>
                            <Button
                                onClick={handleFileUploadAndSubmit}
                                disabled={!selectedFile || !selectedAssignmentId || isUploading}
                                className="flex-1 bg-[#347468] hover:bg-[#347468]/90 text-white"
                            >
                                {isUploading ? 'Submitting...' : 'Submit Assignment'}
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Edit Assignment Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>{editAssignmentTitle}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        {/* File Selection */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Select File
                            </label>
                            <input
                                ref={editFileInputRef}
                                type="file"
                                onChange={handleEditFileSelect}
                                className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#347468] focus:border-[#347468]"
                                disabled={isEditUploading}
                                accept=".pdf,.doc,.docx,.txt,.zip,.rar"
                            />
                            {selectedEditFile && (
                                <p className="text-sm text-gray-600 mt-1">
                                    Selected: {selectedEditFile.name}
                                </p>
                            )}
                        </div>

                        {/* Upload Progress */}
                        {isEditUploading && (
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Upload Progress
                                </label>
                                <Progress value={editUploadProgress} className="w-full" />
                                <p className="text-sm text-gray-600 mt-1">
                                    {editUploadProgress}% uploaded
                                </p>
                            </div>
                        )}

                        {/* Action Buttons */}
                        <div className="flex gap-3 pt-4">
                            <Button
                                onClick={() => setIsEditDialogOpen(false)}
                                variant="outline"
                                disabled={isEditUploading}
                                className="flex-1"
                            >
                                Cancel
                            </Button>
                            <Button
                                onClick={handleEditFileUploadAndSubmit}
                                disabled={!selectedEditFile || isEditUploading}
                                className="flex-1 bg-[#347468] hover:bg-[#347468]/90 text-white"
                            >
                                {isEditUploading ? 'Updating...' : 'Update Assignment'}
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Success Dialog */}
            <Dialog open={isSuccessDialogOpen} onOpenChange={setIsSuccessDialogOpen}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle className="text-center text-[#347468]">Success</DialogTitle>
                    </DialogHeader>
                    <div className="text-center py-4">
                        <div className="mb-4">
                            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                                <CheckCircle className="h-8 w-8 text-green-600" />
                            </div>
                            <p className="text-gray-700 text-lg">{successMessage}</p>
                        </div>
                        <Button
                            onClick={() => setIsSuccessDialogOpen(false)}
                            className="bg-[#347468] hover:bg-[#347468]/90 text-white px-8 py-2"
                        >
                            OK
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};


interface TaskSidebarContentProps {
    projectData: ProjectDetail;
    lastTaskRef: React.RefObject<HTMLDivElement>;
    timelineHeight: string;
    handleTaskClick: (taskId: string) => void;
}

interface TaskListItemsProps {
    tasks: TaskItem[];
    lastTaskRef: React.RefObject<HTMLDivElement>;
    handleTaskClick: (taskId: string) => void;
}

const TaskListItems: React.FC<TaskListItemsProps> = ({ tasks, lastTaskRef, handleTaskClick }) => {
    return (
        <>
            {tasks.map((task, index) => {
                const isLastTask = index === tasks.length - 1;
                return (
                    <motion.div
                        key={task.id}
                        ref={isLastTask ? lastTaskRef : null}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-center relative"
                    >
                        <div className="w-4 h-4 bg-[#347468] rounded-full flex items-center justify-center">
                            {task.completed && <CheckIcon className="h-3 w-3 text-white" />}
                        </div>
                        <div className="w-12 h-1 bg-[#347468]"></div>
                        <Card
                            className={`w-full sm:w-[280px] min-h-[70px] cursor-pointer transition-all duration-200 hover:shadow-md ${task.completed ? 'bg-[#347468]/20' : 'bg-[#347468]/20'} border-gray-200`}
                            onClick={() => handleTaskClick(task.id)}
                        >
                            <CardContent className="p-4 flex justify-between items-center h-full">
                                <div className="flex-1">
                                    <h4 className="text-[#2d2d2d] text-sm font-semibold capitalize mb-2">
                                        {task.title}
                                    </h4>
                                    <p className="text-[#2d2d2d] text-[10px] font-normal capitalize">
                                        {task.description}
                                    </p>
                                </div>
                            </CardContent>
                        </Card>
                    </motion.div>
                );
            })}
        </>
    );
};

const TaskSidebarContent: React.FC<TaskSidebarContentProps> = ({
    projectData,
    lastTaskRef,
    timelineHeight,
    handleTaskClick
}) => {
    return (
        <ScrollArea className="h-full">
            <div className="relative">
                {projectData.tasks.length > 1 && (
                    <div
                        className="absolute left-6 top-0 w-1 bg-[#347468]"
                        style={{ height: timelineHeight }}
                    ></div>
                )}
                <div className="space-y-5">
                    <TaskListItems
                        tasks={projectData.tasks}
                        lastTaskRef={lastTaskRef}
                        handleTaskClick={handleTaskClick}
                    />
                </div>
            </div>
        </ScrollArea>
    );
};

export default ProjectDetailsPage;
