<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 27.5.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 500 500" style="enable-background:new 0 0 500 500;" xml:space="preserve">
<g id="BACKGROUND">
	<rect id="XMLID_1_" style="fill:#FFFFFF;" width="500" height="500"/>
</g>
<g id="OBJECTS">
	<path style="fill:#FFDECF;" d="M299.042,407.798c-36.238-15.368-12.031-55.54,10.932-70.502
		c13.324-8.682,27.053-15.143,42.42-18.436c15.649-3.354,38.371-7.353,66.25-3.721c27.032,3.522,42.138,19.467,32.39,46.939
		c-8.462,23.847-32.203,40.869-55.85,46.421c-15.994,3.755-26.484,5.785-42.924,6.346
		C339.851,415.269,310.501,412.658,299.042,407.798z"/>
	<path style="fill:#FFDECF;" d="M86.345,137.475c-25.89,22.292-44.904,50.442-48.891,84.268
		c-6.242,52.95,22.728,113.37,55.58,153.35c11.005,13.393,24.307,24.613,39.686,32.832c50.818,27.16,100.896-12.752,133.126-49.339
		c21.632-24.556,48.862-45.424,77.837-60.703c56.358-29.719,129.537-57.06,121.825-134.656
		c-3.797-38.204-25.972-61.512-55.443-83.789c-29.293-22.141-61.583-26.674-97.482-26.838c-38.634-0.176-76.152,10.58-111.918,24.04
		C159.358,92.186,119.91,108.575,86.345,137.475z"/>
	<g>
		<g>
			<rect x="183.832" y="95.447" style="fill:#1B56CF;" width="245.728" height="151.011"/>
			<g>
				<defs>
					<rect id="SVGID_1_" x="183.832" y="95.447" width="245.728" height="151.011"/>
				</defs>
				<clipPath id="SVGID_00000057107648732620689890000013806576418338616730_">
					<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
				</clipPath>
				<g style="clip-path:url(#SVGID_00000057107648732620689890000013806576418338616730_);">
					
						<rect x="288.002" y="3.243" transform="matrix(-0.5122 -0.8589 0.8589 -0.5122 322.5361 522.5137)" style="fill:#FFFFFF;" width="43.304" height="332.837"/>
					
						<rect x="290.689" y="39.701" transform="matrix(6.123234e-17 -1 1 6.123234e-17 139.3172 477.6488)" style="fill:#FFFFFF;" width="35.589" height="258.929"/>
					<rect x="291.104" y="74.896" style="fill:#FFFFFF;" width="34.758" height="188.54"/>
					
						<rect x="284.708" y="3.243" transform="matrix(0.5122 -0.8589 0.8589 0.5122 5.3355 348.7169)" style="fill:#FFFFFF;" width="49.893" height="332.837"/>
					<rect x="296.773" y="74.896" style="fill:#EA5533;" width="23.421" height="188.54"/>
					
						<rect x="296.699" y="39.701" transform="matrix(6.123234e-17 -1 1 6.123234e-17 139.3172 477.6488)" style="fill:#EA5533;" width="23.568" height="258.929"/>
					
						<rect x="303.266" y="3.243" transform="matrix(-0.5122 -0.8589 0.8589 -0.5122 322.5353 522.5137)" style="fill:#EA5533;" width="12.777" height="332.837"/>
					
						<rect x="303.266" y="3.243" transform="matrix(0.5122 -0.8589 0.8589 0.5122 5.3364 348.7184)" style="fill:#EA5533;" width="12.777" height="332.837"/>
				</g>
			</g>
		</g>
		<path d="M338.621,236.83c-3.087-0.663-6.006-1.462-9.366-1.314c-3.71,0.164-6.48,2.215-5.321,6.365
			c2.467,8.835,14.979,6.617,21.74,7.446c2.237,0.274,16.678,3.478,11.753-2.664c-2.49-3.105-7.112-6.274-10.8-7.767
			c-1.438-0.582-2.883-1.148-4.383-1.562C340.672,236.899,338.865,236.985,338.621,236.83z"/>
		
			<polyline style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" points="
			383.215,95.447 429.56,95.447 429.56,139.028 		"/>
		
			<line style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="311.257" y1="95.447" x2="349.77" y2="95.447"/>
		
			<line style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="218.015" y1="95.447" x2="257.541" y2="95.447"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M183.832,171.46"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M429.26,196.593c-0.111,16.667-0.111,33.334-0.111,50.001c-10.016,0-20.034,0.007-30.049-0.136"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M429.56,169.433c-0.046,2.849-0.087,5.699-0.124,8.548"/>
	</g>
	<g>
		<g>
			
				<rect x="75.808" y="112.849" transform="matrix(0.9761 0.2174 -0.2174 0.9761 35.9157 -19.2504)" style="fill:#EA5533;" width="59.274" height="81.501"/>
			
				<rect x="74.842" y="106.659" transform="matrix(0.9761 0.2174 -0.2174 0.9761 33.9045 -13.3563)" style="fill:#131733;" width="5.622" height="81.501"/>
			<path style="fill:#FFFFFF;" d="M126.656,143.981l-27.771-6.186c-0.097-0.022-0.159-0.118-0.137-0.216l2.785-12.504
				c0.022-0.097,0.118-0.159,0.216-0.137l27.771,6.186c0.097,0.022,0.159,0.118,0.137,0.216l-2.785,12.504
				C126.85,143.941,126.753,144.003,126.656,143.981z"/>
		</g>
		<g>
			
				<rect x="57.438" y="151.967" transform="matrix(0.959 -0.2833 0.2833 0.959 -51.0307 32.5645)" style="fill:#1B56CF;" width="59.274" height="81.501"/>
			<rect x="56.957" y="160.033" transform="matrix(0.959 -0.2833 0.2833 0.959 -54.4332 25.1581)" width="5.622" height="81.501"/>
			
				<rect x="71.043" y="165.147" transform="matrix(0.959 -0.2833 0.2833 0.959 -45.1518 31.2442)" style="fill:#5AC794;" width="28.814" height="13.172"/>
		</g>
	</g>
	<path d="M156.478,281.578c-1.976-0.009-4.029,2.159-5.639,3.167c-1.775,1.112-3.667,1.964-5.549,2.837
		c-5.745,2.665-11.26,5.517-16.768,8.64c-3.123,1.77-7.508,5.914-6.039,9.934c1.021,2.792,4.443,3.909,7.185,3.813
		c6.593-0.232,8.854-7.191,12.36-11.646c1.785-2.268,3.451-4.613,5.222-6.902c1.299-1.678,2.541-3.207,4.116-4.657
		c1.026-0.946,2.041-1.834,3.009-2.85C155.031,283.226,156.454,282.632,156.478,281.578z"/>
	<g>
		<g>
			<path style="fill:#0F0F0F;" d="M363.539,378.253c1.677,3.299,3.316,6.62,4.813,10.005c1.397,3.158,2.299,4.298,6.071,4.253
				c3.101-0.038,6.532-1.486,9.323-2.7c5.386-2.342,10.51-5.336,15.756-7.97c16.433-8.251,35.559-15.458,49.84-27.28
				c-1.097-6.611-6.458-11.798-12.904-13.368c-12.73-3.098-24.086,7.124-33.725,13.671c-5.266,3.577-10.471-2.783-13.371-6.626
				c-5.304-7.026-12.403-9.636-20.871-6.99c-7.179,2.244-14.83,8.002-13.088,16.463
				C356.701,364.104,360.587,372.444,363.539,378.253z"/>
			<path style="fill:#5AC794;" d="M144.6,370.342c0.509,0.069,1.022,0.128,1.54,0.18c7.94,0.784,16.098,1.067,23.984-0.287
				c5.607-0.963,11.361-2.257,16.809-3.928c6.379-1.957,12.344-5.023,18.197-8.18c5.342-2.881,10.324-6.478,15.716-9.095
				c15.514-7.53,30.734-18.369,46.696-24.131c16.008-5.779,30.454-15.701,47.86-17.875c14.827-1.852,29.31-0.88,37.777-15.815
				c8.391-14.803,3-41.61-3.158-56.456c-5.873-14.16-26.608-19.18-39.946-21.806c-14.585-2.871-29.95-3.568-44.799-4.138
				c-10.662-0.409-21.358-0.043-32.025,0.218c-5.815,0.142-11.648,0.774-17.462,0.271c-5.676-0.49-10.9-2.245-16.677-2.227
				c-26.708,0.082-45.176,8.04-65.45,25.23c-13.921,11.803-23.167,24.912-29.203,42.067c-4.132,11.745-6.083,24.895-3.728,37.259
				C105.238,335.279,117.132,366.643,144.6,370.342z"/>
			<path style="fill:#FFFFFF;" d="M253.09,278.326c-7.196,2.531-14.979,3.378-22.083,6.08c-5.2,1.977-11.45,1.995-15.535-2.771
				c-3.581-4.178-7.393-8.223-10.841-12.601c-2.939-3.731-6.075-7.186-8.92-10.999c-2.608-3.495-4.711-7.497-7.476-10.818
				c3.678-1.057,8.106-1.298,11.991-2.176c7.171-1.621,13.874-4.886,21.017-6.626c6.277-1.529,12.859-2.016,19.27-1.216
				c3.145,0.393,6.016,1.359,9.061,2.151c2.069,0.538,1.753,0.887,3.331-0.709c1.013-1.025,1.654-2.592,2.478-3.78
				c2.827-4.08,6.667-6.797,10.691-9.491c6.017-4.028,12.485-6.222,19.496-7.596c3.845-0.753,7.696-1.641,11.58-2.161
				c2.519-0.337,5.571-0.029,7.886-0.665c-0.715,2.201-0.787,4.982-1.15,7.345c-1.092,7.105-2.12,14.285-3.006,21.41
				c-0.36,2.891-0.728,5.784-0.941,8.692c-0.189,2.578,0.384,4.483-1.38,6.469c-2.241,2.522-5.596,3.773-8.771,5.004
				c-1.023,0.397-2.028,0.792-2.97,1.226C275.892,270.132,264.423,274.341,253.09,278.326z"/>
			<path style="fill:#1B56CF;" d="M312.166,219.394c0.032-0.923,0.066-1.846,0.103-2.769c-1.598-0.261-3.403,0.945-4.856,1.477
				c-4.098,1.503-8.237,2.876-12.201,4.703c-8.376,3.861-16.688,7.803-24.727,12.387c-5.327,3.038-11.113,6.271-16.661,8.821
				c-5.399,2.482-13.288,2.041-19.194,2.544c-7.448,0.635-14.869,1.532-22.284,2.467c-3.507,0.442-7.017,0.865-10.52,1.334
				c-4.707,0.631-9.181,2.072-13.899,2.672c0.545,0.711,1.091,1.414,1.569,2.132c0.034,0.051,0.069,0.103,0.103,0.154
				c4.219,4.513,7.54,10.524,11.41,15.32c2.514,3.116,5.993,5.588,8.771,8.491c1.665,1.741,3.448,3.884,5.614,4.888
				c9.027,4.185,17.065-2.295,25.304-4.576c4.521-1.251,9.067-2.672,13.6-4.241c13.469-4.662,26.821-10.635,39.043-17.373
				c5.396-2.975,11.253-5.927,15.102-10.908c1.121-1.451,2.008-3.063,2.739-4.756C311.806,234.571,311.906,226.988,312.166,219.394z
				"/>
			<path d="M264.572,269.209c-1.595-7.128-2.59-14.423-4.252-21.515c-1.132-4.83-2.677-7.764-7.885-5.014
				c-1.267,0.669-2.68,1.046-2.997,2.524c-0.205,0.956,0.723,2.738,0.951,3.722c1.759,7.593,2.87,15.3,4.577,22.93
				C256.365,278.111,265.971,275.464,264.572,269.209z"/>
			<path style="fill:#5AC794;" d="M186.28,266.507c-2.19-0.947-6.946,2.217-8.608,2.926c-9.322,3.972-17.309,9.059-25.423,15.119
				c-10.098,7.542-21.87,17.762-27.425,29.448c2.036,2.148,3.714,5.413,5.36,7.995c3.874,6.076,8.548,12.009,14.408,16.294
				c5.96,4.358,14.017,8.654,21.079,10.807c1.102,0.336,5.001,2.067,6.41,1.511c0.911-0.36,3.642-5.325,4.281-6.096
				c12.701-15.346,26.038-35.207,24.381-56.084c-0.511-6.444-4.078-12.638-8.427-17.368
				C190.644,269.241,187.102,267.31,186.28,266.507z"/>
			<path style="fill:#0F0F0F;" d="M318.168,397.652c-2.64-0.424-0.602-18.9-0.595-21.644c0.016-6.516,0.354-15.712,2.091-22.001
				c3.93-14.224,28.202-9.853,34.423-0.11c1.851,2.898,0.952,6.758,1.333,10.022c0.53,4.541,1.739,8.99,1.626,13.595
				c2.996-1.686,6.866-1.292,10.214-1.285c7.915,0.017,16.03-0.341,23.874,0.89c4.219,0.662,7.853,1.904,11.611,3.88
				c4.519,2.376,10.4,12.786,2.67,15.011"/>
			<path style="fill:#FFD0C4;" d="M209.824,292.904c-0.661,1.175-1.182,2.396-2.044,3.505c-3.263,4.199-6.72,2.466-9.634-0.753
				c-3.491-3.857-5.832-8.168-8.686-12.445c-3.101-4.648-6.908-9.458-7.762-15.162c-0.048-0.323,13.047-5.768,14.306-6.241
				c4.957-1.861,10.155-3.111,15.385-3.977c4.168-0.69,8.362-1.621,12.595-1.68c3.196-0.045,6.352,0.002,9.361,1.201
				c1.136,0.453,5.644,2.506,2.922,3.817c1.909,0.943,2.611,3.588,0.201,4.439c2.119,0.729,3.535,3.135,2.948,5.301
				c-0.891,3.284-3.686,5.599-6.479,7.297c-3.974,2.417-8.036,4.683-12.102,6.941c-3.26,1.81-7.532,3.216-9.934,6.145
				C210.477,291.807,210.135,292.35,209.824,292.904z"/>
			<path style="fill:#5AC794;" d="M337.921,373.729c0,2.715-2.176,4.915-4.859,4.915c-2.684,0-4.859-2.201-4.859-4.915
				s2.176-4.915,4.859-4.915C335.745,368.814,337.921,371.015,337.921,373.729z"/>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M236.417,261.208c-3.532-1.02-8.037-0.624-11.661-0.375c-8.173,0.564-15.797,3.275-23.621,5.478"/>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M236.743,265.121c-2.757,0.795-5.591,1.302-8.401,1.933c-8.673,1.949-16.559,5.098-24.861,8.176"/>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M230.547,273.274c-7.121,2.111-13.804,4.233-20.544,7.5"/>
			<path style="fill:#DE81CB;" d="M307.93,334.773c0.782,6.804,2.077,13.494,3.326,20.219c1.015,5.46,1.785,11.258,2.713,17.005
				c5.89-0.11,11.778-0.325,17.661-0.643c14.348-0.776,28.649-2.102,42.923-3.69c-2.849-16.073-0.612-32.572-2.476-48.839
				c-2.331-20.336-7.383-40.277-15.211-59.197c-4.55-10.998-9.838-19.334-19.026-26.72c-9.295-7.469-18.76-8.291-30.376-7.272
				c-36.871,3.234-67.381,32.47-90.708,58.512c-22.387,24.993-55.691,53.372-80.786,75.887
				c8.953,17.323,27.707,22.274,44.368,30.806c6.597,3.378,15.7,6.014,22.985,7.324c8.306,1.494,16.92,0.777,25.031-1.432
				c34.641-9.433,58.201-49.881,77.845-76.829C307.897,322.144,307.598,331.884,307.93,334.773z"/>
			<path style="fill:#DE81CB;" d="M319.523,337.447c1.992,6.881,5.606,19.194,12.715,22.374c5.045,2.257,10.771,1.851,16.117,1.241
				c4.969-0.567,9.679,0.022,14.672-0.559c12.124-1.411,23.725-5.468,35.646-7.899c-2.638-5.297-3.447-11.667-5.022-17.328
				c-1.8-6.471-3.838-12.875-6.117-19.193c-4.312-11.955-7.625-24.243-12.116-36.147c-1.704-4.517-3.012-9.732-5.142-14.069
				c-7.118-14.494-10.461-29.635-26.411-37.338c-24.59-11.875-46.907,7.556-65.187,21.682
				c-15.014,11.602-29.492,24.474-40.611,39.959c-5.449,7.589-8.898,15.104-11.921,23.766c-1.613,4.621-5.485,8.759-8.094,12.893
				c-2.879,4.563-5.605,9.22-8.246,13.924c-6.653,11.85-22.969,36.225-10.129,49.245c5.579,5.657,14.998,7.278,22.6,7.777
				c6.516,0.428,13.178,1.047,19.701,1.043c10.929-0.005,22.921-0.843,33.307-4.431c8.454-2.92,15.893-10.245,20.314-17.861
				c5.037-8.678,4.877-19.361,6.711-28.994c1.363-7.161,1.24-14.494,2.423-21.717c0.69-4.217,2.29-8.755,2.138-13.026
				C314.03,317.832,317.236,329.547,319.523,337.447z"/>
			<g>
				<defs>
					<path id="SVGID_00000081614127458263463120000007121395279657727132_" d="M307.93,334.773
						c0.782,6.804,2.077,13.494,3.326,20.219c1.015,5.46,1.785,11.258,2.713,17.005c5.89-0.11,11.778-0.325,17.661-0.643
						c14.348-0.776,28.649-2.102,42.923-3.69c-2.849-16.073-0.612-32.572-2.476-48.839c-2.331-20.336-7.383-40.277-15.211-59.197
						c-4.55-10.998-9.838-19.334-19.026-26.72c-9.295-7.469-18.76-8.291-30.376-7.272c-36.871,3.234-66.652,33.142-90.708,58.512
						c-25.727,27.134-50.355,52.574-80.056,75.294c8.953,17.323,26.976,22.867,43.637,31.399c6.597,3.378,15.7,6.014,22.985,7.324
						c8.306,1.494,16.92,0.777,25.031-1.432c34.641-9.433,58.201-49.881,77.845-76.829
						C307.897,322.144,307.598,331.884,307.93,334.773z"/>
				</defs>
				<clipPath id="SVGID_00000068677291225234589920000014903408296331310472_">
					<use xlink:href="#SVGID_00000081614127458263463120000007121395279657727132_"  style="overflow:visible;"/>
				</clipPath>
				<g style="clip-path:url(#SVGID_00000068677291225234589920000014903408296331310472_);">
					<path style="fill:#EA5533;" d="M362.276,375.093c-0.683-2.071-1.517-3.666-1.72-5.863c-0.206-2.227-0.283-4.388-0.692-6.612
						c-0.922-5.013-2.479-9.949-3.217-14.966c-0.502-3.413-4.781-5.223-7.798-4.427c-3.642,0.96-4.923,4.427-4.427,7.798
						c0.74,5.026,2.62,9.894,3.217,14.966c0.225,1.914,0.263,3.826,0.536,5.763c0.368,2.607,1.065,4.255,1.875,6.712
						C352.595,386.176,364.842,382.873,362.276,375.093z"/>
					<path style="fill:#EA5533;" d="M372.086,304.046c-2.059,2.137-6.922,1.789-9.701,2.454c-3.583,0.858-8.51,1.435-11.532,3.591
						c-6.585,4.699-0.261,15.699,6.399,10.947c1.282-0.915,3.765-1.116,5.327-1.517c2.184-0.561,4.35-1.084,6.578-1.438
						c4.478-0.711,8.636-1.693,11.894-5.073C386.714,307.134,377.761,298.158,372.086,304.046z"/>
					<path style="fill:#EA5533;" d="M327.634,329.088c-2.702,1.702-4.332,3.829-6.414,6.157c-2.575,2.88-5.486,5.48-8.119,8.317
						c-2.288,2.464-4.334,4.513-6.013,5.854c-0.647,0.517-1.242,1.016-1.792,1.529c-0.78,0.493-1.437,1.218-1.89,2.196
						c-0.42,0.638-0.79,1.339-1.092,2.16c-1.331,3.621,2.193,8.429,6.113,8.024l0.485-0.05c2.388-0.246,5.017-1.506,5.898-3.908
						c0.595-0.397,1.388-1.07,1.806-1.418c2.173-1.811,4.077-3.942,5.996-6.014c2.115-2.284,4.372-4.395,6.53-6.631
						c1.606-1.664,2.931-4.034,4.891-5.269C340.919,335.697,334.564,324.722,327.634,329.088z"/>
					<path style="fill:#EA5533;" d="M324.494,291.642c-0.101,6.136-0.768,12.136-0.704,18.294c0.086,8.153,12.764,8.174,12.678,0
						c-0.065-6.156,0.603-12.159,0.704-18.294C337.307,283.48,324.629,283.478,324.494,291.642z"/>
					<path style="fill:#EA5533;" d="M371.258,269.985c-2.349,1.521-6.077,2.165-8.733,3.12c-4.045,1.454-8.043,3.124-12.269,3.981
						c-7.99,1.621-4.608,13.844,3.37,12.225c4.778-0.97,9.251-2.969,13.835-4.57c3.295-1.151,7.259-1.908,10.195-3.808
						C384.482,276.514,378.134,265.534,371.258,269.985z"/>
					<path style="fill:#EA5533;" d="M343.055,258.311c-2.777-2.517-5.817-4.747-8.958-6.787c-0.713-0.463-1.432-0.916-2.156-1.363
						c-0.214-0.132-0.427-0.263-0.641-0.395c-0.021-0.024-0.102-0.093-0.264-0.223c-1.583-1.209-2.809-2.345-4.85-2.713
						c-3.366-0.606-6.833,0.916-7.798,4.427c-0.837,3.048,1.014,7.122,4.354,7.779c0.48,0.376,0.93,0.809,1.422,1.162
						c1.137,0.815,2.36,1.51,3.534,2.273c2.254,1.464,4.399,2.998,6.392,4.804C340.124,272.744,349.117,263.805,343.055,258.311z"/>
					<path style="fill:#EA5533;" d="M304.374,275.679c-3.536-0.971-6.613,1.202-7.798,4.427c-1.898,5.17-4.57,10.03-5.629,15.479
						c-0.652,3.357,0.948,6.842,4.427,7.798c3.107,0.854,7.142-1.05,7.798-4.427c1.058-5.448,3.73-10.309,5.629-15.479
						C309.982,280.262,307.48,276.532,304.374,275.679z"/>
					<path style="fill:#EA5533;" d="M280.616,249.164c-2.711,2.956-6.382,5.08-9.377,7.763c-3.343,2.995-6.003,6.437-8.917,9.827
						c-5.293,6.158,3.636,15.165,8.965,8.965c2.914-3.391,5.572-6.831,8.916-9.827c3.008-2.694,6.65-4.789,9.378-7.763
						C295.087,252.125,286.145,243.135,280.616,249.164z"/>
					<path style="fill:#EA5533;" d="M276.933,312.266c-3.935-0.634-7.129-2.956-10.277-5.265c-3.715-2.725-7.917-4.915-12.131-6.761
						c-7.402-3.242-13.863,7.678-6.399,10.947c4.647,2.036,9.024,4.538,13.123,7.511c3.635,2.637,7.831,5.07,12.314,5.793
						c3.406,0.549,6.78-0.883,7.798-4.427C282.227,317.049,280.306,312.81,276.933,312.266z"/>
					<path style="fill:#EA5533;" d="M277.139,369.465c-1.072-4.067-3.684-7.587-4.998-11.642c-1.615-4.983-3.42-9.885-4.149-15.096
						c-0.478-3.417-4.798-5.218-7.798-4.427c-3.66,0.965-4.9,4.423-4.427,7.798c0.729,5.21,2.534,10.112,4.149,15.095
						c0.656,2.026,1.317,4.047,2.147,6.01c0.804,1.902,2.333,3.669,2.851,5.631C266.991,380.717,279.224,377.374,277.139,369.465z"
						/>
					<path style="fill:#EA5533;" d="M228.965,330.659c-4.226,6.436-7.484,13.361-10.554,20.405c-1.377,3.16-0.906,6.77,2.274,8.673
						c2.67,1.598,7.307,0.86,8.673-2.274c3.07-7.044,6.328-13.969,10.554-20.405C244.404,330.218,233.43,323.859,228.965,330.659z"
						/>
					<path style="fill:#EA5533;" d="M225.553,308.268c-4.7-4.469-10.059-8.271-15.203-12.206c-4.29-3.282-9.601-7.482-15.035-8.422
						c-7.98-1.38-11.407,10.835-3.37,12.225c4.192,0.725,8.596,4.551,11.907,7.121c4.308,3.343,8.778,6.483,12.736,10.247
						C222.5,322.854,231.48,313.904,225.553,308.268z"/>
					<path style="fill:#EA5533;" d="M191.525,352.152c-0.892-8.185-1.086-16.406-1.407-24.626c-0.318-8.132-12.998-8.172-12.678,0
						c0.322,8.221,0.515,16.442,1.407,24.626c0.374,3.43,2.646,6.3,6.339,6.339C188.328,358.524,191.896,355.553,191.525,352.152z"
						/>
					<path style="fill:#EA5533;" d="M212.847,377.204c-1.17,5.621-2.013,11.253-2.386,16.984c-0.345,5.304-1.491,10.397-2.062,15.66
						c-0.373,3.432,3.181,6.306,6.339,6.339c3.733,0.039,5.971-2.95,6.339-6.339c0.513-4.721,1.621-9.326,1.961-14.095
						c0.368-5.155,0.979-10.113,2.034-15.179C226.73,372.613,214.512,369.209,212.847,377.204z"/>
					<path style="fill:#EA5533;" d="M234.432,248.721c0.046,4.942-0.188,9.861-0.704,14.776c-0.357,3.41,3.169,6.339,6.339,6.339
						c3.725,0,5.981-2.919,6.339-6.339c0.515-4.915,0.749-9.834,0.704-14.776C247.035,240.568,234.356,240.548,234.432,248.721z"/>
					<path style="fill:#EA5533;" d="M329.704,222.204c-3.879-1.358-7.557-2.99-11.151-4.99c-7.136-3.971-13.537,6.975-6.399,10.947
						c4.526,2.518,9.293,4.558,14.179,6.268c3.231,1.131,6.935-1.286,7.798-4.427C335.092,226.507,332.947,223.339,329.704,222.204z
						"/>
				</g>
			</g>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M225.656,397.841c12.7-2.141,23.805-10.513,33.049-19.021c11.064-10.183,20.604-21.999,29.324-34.213
				c5.981-8.378,13.335-17.533,16.929-27.21c1.231,1.79,2.385,9.03,3.118,10.742"/>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M321.772,226.643c24.772,2.921,35.001,30.878,41.086,52.015c9.022,31.337,9.414,47.513,10.844,80.053"/>
			<path style="fill:#FFD0C4;" d="M224.492,189.034c1.873-3.56,3.833-7.055,5.755-10.584c2.638-4.845,6.705-9.849,10.816-13.527
				c6.42-5.744,16.796-7.259,24.735-4.071c6.508,2.614,13.66,6.914,13.309,12.809c-0.641,10.76-8.198,21.263-14.077,25.253
				c-2.482,1.684-6.114,2.33-9.792,2.295c-3.463-0.033-6.324-0.751-9.507-1.94c0.035,2.585-1.708,11.278,1.759,12.41
				c-2.259,2.604-4.832,4.857-8.157,5.896c-7.718,2.411-17.312-0.114-22.88-6.295c-1.676-1.86-1.092-1.705,0.128-4.9
				c2.12-5.553,4.564-10.892,7.293-16.163C224.08,189.821,224.285,189.427,224.492,189.034z"/>
			<path d="M264.526,172.706c0.456,1.398,0.32,3.465,1.769,4.298c2.007,1.154,3.779-1.247,4.678-2.933
				c-0.589,1.104,0.29,5.27,3.32,1.711c0.238,2.386,1.266,8.476,4.8,6.639c2.23-1.159,3.938-6.611,4.086-9.12
				c0.267-4.544-0.798-11.255-3.14-13.932c-1.292-1.477-2.376-2.613-5.212-2.615c-10.538-13.173-25.574-7.805-32.935-4.365
				c-6.474,3.025-37.704,16.682-23.252,21.27c-8.613,5.327-36.944,31.722-20.664,38.635c4.906,2.083,9.555,3.978,14.941,3.014
				c7.327-1.31,10.617-5.899,14.921-11.693c-0.58,0.781,0.66,1.937,1.577,2.293c1.281,0.498,5.069-0.054,7.884-1.863
				c6.02-3.87,13.686-14.984,21.268-22.837C260.658,179.044,263.748,175.6,264.526,172.706z"/>
			<path d="M256.961,191.18c0.457,4.417,5.781,5.719,9.294,3.18"/>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M199.242,296.753c-7.076-8.336-14.796-19.129-17.935-29.674"/>
			<path style="fill:none;stroke:#5AC794;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M405.007,382.515c-12.777-10.481-32.017-9.913-46.957-5.544"/>
			<path style="fill:none;stroke:#EA5533;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M401.746,348.275c-1.188,2.007-3.862,6.821-2.13,9.019c0.589,0.747,1.843,0.971,2.688,0.489c2.41-1.375,2.111-5.954,2.79-8.177
				c0.379,1.181,0.605,2.895,1.652,3.708c1.327,1.03,2.638,0.036,3.24-1.277c1.076-2.349,1.091-4.8,0.847-7.268
				c0.797,1.528,1.584,3.323,3.095,4.331c3.738,2.492,1.875-7.846,1.515-8.977"/>
			<path style="fill:none;stroke:#EA5533;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M372.464,371.501c-0.157,1.899-1.33,7.369-4.457,5.869c-1.667-0.8-1.398-3.552-1.047-5.021c-0.577,1.432-5.18,9.212-6.668,5.017
				c-0.508-1.432,0.23-3.005,0.564-4.391c-0.042,0.133-0.078,0.268-0.119,0.401c-0.408,1.345-0.907,2.904-2.021,3.842
				c-0.923,0.778-2.942,1.348-3.766,0.115c-1.147-1.717,1.427-5.206,2.855-6.067"/>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M181.434,268.423c-2.107,2.233-6.193,4.806-8.541,6.779c-3.827,3.214-7.614,6.546-11.333,9.883
				c-10.012,8.986-16.69,19.617-23.297,31.233"/>
			<g>
				<defs>
					<path id="SVGID_00000003092195912402731480000018321177800937992594_" d="M318.168,397.652
						c-2.64-0.424-0.602-18.9-0.595-21.644c0.016-6.516,0.354-15.712,2.091-22.001c3.93-14.224,28.202-9.853,34.423-0.11
						c1.851,2.898,0.952,6.758,1.333,10.022c0.53,4.541,1.739,8.99,1.626,13.595c2.996-1.686,6.866-1.292,10.214-1.285
						c7.915,0.017,16.03-0.341,23.874,0.89c6.825,1.071,13.212,3.117,16.927,9.382c1.831,3.089,3.722,9.604-1.004,10.681"/>
				</defs>
				<clipPath id="SVGID_00000060721682581186330000000005726064932463101602_">
					<use xlink:href="#SVGID_00000003092195912402731480000018321177800937992594_"  style="overflow:visible;"/>
				</clipPath>
				
					<ellipse style="clip-path:url(#SVGID_00000060721682581186330000000005726064932463101602_);fill:none;stroke:#5AC794;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" cx="319.693" cy="395.307" rx="10.202" ry="10.32"/>
			</g>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M245.931,199.547c-0.2-0.305-0.573-0.532-0.862-0.782c-1.009-0.87-1.783-1.988-2.577-3.049"/>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M247.886,210.727c-0.947,2.23-3.349,3.881-5.319,5.124c-3.493,2.203-7.063,2.758-11.142,2.383
				c-2.674-0.245-5.283-0.65-7.778-1.683c-3.728-1.544-7.086-4.408-9.144-7.857"/>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M176.579,324.156c-8.873,8.193-17.907,16.21-27.005,24.152"/>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M206.347,295.099c-8.495,8.846-17.278,17.413-26.242,25.783"/>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M225.676,274.569c-4.268,4.78-8.63,9.475-13.067,14.099"/>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M278.96,233.932c-18.554,8.567-35.338,22.511-49.658,36.921"/>
			<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M288.889,229.398c3.892-1.202,8.166-2.232,12.196-3.283"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000159464154108749918210000004872676126752343729_" d="M319.523,337.447
					c1.992,6.881,5.606,19.194,12.715,22.374c5.045,2.257,10.771,1.851,16.117,1.241c4.969-0.567,9.679,0.022,14.672-0.559
					c12.124-1.411,23.725-5.468,35.646-7.899c-2.638-5.297-3.447-11.667-5.022-17.328c-1.8-6.471-3.838-12.875-6.117-19.193
					c-4.312-11.955-7.625-24.243-12.116-36.147c-1.704-4.517-3.012-9.732-5.142-14.069c-7.118-14.494-10.461-29.635-26.411-37.338
					c-24.59-11.875-46.907,7.556-65.187,21.682c-15.014,11.602-29.492,24.474-40.611,39.959
					c-5.449,7.589-8.898,15.104-11.921,23.766c-1.613,4.621-5.485,8.759-8.094,12.893c-2.879,4.563-5.605,9.22-8.246,13.924
					c-6.653,11.85-22.969,36.225-10.129,49.245c5.579,5.657,14.998,7.278,22.6,7.777c6.516,0.428,13.178,1.047,19.701,1.043
					c10.929-0.005,22.921-0.843,33.307-4.431c8.454-2.92,15.893-10.245,20.314-17.861c5.037-8.678,4.877-19.361,6.711-28.994
					c1.363-7.161,1.24-14.494,2.423-21.717c0.69-4.217,2.29-8.755,2.138-13.026C314.03,317.832,317.236,329.547,319.523,337.447z"/>
			</defs>
			<clipPath id="SVGID_00000167375210037180858200000015667093988563191720_">
				<use xlink:href="#SVGID_00000159464154108749918210000004872676126752343729_"  style="overflow:visible;"/>
			</clipPath>
			<path style="clip-path:url(#SVGID_00000167375210037180858200000015667093988563191720_);" d="M270.983,391.429
				c-2.008,0.811-4.18,1.229-6.747,0.906c-1.948-0.245-5.456-1.209-7.104-2.366c-2.457-1.725-1.607-4.981-2.393-7.393
				c-1.506,0.677-2.807,2.145-4.161,3.122c-1.882,1.358-3.888,2.719-5.837,3.987c-5.408,3.52-12.93,5.849-19.129,7.748
				c-3.277,1.004-7.441,1.133-10.031,3.648c-0.221,0.215,10.067,2.092,10.942,2.23c3.87,0.609,7.102,2.542,10.633,4.113
				c3.58,1.593,7.98,0.828,11.74,0.178c5.489-0.949,10.294-4.162,15.769-5.116c5.874-1.024,12.183-0.376,17.752-2.827
				c4.214-1.854,12.311-9.133,9.208-14.363c-2.577-4.346-8.74-0.866-11.813,0.939C276.706,388.06,274.033,390.198,270.983,391.429z"
				/>
		</g>
		<path d="M379.382,362.706c-8.282,0.594-5.907-14.771-6.332-19.692c-0.824-9.539,0.084-19.843-2.844-29.014
			c2.677,2.7,3.326,6.363,4.011,9.994c1.138,6.034,2.217,12.069,3.132,18.151c0.813,5.4,1.89,9.911,4.103,14.92
			C382.457,359.34,382.762,360.504,379.382,362.706z"/>
	</g>
	<g>
		<path style="fill:#FFFFFF;" d="M219.42,345.372c-0.562,2.926-1.594,5.735-2.727,7.952c-1.91,3.738-4.55,10.053-8.105,12.5
			c-2.808,1.933-5.885,1.058-8.935,0.379c-2.439-0.543-5.131,0.059-7.206-1.512c-6.864-5.196-11.928-13.816-11.71-22.552
			c0.129-5.181,3.169-11.028,8.616-12.193c2.232-0.478,4.016,0.254,6.137,0.394c2.536,0.167,5.473-0.457,7.931-1.027
			c4.739-1.1,9.325-0.947,13.04,2.531C219.98,335.138,220.372,340.42,219.42,345.372z"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M199.042,333.353c-0.171-4.999,4.131-9.915,10.308-11.779"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M210.267,357.382c-0.547,1.088-1.175,2.138-1.878,3.132"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M215.691,336.172c0.88,4.824-0.887,9.8-2.582,14.247"/>
	</g>
	<g>
		<g>
			<polygon style="fill:#312D54;" points="328.569,412.239 328.25,406.211 348.913,404.958 349.232,410.986 			"/>
			<polygon style="fill:#EA5533;" points="256.459,416.676 256.141,410.649 346.922,405.145 347.241,411.172 			"/>
			<polygon style="fill:#FFD0C4;" points="247.135,413.862 256.131,410.471 256.45,416.499 			"/>
			<polygon style="fill:#EA5533;" points="251.039,412.391 247.135,413.862 251.177,415.007 			"/>
		</g>
		<g>
			<polygon style="fill:#312D54;" points="331.607,430 331.888,423.905 352.804,425.142 352.524,431.238 			"/>
			<polygon style="fill:#5AC794;" points="258.607,425.747 258.887,419.651 350.782,425.089 350.501,431.184 			"/>
			<polygon style="fill:#FFD0C4;" points="249.506,421.793 258.896,419.472 258.615,425.568 			"/>
			<polygon style="fill:#0F0F0F;" points="253.58,420.786 249.506,421.793 253.459,423.431 			"/>
		</g>
		<g>
			<polygon style="fill:#312D54;" points="395.322,418.549 395.104,412.451 416.051,411.975 416.27,418.074 			"/>
			<polygon style="fill:#1B56CF;" points="322.218,420.274 322,414.176 414.031,412.087 414.25,418.186 			"/>
			<polygon style="fill:#FFD0C4;" points="312.824,417.076 321.993,413.996 322.212,420.095 			"/>
			<polygon style="fill:#1B56CF;" points="316.803,415.74 312.824,417.076 316.898,418.386 			"/>
		</g>
	</g>
	<g>
		<g>
			<polygon points="431.21,301.084 432.849,297.676 434.55,301.052 438.824,300.806 438.767,300.82 435.314,304.203 
				438.252,308.562 432.826,306.305 428.104,308.647 430.099,304.254 426.76,301.386 			"/>
		</g>
		<g>
			<polygon points="137.596,251.39 138.926,248.602 140.307,251.363 143.777,251.163 143.731,251.174 140.928,253.943 
				143.313,257.509 138.907,255.662 135.074,257.579 136.693,253.984 133.982,251.637 			"/>
		</g>
		<g>
			<polygon points="38.246,295.742 39.836,292.436 41.486,295.71 45.631,295.472 45.577,295.486 42.227,298.768 45.077,302.996 
				39.813,300.806 35.232,303.079 37.167,298.817 33.929,296.034 			"/>
		</g>
		<g>
			<polygon points="55.863,130.765 57.195,127.994 58.578,130.738 62.052,130.539 62.007,130.55 59.199,133.301 61.588,136.844 
				57.176,135.009 53.338,136.913 54.959,133.342 52.245,131.01 			"/>
		</g>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M418.334,276.728c0,3.154-2.388,5.71-5.335,5.71c-2.946,0-5.335-2.557-5.335-5.71c0-3.153,2.388-5.71,5.335-5.71
			C415.946,271.018,418.334,273.574,418.334,276.728z"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M176.108,123.69c0,3.129-2.37,5.666-5.294,5.666s-5.294-2.537-5.294-5.666c0-3.129,2.37-5.666,5.294-5.666
			S176.108,120.56,176.108,123.69z"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M63.101,270.53c0,3.294-2.495,5.964-5.572,5.964c-3.077,0-5.572-2.67-5.572-5.964c0-3.294,2.495-5.964,5.572-5.964
			C60.607,264.566,63.101,267.236,63.101,270.53z"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M440.833,275.059c0,1.318-0.998,2.386-2.229,2.386c-1.231,0-2.229-1.068-2.229-2.386c0-1.318,0.998-2.386,2.229-2.386
			C439.835,272.674,440.833,273.742,440.833,275.059z"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M77.294,378.879c0,1.629-1.234,2.95-2.756,2.95c-1.522,0-2.755-1.32-2.755-2.95c0-1.629,1.234-2.949,2.755-2.949
			C76.061,375.93,77.294,377.251,77.294,378.879z"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M67.259,363.832c0-2.771,2.246-5.018,5.018-5.018c2.771,0,5.018,2.247,5.018,5.018"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M454.427,183.093c-2.582,0-4.674-2.093-4.674-4.675c0-2.582,2.093-4.674,4.674-4.674"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M149.371,180.059c3.012,0,5.453,2.442,5.453,5.453c0,3.012-2.441,5.454-5.453,5.454"/>
	</g>
	<g>
		<path d="M120.736,277.605c-2.109-2.429-5.019-3.715-7.683-3.551c-2.675,0.165-4.822,1.595-6.377,3.634
			c-1.738,2.279-2.696,5.277-2.954,8.458c-0.228,2.819,3.967,4.026,4.194,1.219c0.212-2.624,1.084-5.055,2.624-6.828
			c1.37-1.578,3.303-2.35,5.438-1.634c2.024,0.679,3.188,2.828,3.586,4.875c0.523,2.686-0.347,5.373-1.223,7.753
			c-0.918,2.495,3.089,4.797,4.002,2.314C124.079,289.131,125.162,282.703,120.736,277.605z"/>
		<path d="M174.524,376.586c-1.702-5.15-3.942-10.169-6.05-15.163c-4.185-9.914-8.721-19.69-13.987-29.08
			c-5.63-10.037-12.613-19.499-16.567-30.388c-0.629-1.732-2.32-2.9-4.198-2.384c-1.671,0.459-3.016,2.457-2.384,4.198
			c3.314,9.128,8.49,17.283,13.48,25.554c5.131,8.504,9.67,17.33,13.765,26.377c2.069,4.57,4.03,9.186,5.919,13.833
			c1.746,4.294,4.007,8.794,4.575,13.443c0.891,7.287-4.664,13.389-11.053,16.006c-9.608,3.937-20.626,0.214-29.559-3.797
			c-1.68-0.755-3.669-0.485-4.669,1.224c-0.853,1.459-0.465,3.91,1.224,4.669c9.633,4.326,20.198,7.821,30.853,5.661
			c8.4-1.703,16.193-7.721,19.091-15.894C176.613,386.198,176.043,381.182,174.524,376.586z"/>
		<path style="fill:#EA5533;" d="M150.444,358.5c-1.354-17.565-5.182-34.866-10.1-51.752c-0.96-3.296-2.068-6.208-3.353-8.799
			c-2.851-2.236-5.824-4.312-8.957-6.157c-9.242-5.442-19.596-8.865-30.27-9.946c-2.129,0.319-4.218,0.828-6.221,1.651
			c-7.125,2.928-12.303,9.201-16.51,15.654c-14.42,22.115-20.587,47.072-25.685,72.333c-2.595,12.857-4.561,26.157-2.081,39.037
			c0.357,1.852,0.864,3.795,2.224,5.102c1.215,1.167,2.92,1.641,4.56,2.023c16.163,3.767,33.016,2.578,49.567,1.367
			c6.329-0.463,12.264-3.647,18.721-4.335c2.991-0.319,6.002-0.414,8.996-0.708c3.23-0.317,6.438-0.866,9.59-1.64
			c1.677-0.412,3.421-0.935,4.628-2.171c0.985-1.009,1.496-2.381,1.918-3.727C151.987,392.031,151.594,373.422,150.444,358.5z"/>
		<g>
			<defs>
				<path id="SVGID_00000083789796306088398420000007985870528618728834_" d="M150.444,358.5
					c-1.354-17.565-5.182-34.866-10.1-51.752c-0.96-3.296-2.068-6.208-3.353-8.799c-2.851-2.236-5.824-4.312-8.957-6.157
					c-9.242-5.442-19.596-8.865-30.27-9.946c-2.129,0.319-4.218,0.828-6.221,1.651c-7.125,2.928-12.303,9.201-16.51,15.654
					c-14.42,22.115-20.587,47.072-25.685,72.333c-2.595,12.857-4.561,26.157-2.081,39.037c0.357,1.852,0.864,3.795,2.224,5.102
					c1.215,1.167,2.92,1.641,4.56,2.023c16.163,3.767,33.016,2.578,49.567,1.367c6.329-0.463,12.264-3.647,18.721-4.335
					c2.991-0.319,6.002-0.414,8.996-0.708c3.23-0.317,6.438-0.866,9.59-1.64c1.677-0.412,3.421-0.935,4.628-2.171
					c0.985-1.009,1.496-2.381,1.918-3.727C151.987,392.031,151.594,373.422,150.444,358.5z"/>
			</defs>
			<clipPath id="SVGID_00000114793805527512986230000011272947119058563469_">
				<use xlink:href="#SVGID_00000083789796306088398420000007985870528618728834_"  style="overflow:visible;"/>
			</clipPath>
			<path style="clip-path:url(#SVGID_00000114793805527512986230000011272947119058563469_);fill:#EA5533;" d="M157.839,316.894
				c-2.132-6.89-4.937-13.971-10.511-18.548c-1.584-1.301-4.289-2.716-7.152-3.531c-0.853-0.243-4.822,18.878-5.313,20.343
				c-3.066,9.147-5.995,18.116-7.927,27.539c-2.598,12.668-0.349,25.748,0.419,38.657c0.593,9.976,0.293,20.005-0.897,29.928
				c-0.335,2.79-0.675,5.867,0.91,8.187c1.153,1.687,3.147,2.635,5.154,3.021c4.873,0.938,9.969-1.096,13.686-4.383
				c3.717-3.288,6.244-7.703,8.357-12.193C167.503,378.422,166.822,345.921,157.839,316.894z"/>
		</g>
		<path style="fill:#B51C11;" d="M112.435,417.496c1.643-8.009,2.54-16.171,2.678-24.346"/>
		<path style="fill:#EA5533;" d="M108.34,417.496c1.643-8.009,2.54-16.171,2.678-24.346c0.127-7.546-0.501-15.455-4.477-21.87
			c-5.713-9.215-17.792-13.402-28.412-11.221c-10.62,2.181-19.577,9.935-24.839,19.414c-5.261,9.479-7.142,20.529-7.164,31.371"/>
		<path style="fill:#FFFFFF;" d="M58.233,385.807c-1.8,6.389-3.168,12.9-4.092,19.474c-0.121,0.864-0.231,1.772,0.078,2.588
			c0.641,1.696,2.718,2.263,4.509,2.543c9.381,1.462,19.005,1.357,28.351-0.311c1.78-0.318,3.658-0.747,4.94-2.023
			c0.79-0.786,1.275-1.821,1.715-2.845c2.786-6.478,4.421-13.45,4.804-20.491"/>
		<path style="fill:#EA5533;" d="M91.095,283.76c-8.782,6.524-15.812,15.382-20.173,25.415c-3.156,7.261-4.546,16.66,0.978,22.332
			c3.256,3.343,8.122,4.487,12.747,5.108c5.216,0.701,10.693,0.901,15.572-1.072c5.408-2.187,9.431-6.758,13.242-11.173
			c7.417-8.593,14.834-17.185,22.251-25.778"/>
		<path d="M139.164,361.692c-0.173,13.033-0.876,26.054-1.416,39.079c3.038,0.301,6.105,0.165,8.546-1.542
			c1.773-1.24,2.952-3.16,3.84-5.132c2.36-5.24,2.97-11.073,3.55-16.79c0.553-5.448,1.106-10.896,1.659-16.344
			C149.95,361.205,144.557,361.448,139.164,361.692z"/>
		<path style="fill:#FF6795;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M140.285,371.853c0.061,0.772-0.004,1.554-0.192,2.306"/>
		<path style="fill:#FF6795;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M140.089,377.427c0.08,1.091,0.021,2.193-0.177,3.269"/>
		<path style="fill:#FF6795;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M139.75,383.772c-0.129,1.217-0.258,2.434-0.387,3.651"/>
		<path style="fill:#FF6795;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M59.371,387.802c-0.261,1.152-0.522,2.304-0.783,3.456"/>
		<path style="fill:#FF6795;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M56.693,399.719c-0.007,1.129-0.268,2.256-0.757,3.274"/>
		<path style="fill:#FF6795;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M59.763,409.333c0.881,0.197,1.792,0.259,2.692,0.183"/>
		<path style="fill:#FF6795;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M71.295,410.288c0.961,0,1.922,0,2.883,0"/>
		<path style="fill:#FF6795;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M82.249,409.904c1.1,0.079,2.215-0.052,3.267-0.384"/>
		<path style="fill:#FF6795;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M91.462,404.711c0.191-0.577,0.382-1.154,0.573-1.731"/>
		<path style="fill:#FF6795;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M92.666,400.116c0.186-0.579,0.372-1.157,0.558-1.736"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M113.262,415.086c2.301-1.322,1.481-11.822,1.64-14.702c0.334-6.037,0.493-12.564-1.199-18.436
			c-2.262-7.853-8.396-13.801-15.461-17.606c-13.873-7.473-30.949-9.305-44.397,0.397"/>
		<path style="fill:none;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M137.341,298.131c-2.957,2.221-4.17,7.784-5.697,11.021c-2.42,5.129-5.522,9.702-9.603,13.683
			c-7.176,7-16.931,14.278-27.248,14.987c-8.35,0.574-17.448-0.842-23.3-7.324c-1.842-2.041-7.411-10.528-6.077-12.979"/>
		<path style="fill:#FF6795;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
			M94.588,391.273c0.306-1.221,0.613-2.441,0.919-3.662"/>
		<path style="fill:#FFFFFF;" d="M82.44,346.384c0.548-1.626,0.767-3.503,1.145-4.922c0.787-2.951,1.594-5.948,3.179-8.558
			c1.768-2.912,5.396-9.789-0.702-10.767c-4.429-0.71-7.33,5.755-8.267,9.119c-1.324,4.754-4.94,10.962-3.636,16.065
			c0.816,3.196,4.119,3.948,6.711,1.679C81.61,348.352,82.092,347.419,82.44,346.384z"/>
		<path style="fill:#2F2955;" d="M77.637,343.776c-1.757,0-1.76,2.73,0,2.73C79.393,346.506,79.396,343.776,77.637,343.776z"/>
		<path style="fill:#2F2955;" d="M80.806,337.953c-0.055-0.169-0.149-0.316-0.282-0.439c-0.124-0.133-0.27-0.227-0.439-0.282
			c-0.163-0.085-0.338-0.125-0.526-0.118c-0.121,0.016-0.242,0.032-0.363,0.049c-0.231,0.065-0.432,0.182-0.602,0.351
			c-0.071,0.092-0.142,0.184-0.213,0.276c-0.123,0.213-0.186,0.443-0.186,0.689v0.128c-0.007,0.188,0.032,0.363,0.118,0.526
			c0.055,0.169,0.149,0.316,0.282,0.439c0.124,0.133,0.27,0.227,0.439,0.282c0.163,0.085,0.338,0.125,0.526,0.117l0.363-0.049
			c0.231-0.065,0.432-0.182,0.602-0.351c0.071-0.092,0.142-0.184,0.213-0.276c0.123-0.213,0.186-0.442,0.186-0.689v-0.128
			C80.93,338.291,80.891,338.116,80.806,337.953z"/>
		<path style="fill:#2F2955;" d="M82.249,330.324c-1.757,0-1.76,2.73,0,2.73C84.005,333.054,84.008,330.324,82.249,330.324z"/>
	</g>
	<g>
		<path style="fill:#EA5533;" d="M222.991,433.402h-83.253v-28.486h83.253c7.866,0,14.243,6.377,14.243,14.243l0,0
			C237.234,427.025,230.857,433.402,222.991,433.402z"/>
		<path style="fill:#FFDECF;" d="M220.059,430.422h-80.468l-0.198-23.339h80.468c6.797,0,12.307,5.51,12.307,12.307l0,0
			C232.167,426.186,226.856,430.422,220.059,430.422z"/>
		
			<line style="fill:none;stroke:#FFFFFF;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="139.508" y1="411.157" x2="227.913" y2="411.388"/>
		
			<line style="fill:none;stroke:#FFFFFF;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="139.508" y1="416.344" x2="230.075" y2="416.488"/>
		
			<line style="fill:none;stroke:#FFFFFF;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="139.508" y1="421.53" x2="230.53" y2="421.761"/>
		
			<line style="fill:none;stroke:#FFFFFF;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="139.508" y1="426.717" x2="227.913" y2="426.947"/>
		<path d="M228.256,409.83l-0.13,18.272c2.392-2.067,4.042-4.27,4.042-7.679v-2.068C232.167,414.946,230.648,411.897,228.256,409.83
			z"/>
		<path style="fill:#1B56CF;" d="M223.991,402.592v-7.663h4.956v-1.078c0-0.776-0.629-1.405-1.405-1.405h-99.137
			c-3.48,0-6.301,2.821-6.301,6.301c0,3.48,2.821,6.302,6.301,6.302h99.137c0.776,0,1.405-0.629,1.405-1.405v-1.052H223.991z"/>
		<path style="fill:#F5E9DE;" d="M228.64,402.568h-97.183c-2.11,0-3.821-1.711-3.821-3.821l0,0c0-2.11,1.711-3.821,3.821-3.821
			h97.183V402.568z"/>
		<path style="fill:#1B56CF;" d="M221.763,392.54h-74.922c-1.486,0-2.69-1.204-2.69-2.69v-12.56c0-1.486,1.204-2.69,2.69-2.69
			h74.922c1.486,0,2.69,1.204,2.69,2.69v12.56C224.453,391.336,223.248,392.54,221.763,392.54z"/>
		<path style="fill:#5AC794;" d="M215.467,374.535h-80.024c-0.724,0-1.31-0.587-1.31-1.31V367.3c0-0.724,0.587-1.31,1.31-1.31
			h80.024c0.724,0,1.31,0.587,1.31,1.31v5.925C216.777,373.948,216.19,374.535,215.467,374.535z"/>
		<path d="M213.782,384.615H156.51c-0.941,0-1.704-0.763-1.704-1.704l0,0c0-0.941,0.763-1.704,1.704-1.704h57.272
			c0.941,0,1.704,0.763,1.704,1.704l0,0C215.486,383.852,214.723,384.615,213.782,384.615z"/>
		
			<line style="fill:none;stroke:#FFFFFF;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="128.443" y1="396.865" x2="228.179" y2="397.096"/>
		
			<line style="fill:none;stroke:#FFFFFF;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="128.443" y1="400.323" x2="228.179" y2="400.553"/>
		<polygon points="225.604,405.214 139.546,404.66 139.425,410.237 		"/>
		<polygon points="220.735,392.487 219.524,395.152 145.342,394.828 146.126,392.245 		"/>
	</g>
</g>
</svg>
