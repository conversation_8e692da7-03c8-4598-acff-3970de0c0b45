import React, { useRef, useState } from 'react';
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
    Link2,
    FolderOpen,
    Type,
    FileText,
    Image,
    Video,
    AudioLines,
    File,
    Upload,
    Loader2,
    X
} from 'lucide-react';
import { ResourceMetadata } from './types';
import { useFileUpload } from '@/lib/utils';
import { toast } from '@/hooks/use-toast';

interface ResourceMetadataFormProps {
    label: string;
    metadata: ResourceMetadata | null;
    onChange: (metadata: ResourceMetadata | null) => void;
}

const ResourceMetadataForm: React.FC<ResourceMetadataFormProps> = ({
    label,
    metadata,
    onChange
}) => {
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isUploading, setIsUploading] = useState(false);
    const { uploadFile, progress } = useFileUpload();

    const handleToggle = (checked: boolean) => {
        if (checked) {
            onChange({
                resourceSource: '',
                resourcePath: '',
                resourceName: '',
                resourceTitle: '',
                resourceDescription: ''
            });
        } else {
            onChange(null);
        }
    };

    const handleChange = (field: keyof ResourceMetadata, value: string) => {
        if (metadata) {
            onChange({
                ...metadata,
                [field]: value
            });
        }
    };

    const getIcon = () => {
        switch (label.toLowerCase()) {
            case 'question image':
            case 'answer image':
            case 'explanation image':
                return <Image className="w-4 h-4" />;
            case 'question video':
            case 'answer video':
            case 'explanation video':
                return <Video className="w-4 h-4" />;
            case 'question audio':
            case 'answer audio':
            case 'explanation audio':
                return <AudioLines className="w-4 h-4" />;
            default:
                return <File className="w-4 h-4" />;
        }
    };

    const getAcceptedFileTypes = () => {
        switch (label.toLowerCase()) {
            case 'question image':
            case 'answer image':
            case 'explanation image':
                return 'image/*';
            case 'question video':
            case 'answer video':
            case 'explanation video':
                return 'video/*';
            case 'question audio':
            case 'answer audio':
            case 'explanation audio':
                return 'audio/*';
            default:
                return '*/*';
        }
    };

    const getMediaType = () => {
        switch (label.toLowerCase()) {
            case 'question image':
            case 'answer image':
            case 'explanation image':
                return 'image';
            case 'question video':
            case 'answer video':
            case 'explanation video':
                return 'video';
            case 'question audio':
            case 'answer audio':
            case 'explanation audio':
                return 'audio';
            default:
                return 'other';
        }
    };

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        // Validate file size (10MB limit)
        const maxSize = 10 * 1024 * 1024; // 10MB in bytes
        if (file.size > maxSize) {
            toast({
                title: "File too large",
                description: "Please select a file smaller than 10MB",
                variant: "destructive",
            });
            return;
        }

        try {
            setIsUploading(true);
            const fileName = `${Date.now()}-${file.name}`;

            // Await the upload and get the uploaded URL
            const uploadedUrl = await uploadFile(file, fileName);

            if (uploadedUrl && metadata) {
                onChange({
                    ...metadata,
                    resourceSource: uploadedUrl,
                    resourceName: file.name,
                    resourcePath: fileName,
                });
                toast({
                    title: "Upload successful",
                    description: "File has been uploaded successfully",
                });
            }
        } catch (error) {
            console.error("Error uploading file:", error);
            toast({
                title: "Upload failed",
                description:
                    error instanceof Error
                        ? error.message
                        : "An error occurred while uploading the file",
                variant: "destructive",
            });
        } finally {
            setIsUploading(false);
            if (fileInputRef.current) {
                fileInputRef.current.value = "";
            }
        }
    };

    const renderPreview = () => {
        if (!metadata?.resourceSource) return null;

        const mediaType = getMediaType();
        const previewClasses = "mt-4 rounded-lg border bg-muted/20 p-2";

        switch (mediaType) {
            case 'image':
                return (
                    <div className={previewClasses}>
                        <img
                            src={metadata.resourceSource}
                            alt={metadata.resourceTitle || "Preview"}
                            className="max-h-[200px] w-auto mx-auto rounded"
                            onError={() => {
                                toast({
                                    title: "Error loading image",
                                    description: "The image URL might be invalid or inaccessible",
                                    variant: "destructive"
                                });
                            }}
                        />
                    </div>
                );
            case 'video':
                return (
                    <div className={previewClasses}>
                        <video
                            src={metadata.resourceSource}
                            controls
                            className="max-h-[200px] w-auto mx-auto rounded"
                            onError={() => {
                                toast({
                                    title: "Error loading video",
                                    description: "The video URL might be invalid or inaccessible",
                                    variant: "destructive"
                                });
                            }}
                        >
                            Your browser does not support the video tag.
                        </video>
                    </div>
                );
            case 'audio':
                return (
                    <div className={previewClasses}>
                        <audio
                            src={metadata.resourceSource}
                            controls
                            className="w-full"
                            onError={() => {
                                toast({
                                    title: "Error loading audio",
                                    description: "The audio URL might be invalid or inaccessible",
                                    variant: "destructive"
                                });
                            }}
                        >
                            Your browser does not support the audio tag.
                        </audio>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <Card className="p-4">
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                        {getIcon()}
                        <Label className="font-medium">{label}</Label>
                    </div>
                    <Switch
                        checked={!!metadata}
                        onCheckedChange={handleToggle}
                    />
                </div>

                {metadata && (
                    <div className="space-y-3 pt-2">
                        <div className="space-y-2">
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                <Link2 className="w-4 h-4" />
                                <Label>Source URL</Label>
                            </div>
                            <div className="flex gap-2">
                                <Input
                                    placeholder="Enter resource source URL"
                                    value={metadata.resourceSource}
                                    onChange={(e) => handleChange('resourceSource', e.target.value)}
                                />
                                <input
                                    type="file"
                                    className="hidden"
                                    ref={fileInputRef}
                                    accept={getAcceptedFileTypes()}
                                    onChange={handleFileUpload}
                                />
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => fileInputRef.current?.click()}
                                    disabled={isUploading}
                                >
                                    {isUploading ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                    ) : (
                                        <Upload className="h-4 w-4" />
                                    )}
                                </Button>
                            </div>
                            {isUploading && (
                                <div className="space-y-2">
                                    <Progress value={progress} className="h-1" />
                                    <p className="text-sm text-muted-foreground">
                                        Uploading... {Math.round(progress)}%
                                    </p>
                                </div>
                            )}
                        </div>

                        {renderPreview()}

                        <div className="space-y-2">
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                <FolderOpen className="w-4 h-4" />
                                <Label>Path</Label>
                            </div>
                            <Input
                                placeholder="Enter resource path"
                                value={metadata.resourcePath}
                                onChange={(e) => handleChange('resourcePath', e.target.value)}
                            />
                        </div>

                        <div className="space-y-2">
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                <Type className="w-4 h-4" />
                                <Label>Name</Label>
                            </div>
                            <Input
                                placeholder="Enter resource name"
                                value={metadata.resourceName}
                                onChange={(e) => handleChange('resourceName', e.target.value)}
                            />
                        </div>

                        <div className="space-y-2">
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                <Type className="w-4 h-4" />
                                <Label>Title</Label>
                            </div>
                            <Input
                                placeholder="Enter resource title"
                                value={metadata.resourceTitle}
                                onChange={(e) => handleChange('resourceTitle', e.target.value)}
                            />
                        </div>

                        <div className="space-y-2">
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                <FileText className="w-4 h-4" />
                                <Label>Description</Label>
                            </div>
                            <Textarea
                                placeholder="Enter resource description"
                                value={metadata.resourceDescription}
                                onChange={(e) => handleChange('resourceDescription', e.target.value)}
                                rows={3}
                            />
                        </div>
                    </div>
                )}
            </div>
        </Card>
    );
};

export default ResourceMetadataForm;
