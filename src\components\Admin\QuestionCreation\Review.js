import { useSelector } from "react-redux";

const Review = ({ handleReviewComplete, handleCancel }) => {
  const questionText = useSelector(
    (state) => state.questions.questions.questionText
  );
  const answers = useSelector((state) => state.questions.questions.answers);
  const questionType = useSelector(
    (state) => state.questions.questions.questionType
  );
  const correctAnswer = useSelector(
    (state) => state.questions.questions.correctAnswer
  );
  const explanation = useSelector(
    (state) => state.questions.questions.explanation
  );
  const questionText2 = useSelector(
    (state) => state.questions.questions.questionText2
  );

  const getCheckedValue = (indexval, ans) => {
    switch (questionType) {
      case "SINGLECHOICE":
        return singleChoiceChecked(indexval);
      case "MULTICHOICE":
        return multiChoiceChecked(ans);
    }
  };

  const singleChoiceChecked = (indexval) => {
    return indexval === correctAnswer ? true : false;
  };

  const multiChoiceChecked = (ans) => {
    return correctAnswer?.includes(ans);
  };

  return (
    <>
      <br />
      <span className="rev">Review Question </span>
      <br />
      <br />
      <span className="rev">Question </span>
      <div
        className="html-content question-text"
        dangerouslySetInnerHTML={{
          __html: questionText + ' ' + questionText2,
        }}
      />

      <span className="rev">Answers</span>

      {questionType === "MATCH" &&
        correctAnswer &&
        Object.keys(correctAnswer)?.length > 0 && (
          <>
            {Object.keys(correctAnswer).map((keyName, index) => (
              <div className="answer-option">
                <div
                  key={index}
                  style={{ display: "flex" }}
                  className="html-content"
                >
                  <div
                    className="html-content answer-text"
                    dangerouslySetInnerHTML={{
                      __html: keyName,
                    }}
                  />
                  <div
                    className="html-content answer-text"
                    dangerouslySetInnerHTML={{
                      __html: correctAnswer[keyName],
                    }}
                  />
                </div>
              </div>
            ))}
          </>
        )}

      {correctAnswer !== null &&
        (questionType === "TEXTINPUT" || questionType === "BINARY") && (
          <div className="answer-option">
            <div
              className="html-content answer-text"
              dangerouslySetInnerHTML={{
                __html: correctAnswer,
              }}
            />
          </div>
        )}

      {answers?.map((item, index) => (
        <div key={index} className="answer-option">
          {(questionType === "MULTICHOICE" ||
            questionType === "SINGLECHOICE" ||
            questionType === "BINARY") && (
            <label className="checkbox-toggle">
              <input
                type="checkbox"
                disabled={true}
                //checked={item?.isCorrect}
                checked={getCheckedValue(index, item?.answerText)}
              />
              <span className="toggle-label"></span>
            </label>
          )}
          <div
            className="html-content answer-text"
            dangerouslySetInnerHTML={{
              __html: item.answerText,
            }}
          />
        </div>
      ))}

      <span className="rev">Explanation</span>
      <div
        className="html-content explanation-text"
        dangerouslySetInnerHTML={{
          __html: explanation,
        }}
      />
      <div className="button-container">
        <div className="align-right">
          <button
            id="cancelbutton"
            className="cancel-button"
            onClick={() => handleCancel()}
          >
            Cancel
          </button>
        </div>
        <div className="align-right">
          <button onClick={handleReviewComplete} className="submit-button">
            Submit
          </button>
        </div>
      </div>
    </>
  );
};

export default Review;
