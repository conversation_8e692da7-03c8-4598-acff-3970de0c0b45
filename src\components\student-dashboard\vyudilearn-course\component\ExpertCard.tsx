import React from 'react';
import { Button } from '@/components/ui/button';

interface ExpertCardProps {
  expert: {
    name: string;
    title: string;
    university: string;
    bio: string;
    image: string;
  };
}

const ExpertCard: React.FC<ExpertCardProps> = ({ expert }) => {
  return (
    <div className="bg-white rounded-[10px] shadow-md flex flex-col md:flex-row items-center md:items-stretch p-2 sm:p-0 gap-4 border-2 border-[#347468]">
      {/* Image */}
      <div className="flex-shrink-0 flex justify-center">
        <img src={expert.image} alt={expert.name} className="w-32 h-32 object-cover border border-[#347468] rounded-[10px]" />
      </div>

      {/* Details */}
      <div className="flex-shrink-0 w-full md:w-48 text-center md:text-left border-b-2 md:border-b-0 md:border-r-2 rounded-[10px] border-[#347468] pb-4 md:pb-0 md:pr-4 flex flex-col items-center md:items-start justify-center">
        <h3 className="text-lg font-semibold text-black">{expert.name}</h3>
        <p className="text-sm text-gray-600">{expert.title}</p>
        <p className="text-sm text-[#347468] font-semibold">{expert.university}</p>
        <Button variant="outline" className="mt-2 text-[#347468] border-[#347468] rounded-full hover:bg-[#347468] hover:text-white px-3 py-1 text-xs">
          Watch Intro Video
        </Button>
      </div>

      {/* Bio */}
      <div className="flex-1 flex items-center text-center md:text-left">
        <p className="text-sm text-black">
          <span className="font-semibold text-[#347468] ">Bio: </span>
          <div className='text-xs'>{expert.bio}</div>
        </p>
      </div>
    </div>
  );
};

export default ExpertCard;
