import React from 'react';
import { Card } from "@/components/ui/card";
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Users } from 'lucide-react';
import { LifecycleStage, ProjectCard } from '@/types/projectCenter';

interface ProjectLifecycleTrackerProps {
  stages: LifecycleStage[];
  activeTab: 'academic' | 'industry';
  onTabChange: (tab: 'academic' | 'industry') => void;
  onProjectClick: (project: ProjectCard) => void;
}

const ProjectLifecycleTracker: React.FC<ProjectLifecycleTrackerProps> = ({ 
  stages, 
  activeTab, 
  onTabChange,
  onProjectClick
}) => {
  return (
    <div className="space-y-3">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <h2 className="text-zinc-800 text-xl font-semibold">Project Lifecycle Tracker</h2>
        <Tabs 
          value={activeTab} 
          onValueChange={(value) => onTabChange(value as 'academic' | 'industry')} 
          className="w-full sm:w-44"
        >
          <TabsList className="grid w-full grid-cols-2 h-10 bg-white border border-slate-600 p-0 rounded-xl overflow-hidden">
            <TabsTrigger 
              value="academic" 
              className="data-[state=active]:bg-slate-600 data-[state=active]:text-white rounded-l-lg px-3 py-2 text-base"
            >
              Academic
            </TabsTrigger>
            <TabsTrigger 
              value="industry" 
              className="data-[state=active]:bg-slate-600 data-[state=active]:text-white rounded-r-lg px-3 py-2 text-base"
            >
              Industry
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      <div className="bg-stone-50 rounded-xl border border-lime-500 overflow-x-auto">
        <div className="grid grid-cols-6 min-w-[1200px]">
          {stages.map((stage, index) => (
            <div 
              key={stage.id} 
              className={`flex flex-col ${index < stages.length - 1 ? 'border-r border-lime-500' : ''}`}
            >
              <div className={`p-3 bg-white border-b border-stone-300 ${index === 0 ? 'rounded-tl-xl' : ''} ${index === stages.length - 1 ? 'rounded-tr-xl' : ''}`}>
                <h3 className="text-slate-600 text-xs font-semibold leading-tight text-center">
                  {stage.title}
                </h3>
              </div>
              <ScrollArea className="h-[280px] p-3">
                <div className="space-y-2.5">
                  {stage.projects.map(project => (
                    <Card 
                      key={project.id} 
                      className="p-2.5 bg-white rounded-xl border-[0.5px] border-lime-500 hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => onProjectClick(project)}
                    >
                      <p className="text-zinc-800 text-sm font-semibold leading-tight">
                        {project.name}
                      </p>
                      <div className="flex items-center gap-1.5 mt-1">
                        <Users size={12} className="text-slate-600" />
                        <p className="text-neutral-500 text-[10px] font-normal">
                          {project.members} Members
                        </p>
                      </div>
                    </Card>
                  ))}
                  {stage.projects.length === 0 && (
                    <p className="text-xs text-gray-400 text-center py-10">No projects</p>
                  )}
                </div>
              </ScrollArea>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProjectLifecycleTracker;