import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Loader2, Search, Pencil, Trash2, BookOpen, Crown } from 'lucide-react';
import { useGetAdminCoursesQuery, useDeleteCourseMutation } from '@/APIConnect';
import { Course } from './types';
import { toast } from '@/hooks/use-toast';
import { useAuth0 } from "@auth0/auth0-react";
import { Dialog } from '@radix-ui/react-dialog';
import { DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import ChapterAnalyticsExample from '@/components/Analytics/ChapterAnalyticsExample';
import CoursesTab from '../Schools/CoursesTab';

const CoursesPage = () => {
    const navigate = useNavigate();
    const [searchQuery, setSearchQuery] = useState('');
    const { user } = useAuth0();

    // Safe access to user properties with type checking
    const roleId = user?.["http://learnido-app/roleId"] as string | undefined;
    const schoolId = user?.["http://learnido-app/schoolId"] as string | undefined;

    const { data: coursesData, isLoading: isLoadingCourses, refetch } = useGetAdminCoursesQuery('');
    const [deleteCourse] = useDeleteCourseMutation();

    const handleEdit = (course: Course) => {
        const basePath = roleId === "2" ? "/school-admin" : "/admin";
        navigate(`${basePath}/courses/${course.id}`);
    };

    const handleDelete = async (id: string) => {
        if (!window.confirm('Are you sure you want to delete this course?')) return;
        try {
            await deleteCourse({ id }).unwrap();
            toast({
                title: "Course deleted successfully",
                variant: "default",
            });
            refetch();
        } catch (error) {
            console.error('Error deleting course:', error);
            toast({
                title: "Failed to delete course",
                description: "Please try again later",
                variant: "destructive",
            });
        }
    };

    const filteredCourses = (cd: any) => {
        return (cd?.resultObject || []).filter((course: Course) => {
            // Filter by search query
            const matchesSearch = searchQuery
                ? course.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                course.description.toLowerCase().includes(searchQuery.toLowerCase())
                : true;

            // // For SchoolAdmin, only show courses associated with their school
            // const matchesSchool = roleId === "2" && schoolId
            //     ? course.schoolId === schoolId
            //     : true;

            return matchesSearch;
        })
    };

    // If user role is not available, show loading state
    if (!roleId) {
        return (
            <div className="flex justify-center items-center h-screen">
                <Loader2 className="w-8 h-8 animate-spin" />
            </div>
        );
    }

    return (
        <div className="container mx-auto p-6">
            {/* <CoursesTab
                schoolId={schoolId ?? ""}
                isSchoolAdmin={roleId === "2"}
            /> */}
            <div className="flex flex-col space-y-8">
                <Card className="p-6">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
                        <div>
                            <h1 className="text-3xl font-semibold tracking-tight text-foreground">Courses</h1>
                            <p className="text-sm text-muted-foreground mt-1">Manage and organize your educational content</p>
                        </div>
                        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full md:w-auto">
                            <div className="relative flex-1 sm:flex-none">
                                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search courses..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="pl-9 w-full sm:w-[260px] bg-background"
                                />
                            </div>
                            {roleId === "1" && (
                                <Button
                                    onClick={() => navigate('/admin/courses/create')}
                                    className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-primary-foreground transition-colors"
                                >
                                    <Plus className="w-4 h-4 mr-2" />
                                    Create Course
                                </Button>
                            )}
                        </div>
                    </div>
                </Card>

                <Card>
                    <ScrollArea className="h-[calc(100vh-280px)] rounded-md">
                        {isLoadingCourses ? (
                            <div className="flex justify-center items-center h-32">
                                <div className="flex flex-col items-center space-y-3">
                                    <Loader2 className="w-8 h-8 animate-spin text-muted-foreground" />
                                    <p className="text-sm text-muted-foreground">Loading courses...</p>
                                </div>
                            </div>
                        ) : (
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-muted/50">
                                        <TableHead className="w-[300px] font-bold text-primary">Course Details</TableHead>
                                        <TableHead className="font-bold text-primary">Description</TableHead>
                                        {/* <TableHead className="w-[100px] font-bold text-primary">Status</TableHead> */}
                                        <TableHead className="w-[120px] font-bold text-primary">Created On</TableHead>
                                        <TableHead className="w-[100px] text-right font-bold text-primary">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredCourses(coursesData).map((course: Course) => (
                                        <TableRow key={course.id} className="group hover:bg-muted/50 transition-colors">
                                            <TableCell>
                                                <div className="flex items-center space-x-3">
                                                    <div className="h-10 w-10 rounded-lg bg-muted flex items-center justify-center">
                                                        <BookOpen className="h-5 w-5 text-muted-foreground" />
                                                    </div>
                                                    <div>
                                                        <div className="font-medium flex items-center space-x-2">
                                                            <span className="text-primary">{course.name}</span>
                                                            {course.premium && (
                                                                <Crown className="h-4 w-4 text-yellow-500" />
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-muted-foreground">
                                                {course.description}
                                            </TableCell>
                                            {/* <TableCell>
                                                <Badge variant={course.premium ? "default" : "secondary"}>
                                                    {course.premium ? 'Premium' : 'Free'}
                                                </Badge>
                                            </TableCell> */}
                                            <TableCell className="text-muted-foreground">
                                                {new Date(course.createdOn).toLocaleDateString()}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex justify-end space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        onClick={() => handleEdit(course)}
                                                        className="hover:bg-muted hover:text-foreground"
                                                    >
                                                        <Pencil className="h-4 w-4" />
                                                    </Button>
                                                    {roleId === "1" && (
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            onClick={() => handleDelete(course.id)}
                                                            className="hover:bg-muted hover:text-destructive"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    )}
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                    {filteredCourses(coursesData).length === 0 && (
                                        <TableRow>
                                            <TableCell colSpan={5}>
                                                <div className="flex flex-col items-center justify-center py-8 space-y-2">
                                                    <BookOpen className="h-8 w-8 text-muted-foreground" />
                                                    <p className="text-muted-foreground text-sm">No courses found</p>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        )}
                    </ScrollArea>
                </Card>
            </div>
        </div>
    );
};

export default CoursesPage;
